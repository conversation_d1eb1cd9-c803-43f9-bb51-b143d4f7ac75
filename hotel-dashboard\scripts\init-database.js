/**
 * <PERSON><PERSON><PERSON> to initialize the database with sample data
 * 
 * This script:
 * 1. Creates sample hotels
 * 2. Creates sample rooms
 * 3. Creates sample bookings
 * 
 * Run with: node scripts/init-database.js
 */

const { initializeApp } = require('firebase/app');
const { 
  getAuth, 
  signInWithEmailAndPassword,
  signOut
} = require('firebase/auth');
const { 
  getFirestore,
  doc, 
  setDoc,
  collection,
  getDocs,
  serverTimestamp
} = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI",
  authDomain: "linkinblink-hotel.firebaseapp.com",
  projectId: "linkinblink-hotel",
  storageBucket: "linkinblink-hotel.firebasestorage.app",
  messagingSenderId: "132613661307",
  appId: "1:132613661307:web:a9e5c8f4c2b8e9a9e5c8f4",
  measurementId: "G-MEASUREMENT_ID"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Sample data
const sampleData = {
  hotels: [
    {
      id: 'hotel1',
      name: 'Grand Hotel',
      description: 'A luxurious hotel in the heart of the city',
      address: '123 Main Street',
      city: 'New York',
      country: 'USA',
      zipCode: '10001',
      phone: '******-456-7890',
      email: '<EMAIL>',
      website: 'https://www.grandhotel.com',
      rating: 4.5,
      price: 200,
      amenities: ['WiFi', 'Pool', 'Spa', 'Gym', 'Restaurant'],
      images: [
        'https://source.unsplash.com/random/800x600?hotel',
        'https://source.unsplash.com/random/800x600?hotel-room',
        'https://source.unsplash.com/random/800x600?hotel-lobby'
      ],
      latitude: 40.7128,
      longitude: -74.0060,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    },
    {
      id: 'hotel2',
      name: 'Seaside Resort',
      description: 'A beautiful resort by the beach',
      address: '456 Ocean Drive',
      city: 'Miami',
      country: 'USA',
      zipCode: '33139',
      phone: '******-654-3210',
      email: '<EMAIL>',
      website: 'https://www.seasideresort.com',
      rating: 4.8,
      price: 350,
      amenities: ['WiFi', 'Pool', 'Beach Access', 'Spa', 'Gym', 'Restaurant', 'Bar'],
      images: [
        'https://source.unsplash.com/random/800x600?resort',
        'https://source.unsplash.com/random/800x600?beach-hotel',
        'https://source.unsplash.com/random/800x600?resort-pool'
      ],
      latitude: 25.7617,
      longitude: -80.1918,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    }
  ],
  rooms: [
    {
      id: 'room1',
      hotelId: 'hotel1',
      name: 'Deluxe Room',
      description: 'A spacious room with a king-size bed',
      price: 200,
      capacity: 2,
      amenities: ['WiFi', 'TV', 'Mini Bar', 'Air Conditioning'],
      images: [
        'https://source.unsplash.com/random/800x600?hotel-room',
        'https://source.unsplash.com/random/800x600?hotel-bathroom'
      ],
      status: 'available',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    },
    {
      id: 'room2',
      hotelId: 'hotel1',
      name: 'Suite',
      description: 'A luxurious suite with a separate living area',
      price: 350,
      capacity: 4,
      amenities: ['WiFi', 'TV', 'Mini Bar', 'Air Conditioning', 'Jacuzzi'],
      images: [
        'https://source.unsplash.com/random/800x600?hotel-suite',
        'https://source.unsplash.com/random/800x600?hotel-living-room'
      ],
      status: 'available',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    }
  ]
};

async function initializeDatabase() {
  try {
    console.log('Signing in as admin...');
    
    // Sign in as admin
    await signInWithEmailAndPassword(auth, '<EMAIL>', 'Admin123!');
    console.log('Successfully signed in as admin');
    
    // Get the current user
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('Failed to get current user');
    }
    
    // Check if hotels already exist
    const hotelsCollection = collection(db, 'hotels');
    const hotelsSnapshot = await getDocs(hotelsCollection);
    
    if (!hotelsSnapshot.empty) {
      console.log('Hotels already exist. Skipping initialization.');
    } else {
      console.log('Initializing database with sample data...');
      
      // Add hotels
      for (const hotel of sampleData.hotels) {
        // Set the vendorId to the current user's ID
        const hotelWithVendorId = {
          ...hotel,
          vendorId: user.uid
        };
        
        await setDoc(doc(db, 'hotels', hotel.id), hotelWithVendorId);
        console.log(`Hotel ${hotel.name} created successfully`);
      }
      
      // Add rooms
      for (const room of sampleData.rooms) {
        await setDoc(doc(db, 'rooms', room.id), room);
        console.log(`Room ${room.name} created successfully`);
      }
      
      console.log('Database initialized successfully');
    }
    
    // Sign out
    await signOut(auth);
    console.log('Signed out');
    
    console.log('Script completed');
    process.exit(0);
  } catch (error) {
    console.error('Error initializing database:', error);
    process.exit(1);
  }
}

// Run the function
initializeDatabase();
