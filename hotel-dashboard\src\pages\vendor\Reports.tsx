import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardContent,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  DateRange as DateRangeIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Print as PrintIcon,
  BarChart as BarChartIcon,
  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon
} from '@mui/icons-material';
import { auth } from '../../firebase/config';
import { getHotelsForVendor, HotelData } from '../../firebase/hotelService';
import {
  getRevenueReport,
  getOccupancyReport,
  getBookingAnalytics,
  getGuestAnalytics,
  getComprehensiveReport,
  DateRange,
  RevenueData,
  OccupancyData,
  BookingAnalyticsData,
  GuestAnalyticsData,
  ReportData
} from '../../services/reportingService';

// Define TabPanel component for tab content
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`report-tabpanel-${index}`}
      aria-labelledby={`report-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const Reports: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hotels, setHotels] = useState<HotelData[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');

  // Date range state
  const [dateRange, setDateRange] = useState<DateRange>({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1), // First day of previous month
    endDate: new Date(new Date().getFullYear(), new Date().getMonth(), 0) // Last day of previous month
  });

  // Report data state
  const [reportData, setReportData] = useState<ReportData | null>(null);

  // Load data on component mount
  useEffect(() => {
    fetchHotels();
  }, []);

  // Fetch reports when hotel or date range changes
  useEffect(() => {
    if (selectedHotel) {
      fetchReports();
    }
  }, [selectedHotel, dateRange]);

  // Fetch hotels
  const fetchHotels = async () => {
    try {
      setLoading(true);
      if (!auth.currentUser) return;

      const hotelsList = await getHotelsForVendor(auth.currentUser.uid);
      setHotels(hotelsList);

      if (hotelsList.length > 0) {
        setSelectedHotel(hotelsList[0].id || '');
      }

      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching hotels:', err);
      setError('Failed to load hotels');
      setLoading(false);
    }
  };

  // Fetch reports
  const fetchReports = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await getComprehensiveReport(selectedHotel, dateRange);
      setReportData(data);

      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching reports:', err);
      setError(err.message || 'Failed to load reports');
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle hotel change
  const handleHotelChange = (event: any) => {
    setSelectedHotel(event.target.value);
  };

  // Handle date range change
  const handleDateChange = (field: 'startDate' | 'endDate') => (event: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = new Date(event.target.value);

    setDateRange(prev => ({
      ...prev,
      [field]: newDate
    }));
  };

  // Handle preset date range selection
  const handlePresetRange = (preset: 'lastMonth' | 'lastQuarter' | 'lastYear' | 'ytd') => {
    const now = new Date();
    let startDate: Date;
    let endDate: Date;

    switch (preset) {
      case 'lastMonth':
        // Last month
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        endDate = new Date(now.getFullYear(), now.getMonth(), 0);
        break;
      case 'lastQuarter':
        // Last quarter
        const currentQuarter = Math.floor(now.getMonth() / 3);
        const lastQuarterStartMonth = (currentQuarter - 1 + 4) % 4 * 3;
        startDate = new Date(now.getFullYear() - (lastQuarterStartMonth > now.getMonth() ? 1 : 0), lastQuarterStartMonth, 1);
        endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 3, 0);
        break;
      case 'lastYear':
        // Last year
        startDate = new Date(now.getFullYear() - 1, 0, 1);
        endDate = new Date(now.getFullYear() - 1, 11, 31);
        break;
      case 'ytd':
        // Year to date
        startDate = new Date(now.getFullYear(), 0, 1);
        endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      default:
        return;
    }

    setDateRange({ startDate, endDate });
  };

  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1
    }).format(value);
  };

  // Format date
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle print report
  const handlePrintReport = () => {
    window.print();
  };

  // Handle download report
  const handleDownloadReport = () => {
    if (!reportData) return;

    // Create a JSON blob
    const reportJson = JSON.stringify(reportData, null, 2);
    const blob = new Blob([reportJson], { type: 'application/json' });

    // Create a download link
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `report_${selectedHotel}_${formatDate(dateRange.startDate)}_${formatDate(dateRange.endDate)}.json`;
    document.body.appendChild(a);
    a.click();

    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Reports & Analytics
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchReports}
            sx={{ mr: 1 }}
            disabled={loading || !selectedHotel}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<PrintIcon />}
            onClick={handlePrintReport}
            sx={{ mr: 1 }}
            disabled={!reportData}
          >
            Print
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={handleDownloadReport}
            disabled={!reportData}
          >
            Download
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Hotel selector and date range */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel id="hotel-select-label">Select Hotel</InputLabel>
              <Select
                labelId="hotel-select-label"
                value={selectedHotel}
                label="Select Hotel"
                onChange={(e: any) => setSelectedHotel(e.target.value)}
              >
                {hotels.map((hotel) => (
                  <MenuItem key={hotel.id} value={hotel.id}>
                    {hotel.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              label="Start Date"
              type="date"
              value={dateRange.startDate.toISOString().split('T')[0]}
              onChange={handleDateChange('startDate')}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>

          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              label="End Date"
              type="date"
              value={dateRange.endDate.toISOString().split('T')[0]}
              onChange={handleDateChange('endDate')}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>

          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel id="preset-range-label">Preset Range</InputLabel>
              <Select
                labelId="preset-range-label"
                value=""
                label="Preset Range"
                onChange={(e: any) => handlePresetRange(e.target.value)}
              >
                <MenuItem value="lastMonth">Last Month</MenuItem>
                <MenuItem value="lastQuarter">Last Quarter</MenuItem>
                <MenuItem value="lastYear">Last Year</MenuItem>
                <MenuItem value="ytd">Year to Date</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : hotels.length === 0 ? (
        <Alert severity="info">
          You need to create at least one hotel before you can view reports.
        </Alert>
      ) : !reportData ? (
        <Alert severity="info">
          Select a hotel and date range to view reports.
        </Alert>
      ) : (
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab label="Overview" />
            <Tab label="Revenue" />
            <Tab label="Occupancy" />
            <Tab label="Bookings" />
            <Tab label="Guests" />
          </Tabs>

          {/* Overview Tab */}
          <TabPanel value={tabValue} index={0}>
            <Typography variant="h6" gutterBottom>
              Performance Overview
            </Typography>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              {formatDate(dateRange.startDate)} - {formatDate(dateRange.endDate)}
            </Typography>

            <Grid container spacing={3} sx={{ mt: 2 }}>
              {/* Revenue Card */}
              <Grid item xs={12} md={6} lg={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">
                          Total Revenue
                        </Typography>
                        <Typography variant="h4" sx={{ my: 1 }}>
                          {formatCurrency(reportData.revenue.totalRevenue)}
                        </Typography>
                      </Box>
                      <TrendingUpIcon color="primary" />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      ADR: {formatCurrency(reportData.revenue.averageDailyRate)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      RevPAR: {formatCurrency(reportData.revenue.revenuePerAvailableRoom)}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              {/* Occupancy Card */}
              <Grid item xs={12} md={6} lg={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">
                          Average Occupancy
                        </Typography>
                        <Typography variant="h4" sx={{ my: 1 }}>
                          {formatPercentage(reportData.occupancy.averageOccupancy)}
                        </Typography>
                      </Box>
                      <BarChartIcon color="primary" />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Peak: {formatPercentage(reportData.occupancy.peakOccupancy)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Lowest: {formatPercentage(reportData.occupancy.lowestOccupancy)}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              {/* Bookings Card */}
              <Grid item xs={12} md={6} lg={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">
                          Total Bookings
                        </Typography>
                        <Typography variant="h4" sx={{ my: 1 }}>
                          {reportData.bookings.totalBookings}
                        </Typography>
                      </Box>
                      <PieChartIcon color="primary" />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Avg. Stay: {reportData.bookings.averageStayLength.toFixed(1)} nights
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Cancellation Rate: {formatPercentage(reportData.bookings.cancellationRate)}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              {/* Guests Card */}
              <Grid item xs={12} md={6} lg={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">
                          Total Guests
                        </Typography>
                        <Typography variant="h4" sx={{ my: 1 }}>
                          {reportData.guests.totalGuests}
                        </Typography>
                      </Box>
                      <PeopleIcon color="primary" />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      New Guests: {reportData.guests.newGuests}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Returning: {reportData.guests.returningGuests}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Revenue Tab */}
          <TabPanel value={tabValue} index={1}>
            <Typography variant="h6" gutterBottom>
              Revenue Analysis
            </Typography>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              {formatDate(dateRange.startDate)} - {formatDate(dateRange.endDate)}
            </Typography>

            <Alert severity="info" sx={{ mt: 3 }}>
              Revenue charts and detailed analysis will be implemented in the next phase.
            </Alert>
          </TabPanel>

          {/* Occupancy Tab */}
          <TabPanel value={tabValue} index={2}>
            <Typography variant="h6" gutterBottom>
              Occupancy Analysis
            </Typography>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              {formatDate(dateRange.startDate)} - {formatDate(dateRange.endDate)}
            </Typography>

            <Alert severity="info" sx={{ mt: 3 }}>
              Occupancy charts and detailed analysis will be implemented in the next phase.
            </Alert>
          </TabPanel>

          {/* Bookings Tab */}
          <TabPanel value={tabValue} index={3}>
            <Typography variant="h6" gutterBottom>
              Booking Analysis
            </Typography>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              {formatDate(dateRange.startDate)} - {formatDate(dateRange.endDate)}
            </Typography>

            <Alert severity="info" sx={{ mt: 3 }}>
              Booking charts and detailed analysis will be implemented in the next phase.
            </Alert>
          </TabPanel>

          {/* Guests Tab */}
          <TabPanel value={tabValue} index={4}>
            <Typography variant="h6" gutterBottom>
              Guest Analysis
            </Typography>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              {formatDate(dateRange.startDate)} - {formatDate(dateRange.endDate)}
            </Typography>

            <Alert severity="info" sx={{ mt: 3 }}>
              Guest charts and detailed analysis will be implemented in the next phase.
            </Alert>
          </TabPanel>
        </Paper>
      )}
    </Box>
  );
};

export default Reports;
