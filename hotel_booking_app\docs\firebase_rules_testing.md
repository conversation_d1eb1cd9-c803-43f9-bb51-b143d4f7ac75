# Firebase Security Rules for Testing

To quickly resolve the permission issues, you can temporarily use these highly permissive rules for testing. **Note that these rules are NOT suitable for production use as they allow full access to your database.**

## Temporary Testing Rules (Most Permissive)

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if true;  // Allow all operations for testing
    }
  }
}
```

## How to Apply These Rules

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to Firestore Database in the left sidebar
4. Click on the "Rules" tab
5. Replace the existing rules with the rules above
6. Click "Publish"

## Important Warning

⚠️ **SECURITY WARNING**: These rules allow anyone to read and write to your entire database. Only use these rules temporarily for testing in a development environment. Do not use these rules in production or with real user data.

## After Testing

Once you've confirmed that your app works with these permissive rules, you should immediately replace them with more secure rules. Here's a moderately secure version that still allows your app to function:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Base rules
    match /{document=**} {
      allow read, write: if false; // Default deny all
    }

    // User profiles
    match /users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    // Aadhaar verifications
    match /aadhaar_verifications/{verificationId} {
      // Allow authenticated users to read/write
      allow read, write: if request.auth != null;
    }

    // Hotels
    match /hotels/{hotelId} {
      allow read: if true; // Public read access
      allow write: if request.auth != null;
    }

    // Bookings
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null;
    }

    // Service requests
    match /service_requests/{requestId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## Troubleshooting

If you're still experiencing permission issues after applying these rules, check the following:

1. **Collection Name**: Make sure the collection name in your code matches exactly what you're using in the rules. Check for typos or case sensitivity issues.

2. **Authentication**: Ensure the user is properly authenticated before trying to access Firestore. You can check this with:
   ```dart
   final user = FirebaseAuth.instance.currentUser;
   if (user == null) {
     // User is not authenticated
     return;
   }
   ```

3. **Rules Propagation**: Sometimes it takes a few minutes for rule changes to propagate. Wait a few minutes after publishing new rules before testing again.

4. **Collection Path**: Verify that you're using the correct path to your collection. For example, if your collection is nested under another collection, the rules need to reflect that structure.

5. **Console Logs**: Check the Firebase console logs for any additional error information that might help diagnose the issue.

## Collection Structure

Make sure your Firestore collection structure matches what your code expects:

- Collection name: `aadhaar_verifications` (snake_case, not camelCase)
- Document fields:
  - `userId`: String (the Firebase Auth UID)
  - `maskedAadhaarNumber`: String
  - `status`: String ('pending' or 'verified')
  - `referenceId`: String
  - Other fields as needed

If your collection has a different name or structure, update either your code or your database structure to match.
