{"indexes": [{"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "checkIn", "order": "ASCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "checkOut", "order": "DESCENDING"}]}], "fieldOverrides": []}