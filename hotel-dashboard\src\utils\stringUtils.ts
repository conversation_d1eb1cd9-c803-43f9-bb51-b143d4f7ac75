/**
 * Safely converts a value to lowercase
 * @param value - The value to convert to lowercase
 * @returns The lowercase string or an empty string if the value is not a valid string
 */
export const safeToLowerCase = (value: any): string => {
  if (value === null || value === undefined) {
    return '';
  }
  
  try {
    if (typeof value === 'string') {
      return value.toLowerCase();
    }
    return String(value).toLowerCase();
  } catch (error) {
    console.error('Error in safeToLowerCase:', error);
    return '';
  }
};

/**
 * Safely checks if a string includes a substring
 * @param str - The string to check
 * @param searchValue - The substring to search for
 * @returns True if the string includes the substring, false otherwise
 */
export const safeIncludes = (str: any, searchValue: string): boolean => {
  if (str === null || str === undefined) {
    return false;
  }
  
  try {
    if (typeof str === 'string') {
      return str.toLowerCase().includes(searchValue.toLowerCase());
    }
    return String(str).toLowerCase().includes(searchValue.toLowerCase());
  } catch (error) {
    console.error('Error in safeIncludes:', error);
    return false;
  }
};

// Monkey patch String prototype to add safe methods
declare global {
  interface String {
    safeToLowerCase(): string;
    safeIncludes(searchString: string): boolean;
  }
}

// Only add these methods if they don't already exist
if (!String.prototype.safeToLowerCase) {
  String.prototype.safeToLowerCase = function(this: string): string {
    return safeToLowerCase(this);
  };
}

if (!String.prototype.safeIncludes) {
  String.prototype.safeIncludes = function(this: string, searchString: string): boolean {
    return safeIncludes(this, searchString);
  };
}

export default {
  safeToLowerCase,
  safeIncludes
};
