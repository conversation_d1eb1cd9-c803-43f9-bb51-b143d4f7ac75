import 'package:cloud_firestore/cloud_firestore.dart';

enum NotificationType {
  booking,
  promotion,
  system,
  loyalty,
  payment,
  reminder,
  alert,
  serviceRequest
}

class NotificationModel {
  final String id;
  final String userId;
  final String title;
  final String message;
  final NotificationType type;
  final bool read;
  final String? actionUrl;
  final String? bookingId;
  final String? hotelId;
  final DateTime createdAt;

  NotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.message,
    required this.type,
    required this.read,
    this.actionUrl,
    this.bookingId,
    this.hotelId,
    required this.createdAt,
  });

  // Create a copy of the notification with updated fields
  NotificationModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? message,
    NotificationType? type,
    bool? read,
    String? actionUrl,
    String? bookingId,
    String? hotelId,
    DateTime? createdAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      read: read ?? this.read,
      actionUrl: actionUrl ?? this.actionUrl,
      bookingId: bookingId ?? this.bookingId,
      hotelId: hotelId ?? this.hotelId,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // Convert NotificationType string to enum
  static NotificationType _typeFromString(String type) {
    switch (type) {
      case 'booking':
        return NotificationType.booking;
      case 'promotion':
        return NotificationType.promotion;
      case 'system':
        return NotificationType.system;
      case 'loyalty':
        return NotificationType.loyalty;
      case 'payment':
        return NotificationType.payment;
      case 'reminder':
        return NotificationType.reminder;
      case 'alert':
        return NotificationType.alert;
      case 'serviceRequest':
        return NotificationType.serviceRequest;
      default:
        return NotificationType.system;
    }
  }

  // Convert NotificationType enum to string
  static String _typeToString(NotificationType type) {
    switch (type) {
      case NotificationType.booking:
        return 'booking';
      case NotificationType.promotion:
        return 'promotion';
      case NotificationType.system:
        return 'system';
      case NotificationType.loyalty:
        return 'loyalty';
      case NotificationType.payment:
        return 'payment';
      case NotificationType.reminder:
        return 'reminder';
      case NotificationType.alert:
        return 'alert';
      case NotificationType.serviceRequest:
        return 'serviceRequest';
    }
  }

  // Create a NotificationModel from a Firestore document
  factory NotificationModel.fromMap(String id, Map<String, dynamic> map) {
    return NotificationModel(
      id: id,
      userId: map['userId'] ?? '',
      title: map['title'] ?? '',
      message: map['message'] ?? '',
      type: _typeFromString(map['type'] ?? 'system'),
      read: map['read'] ?? false,
      actionUrl: map['actionUrl'],
      bookingId: map['bookingId'],
      hotelId: map['hotelId'],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  // Convert NotificationModel to a map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'title': title,
      'message': message,
      'type': _typeToString(type),
      'read': read,
      'actionUrl': actionUrl,
      'bookingId': bookingId,
      'hotelId': hotelId,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }
}
