import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Button,
  TextField,
  InputAdornment,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  CircularProgress,
  Alert,
  Tooltip,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Divider,
  Avatar
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  MoreVert as MoreVertIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  PlayArrow as StartIcon,
  Assignment as AssignmentIcon,
  Person as PersonIcon,
  AccessTime as TimeIcon,
  Room as RoomIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { getServiceRequestsForHotel, updateServiceRequestStatus, ServiceRequest } from '../../services/serviceRequestService';
import { getAvailableStaffForService, createStaffAssignment, StaffMember } from '../../services/staffManagementService';
import ServiceRequestDetails from './ServiceRequestDetails';

interface ServiceRequestListProps {
  hotelId: string;
  serviceType: string; // 'cleaning', 'food', 'maintenance'
  title: string;
}

const ServiceRequestList: React.FC<ServiceRequestListProps> = ({ hotelId, serviceType, title }) => {
  const [requests, setRequests] = useState<ServiceRequest[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRequest, setSelectedRequest] = useState<ServiceRequest | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [assignedTo, setAssignedTo] = useState('');
  const [assignedStaffId, setAssignedStaffId] = useState('');
  const [staffList, setStaffList] = useState<StaffMember[]>([]);
  const [loadingStaff, setLoadingStaff] = useState(false);
  const [staffError, setStaffError] = useState<string | null>(null);

  useEffect(() => {
    fetchRequests();
  }, [hotelId, serviceType, statusFilter]);

  const fetchRequests = async () => {
    try {
      setLoading(true);
      setError(null);

      const options: any = {
        type: serviceType
      };

      if (statusFilter !== 'all') {
        options.status = statusFilter;
      }

      const fetchedRequests = await getServiceRequestsForHotel(hotelId, options);
      setRequests(fetchedRequests);
    } catch (err: any) {
      console.error('Error fetching service requests:', err);
      setError(err.message || 'Failed to load service requests');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleStatusFilterChange = (event: any) => {
    setStatusFilter(event.target.value);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handleViewDetails = (request: ServiceRequest) => {
    setSelectedRequest(request);
    setDetailsOpen(true);
  };

  const handleCloseDetails = () => {
    setDetailsOpen(false);
  };

  const fetchAvailableStaff = async () => {
    try {
      setLoadingStaff(true);
      setStaffError(null);

      const staff = await getAvailableStaffForService(hotelId, serviceType);
      setStaffList(staff);
    } catch (err: any) {
      console.error('Error fetching available staff:', err);
      setStaffError(err.message || 'Failed to load available staff');
    } finally {
      setLoadingStaff(false);
    }
  };

  const handleOpenAssignDialog = async (request: ServiceRequest) => {
    setSelectedRequest(request);
    setAssignedTo(request.assignedTo || '');
    setAssignedStaffId('');
    setAssignDialogOpen(true);

    // Fetch available staff
    await fetchAvailableStaff();
  };

  const handleCloseAssignDialog = () => {
    setAssignDialogOpen(false);
    setStaffError(null);
  };

  const handleAssignRequest = async () => {
    if (!selectedRequest || !selectedRequest.id) return;
    if (!assignedStaffId || !assignedTo) return;

    try {
      // Update service request status
      await updateServiceRequestStatus(
        selectedRequest.id,
        'in_progress',
        assignedTo
      );

      // Create staff assignment
      await createStaffAssignment({
        staffId: assignedStaffId,
        staffName: assignedTo,
        serviceRequestId: selectedRequest.id,
        serviceRequestType: serviceType,
        roomNumber: selectedRequest.roomNumber,
        priority: selectedRequest.priority,
        hotelId: hotelId,
        vendorId: 'vendor123' // Replace with actual vendor ID
      });

      fetchRequests();
      handleCloseAssignDialog();
    } catch (err: any) {
      console.error('Error assigning request:', err);
      setError(err.message || 'Failed to assign request');
    }
  };

  const handleUpdateStatus = async (requestId: string, newStatus: string) => {
    try {
      await updateServiceRequestStatus(requestId, newStatus);
      fetchRequests();
    } catch (err: any) {
      console.error('Error updating request status:', err);
      setError(err.message || 'Failed to update request status');
    }
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'pending':
        return <Chip label="Pending" color="warning" size="small" />;
      case 'in_progress':
        return <Chip label="In Progress" color="info" size="small" />;
      case 'completed':
        return <Chip label="Completed" color="success" size="small" />;
      case 'cancelled':
        return <Chip label="Cancelled" color="error" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const getPriorityChip = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Chip label="High" color="error" size="small" />;
      case 'medium':
        return <Chip label="Medium" color="warning" size="small" />;
      case 'low':
        return <Chip label="Low" color="info" size="small" />;
      default:
        return <Chip label={priority} size="small" />;
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';

    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return format(date, 'MMM d, yyyy h:mm a');
  };

  // Filter requests based on search query
  const filteredRequests = requests.filter(request => {
    const searchLower = searchQuery.toLowerCase();
    return (
      request.roomNumber.toLowerCase().includes(searchLower) ||
      (request.guestName && request.guestName.toLowerCase().includes(searchLower)) ||
      (request.notes && request.notes.toLowerCase().includes(searchLower))
    );
  });

  // Paginate requests
  const paginatedRequests = filteredRequests.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">
          {title}
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchRequests}
        >
          Refresh
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Paper sx={{ mb: 3, p: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search by room, guest, or notes"
              value={searchQuery}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel id="status-filter-label">Status</InputLabel>
              <Select
                labelId="status-filter-label"
                value={statusFilter}
                label="Status"
                onChange={handleStatusFilterChange}
                startAdornment={
                  <InputAdornment position="start">
                    <FilterIcon />
                  </InputAdornment>
                }
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="in_progress">In Progress</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
                <MenuItem value="cancelled">Cancelled</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      <Paper>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : filteredRequests.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1">
              No {serviceType} requests found.
            </Typography>
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Room</TableCell>
                    <TableCell>Guest</TableCell>
                    <TableCell>Request Time</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Priority</TableCell>
                    <TableCell>Assigned To</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {paginatedRequests.map((request) => (
                    <TableRow key={request.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <RoomIcon sx={{ mr: 1, fontSize: 16 }} />
                          {request.roomNumber}
                        </Box>
                      </TableCell>
                      <TableCell>{request.guestName || 'N/A'}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <TimeIcon sx={{ mr: 1, fontSize: 16 }} />
                          {formatDate(request.requestTime)}
                        </Box>
                      </TableCell>
                      <TableCell>{getStatusChip(request.status)}</TableCell>
                      <TableCell>{getPriorityChip(request.priority)}</TableCell>
                      <TableCell>
                        {request.assignedTo ? (
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <PersonIcon sx={{ mr: 1, fontSize: 16 }} />
                            {request.assignedTo}
                          </Box>
                        ) : (
                          'Not assigned'
                        )}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex' }}>
                          <Tooltip title="View Details">
                            <IconButton
                              size="small"
                              onClick={() => handleViewDetails(request)}
                            >
                              <InfoIcon />
                            </IconButton>
                          </Tooltip>

                          {request.status === 'pending' && (
                            <Tooltip title="Assign & Start">
                              <IconButton
                                size="small"
                                color="primary"
                                onClick={() => handleOpenAssignDialog(request)}
                              >
                                <StartIcon />
                              </IconButton>
                            </Tooltip>
                          )}

                          {request.status === 'in_progress' && request.id && (
                            <Tooltip title="Mark as Completed">
                              <IconButton
                                size="small"
                                color="success"
                                onClick={() => handleUpdateStatus(request.id!, 'completed')}
                              >
                                <CheckCircleIcon />
                              </IconButton>
                            </Tooltip>
                          )}

                          {(request.status === 'pending' || request.status === 'in_progress') && request.id && (
                            <Tooltip title="Cancel Request">
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleUpdateStatus(request.id!, 'cancelled')}
                              >
                                <CancelIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={filteredRequests.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Paper>

      {/* Service Request Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={handleCloseDetails}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Service Request Details
          <IconButton
            aria-label="close"
            onClick={handleCloseDetails}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <CancelIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          {selectedRequest && (
            <ServiceRequestDetails request={selectedRequest} />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDetails}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Assign Staff Dialog */}
      <Dialog
        open={assignDialogOpen}
        onClose={handleCloseAssignDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Assign Staff Member
        </DialogTitle>
        <DialogContent dividers>
          {staffError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {staffError}
            </Alert>
          )}

          {loadingStaff ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body1" gutterBottom>
                  Assign a staff member to handle this request:
                </Typography>

                {staffList.length === 0 ? (
                  <Alert severity="warning" sx={{ mt: 2 }}>
                    No available staff members found for this service type. Please add staff members with the appropriate role.
                  </Alert>
                ) : (
                  <FormControl fullWidth sx={{ mt: 2 }}>
                    <InputLabel id="assign-staff-label">Staff Member</InputLabel>
                    <Select
                      labelId="assign-staff-label"
                      value={assignedStaffId}
                      label="Staff Member"
                      onChange={(e) => {
                        const staffId = e.target.value;
                        setAssignedStaffId(staffId);

                        // Find the staff name
                        const staff = staffList.find(s => s.id === staffId);
                        if (staff) {
                          setAssignedTo(staff.name);
                        }
                      }}
                    >
                      {staffList.map((staff) => (
                        <MenuItem key={staff.id} value={staff.id || ''}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {staff.profileImage ? (
                              <Avatar
                                src={staff.profileImage}
                                sx={{ width: 24, height: 24, mr: 1 }}
                              />
                            ) : (
                              <PersonIcon sx={{ mr: 1 }} />
                            )}
                            <Box>
                              <Typography variant="body1">
                                {staff.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {staff.role.replace('_', ' ')}
                              </Typography>
                            </Box>
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              </Box>

              {assignedStaffId && (
                <Box sx={{ mb: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Selected Staff Member:
                  </Typography>

                  {(() => {
                    const staff = staffList.find(s => s.id === assignedStaffId);
                    if (!staff) return null;

                    return (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {staff.profileImage ? (
                          <Avatar
                            src={staff.profileImage}
                            sx={{ width: 40, height: 40, mr: 2 }}
                          />
                        ) : (
                          <PersonIcon sx={{ fontSize: 40, mr: 2 }} />
                        )}
                        <Box>
                          <Typography variant="body1">
                            {staff.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {staff.email}
                          </Typography>
                          {staff.skills && staff.skills.length > 0 && (
                            <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                              {staff.skills.map((skill, index) => (
                                <Chip
                                  key={index}
                                  label={skill}
                                  size="small"
                                  variant="outlined"
                                />
                              ))}
                            </Box>
                          )}
                        </Box>
                      </Box>
                    );
                  })()}
                </Box>
              )}

              <Typography variant="body2" color="text.secondary">
                This will change the status to "In Progress" and assign the selected staff member.
              </Typography>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAssignDialog}>Cancel</Button>
          <Button
            onClick={handleAssignRequest}
            variant="contained"
            disabled={!assignedStaffId || loadingStaff || staffList.length === 0}
          >
            Assign & Start
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ServiceRequestList;
