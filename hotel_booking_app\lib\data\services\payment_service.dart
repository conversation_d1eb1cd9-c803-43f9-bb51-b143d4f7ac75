import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/data/models/booking_model.dart';
import 'package:hotel_booking_app/data/models/payment_model.dart';
import 'package:hotel_booking_app/data/repositories/booking_repository.dart';

class PaymentService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final BookingRepository _bookingRepository = BookingRepository();

  bool _isLoading = false;
  String? _error;
  List<SavedPaymentMethod> _savedPaymentMethods = [];
  List<PaymentDetails> _paymentHistory = [];

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<SavedPaymentMethod> get savedPaymentMethods => _savedPaymentMethods;
  List<PaymentDetails> get paymentHistory => _paymentHistory;

  // Initialize the service
  PaymentService() {
    _loadSavedPaymentMethods();
  }

  // Load saved payment methods for the current user
  Future<void> _loadSavedPaymentMethods() async {
    if (_auth.currentUser == null) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final snapshot = await _firestore
          .collection(AppConstants.paymentMethodsCollection)
          .where('userId', isEqualTo: _auth.currentUser!.uid)
          .get();

      _savedPaymentMethods = snapshot.docs
          .map((doc) => SavedPaymentMethod.fromMap(doc.data(), doc.id))
          .toList();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Load payment history for the current user
  Future<void> loadPaymentHistory() async {
    if (_auth.currentUser == null) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final snapshot = await _firestore
          .collection(AppConstants.paymentsCollection)
          .where('userId', isEqualTo: _auth.currentUser!.uid)
          .orderBy('createdAt', descending: true)
          .get();

      _paymentHistory = snapshot.docs
          .map((doc) => PaymentDetails.fromMap(doc.data(), doc.id))
          .toList();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Add a new payment method
  Future<String?> addPaymentMethod(SavedPaymentMethod paymentMethod) async {
    if (_auth.currentUser == null) {
      _error = 'User not authenticated';
      notifyListeners();
      return null;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // If this is the first payment method or marked as default, set all others to non-default
      if (paymentMethod.isDefault || _savedPaymentMethods.isEmpty) {
        await _setAllPaymentMethodsToNonDefault();
      }

      // Add the new payment method
      final docRef = await _firestore
          .collection(AppConstants.paymentMethodsCollection)
          .add(paymentMethod.toMap());

      // Refresh the list
      await _loadSavedPaymentMethods();

      _isLoading = false;
      notifyListeners();
      return docRef.id;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  // Set all payment methods to non-default
  Future<void> _setAllPaymentMethodsToNonDefault() async {
    final batch = _firestore.batch();
    final snapshot = await _firestore
        .collection(AppConstants.paymentMethodsCollection)
        .where('userId', isEqualTo: _auth.currentUser!.uid)
        .where('isDefault', isEqualTo: true)
        .get();

    for (var doc in snapshot.docs) {
      batch.update(doc.reference, {'isDefault': false});
    }

    await batch.commit();
  }

  // Set a payment method as default
  Future<bool> setDefaultPaymentMethod(String paymentMethodId) async {
    if (_auth.currentUser == null) {
      _error = 'User not authenticated';
      notifyListeners();
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Set all payment methods to non-default
      await _setAllPaymentMethodsToNonDefault();

      // Set the selected payment method as default
      await _firestore
          .collection(AppConstants.paymentMethodsCollection)
          .doc(paymentMethodId)
          .update({
        'isDefault': true,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Refresh the list
      await _loadSavedPaymentMethods();

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Delete a payment method
  Future<bool> deletePaymentMethod(String paymentMethodId) async {
    if (_auth.currentUser == null) {
      _error = 'User not authenticated';
      notifyListeners();
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Get the payment method to check if it's default
      final docSnapshot = await _firestore
          .collection(AppConstants.paymentMethodsCollection)
          .doc(paymentMethodId)
          .get();

      final isDefault = docSnapshot.data()?['isDefault'] ?? false;

      // Delete the payment method
      await _firestore
          .collection(AppConstants.paymentMethodsCollection)
          .doc(paymentMethodId)
          .delete();

      // If it was the default payment method, set another one as default
      if (isDefault) {
        final remainingMethods = await _firestore
            .collection(AppConstants.paymentMethodsCollection)
            .where('userId', isEqualTo: _auth.currentUser!.uid)
            .limit(1)
            .get();

        if (remainingMethods.docs.isNotEmpty) {
          await _firestore
              .collection(AppConstants.paymentMethodsCollection)
              .doc(remainingMethods.docs.first.id)
              .update({
            'isDefault': true,
            'updatedAt': FieldValue.serverTimestamp(),
          });
        }
      }

      // Refresh the list
      await _loadSavedPaymentMethods();

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Process a payment for a booking
  Future<PaymentDetails?> processPayment({
    required Booking booking,
    required PaymentMethod paymentMethod,
    String? savedPaymentMethodId,
    String? transactionId,
    String? gatewayResponse,
    Map<String, dynamic>? metadata,
  }) async {
    if (_auth.currentUser == null) {
      _error = 'User not authenticated';
      notifyListeners();
      return null;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Create a payment record with real transaction data
      final payment = PaymentDetails(
        id: '',
        userId: _auth.currentUser!.uid,
        bookingId: booking.id,
        amount: booking.totalPrice,
        currency: 'INR',
        method: paymentMethod,
        status: paymentMethod == PaymentMethod.cash
            ? PaymentStatus.pending
            : PaymentStatus.processing,
        transactionId: transactionId,
        gatewayResponse: gatewayResponse,
        metadata: metadata,
        createdAt: DateTime.now(),
      );

      // Add the payment to Firestore
      final docRef = await _firestore
          .collection(AppConstants.paymentsCollection)
          .add(payment.toMap());

      // Update the payment status in the booking
      await _bookingRepository.updatePaymentStatus(
        booking.id,
        paymentMethod == PaymentMethod.cash
            ? AppConstants.paymentPending
            : AppConstants.paymentProcessing,
      );

      // For cash payments, we're done
      if (paymentMethod == PaymentMethod.cash) {
        final paymentDetails = PaymentDetails(
          id: docRef.id,
          userId: payment.userId,
          bookingId: payment.bookingId,
          amount: payment.amount,
          currency: payment.currency,
          method: payment.method,
          status: payment.status,
          createdAt: payment.createdAt,
        );

        _isLoading = false;
        notifyListeners();
        return paymentDetails;
      }

      // For online payments with real transaction data
      // Update the payment status to completed
      final updateData = {
        'status': PaymentStatus.completed.toString().split('.').last,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // Add real transaction data if available
      if (transactionId != null) {
        updateData['transactionId'] = transactionId;
      } else {
        updateData['transactionId'] =
            'payment_${DateTime.now().millisecondsSinceEpoch}';
      }

      if (gatewayResponse != null) {
        updateData['gatewayResponse'] = gatewayResponse;
      }

      if (metadata != null) {
        updateData['metadata'] = metadata;
      }

      // Update the payment record with real transaction data
      await _firestore
          .collection(AppConstants.paymentsCollection)
          .doc(docRef.id)
          .update(updateData);

      // Update the booking payment status
      await _bookingRepository.updatePaymentStatus(
        booking.id,
        AppConstants.paymentCompleted,
      );

      // Return the payment details with real transaction data
      final paymentDetails = PaymentDetails(
        id: docRef.id,
        userId: payment.userId,
        bookingId: payment.bookingId,
        amount: payment.amount,
        currency: payment.currency,
        method: payment.method,
        status: PaymentStatus.completed,
        transactionId:
            transactionId ?? 'payment_${DateTime.now().millisecondsSinceEpoch}',
        gatewayResponse: gatewayResponse,
        metadata: metadata,
        createdAt: payment.createdAt,
        updatedAt: DateTime.now(),
      );

      _isLoading = false;
      notifyListeners();
      return paymentDetails;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }
}
