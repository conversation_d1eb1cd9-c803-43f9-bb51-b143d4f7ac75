import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Button,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  IconButton,
  Tooltip,
  useTheme
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Hotel as HotelIcon,
  People as PeopleIcon,
  Person as PersonIcon,
  BookOnline as BookingIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { auth } from '../../firebase/config';
import { getVendors, UserData } from '../../firebase/userService';
import { getHotelsCount } from '../../firebase/hotelService';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { useIsMobile } from '../../utils/responsiveUtils';

const Dashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalHotels: 0,
    totalVendors: 0,
    totalBookings: 0,
    totalRevenue: 0
  });
  const [vendors, setVendors] = useState<UserData[]>([]);
  const [recentBookings, setRecentBookings] = useState<any[]>([]);

  // Load data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Fetch vendors
      const vendorsList = await getVendors();
      setVendors(vendorsList);

      // Fetch hotel count
      const hotelCount = await getHotelsCount();

      // Set stats
      setStats({
        totalHotels: hotelCount,
        totalVendors: vendorsList.length,
        totalBookings: 125, // Sample data
        totalRevenue: 45000 // Sample data
      });

      // Sample recent bookings data
      setRecentBookings([
        { id: 1, guest: 'John Doe', hotel: 'Grand Hotel', checkIn: '2023-07-15', checkOut: '2023-07-20', status: 'Confirmed', amount: 1200 },
        { id: 2, guest: 'Jane Smith', hotel: 'Luxury Resort', checkIn: '2023-07-18', checkOut: '2023-07-25', status: 'Pending', amount: 2100 },
        { id: 3, guest: 'Robert Johnson', hotel: 'Seaside Inn', checkIn: '2023-07-20', checkOut: '2023-07-22', status: 'Confirmed', amount: 800 },
      ]);

      setLoading(false);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchData();
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Colors for charts
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
  ];

  // Sample data for revenue chart
  const revenueData = [
    { month: 'Jan', revenue: 12000 },
    { month: 'Feb', revenue: 15000 },
    { month: 'Mar', revenue: 18000 },
    { month: 'Apr', revenue: 16000 },
    { month: 'May', revenue: 21000 },
    { month: 'Jun', revenue: 25000 },
    { month: 'Jul', revenue: 30000 },
  ];

  // Sample data for hotel distribution
  const hotelDistributionData = [
    { name: 'Mumbai', value: 35 },
    { name: 'Delhi', value: 25 },
    { name: 'Bangalore', value: 20 },
    { name: 'Chennai', value: 15 },
    { name: 'Other', value: 5 },
  ];

  return (
    <Box>
      <Box sx={{
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', sm: 'center' },
        gap: { xs: 2, sm: 0 },
        mb: 3
      }}>
        <Typography
          variant="h4"
          sx={{
            fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' }
          }}
        >
          Admin Dashboard
        </Typography>
        <Button
          startIcon={<RefreshIcon />}
          variant="outlined"
          onClick={handleRefresh}
          size={isMobile ? "small" : "medium"}
        >
          Refresh
        </Button>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          {/* Stats Cards */}
          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={2}
              sx={{
                p: { xs: 1.5, sm: 2 },
                display: 'flex',
                flexDirection: 'column',
                height: { xs: 120, sm: 140 },
                bgcolor: 'primary.light',
                color: 'primary.contrastText',
                borderRadius: 2,
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6" component="div" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
                  Hotels
                </Typography>
                <HotelIcon sx={{ fontSize: { xs: '1.25rem', sm: '1.5rem' } }} />
              </Box>
              <Typography
                variant="h3"
                component="div"
                sx={{
                  mt: { xs: 1, sm: 2 },
                  fontSize: { xs: '1.75rem', sm: '2.5rem' }
                }}
              >
                {stats.totalHotels}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto', pt: 1 }}>
                <Typography
                  variant="body2"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}
                >
                  <TrendingUpIcon fontSize="small" sx={{ mr: 0.5 }} />
                  +5% this month
                </Typography>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={2}
              sx={{
                p: { xs: 1.5, sm: 2 },
                display: 'flex',
                flexDirection: 'column',
                height: { xs: 120, sm: 140 },
                bgcolor: 'secondary.light',
                color: 'secondary.contrastText',
                borderRadius: 2,
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6" component="div" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
                  Vendors
                </Typography>
                <PeopleIcon sx={{ fontSize: { xs: '1.25rem', sm: '1.5rem' } }} />
              </Box>
              <Typography
                variant="h3"
                component="div"
                sx={{
                  mt: { xs: 1, sm: 2 },
                  fontSize: { xs: '1.75rem', sm: '2.5rem' }
                }}
              >
                {stats.totalVendors}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto', pt: 1 }}>
                <Typography
                  variant="body2"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}
                >
                  <TrendingUpIcon fontSize="small" sx={{ mr: 0.5 }} />
                  +2% this month
                </Typography>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={2}
              sx={{
                p: { xs: 1.5, sm: 2 },
                display: 'flex',
                flexDirection: 'column',
                height: { xs: 120, sm: 140 },
                bgcolor: 'success.light',
                color: 'success.contrastText',
                borderRadius: 2,
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6" component="div" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
                  Bookings
                </Typography>
                <BookingIcon sx={{ fontSize: { xs: '1.25rem', sm: '1.5rem' } }} />
              </Box>
              <Typography
                variant="h3"
                component="div"
                sx={{
                  mt: { xs: 1, sm: 2 },
                  fontSize: { xs: '1.75rem', sm: '2.5rem' }
                }}
              >
                {stats.totalBookings}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto', pt: 1 }}>
                <Typography
                  variant="body2"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}
                >
                  <TrendingUpIcon fontSize="small" sx={{ mr: 0.5 }} />
                  +12% this month
                </Typography>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={2}
              sx={{
                p: { xs: 1.5, sm: 2 },
                display: 'flex',
                flexDirection: 'column',
                height: { xs: 120, sm: 140 },
                bgcolor: 'warning.light',
                color: 'warning.contrastText',
                borderRadius: 2,
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6" component="div" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
                  Revenue
                </Typography>
                <PersonIcon sx={{ fontSize: { xs: '1.25rem', sm: '1.5rem' } }} />
              </Box>
              <Typography
                variant="h3"
                component="div"
                sx={{
                  mt: { xs: 1, sm: 2 },
                  fontSize: { xs: '1.75rem', sm: '2.5rem' }
                }}
              >
                {formatCurrency(stats.totalRevenue)}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto', pt: 1 }}>
                <Typography
                  variant="body2"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}
                >
                  <TrendingUpIcon fontSize="small" sx={{ mr: 0.5 }} />
                  +8% this month
                </Typography>
              </Box>
            </Paper>
          </Grid>

          {/* Revenue Chart */}
          <Grid item xs={12} md={8}>
            <Card sx={{ borderRadius: 2, overflow: 'hidden' }}>
              <CardHeader
                title={
                  <Typography
                    variant="h6"
                    sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}
                  >
                    Revenue Trends
                  </Typography>
                }
                action={
                  <Tooltip title="View detailed reports">
                    <IconButton
                      onClick={() => navigate('/admin/reports')}
                      size={isMobile ? "small" : "medium"}
                    >
                      <VisibilityIcon />
                    </IconButton>
                  </Tooltip>
                }
                sx={{
                  p: { xs: 2, sm: 3 },
                  pb: { xs: 1, sm: 2 }
                }}
              />
              <Divider />
              <CardContent sx={{ p: { xs: 1, sm: 2 } }}>
                <ResponsiveContainer width="100%" height={isMobile ? 200 : 300}>
                  <LineChart
                    data={revenueData}
                    margin={{
                      top: 5,
                      right: isMobile ? 10 : 30,
                      left: isMobile ? 0 : 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="month"
                      tick={{ fontSize: isMobile ? 10 : 12 }}
                      tickMargin={isMobile ? 5 : 10}
                    />
                    <YAxis
                      tick={{ fontSize: isMobile ? 10 : 12 }}
                      tickFormatter={(value) => isMobile ? `${value/1000}K` : `₹${value/1000}K`}
                    />
                    <RechartsTooltip formatter={(value: any) => formatCurrency(value)} />
                    <Line
                      type="monotone"
                      dataKey="revenue"
                      stroke={theme.palette.primary.main}
                      activeDot={{ r: isMobile ? 6 : 8 }}
                      name="Revenue"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Hotel Distribution */}
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '100%', borderRadius: 2, overflow: 'hidden' }}>
              <CardHeader
                title={
                  <Typography
                    variant="h6"
                    sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}
                  >
                    Hotel Distribution
                  </Typography>
                }
                sx={{
                  p: { xs: 2, sm: 3 },
                  pb: { xs: 1, sm: 2 }
                }}
              />
              <Divider />
              <CardContent sx={{ p: { xs: 1, sm: 2 } }}>
                <ResponsiveContainer width="100%" height={isMobile ? 200 : 300}>
                  <PieChart>
                    <Pie
                      data={hotelDistributionData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={isMobile ? 60 : 80}
                      fill="#8884d8"
                      dataKey="value"
                      nameKey="name"
                      label={({ name, percent }) =>
                        isMobile
                          ? `${(percent * 100).toFixed(0)}%`
                          : `${name}: ${(percent * 100).toFixed(0)}%`
                      }
                    >
                      {hotelDistributionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Legend
                      layout={isMobile ? "horizontal" : "vertical"}
                      verticalAlign={isMobile ? "bottom" : "middle"}
                      align={isMobile ? "center" : "right"}
                      wrapperStyle={isMobile ? { fontSize: '10px' } : { fontSize: '12px' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Recent Vendors */}
          <Grid item xs={12} md={6}>
            <Card sx={{ borderRadius: 2, overflow: 'hidden' }}>
              <CardHeader
                title={
                  <Typography
                    variant="h6"
                    sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}
                  >
                    Recent Vendors
                  </Typography>
                }
                action={
                  <Button
                    size={isMobile ? "small" : "medium"}
                    onClick={() => navigate('/admin/vendors')}
                  >
                    View All
                  </Button>
                }
                sx={{
                  p: { xs: 2, sm: 3 },
                  pb: { xs: 1, sm: 2 }
                }}
              />
              <Divider />
              <List sx={{ p: 0 }}>
                {vendors.length === 0 ? (
                  <ListItem>
                    <ListItemText
                      primary={
                        <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                          No vendors found
                        </Typography>
                      }
                    />
                  </ListItem>
                ) : (
                  vendors.slice(0, 3).map((vendor) => (
                    <React.Fragment key={vendor.id}>
                      <ListItem
                        alignItems="flex-start"
                        sx={{
                          px: { xs: 2, sm: 3 },
                          py: { xs: 1, sm: 1.5 }
                        }}
                      >
                        <ListItemAvatar>
                          <Avatar sx={{ width: { xs: 32, sm: 40 }, height: { xs: 32, sm: 40 } }}>
                            {vendor.displayName.charAt(0)}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                              {vendor.displayName}
                            </Typography>
                          }
                          secondary={
                            <Typography
                              variant="body2"
                              sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                            >
                              {vendor.email}
                            </Typography>
                          }
                        />
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => navigate(`/admin/vendors?id=${vendor.id}`)}
                          sx={{
                            minWidth: { xs: '60px', sm: '64px' },
                            fontSize: { xs: '0.75rem', sm: '0.8125rem' }
                          }}
                        >
                          View
                        </Button>
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))
                )}
              </List>
            </Card>
          </Grid>

          {/* Recent Bookings */}
          <Grid item xs={12} md={6}>
            <Card sx={{ borderRadius: 2, overflow: 'hidden' }}>
              <CardHeader
                title={
                  <Typography
                    variant="h6"
                    sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}
                  >
                    Recent Bookings
                  </Typography>
                }
                action={
                  <Button
                    size={isMobile ? "small" : "medium"}
                    onClick={() => navigate('/admin/bookings')}
                  >
                    View All
                  </Button>
                }
                sx={{
                  p: { xs: 2, sm: 3 },
                  pb: { xs: 1, sm: 2 }
                }}
              />
              <Divider />
              <List sx={{ p: 0 }}>
                {recentBookings.length === 0 ? (
                  <ListItem>
                    <ListItemText
                      primary={
                        <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                          No recent bookings
                        </Typography>
                      }
                    />
                  </ListItem>
                ) : (
                  recentBookings.map((booking) => (
                    <React.Fragment key={booking.id}>
                      <ListItem
                        alignItems="flex-start"
                        sx={{
                          px: { xs: 2, sm: 3 },
                          py: { xs: 1, sm: 1.5 }
                        }}
                      >
                        <ListItemAvatar>
                          <Avatar sx={{ width: { xs: 32, sm: 40 }, height: { xs: 32, sm: 40 } }}>
                            {booking.guest.charAt(0)}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Typography sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                              {booking.guest}
                            </Typography>
                          }
                          secondary={
                            <React.Fragment>
                              <Typography
                                sx={{
                                  display: 'inline',
                                  fontSize: { xs: '0.75rem', sm: '0.875rem' }
                                }}
                                component="span"
                                variant="body2"
                                color="text.primary"
                              >
                                {booking.hotel}
                              </Typography>
                              <Typography
                                component="span"
                                variant="body2"
                                sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                              >
                                {` — ${booking.checkIn} to ${booking.checkOut}`}
                              </Typography>
                            </React.Fragment>
                          }
                        />
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                          <Chip
                            label={booking.status}
                            color={booking.status === 'Confirmed' ? 'success' : 'warning'}
                            size="small"
                            sx={{
                              mb: 1,
                              height: { xs: 20, sm: 24 },
                              '& .MuiChip-label': {
                                fontSize: { xs: '0.625rem', sm: '0.75rem' },
                                px: { xs: 1, sm: 1.5 }
                              }
                            }}
                          />
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                          >
                            {formatCurrency(booking.amount)}
                          </Typography>
                        </Box>
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))
                )}
              </List>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default Dashboard;
