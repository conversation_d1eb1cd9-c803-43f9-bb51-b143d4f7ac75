import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Button,
  CircularProgress,
  Alert,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip
} from '@mui/material';
import {
  VerifiedUser as VerifiedUserIcon,
  PendingActions as PendingIcon,
  ErrorOutline as ErrorIcon,
  HelpOutline as HelpIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  DateRange as DateRangeIcon
} from '@mui/icons-material';
import { getVerifications, getVerificationStats } from '../../services/verificationService';
import { AadhaarVerificationData } from '../../components/AadhaarVerificationCard';
// import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
// import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { format, subDays, isWithinInterval } from 'date-fns';
import { PieChart, Pie, Cell, ResponsiveContainer, Legend, Tooltip as RechartsTooltip } from 'recharts';
import { useNavigate } from 'react-router-dom';

const VendorVerificationReports: React.FC = () => {
  // We'll keep the navigate hook for potential future use
  const navigate = useNavigate(); // eslint-disable-line @typescript-eslint/no-unused-vars
  const [verifications, setVerifications] = useState<AadhaarVerificationData[]>([]);
  const [filteredVerifications, setFilteredVerifications] = useState<AadhaarVerificationData[]>([]);
  const [stats, setStats] = useState({
    total: 0,
    verified: 0,
    pending: 0,
    failed: 0,
    notVerified: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [startDate, setStartDate] = useState<Date | null>(subDays(new Date(), 30));
  const [endDate, setEndDate] = useState<Date | null>(new Date());
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [dateRange, setDateRange] = useState('30');

  // Load data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  // Define applyFilters before using it in useEffect
  const applyFilters = useCallback(() => {
    let filtered = [...verifications];

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(v => v.status === statusFilter);
    }

    // Apply date filter
    if (startDate && endDate) {
      filtered = filtered.filter(v => {
        const verifiedAt = v.verifiedAt || v.createdAt;
        if (!verifiedAt) return false;

        return isWithinInterval(new Date(verifiedAt), {
          start: startDate,
          end: endDate
        });
      });
    }

    setFilteredVerifications(filtered);
  }, [verifications, statusFilter, startDate, endDate]);

  // Apply filters when they change
  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch verification stats
      const statsData = await getVerificationStats();
      setStats(statsData);

      // Fetch all verifications
      const verificationsData = await getVerifications();
      setVerifications(verificationsData);
    } catch (err: any) {
      setError(err.message || 'Failed to load verifications');
      console.error('Error fetching verifications:', err);
    } finally {
      setLoading(false);
    }
  };



  const handleStatusFilterChange = (event: SelectChangeEvent) => {
    setStatusFilter(event.target.value);
  };

  const handleDateRangeChange = (event: SelectChangeEvent) => {
    const range = event.target.value;
    setDateRange(range);

    const end = new Date();
    let start;

    switch (range) {
      case '7':
        start = subDays(end, 7);
        break;
      case '30':
        start = subDays(end, 30);
        break;
      case '90':
        start = subDays(end, 90);
        break;
      case '365':
        start = subDays(end, 365);
        break;
      default:
        start = subDays(end, 30);
    }

    setStartDate(start);
    setEndDate(end);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExport = () => {
    // Create CSV content
    const headers = ['Name', 'Aadhaar Number', 'Status', 'Verified On', 'User ID'];
    const csvContent = [
      headers.join(','),
      ...filteredVerifications.map(v => [
        v.fullName,
        v.maskedAadhaarNumber,
        v.status,
        v.verifiedAt ? format(new Date(v.verifiedAt), 'yyyy-MM-dd HH:mm:ss') : 'N/A',
        v.userId
      ].join(','))
    ].join('\n');

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `verification_report_${format(new Date(), 'yyyy-MM-dd')}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Format date
  const formatDate = (date?: Date) => {
    if (!date) return 'N/A';
    return format(new Date(date), 'MMM dd, yyyy');
  };

  // Get status color and icon
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'verified':
        return {
          color: 'success',
          icon: <VerifiedUserIcon fontSize="small" />,
          text: 'Verified'
        };
      case 'pending':
        return {
          color: 'warning',
          icon: <PendingIcon fontSize="small" />,
          text: 'Pending'
        };
      case 'failed':
        return {
          color: 'error',
          icon: <ErrorIcon fontSize="small" />,
          text: 'Failed'
        };
      case 'notVerified':
        return {
          color: 'default',
          icon: <HelpIcon fontSize="small" />,
          text: 'Not Verified'
        };
      default:
        return {
          color: 'default',
          icon: <HelpIcon fontSize="small" />,
          text: 'Unknown'
        };
    }
  };

  // Prepare chart data
  const chartData = [
    { name: 'Verified', value: stats.verified, color: '#4caf50' },
    { name: 'Pending', value: stats.pending, color: '#ff9800' },
    { name: 'Failed', value: stats.failed, color: '#f44336' },
    { name: 'Not Verified', value: stats.notVerified, color: '#9e9e9e' }
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Verification Reports
      </Typography>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth>
              <InputLabel id="status-filter-label">Status</InputLabel>
              <Select
                labelId="status-filter-label"
                value={statusFilter}
                label="Status"
                onChange={handleStatusFilterChange}
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value="verified">Verified</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="failed">Failed</MenuItem>
                <MenuItem value="notVerified">Not Verified</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth>
              <InputLabel id="date-range-label">Date Range</InputLabel>
              <Select
                labelId="date-range-label"
                value={dateRange}
                label="Date Range"
                onChange={handleDateRangeChange}
                startAdornment={<DateRangeIcon color="action" sx={{ mr: 1 }} />}
              >
                <MenuItem value="7">Last 7 Days</MenuItem>
                <MenuItem value="30">Last 30 Days</MenuItem>
                <MenuItem value="90">Last 90 Days</MenuItem>
                <MenuItem value="365">Last Year</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>
      {/* Stats and Chart */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={5}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Verification Statistics
              </Typography>
              <Divider sx={{ mb: 2 }} />

              {loading ? (
                <Box display="flex" justifyContent="center" py={3}>
                  <CircularProgress />
                </Box>
              ) : (
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Total Verifications
                    </Typography>
                    <Typography variant="h4">
                      {stats.total}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Verified Users
                    </Typography>
                    <Typography variant="h4" color="success.main">
                      {stats.verified}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Pending Verifications
                    </Typography>
                    <Typography variant="h4" color="warning.main">
                      {stats.pending}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Failed Verifications
                    </Typography>
                    <Typography variant="h4" color="error.main">
                      {stats.failed}
                    </Typography>
                  </Grid>
                </Grid>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={7}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Verification Distribution
              </Typography>
              <Divider sx={{ mb: 2 }} />

              {loading ? (
                <Box display="flex" justifyContent="center" py={3}>
                  <CircularProgress />
                </Box>
              ) : (
                <Box height={300}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={chartData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {chartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Legend />
                      <RechartsTooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Export/Print Buttons */}
      <Box display="flex" justifyContent="flex-end" mb={2}>
        <Button
          variant="outlined"
          startIcon={<PrintIcon />}
          onClick={handlePrint}
          sx={{ mr: 1 }}
        >
          Print Report
        </Button>
        <Button
          variant="contained"
          startIcon={<DownloadIcon />}
          onClick={handleExport}
        >
          Export CSV
        </Button>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Verifications Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Aadhaar Number</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Verified On</TableCell>
                <TableCell>User ID</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : filteredVerifications.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                    No verifications found matching the filters
                  </TableCell>
                </TableRow>
              ) : (
                filteredVerifications
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((verification) => {
                    const statusInfo = getStatusInfo(verification.status);
                    return (
                      <TableRow key={verification.id}>
                        <TableCell>{verification.fullName}</TableCell>
                        <TableCell>{verification.maskedAadhaarNumber}</TableCell>
                        <TableCell>
                          <Chip
                            label={statusInfo.text}
                            color={statusInfo.color as any}
                            size="small"
                            icon={statusInfo.icon}
                          />
                        </TableCell>
                        <TableCell>
                          {verification.verifiedAt ? formatDate(verification.verifiedAt) : 'N/A'}
                        </TableCell>
                        <TableCell>{verification.userId}</TableCell>
                      </TableRow>
                    );
                  })
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={filteredVerifications.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
    </Box>
  );
};

export default VendorVerificationReports;
