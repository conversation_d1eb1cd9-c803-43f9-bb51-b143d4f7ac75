import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Snackbar,
  Alert,
  CircularProgress,
  Chip,
  Grid,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  OutlinedInput,
  Checkbox,
  ListItemText,
  SelectChangeEvent,
  Autocomplete
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Image as ImageIcon,
  Hotel as HotelIcon
} from '@mui/icons-material';
import { getAllRooms, createRoom, updateRoom, deleteRoom, RoomData } from '../../firebase/roomService';
import { getAllHotels, HotelData } from '../../firebase/hotelService';
import { auth } from '../../firebase/config';

// Common amenities for rooms
const AMENITIES = [
  'WiFi',
  'TV',
  'Air Conditioning',
  'Mini Bar',
  'Safe',
  'Desk',
  'Balcony',
  'Sea View',
  'Mountain View',
  'City View',
  'Bathtub',
  'Shower',
  'Hairdryer',
  'Iron',
  'Coffee Machine'
];

// Bed types
const BED_TYPES = [
  'Single',
  'Double',
  'Queen',
  'King',
  'Twin',
  'Bunk',
  'Sofa Bed'
];

// Room statuses
const ROOM_STATUSES = [
  'available',
  'booked',
  'maintenance'
];

const Rooms: React.FC = () => {
  const [rooms, setRooms] = useState<RoomData[]>([]);
  const [hotels, setHotels] = useState<HotelData[]>([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingRoom, setEditingRoom] = useState<RoomData | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [formData, setFormData] = useState<RoomData>({
    hotelId: '',
    name: '',
    description: '',
    price: 0,
    capacity: 1,
    amenities: [],
    images: [],
    status: 'available',
    roomNumber: '',
    floor: 1,
    size: 0,
    bedType: ''
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  // Load rooms and hotels on component mount
  useEffect(() => {
    fetchRooms();
    fetchHotels();
  }, []);

  const fetchRooms = async () => {
    try {
      setLoading(true);
      const roomsList = await getAllRooms();
      setRooms(roomsList);
    } catch (error) {
      console.error('Error fetching rooms:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load rooms',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchHotels = async () => {
    try {
      const hotelsList = await getAllHotels();
      setHotels(hotelsList);
    } catch (error) {
      console.error('Error fetching hotels:', error);
    }
  };

  const handleOpenDialog = (room?: RoomData) => {
    if (room) {
      // Edit mode
      setEditingRoom(room);
      setFormData({
        hotelId: room.hotelId,
        name: room.name,
        description: room.description,
        price: room.price,
        capacity: room.capacity,
        amenities: room.amenities || [],
        images: room.images || [],
        status: room.status,
        roomNumber: room.roomNumber || '',
        floor: room.floor || 1,
        size: room.size || 0,
        bedType: room.bedType || ''
      });
    } else {
      // Create mode
      setEditingRoom(null);
      setFormData({
        hotelId: hotels.length > 0 ? hotels[0].id! : '',
        name: '',
        description: '',
        price: 0,
        capacity: 1,
        amenities: [],
        images: [],
        status: 'available',
        roomNumber: '',
        floor: 1,
        size: 0,
        bedType: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNumberInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: parseFloat(value) || 0
    }));
  };

  const handleIntegerInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: parseInt(value) || 0
    }));
  };

  const handleHotelChange = (e: SelectChangeEvent) => {
    setFormData(prev => ({
      ...prev,
      hotelId: e.target.value
    }));
  };

  const handleStatusChange = (e: SelectChangeEvent) => {
    setFormData(prev => ({
      ...prev,
      status: e.target.value as 'available' | 'booked' | 'maintenance'
    }));
  };

  const handleBedTypeChange = (e: SelectChangeEvent) => {
    setFormData(prev => ({
      ...prev,
      bedType: e.target.value
    }));
  };

  const handleAmenitiesChange = (event: SelectChangeEvent<string[]>) => {
    const { value } = event.target;
    setFormData(prev => ({
      ...prev,
      amenities: typeof value === 'string' ? value.split(',') : value
    }));
  };

  const handleImageUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const imageUrl = e.target.value.trim();
    if (imageUrl) {
      setFormData(prev => ({
        ...prev,
        images: [...(prev.images || []), imageUrl]
      }));
      e.target.value = '';
    }
  };

  const handleRemoveImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images?.filter((_, i) => i !== index) || []
    }));
  };

  const handleSubmit = async () => {
    try {
      if (!auth.currentUser) {
        throw new Error('You must be logged in to perform this action');
      }

      if (!formData.name || !formData.description || !formData.hotelId) {
        throw new Error('Please fill in all required fields');
      }

      if (editingRoom) {
        // Update existing room
        await updateRoom(editingRoom.id!, formData);

        setSnackbar({
          open: true,
          message: 'Room updated successfully',
          severity: 'success'
        });
      } else {
        // Create new room
        await createRoom(formData);

        setSnackbar({
          open: true,
          message: 'Room created successfully',
          severity: 'success'
        });
      }

      handleCloseDialog();
      fetchRooms(); // Refresh the list
    } catch (error: any) {
      console.error('Error saving room:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to save room',
        severity: 'error'
      });
    }
  };

  const handleDeleteRoom = async (roomId: string) => {
    if (window.confirm('Are you sure you want to delete this room?')) {
      try {
        await deleteRoom(roomId);
        setSnackbar({
          open: true,
          message: 'Room deleted successfully',
          severity: 'success'
        });
        fetchRooms(); // Refresh the list
      } catch (error) {
        console.error('Error deleting room:', error);
        setSnackbar({
          open: true,
          message: 'Failed to delete room',
          severity: 'error'
        });
      }
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleHotelFilter = (e: SelectChangeEvent) => {
    setSelectedHotel(e.target.value);
  };

  // Filter rooms based on search term and selected hotel
  const filteredRooms = rooms.filter(room => {
    const matchesSearch =
      room.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      room.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      room.roomNumber?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesHotel = selectedHotel ? room.hotelId === selectedHotel : true;

    return matchesSearch && matchesHotel;
  });

  // Find hotel name by ID
  const getHotelName = (hotelId: string) => {
    const hotel = hotels.find(h => h.id === hotelId);
    return hotel ? hotel.name : 'Unknown Hotel';
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'success';
      case 'booked':
        return 'primary';
      case 'maintenance':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Rooms Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Add Room
        </Button>
      </Box>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              placeholder="Search rooms..."
              variant="outlined"
              fullWidth
              value={searchTerm}
              onChange={handleSearch}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Filter by Hotel</InputLabel>
              <Select
                value={selectedHotel}
                onChange={handleHotelFilter}
                label="Filter by Hotel"
              >
                <MenuItem value="">All Hotels</MenuItem>
                {hotels.map((hotel) => (
                  <MenuItem key={hotel.id} value={hotel.id}>
                    {hotel.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchRooms}
              fullWidth
              sx={{ height: '100%' }}
            >
              Refresh
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <Paper>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Room</TableCell>
                  <TableCell>Hotel</TableCell>
                  <TableCell>Capacity</TableCell>
                  <TableCell>Price</TableCell>
                  <TableCell>Bed Type</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Amenities</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredRooms.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      No rooms found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredRooms.map((room) => (
                    <TableRow key={room.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                          <Typography variant="body1">{room.name}</Typography>
                          {room.roomNumber && (
                            <Typography variant="caption" color="text.secondary">
                              Room #{room.roomNumber}, Floor {room.floor}
                            </Typography>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>{getHotelName(room.hotelId)}</TableCell>
                      <TableCell>{room.capacity} {room.capacity > 1 ? 'persons' : 'person'}</TableCell>
                      <TableCell>₹{room.price}/night</TableCell>
                      <TableCell>{room.bedType || 'Not specified'}</TableCell>
                      <TableCell>
                        <Chip
                          label={room.status.charAt(0).toUpperCase() + room.status.slice(1)}
                          color={getStatusColor(room.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {room.amenities?.slice(0, 2).map((amenity, index) => (
                            <Chip key={index} label={amenity} size="small" />
                          ))}
                          {room.amenities && room.amenities.length > 2 && (
                            <Chip label={`+${room.amenities.length - 2} more`} size="small" variant="outlined" />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <IconButton
                          color="primary"
                          onClick={() => handleOpenDialog(room)}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          color="error"
                          onClick={() => room.id && handleDeleteRoom(room.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* Add/Edit Room Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingRoom ? 'Edit Room' : 'Add New Room'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal" required>
                  <InputLabel>Hotel</InputLabel>
                  <Select
                    value={formData.hotelId}
                    onChange={handleHotelChange}
                    label="Hotel"
                  >
                    {hotels.map((hotel) => (
                      <MenuItem key={hotel.id} value={hotel.id}>
                        {hotel.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Room Name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="e.g., Deluxe Room, Suite, etc."
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  multiline
                  rows={3}
                  placeholder="Describe the room features and amenities"
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  margin="normal"
                  fullWidth
                  label="Room Number"
                  name="roomNumber"
                  value={formData.roomNumber}
                  onChange={handleInputChange}
                  placeholder="e.g., 101, A12, etc."
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  margin="normal"
                  fullWidth
                  label="Floor"
                  name="floor"
                  type="number"
                  value={formData.floor || ''}
                  onChange={handleIntegerInputChange}
                  InputProps={{
                    inputProps: { min: 0 }
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  margin="normal"
                  fullWidth
                  label="Size (sq m)"
                  name="size"
                  type="number"
                  value={formData.size || ''}
                  onChange={handleNumberInputChange}
                  InputProps={{
                    inputProps: { min: 0 }
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Price per Night"
                  name="price"
                  type="number"
                  value={formData.price || ''}
                  onChange={handleNumberInputChange}
                  InputProps={{
                    startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                    inputProps: { min: 0 }
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Capacity"
                  name="capacity"
                  type="number"
                  value={formData.capacity || ''}
                  onChange={handleIntegerInputChange}
                  InputProps={{
                    inputProps: { min: 1 }
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={formData.status}
                    onChange={handleStatusChange}
                    label="Status"
                  >
                    {ROOM_STATUSES.map((status) => (
                      <MenuItem key={status} value={status}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Bed Type</InputLabel>
                  <Select
                    value={formData.bedType || ''}
                    onChange={handleBedTypeChange}
                    label="Bed Type"
                  >
                    <MenuItem value="">Select Bed Type</MenuItem>
                    {BED_TYPES.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Amenities</InputLabel>
                  <Select
                    multiple
                    value={formData.amenities || []}
                    onChange={handleAmenitiesChange}
                    input={<OutlinedInput label="Amenities" />}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} />
                        ))}
                      </Box>
                    )}
                  >
                    {AMENITIES.map((amenity) => (
                      <MenuItem key={amenity} value={amenity}>
                        <Checkbox checked={(formData.amenities || []).indexOf(amenity) > -1} />
                        <ListItemText primary={amenity} />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" sx={{ mt: 2 }}>Room Images</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <TextField
                    fullWidth
                    label="Image URL"
                    placeholder="Enter image URL and press Enter"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleImageUrlChange(e as any);
                      }
                    }}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={(e) => handleImageUrlChange(e as any)}
                          >
                            <AddIcon />
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                </Box>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {formData.images?.map((image, index) => (
                    <Box
                      key={index}
                      sx={{
                        position: 'relative',
                        width: 100,
                        height: 100,
                        border: '1px solid #ddd',
                        borderRadius: 1,
                        overflow: 'hidden',
                      }}
                    >
                      <img
                        src={image}
                        alt={`Room ${index + 1}`}
                        style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = 'https://via.placeholder.com/100?text=Error';
                        }}
                      />
                      <IconButton
                        size="small"
                        sx={{
                          position: 'absolute',
                          top: 0,
                          right: 0,
                          bgcolor: 'rgba(255,255,255,0.7)',
                        }}
                        onClick={() => handleRemoveImage(index)}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  ))}
                  {(!formData.images || formData.images.length === 0) && (
                    <Box
                      sx={{
                        width: 100,
                        height: 100,
                        border: '1px dashed #ddd',
                        borderRadius: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <ImageIcon sx={{ color: '#ddd' }} />
                    </Box>
                  )}
                </Box>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingRoom ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Rooms;
