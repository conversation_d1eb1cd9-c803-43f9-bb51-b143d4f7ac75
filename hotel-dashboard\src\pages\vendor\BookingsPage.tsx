import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  InputAdornment,
  Grid,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Cancel as CancelIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { auth, db } from '../../firebase/config';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';

// Define TabPanel component for tab content
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`bookings-tabpanel-${index}`}
      aria-labelledby={`bookings-tab-${index}`}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

// Simple Hotel interface
interface Hotel {
  id: string;
  name: string;
}

// Simple Booking interface
interface Booking {
  id: string;
  bookingNumber: string;
  guestName: string;
  checkInDate: string;
  checkOutDate: string;
  roomNumber: string;
  status: string;
  totalAmount: number;
}

const BookingsPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [bookings, setBookings] = useState<Booking[]>([]);

  // Load data on component mount
  useEffect(() => {
    fetchHotels();
  }, []);

  // Fetch hotels
  const fetchHotels = async () => {
    try {
      setLoading(true);
      if (!auth.currentUser) {
        setError('You must be logged in to view bookings');
        setLoading(false);
        return;
      }

      // Fetch hotels from Firestore
      const hotelsCollection = collection(db, 'hotels');
      const hotelsQuery = query(hotelsCollection, where('vendorId', '==', auth.currentUser.uid));
      const hotelsSnapshot = await getDocs(hotelsQuery);

      const hotelsList: Hotel[] = [];
      hotelsSnapshot.forEach((doc) => {
        const data = doc.data();
        hotelsList.push({
          id: doc.id,
          name: data.name || 'Unnamed Hotel'
        });
      });

      setHotels(hotelsList);

      if (hotelsList.length > 0) {
        setSelectedHotel(hotelsList[0].id);
        fetchBookings(hotelsList[0].id);
      }

      setLoading(false);
    } catch (err) {
      console.error('Error fetching hotels:', err);
      setError('Failed to load hotels. Please try again later.');
      setLoading(false);
    }
  };

  // Fetch bookings
  const fetchBookings = async (hotelId: string) => {
    try {
      setLoading(true);

      // Fetch bookings from Firestore
      const bookingsCollection = collection(db, 'bookings');
      const bookingsQuery = query(
        bookingsCollection,
        where('hotelId', '==', hotelId),
        orderBy('createdAt', 'desc'),
        limit(20)
      );

      const bookingsSnapshot = await getDocs(bookingsQuery);

      const bookingsList: Booking[] = [];
      bookingsSnapshot.forEach((doc) => {
        const data = doc.data();

        // Format dates
        let checkInDate = 'N/A';
        let checkOutDate = 'N/A';

        if (data.checkInDate && typeof data.checkInDate.toDate === 'function') {
          checkInDate = data.checkInDate.toDate().toLocaleDateString();
        }

        if (data.checkOutDate && typeof data.checkOutDate.toDate === 'function') {
          checkOutDate = data.checkOutDate.toDate().toLocaleDateString();
        }

        // Format guest name
        let guestName = 'N/A';
        if (data.guestInfo) {
          const firstName = data.guestInfo.firstName || '';
          const lastName = data.guestInfo.lastName || '';
          guestName = `${firstName} ${lastName}`.trim() || 'N/A';
        }

        bookingsList.push({
          id: doc.id,
          bookingNumber: data.bookingNumber || 'N/A',
          guestName,
          checkInDate,
          checkOutDate,
          roomNumber: data.roomId || 'N/A',
          status: data.bookingStatus || 'N/A',
          totalAmount: data.totalAmount || 0
        });
      });

      setBookings(bookingsList);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching bookings:', err);
      setError('Failed to load bookings. Please try again later.');
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle hotel change
  const handleHotelChange = (event: any) => {
    const hotelId = event.target.value;
    setSelectedHotel(hotelId);
    fetchBookings(hotelId);
  };

  // Handle search term change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  // Handle refresh
  const handleRefresh = () => {
    if (selectedHotel) {
      fetchBookings(selectedHotel);
    }
  };

  // Get booking status color
  const getBookingStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'checked_in':
        return 'primary';
      case 'checked_out':
        return 'default';
      case 'cancelled':
        return 'error';
      case 'no_show':
        return 'error';
      default:
        return 'default';
    }
  };

  // Filter bookings based on search term
  const filteredBookings = bookings.filter(booking => {
    if (!searchTerm) return true;

    const term = searchTerm.toLowerCase();
    return (
      booking.bookingNumber.toLowerCase().includes(term) ||
      booking.guestName.toLowerCase().includes(term)
    );
  });

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Bookings Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          disabled={!selectedHotel}
        >
          New Booking
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Hotel selector and search */}
      {hotels.length > 0 && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel id="hotel-select-label">Select Hotel</InputLabel>
                <Select
                  labelId="hotel-select-label"
                  value={selectedHotel}
                  label="Select Hotel"
                  onChange={handleHotelChange}
                >
                  {hotels.map((hotel) => (
                    <MenuItem key={hotel.id} value={hotel.id}>
                      {hotel.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search by booking # or guest name"
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  )
                }}
              />
            </Grid>

            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleRefresh}
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
        </Paper>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : hotels.length === 0 ? (
        <Alert severity="info">
          You need to create at least one hotel before you can manage bookings.
        </Alert>
      ) : (
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab label="All Bookings" />
            <Tab label="Upcoming" />
            <Tab label="Current" />
            <Tab label="Past" />
            <Tab label="Cancelled" />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Box>
                <Typography variant="h6" gutterBottom>
                  All Bookings
                </Typography>
                <Typography variant="body1">
                  View and manage all bookings for your hotel.
                </Typography>
              </Box>
            </Box>

            {filteredBookings.length === 0 ? (
              <Alert severity="info" sx={{ mb: 3 }}>
                No bookings found. Try adjusting your search or filters.
              </Alert>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Booking #</TableCell>
                      <TableCell>Guest</TableCell>
                      <TableCell>Dates</TableCell>
                      <TableCell>Room</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Total</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredBookings.map((booking) => (
                      <TableRow key={booking.id}>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {booking.bookingNumber}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {booking.guestName}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {booking.checkInDate}
                          </Typography>
                          <Typography variant="body2">
                            to {booking.checkOutDate}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            Room {booking.roomNumber}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={booking.status.replace('_', ' ')}
                            color={getBookingStatusColor(booking.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          ₹{booking.totalAmount}
                        </TableCell>
                        <TableCell align="right">
                          <Tooltip title="View Details">
                            <IconButton size="small" color="primary">
                              <VisibilityIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          {booking.status === 'confirmed' && (
                            <Tooltip title="Check In">
                              <IconButton size="small" color="success">
                                <CheckCircleIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                          {(booking.status === 'confirmed' || booking.status === 'pending') && (
                            <>
                              <Tooltip title="Edit">
                                <IconButton size="small" color="primary">
                                  <EditIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Cancel">
                                <IconButton size="small" color="error">
                                  <CancelIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </TabPanel>

          {/* Other tab panels would go here */}
        </Paper>
      )}
    </Box>
  );
};

export default BookingsPage;
