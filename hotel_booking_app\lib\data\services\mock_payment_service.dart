import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/data/models/booking_model.dart';
import 'package:hotel_booking_app/data/models/payment_model.dart';
import 'package:hotel_booking_app/data/repositories/booking_repository.dart';

/// A mock payment service that simulates payment processing
/// This is used instead of the real Razorpay integration
class MockPaymentService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final BookingRepository _bookingRepository = BookingRepository();
  
  bool _isLoading = false;
  String? _error;
  
  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Callbacks for payment events
  Function(PaymentDetails)? onPaymentSuccess;
  Function(String)? onPaymentError;
  
  // Generate a mock payment ID
  String _generateMockPaymentId() {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = Random();
    final result = StringBuffer('mock_pay_');
    for (var i = 0; i < 14; i++) {
      result.write(chars[random.nextInt(chars.length)]);
    }
    return result.toString();
  }
  
  // Process a payment for a booking
  Future<PaymentDetails?> processPayment({
    required Booking booking,
    required PaymentMethod paymentMethod,
    String? savedPaymentMethodId,
    String? customerName,
    String? customerEmail,
    String? customerPhone,
  }) async {
    if (_auth.currentUser == null) {
      _error = 'User not authenticated';
      notifyListeners();
      return null;
    }
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Generate mock payment ID
      final mockPaymentId = _generateMockPaymentId();
      
      // Create payment metadata
      final metadata = {
        'mock_payment_id': mockPaymentId,
        'mock_order_id': 'mock_order_${DateTime.now().millisecondsSinceEpoch}',
        'payment_timestamp': DateTime.now().millisecondsSinceEpoch,
        'payment_gateway': 'mock_gateway',
        'is_mock': true,
        'customer_name': customerName,
        'customer_email': customerEmail,
        'customer_phone': customerPhone,
      };
      
      // Create a payment record
      final payment = PaymentDetails(
        id: '',
        userId: _auth.currentUser!.uid,
        bookingId: booking.id,
        amount: booking.totalPrice,
        currency: 'INR',
        method: paymentMethod,
        status: PaymentStatus.processing,
        transactionId: mockPaymentId,
        gatewayResponse: 'MOCK Payment successful',
        metadata: metadata,
        createdAt: DateTime.now(),
      );
      
      // Save payment to Firestore
      final docRef = await _firestore
          .collection(AppConstants.paymentsCollection)
          .add(payment.toMap());
      
      // Update the payment with the document ID
      await _firestore
          .collection(AppConstants.paymentsCollection)
          .doc(docRef.id)
          .update({
        'id': docRef.id,
      });
      
      // Simulate payment processing delay
      await Future.delayed(const Duration(seconds: 2));
      
      // Update payment status to completed
      await _firestore
          .collection(AppConstants.paymentsCollection)
          .doc(docRef.id)
          .update({
        'status': PaymentStatus.completed.toString().split('.').last,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      // Update booking payment status
      await _bookingRepository.updatePaymentStatus(
        booking.id,
        AppConstants.paymentCompleted,
      );
      
      // Return the payment details
      final paymentDetails = PaymentDetails(
        id: docRef.id,
        userId: payment.userId,
        bookingId: payment.bookingId,
        amount: payment.amount,
        currency: payment.currency,
        method: payment.method,
        status: PaymentStatus.completed,
        transactionId: mockPaymentId,
        gatewayResponse: payment.gatewayResponse,
        metadata: payment.metadata,
        createdAt: payment.createdAt,
        updatedAt: DateTime.now(),
      );
      
      _isLoading = false;
      notifyListeners();
      
      // Call success callback if provided
      if (onPaymentSuccess != null) {
        onPaymentSuccess!(paymentDetails);
      }
      
      return paymentDetails;
    } catch (e) {
      _error = e.toString();
      
      // Call error callback if provided
      if (onPaymentError != null) {
        onPaymentError!(_error!);
      }
      
      _isLoading = false;
      notifyListeners();
      return null;
    }
  }
  
  // Process UPI payment
  Future<PaymentDetails?> processUpiPayment({
    required Booking booking,
    required String customerName,
    required String customerEmail,
    required String customerPhone,
    required String upiId,
  }) async {
    return processPayment(
      booking: booking,
      paymentMethod: PaymentMethod.upi,
      customerName: customerName,
      customerEmail: customerEmail,
      customerPhone: customerPhone,
      savedPaymentMethodId: upiId,
    );
  }
  
  // Process card payment
  Future<PaymentDetails?> processCardPayment({
    required Booking booking,
    required String customerName,
    required String customerEmail,
    required String customerPhone,
  }) async {
    return processPayment(
      booking: booking,
      paymentMethod: PaymentMethod.creditCard,
      customerName: customerName,
      customerEmail: customerEmail,
      customerPhone: customerPhone,
    );
  }
}
