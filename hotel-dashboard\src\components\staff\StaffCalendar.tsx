import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Person as PersonIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import ConflictAlert from './ConflictAlert';
import { checkAllConflicts, ScheduleConflict, CONFLICT_SEVERITY_ERROR } from '../../services/scheduleConflictService';

// Define shift types
const SHIFT_TYPE_MORNING = 'morning';
const SHIFT_TYPE_AFTERNOON = 'afternoon';
const SHIFT_TYPE_NIGHT = 'night';
const SHIFT_TYPE_CUSTOM = 'custom';

interface StaffCalendarProps {
  hotelId: string;
  vendorId: string;
  staffList: any[];
  shifts: any[];
  timeOffRequests: any[];
  loading: boolean;
  error: string | null;
  onRefresh: () => void;
  onShiftCreate: (shift: any) => Promise<void>;
  onShiftUpdate: (shiftId: string, updates: any) => Promise<void>;
  onShiftDelete: (shiftId: string) => Promise<void>;
}

const StaffCalendar: React.FC<StaffCalendarProps> = ({
  hotelId,
  vendorId,
  staffList,
  shifts,
  timeOffRequests,
  loading,
  error,
  onRefresh,
  onShiftCreate,
  onShiftUpdate,
  onShiftDelete
}) => {
  const [conflicts, setConflicts] = useState<ScheduleConflict[]>([]);
  const [showConflictDialog, setShowConflictDialog] = useState<boolean>(false);
  const [pendingShift, setPendingShift] = useState<any>(null);
  const [isUpdate, setIsUpdate] = useState<boolean>(false);
  // Function to check for conflicts
  const checkConflicts = async (shift: any) => {
    try {
      const detectedConflicts = await checkAllConflicts(shift, hotelId);
      setConflicts(detectedConflicts);

      if (detectedConflicts.length > 0) {
        // If there are conflicts, show the conflict dialog
        setPendingShift(shift);
        setShowConflictDialog(true);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error checking conflicts:', error);
      return false;
    }
  };

  // Function to handle shift creation with conflict checking
  const handleCreateShift = async (shift: any) => {
    const hasConflicts = await checkConflicts(shift);

    if (!hasConflicts) {
      // If no conflicts, create the shift directly
      await onShiftCreate(shift);
    } else {
      // If there are conflicts, the dialog will be shown
      // and the user can decide whether to proceed
      setIsUpdate(false);
    }
  };

  // Function to handle shift update with conflict checking
  const handleUpdateShift = async (shiftId: string, updates: any) => {
    const shift = {
      ...updates,
      id: shiftId
    };

    const hasConflicts = await checkConflicts(shift);

    if (!hasConflicts) {
      // If no conflicts, update the shift directly
      await onShiftUpdate(shiftId, updates);
    } else {
      // If there are conflicts, the dialog will be shown
      // and the user can decide whether to proceed
      setIsUpdate(true);
    }
  };

  // Function to proceed with shift creation/update despite conflicts
  const handleProceedDespiteConflicts = async () => {
    try {
      if (isUpdate && pendingShift) {
        await onShiftUpdate(pendingShift.id, pendingShift);
      } else if (pendingShift) {
        await onShiftCreate(pendingShift);
      }

      // Clear conflicts and close dialog
      setConflicts([]);
      setShowConflictDialog(false);
      setPendingShift(null);
    } catch (error) {
      console.error('Error proceeding with shift:', error);
    }
  };

  // Function to cancel shift creation/update due to conflicts
  const handleCancelDueToConflicts = () => {
    setConflicts([]);
    setShowConflictDialog(false);
    setPendingShift(null);
  };

  // Check if there are any error-level conflicts
  const hasErrorConflicts = conflicts.some(conflict => conflict.severity === CONFLICT_SEVERITY_ERROR);

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel id="staff-filter-label">Staff Member</InputLabel>
          <Select
            labelId="staff-filter-label"
            value="all"
            label="Staff Member"
            startAdornment={
              <PersonIcon sx={{ mr: 1, color: 'action.active' }} />
            }
          >
            <MenuItem value="all">All Staff</MenuItem>
            {staffList.map((staff) => (
              <MenuItem key={staff.id} value={staff.id || ''}>
                {staff.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={onRefresh}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Display conflicts if any */}
      {conflicts.length > 0 && !showConflictDialog && (
        <ConflictAlert conflicts={conflicts} />
      )}

      <Paper sx={{ p: 5 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="h6" gutterBottom>
              Calendar View
            </Typography>
            <Typography variant="body1">
              The calendar view requires FullCalendar dependencies to be installed.
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              Please run the following command to install the dependencies:
            </Typography>
            <Box component="pre" sx={{ bgcolor: '#f5f5f5', p: 2, borderRadius: 1, mt: 1, textAlign: 'left', display: 'inline-block' }}>
              npm install @fullcalendar/core @fullcalendar/daygrid @fullcalendar/interaction @fullcalendar/react @fullcalendar/timegrid
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              Staff Members: {staffList.length}, Shifts: {shifts.length}, Time Off Requests: {timeOffRequests.length}
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Conflict Dialog */}
      <Dialog
        open={showConflictDialog}
        onClose={handleCancelDueToConflicts}
        aria-labelledby="conflict-dialog-title"
        aria-describedby="conflict-dialog-description"
      >
        <DialogTitle id="conflict-dialog-title" sx={{
          display: 'flex',
          alignItems: 'center',
          color: hasErrorConflicts ? 'error.main' : 'warning.main'
        }}>
          <WarningIcon sx={{ mr: 1 }} />
          {hasErrorConflicts ? 'Schedule Conflicts Detected' : 'Schedule Warnings'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="conflict-dialog-description" sx={{ mb: 2 }}>
            {hasErrorConflicts ?
              'The following conflicts were detected in the schedule. These conflicts may cause problems with staff scheduling.' :
              'The following warnings were detected in the schedule. These are not critical issues but may affect staff well-being.'
            }
          </DialogContentText>

          <ConflictAlert conflicts={conflicts} />

          <DialogContentText sx={{ mt: 2 }}>
            {hasErrorConflicts ?
              'Do you want to proceed despite these conflicts?' :
              'Do you want to proceed with the schedule?'
            }
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDueToConflicts}>
            Cancel
          </Button>
          <Button
            onClick={handleProceedDespiteConflicts}
            color={hasErrorConflicts ? 'error' : 'warning'}
            variant="contained"
          >
            Proceed Anyway
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StaffCalendar;
