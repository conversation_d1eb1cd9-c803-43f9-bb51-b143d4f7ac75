import React from 'react';
import { Box, Typography, Paper, Button } from '@mui/material';
import { useNavigate } from 'react-router-dom';

const BookingsFallback: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Box sx={{ p: 3 }}>
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h4" gutterBottom>
          Bookings Management
        </Typography>
        <Typography variant="body1" paragraph>
          We're sorry, but there was an error loading the bookings data.
        </Typography>
        <Typography variant="body2" paragraph color="text.secondary">
          This could be due to missing or incomplete data in the database.
        </Typography>
        <Box sx={{ mt: 3 }}>
          <Button 
            variant="contained" 
            color="primary" 
            onClick={() => navigate(0)}
            sx={{ mx: 1 }}
          >
            Refresh Page
          </Button>
          <Button 
            variant="outlined" 
            onClick={() => navigate('/dashboard')}
            sx={{ mx: 1 }}
          >
            Go to Dashboard
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default BookingsFallback;
