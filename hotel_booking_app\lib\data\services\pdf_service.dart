import 'dart:io';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:flutter/material.dart' as material;
import 'package:hotel_booking_app/data/models/booking_model.dart';
import 'package:hotel_booking_app/data/models/aadhaar_verification_model.dart';

class PdfService {
  // Generate booking confirmation PDF
  static Future<Uint8List> generateBookingConfirmation(
    Booking booking, {
    bool isCheckedIn = false,
    AadhaarVerification? aadhaarVerification,
  }) async {
    final pdf = pw.Document();

    // Load logo image
    final ByteData logoData =
        await rootBundle.load('assets/images/link_in_blink_logo.png');
    final Uint8List logoBytes = logoData.buffer.asUint8List();
    final pw.MemoryImage logoImage = pw.MemoryImage(logoBytes);

    // Generate QR code data
    final String qrData =
        'BOOKING:${booking.id}:${booking.userId}:${booking.checkIn.millisecondsSinceEpoch}';

    // Generate QR code image
    final qrPainter = QrPainter(
      data: qrData,
      version: QrVersions.auto,
      gapless: true,
      eyeStyle: const QrEyeStyle(
        eyeShape: QrEyeShape.square,
        color: material.Colors.black,
      ),
      dataModuleStyle: const QrDataModuleStyle(
        dataModuleShape: QrDataModuleShape.square,
        color: material.Colors.black,
      ),
      embeddedImage: null,
      embeddedImageStyle: null,
    );

    // Convert QR code to bytes
    final qrImageBytes = await _generateQrImageBytes(qrPainter);

    // Add pages to the PDF
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        header: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Image(logoImage, width: 100),
                  pw.Text(
                    isCheckedIn
                        ? 'CHECK-IN CONFIRMATION'
                        : 'BOOKING CONFIRMATION',
                    style: pw.TextStyle(
                      fontSize: 20,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ],
              ),
              pw.SizedBox(height: 5),
              pw.Divider(),
            ],
          );
        },
        footer: (pw.Context context) {
          return pw.Column(
            children: [
              pw.Divider(),
              pw.SizedBox(height: 5),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    'Generated on ${DateFormat('dd MMM yyyy, HH:mm').format(DateTime.now())}',
                    style: const pw.TextStyle(fontSize: 10),
                  ),
                  pw.Text(
                    'Page ${context.pageNumber} of ${context.pagesCount}',
                    style: const pw.TextStyle(fontSize: 10),
                  ),
                ],
              ),
            ],
          );
        },
        build: (pw.Context context) {
          return [
            // Booking ID and QR Code
            pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Expanded(
                  flex: 3,
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'Booking ID: ${booking.id.substring(0, 8).toUpperCase()}',
                        style: pw.TextStyle(
                          fontSize: 14,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 5),
                      pw.Text(
                        'Status: ${isCheckedIn ? 'Checked In' : booking.status.toUpperCase()}',
                        style: pw.TextStyle(
                          fontSize: 12,
                          fontWeight: pw.FontWeight.bold,
                          color: isCheckedIn ? PdfColors.green : PdfColors.blue,
                        ),
                      ),
                      pw.SizedBox(height: 5),
                      pw.Text(
                        'Payment: ${booking.paymentStatus.toUpperCase()}',
                        style: pw.TextStyle(
                          fontSize: 12,
                          fontWeight: pw.FontWeight.bold,
                          color: booking.paymentStatus == 'completed'
                              ? PdfColors.green
                              : PdfColors.orange,
                        ),
                      ),
                    ],
                  ),
                ),
                pw.Expanded(
                  flex: 1,
                  child: pw.Container(
                    alignment: pw.Alignment.topRight,
                    child: pw.Image(pw.MemoryImage(qrImageBytes),
                        width: 80, height: 80),
                  ),
                ),
              ],
            ),

            pw.SizedBox(height: 20),

            // Hotel and Room Details
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                color: PdfColors.grey100,
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Hotel Details',
                    style: pw.TextStyle(
                      fontSize: 14,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 5),
                  pw.Text('Hotel: ${booking.hotelName}'),
                  pw.SizedBox(height: 3),
                  pw.Text('Room: ${booking.roomName}'),
                  pw.SizedBox(height: 3),
                  pw.Text('Guests: ${booking.guests}'),
                ],
              ),
            ),

            pw.SizedBox(height: 15),

            // Check-in and Check-out Details
            pw.Row(
              children: [
                pw.Expanded(
                  child: pw.Container(
                    padding: const pw.EdgeInsets.all(10),
                    decoration: pw.BoxDecoration(
                      border: pw.Border.all(color: PdfColors.grey300),
                      borderRadius:
                          const pw.BorderRadius.all(pw.Radius.circular(5)),
                    ),
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          'Check-in',
                          style: pw.TextStyle(
                            fontSize: 12,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.SizedBox(height: 5),
                        pw.Text(
                          DateFormat('dd MMM yyyy').format(booking.checkIn),
                          style: const pw.TextStyle(fontSize: 14),
                        ),
                        pw.SizedBox(height: 3),
                        pw.Text(
                          'From 2:00 PM',
                          style: const pw.TextStyle(fontSize: 10),
                        ),
                      ],
                    ),
                  ),
                ),
                pw.SizedBox(width: 10),
                pw.Expanded(
                  child: pw.Container(
                    padding: const pw.EdgeInsets.all(10),
                    decoration: pw.BoxDecoration(
                      border: pw.Border.all(color: PdfColors.grey300),
                      borderRadius:
                          const pw.BorderRadius.all(pw.Radius.circular(5)),
                    ),
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          'Check-out',
                          style: pw.TextStyle(
                            fontSize: 12,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.SizedBox(height: 5),
                        pw.Text(
                          DateFormat('dd MMM yyyy').format(booking.checkOut),
                          style: const pw.TextStyle(fontSize: 14),
                        ),
                        pw.SizedBox(height: 3),
                        pw.Text(
                          'Until 12:00 PM',
                          style: const pw.TextStyle(fontSize: 10),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            pw.SizedBox(height: 15),

            // Payment Details
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Payment Details',
                    style: pw.TextStyle(
                      fontSize: 14,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text('Total Amount:'),
                      pw.Text(
                        '₹${booking.totalPrice.toStringAsFixed(0)}',
                        style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 5),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text('Payment Status:'),
                      pw.Text(
                        booking.paymentStatus.toUpperCase(),
                        style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold,
                          color: booking.paymentStatus == 'completed'
                              ? PdfColors.green
                              : PdfColors.orange,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Aadhaar Verification Details (if checked in)
            if (isCheckedIn && aadhaarVerification != null) ...[
              pw.SizedBox(height: 15),
              pw.Container(
                padding: const pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(
                  color: PdfColors.green50,
                  border: pw.Border.all(color: PdfColors.green200),
                  borderRadius:
                      const pw.BorderRadius.all(pw.Radius.circular(5)),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Verification Details',
                      style: pw.TextStyle(
                        fontSize: 14,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.green800,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Row(
                      children: [
                        pw.Text('Name:'),
                        pw.SizedBox(width: 5),
                        pw.Text(
                          aadhaarVerification.fullName,
                          style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 5),
                    pw.Row(
                      children: [
                        pw.Text('Aadhaar:'),
                        pw.SizedBox(width: 5),
                        pw.Text(
                          aadhaarVerification.maskedAadhaarNumber,
                          style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 5),
                    pw.Row(
                      children: [
                        pw.Text('Verified On:'),
                        pw.SizedBox(width: 5),
                        pw.Text(
                          aadhaarVerification.verifiedAt != null
                              ? DateFormat('dd MMM yyyy')
                                  .format(aadhaarVerification.verifiedAt!)
                              : 'N/A',
                          style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],

            pw.SizedBox(height: 20),

            // Terms and Conditions
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                color: PdfColors.grey100,
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Terms and Conditions',
                    style: pw.TextStyle(
                      fontSize: 12,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 5),
                  pw.Text(
                    '• Check-in time is 2:00 PM and check-out time is 12:00 PM.',
                    style: const pw.TextStyle(fontSize: 10),
                  ),
                  pw.Text(
                    '• Please present this confirmation and a valid ID at check-in.',
                    style: const pw.TextStyle(fontSize: 10),
                  ),
                  pw.Text(
                    '• Aadhaar verification is required for check-in.',
                    style: const pw.TextStyle(fontSize: 10),
                  ),
                  pw.Text(
                    '• Cancellation policy: Free cancellation up to 24 hours before check-in.',
                    style: const pw.TextStyle(fontSize: 10),
                  ),
                ],
              ),
            ),
          ];
        },
      ),
    );

    return pdf.save();
  }

  // Save PDF to a file and return the file path
  static Future<String> savePdfFile(String fileName, Uint8List byteList) async {
    final output = await getTemporaryDirectory();
    final filePath = '${output.path}/$fileName';
    final file = File(filePath);
    await file.writeAsBytes(byteList);
    return filePath;
  }

  // Print PDF
  static Future<void> printPdf(Uint8List byteList) async {
    await Printing.layoutPdf(onLayout: (_) => byteList);
  }

  // Share PDF
  static Future<void> sharePdf(String fileName, Uint8List byteList) async {
    await savePdfFile(fileName, byteList);
    await Printing.sharePdf(bytes: byteList, filename: fileName);
  }

  // Generate QR code image bytes
  static Future<Uint8List> _generateQrImageBytes(QrPainter qrPainter) async {
    final picData = await qrPainter.toImageData(200);
    return picData!.buffer.asUint8List();
  }
}
