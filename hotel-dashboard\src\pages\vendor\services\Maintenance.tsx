import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, Tabs, Tab } from '@mui/material';
import ServiceRequestList from '../../../components/service-requests/ServiceRequestList';
import { auth } from '../../../firebase/config';
import { getHotelsForVendor } from '../../../firebase/hotelService';

const MaintenanceRequests: React.FC = () => {
  const [hotelId, setHotelId] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHotelId = async () => {
      try {
        setLoading(true);
        setError(null);
        
        if (!auth.currentUser) {
          setError('You must be logged in to view maintenance requests');
          setLoading(false);
          return;
        }
        
        const hotels = await getHotelsForVendor(auth.currentUser.uid);
        
        if (hotels.length === 0) {
          setError('No hotels found for this vendor');
          setLoading(false);
          return;
        }
        
        // Use the first hotel ID (in a multi-hotel system, you might want to add a hotel selector)
        setHotelId(hotels[0].id || '');
        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching hotel ID:', err);
        setError(err.message || 'Failed to load hotel information');
        setLoading(false);
      }
    };
    
    fetchHotelId();
  }, []);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <Typography>Loading...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Box>
      <ServiceRequestList 
        hotelId={hotelId} 
        serviceType="maintenance" 
        title="Maintenance Requests" 
      />
    </Box>
  );
};

export default MaintenanceRequests;
