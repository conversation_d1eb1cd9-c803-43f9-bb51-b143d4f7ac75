import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  LinearProgress,
  Divider,
  Tooltip,
  IconButton,
  useTheme
} from '@mui/material';
import {
  Info as InfoIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon
} from '@mui/icons-material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';

interface Metric {
  name: string;
  value: number;
  target: number;
  change: number;
  info: string;
}

interface PerformanceMetricsProps {
  metrics?: Metric[];
  comparisonData?: any[];
}

const PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({ 
  metrics = [
    {
      name: 'Occupancy Rate',
      value: 78,
      target: 85,
      change: 5,
      info: 'Percentage of rooms occupied'
    },
    {
      name: 'Average Daily Rate',
      value: 65,
      target: 75,
      change: -2,
      info: 'Average rate per occupied room'
    },
    {
      name: 'RevPAR',
      value: 50,
      target: 60,
      change: 3,
      info: 'Revenue per available room'
    },
    {
      name: 'Guest Satisfaction',
      value: 92,
      target: 90,
      change: 4,
      info: 'Based on guest reviews'
    }
  ],
  comparisonData = [
    {
      name: 'Jan',
      current: 4000,
      previous: 2400,
    },
    {
      name: 'Feb',
      current: 3000,
      previous: 1398,
    },
    {
      name: 'Mar',
      current: 2000,
      previous: 9800,
    },
    {
      name: 'Apr',
      current: 2780,
      previous: 3908,
    },
    {
      name: 'May',
      current: 1890,
      previous: 4800,
    },
    {
      name: 'Jun',
      current: 2390,
      previous: 3800,
    }
  ]
}) => {
  const theme = useTheme();

  return (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Typography variant="h6" gutterBottom>
        Performance Metrics
      </Typography>
      <Divider sx={{ mb: 2 }} />
      
      <Grid container spacing={2}>
        {metrics.map((metric, index) => (
          <Grid item xs={12} sm={6} key={index}>
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="body2" sx={{ fontWeight: 'bold', mr: 1 }}>
                    {metric.name}
                  </Typography>
                  <Tooltip title={metric.info}>
                    <InfoIcon fontSize="small" sx={{ color: 'text.secondary', fontSize: '1rem' }} />
                  </Tooltip>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {metric.change > 0 ? (
                    <TrendingUpIcon fontSize="small" sx={{ color: theme.palette.success.main, mr: 0.5 }} />
                  ) : (
                    <TrendingDownIcon fontSize="small" sx={{ color: theme.palette.error.main, mr: 0.5 }} />
                  )}
                  <Typography 
                    variant="caption" 
                    sx={{ 
                      color: metric.change > 0 ? theme.palette.success.main : theme.palette.error.main,
                      fontWeight: 'bold'
                    }}
                  >
                    {metric.change > 0 ? '+' : ''}{metric.change}%
                  </Typography>
                </Box>
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                <Typography variant="h6" sx={{ mr: 1 }}>
                  {metric.name === 'Average Daily Rate' || metric.name === 'RevPAR' ? '$' : ''}{metric.value}{metric.name === 'Occupancy Rate' || metric.name === 'Guest Satisfaction' ? '%' : ''}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  / Target: {metric.name === 'Average Daily Rate' || metric.name === 'RevPAR' ? '$' : ''}{metric.target}{metric.name === 'Occupancy Rate' || metric.name === 'Guest Satisfaction' ? '%' : ''}
                </Typography>
              </Box>
              
              <LinearProgress 
                variant="determinate" 
                value={(metric.value / metric.target) * 100} 
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  bgcolor: theme.palette.grey[200],
                  '& .MuiLinearProgress-bar': {
                    bgcolor: metric.value >= metric.target ? theme.palette.success.main : theme.palette.primary.main
                  }
                }}
              />
            </Box>
          </Grid>
        ))}
      </Grid>
      
      <Divider sx={{ my: 2 }} />
      
      <Typography variant="subtitle2" gutterBottom>
        Current vs Previous Period
      </Typography>
      
      <Box sx={{ height: 250, mt: 2 }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={comparisonData}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <RechartsTooltip />
            <Legend />
            <Bar dataKey="current" name="Current Period" fill={theme.palette.primary.main} />
            <Bar dataKey="previous" name="Previous Period" fill={theme.palette.grey[400]} />
          </BarChart>
        </ResponsiveContainer>
      </Box>
    </Paper>
  );
};

export default PerformanceMetrics;
