# Calendar View for Staff Scheduling

The hotel dashboard now includes a calendar view for staff scheduling, providing a more visual way to manage staff shifts and time off.

## Features

- Interactive calendar interface using FullCalendar
- View staff shifts and approved time off in a unified calendar
- Filter by staff member
- Add, edit, and delete shifts directly from the calendar
- Different colors for different shift types (morning, afternoon, night, custom)
- Day, week, and month views for different planning perspectives

## Installation

To use the calendar view, you need to install the FullCalendar dependencies:

```
npm install @fullcalendar/core @fullcalendar/daygrid @fullcalendar/interaction @fullcalendar/react @fullcalendar/timegrid
```

Or run the provided batch file:

```
install-calendar-deps.bat
```

## Usage

1. Navigate to the Staff Scheduling page
2. Click on the "Calendar View" tab
3. Use the staff filter to view shifts for specific staff members or all staff
4. Click on a time slot to add a new shift
5. Click on an existing shift to edit or delete it

## Benefits

- Better visualization of staff coverage across different days and times
- Easily identify scheduling conflicts
- See the impact of time off requests on staffing
- More intuitive interface for scheduling staff

## Integration with Time Off Management

The calendar view integrates with the time off management system:

- Approved time off requests appear as all-day events in the calendar
- Time off is color-coded differently from shifts for easy identification
- Helps prevent scheduling conflicts with approved time off

## Technical Implementation

The calendar view is implemented using:

- FullCalendar React component
- Firebase Firestore for data storage
- React hooks for state management
- Material-UI for the user interface components
