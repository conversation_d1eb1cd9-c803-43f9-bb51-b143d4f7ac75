import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { auth } from './firebase/config';
import { onAuthStateChanged } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { db } from './firebase/config';
import { ROLE_SUPER_ADMIN, ROLE_VENDOR, ROLE_STAFF } from './firebase/auth';
import { initializeDatabase, isDatabaseInitialized } from './firebase/initializeDatabase';
import { initializeAuth } from './firebase/initializeAuth';
import { AuthProvider } from './contexts/AuthContext';

// Auth Pages
import Login from './pages/auth/Login';
import ForgotPassword from './pages/auth/ForgotPassword';

// Admin Pages
import AdminDashboard from './pages/admin/NewAdminDashboard';
import AdminHotels from './pages/admin/Hotels';
import AdminRooms from './pages/admin/Rooms';
import AdminVendors from './pages/admin/Vendors';
import AdminStaff from './pages/admin/Staff';
import AdminUsers from './pages/admin/Users';
import AdminBookings from './pages/admin/Bookings';
import AdminReports from './pages/admin/Reports';
import AdminNotifications from './pages/admin/Notifications';
import AdminSettings from './pages/admin/Settings';

// Vendor Pages
import VendorDashboard from './pages/vendor/NewDashboard';
import VendorHotels from './pages/vendor/Hotels';
import VendorRooms from './pages/vendor/Rooms';
// Import the Bookings page component
import BookingsPage from './pages/vendor/BookingsPage';
import VendorPricing from './pages/vendor/Pricing';
import VendorStaff from './pages/vendor/Staff';
import VendorStaffManagement from './pages/vendor/StaffManagement';
import VendorStaffScheduling from './pages/vendor/StaffScheduling';
import VendorSettings from './pages/vendor/Settings';
import VendorNotifications from './pages/vendor/Notifications';
import VendorReports from './pages/vendor/Reports';
import VendorVerifications from './pages/vendor/Verifications';
import VendorVerificationDetails from './pages/vendor/VerificationDetails';
import VendorVerificationReports from './pages/vendor/VendorVerificationReports';

// Vendor Service Pages
import CleaningRequests from './pages/vendor/services/CleaningRequests';
import FoodOrders from './pages/vendor/services/Food';
import MaintenanceRequests from './pages/vendor/services/Maintenance';
import ServiceRequestsPage from './pages/vendor/ServiceRequestsPage';

// Staff Pages
import StaffDashboard from './pages/staff/Dashboard';
import StaffBookings from './pages/staff/Bookings';
import StaffCheckIn from './pages/staff/CheckIn';
import StaffCheckOut from './pages/staff/CheckOut';

// Layouts
import AdminLayout from './components/layouts/AdminLayout';
import VendorLayout from './components/layouts/VendorLayout';
import StaffLayout from './components/layouts/StaffLayout';
import AuthLayout from './components/layouts/AuthLayout';

// Loading Component
import LoadingScreen from './components/common/LoadingScreen';



// Create theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#1A73E8',
    },
    secondary: {
      main: '#03DAC6',
    },
    error: {
      main: '#B00020',
    },
    background: {
      default: '#F5F5F5',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
    },
    h2: {
      fontWeight: 700,
    },
    h3: {
      fontWeight: 600,
    },
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 500,
    },
    h6: {
      fontWeight: 500,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)',
        },
      },
    },
  },
});

function App() {
  const [user, setUser] = useState<any>(null);
  const [userData, setUserData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if database needs initialization
    const checkDatabase = async () => {
      const isInitialized = await isDatabaseInitialized();
      if (!isInitialized) {
        console.log('Initializing database with sample data...');
        await initializeDatabase();
        console.log('Database initialized successfully');

        // Initialize authentication with sample users
        console.log('Initializing authentication with sample users...');
        await initializeAuth();
        console.log('Authentication initialized successfully');
      }
    };

    checkDatabase();

    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        try {
          const docRef = doc(db, 'users', user.uid);
          const docSnap = await getDoc(docRef);

          if (docSnap.exists()) {
            setUserData(docSnap.data());
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
        }
      } else {
        setUserData(null);
      }

      setUser(user);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <AuthProvider>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Routes>
            {/* Auth Routes */}
            <Route path="/" element={<AuthLayout />}>
              <Route index element={user ? <Navigate to="/dashboard" /> : <Login />} />
              <Route path="login" element={user ? <Navigate to="/dashboard" /> : <Login />} />
              <Route path="forgot-password" element={user ? <Navigate to="/dashboard" /> : <ForgotPassword />} />
            </Route>

            {/* Admin Routes */}
            <Route
              path="/admin"
              element={
                user && userData?.role === ROLE_SUPER_ADMIN ? (
                  <AdminLayout />
                ) : (
                  <Navigate to="/login" />
                )
              }
            >
              <Route index element={<AdminDashboard />} />
              <Route path="hotels" element={<AdminHotels />} />
              <Route path="rooms" element={<AdminRooms />} />
              <Route path="vendors" element={<AdminVendors />} />
              <Route path="staff" element={<AdminStaff />} />
              <Route path="users" element={<AdminUsers />} />
              <Route path="bookings" element={<AdminBookings />} />
              <Route path="reports" element={<AdminReports />} />
              <Route path="notifications" element={<AdminNotifications />} />
              <Route path="settings" element={<AdminSettings />} />
            </Route>

            {/* Vendor Routes */}
            <Route
              path="/vendor"
              element={
                user && userData?.role === ROLE_VENDOR ? (
                  <VendorLayout />
                ) : (
                  <Navigate to="/login" />
                )
              }
            >
              <Route index element={<VendorDashboard />} />
              <Route path="hotels" element={<VendorHotels />} />
              <Route path="rooms" element={<VendorRooms />} />
              <Route path="bookings" element={<BookingsPage />} />
              <Route path="bookings/new" element={<BookingsPage />} />
              <Route path="guests/new" element={<BookingsPage />} />
              <Route path="rooms/new" element={<VendorRooms />} />
              <Route path="availability" element={<BookingsPage />} />
              <Route path="promotions" element={<BookingsPage />} />
              <Route path="services" element={<ServiceRequestsPage />} />
              <Route path="services/cleaning" element={<CleaningRequests />} />
              <Route path="services/food" element={<FoodOrders />} />
              <Route path="services/maintenance" element={<MaintenanceRequests />} />
              <Route path="verifications" element={<VendorVerifications />} />
              <Route path="verifications/:id" element={<VendorVerificationDetails />} />
              <Route path="verification-reports" element={<VendorVerificationReports />} />
              <Route path="pricing" element={<VendorPricing />} />
              <Route path="notifications" element={<VendorNotifications />} />
              <Route path="reports" element={<VendorReports />} />
              <Route path="staff" element={<VendorStaff />} />
              <Route path="staff-management" element={<VendorStaffManagement />} />
              <Route path="staff-scheduling" element={<VendorStaffScheduling />} />
              <Route path="settings" element={<VendorSettings />} />
            </Route>

            {/* Staff Routes */}
            <Route
              path="/staff"
              element={
                user && userData?.role === ROLE_STAFF ? (
                  <StaffLayout />
                ) : (
                  <Navigate to="/login" />
                )
              }
            >
              <Route index element={<StaffDashboard />} />
              <Route path="bookings" element={<StaffBookings />} />
              <Route path="check-in" element={<StaffCheckIn />} />
              <Route path="check-out" element={<StaffCheckOut />} />
            </Route>

            {/* Dashboard Redirect */}
            <Route
              path="/dashboard"
              element={
                user ? (
                  userData?.role === ROLE_SUPER_ADMIN ? (
                    <Navigate to="/admin" />
                  ) : userData?.role === ROLE_VENDOR ? (
                    <Navigate to="/vendor" />
                  ) : userData?.role === ROLE_STAFF ? (
                    <Navigate to="/staff" />
                  ) : (
                    <Navigate to="/login" />
                  )
                ) : (
                  <Navigate to="/login" />
                )
              }
            />

            {/* Catch all */}
            <Route path="*" element={<Navigate to="/" />} />
          </Routes>
        </Router>
      </ThemeProvider>
    </AuthProvider>
  );
}

export default App;
