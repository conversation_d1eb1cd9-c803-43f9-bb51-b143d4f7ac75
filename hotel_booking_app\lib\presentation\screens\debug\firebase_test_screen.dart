import 'package:flutter/material.dart';
import 'package:hotel_booking_app/utils/firebase_test.dart';

class FirebaseTestScreen extends StatefulWidget {
  const FirebaseTestScreen({super.key});

  @override
  State<FirebaseTestScreen> createState() => _FirebaseTestScreenState();
}

class _FirebaseTestScreenState extends State<FirebaseTestScreen> {
  bool _isLoading = false;
  String _result = '';
  
  Future<void> _testFirebaseConnection() async {
    setState(() {
      _isLoading = true;
      _result = 'Testing Firebase connection...';
    });
    
    try {
      final result = await FirebaseTest.testFirebaseConnection();
      
      setState(() {
        _result = 'Connection Test Result:\n'
            '${result['success'] ? 'SUCCESS' : 'FAILED'}\n\n'
            'Message: ${result['message']}\n'
            'Hotel Count: ${result['hotelCount'] ?? 'N/A'}\n'
            'Hotels Exist: ${result['hotelsExist'] ?? 'N/A'}\n';
      });
    } catch (e) {
      setState(() {
        _result = 'Error testing connection: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  Future<void> _forceCreateHotels() async {
    setState(() {
      _isLoading = true;
      _result = 'Creating hotels...';
    });
    
    try {
      final result = await FirebaseTest.forceCreateHotels();
      
      setState(() {
        _result = 'Create Hotels Result:\n'
            '${result['success'] ? 'SUCCESS' : 'FAILED'}\n\n'
            'Message: ${result['message']}\n'
            'Previous Count: ${result['previousCount'] ?? 'N/A'}\n'
            'New Count: ${result['newCount'] ?? 'N/A'}\n'
            'Added Count: ${result['addedCount'] ?? 'N/A'}\n';
      });
    } catch (e) {
      setState(() {
        _result = 'Error creating hotels: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  Future<void> _clearAllHotels() async {
    setState(() {
      _isLoading = true;
      _result = 'Clearing hotels...';
    });
    
    try {
      final result = await FirebaseTest.clearAllHotels();
      
      setState(() {
        _result = 'Clear Hotels Result:\n'
            '${result['success'] ? 'SUCCESS' : 'FAILED'}\n\n'
            'Message: ${result['message']}\n'
            'Deleted Count: ${result['deletedCount'] ?? 'N/A'}\n';
      });
    } catch (e) {
      setState(() {
        _result = 'Error clearing hotels: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase Test'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Firebase Connection Test',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testFirebaseConnection,
                    child: const Text('Test Connection'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _forceCreateHotels,
                    child: const Text('Create Hotels'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _clearAllHotels,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade100,
                      foregroundColor: Colors.red.shade900,
                    ),
                    child: const Text('Clear Hotels'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            const Text(
              'Result:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : SingleChildScrollView(
                        child: Text(_result),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
