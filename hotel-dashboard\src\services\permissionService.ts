import { 
  collection, 
  query, 
  where, 
  getDocs, 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  deleteDoc,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { ROLE_SUPER_ADMIN, ROLE_VENDOR, ROLE_STAFF } from '../firebase/auth';

// Define permission types
export type ResourceType = 'hotel' | 'room' | 'booking' | 'user' | 'report' | 'setting';
export type ActionType = 'create' | 'read' | 'update' | 'delete' | 'manage';

// Interface for permission
export interface Permission {
  id?: string;
  role: string;
  resource: ResourceType;
  action: ActionType;
  conditions?: Record<string, any>; // Additional conditions (e.g., only own hotel)
  createdAt?: any;
  updatedAt?: any;
}

// Interface for role
export interface Role {
  id?: string;
  name: string;
  description?: string;
  isCustom: boolean;
  baseRole?: string; // Base role for custom roles
  createdBy?: string;
  createdAt?: any;
  updatedAt?: any;
}

// Interface for user role assignment
export interface UserRole {
  userId: string;
  roleId: string;
  assignedBy: string;
  assignedAt: any;
}

// Default permissions for built-in roles
const DEFAULT_PERMISSIONS: Permission[] = [
  // Super Admin permissions (full access)
  { role: ROLE_SUPER_ADMIN, resource: 'hotel', action: 'manage' },
  { role: ROLE_SUPER_ADMIN, resource: 'room', action: 'manage' },
  { role: ROLE_SUPER_ADMIN, resource: 'booking', action: 'manage' },
  { role: ROLE_SUPER_ADMIN, resource: 'user', action: 'manage' },
  { role: ROLE_SUPER_ADMIN, resource: 'report', action: 'manage' },
  { role: ROLE_SUPER_ADMIN, resource: 'setting', action: 'manage' },
  
  // Vendor permissions
  { role: ROLE_VENDOR, resource: 'hotel', action: 'create' },
  { role: ROLE_VENDOR, resource: 'hotel', action: 'read', conditions: { ownedByUser: true } },
  { role: ROLE_VENDOR, resource: 'hotel', action: 'update', conditions: { ownedByUser: true } },
  { role: ROLE_VENDOR, resource: 'hotel', action: 'delete', conditions: { ownedByUser: true } },
  
  { role: ROLE_VENDOR, resource: 'room', action: 'create', conditions: { hotelOwnedByUser: true } },
  { role: ROLE_VENDOR, resource: 'room', action: 'read', conditions: { hotelOwnedByUser: true } },
  { role: ROLE_VENDOR, resource: 'room', action: 'update', conditions: { hotelOwnedByUser: true } },
  { role: ROLE_VENDOR, resource: 'room', action: 'delete', conditions: { hotelOwnedByUser: true } },
  
  { role: ROLE_VENDOR, resource: 'booking', action: 'read', conditions: { hotelOwnedByUser: true } },
  { role: ROLE_VENDOR, resource: 'booking', action: 'update', conditions: { hotelOwnedByUser: true } },
  
  { role: ROLE_VENDOR, resource: 'user', action: 'create', conditions: { role: ROLE_STAFF } },
  { role: ROLE_VENDOR, resource: 'user', action: 'read', conditions: { createdByUser: true } },
  { role: ROLE_VENDOR, resource: 'user', action: 'update', conditions: { createdByUser: true } },
  { role: ROLE_VENDOR, resource: 'user', action: 'delete', conditions: { createdByUser: true } },
  
  { role: ROLE_VENDOR, resource: 'report', action: 'read', conditions: { hotelOwnedByUser: true } },
  { role: ROLE_VENDOR, resource: 'setting', action: 'read', conditions: { hotelOwnedByUser: true } },
  { role: ROLE_VENDOR, resource: 'setting', action: 'update', conditions: { hotelOwnedByUser: true } },
  
  // Staff permissions
  { role: ROLE_STAFF, resource: 'hotel', action: 'read', conditions: { assignedToUser: true } },
  
  { role: ROLE_STAFF, resource: 'room', action: 'read', conditions: { hotelAssignedToUser: true } },
  
  { role: ROLE_STAFF, resource: 'booking', action: 'create', conditions: { hotelAssignedToUser: true } },
  { role: ROLE_STAFF, resource: 'booking', action: 'read', conditions: { hotelAssignedToUser: true } },
  { role: ROLE_STAFF, resource: 'booking', action: 'update', conditions: { hotelAssignedToUser: true } },
  
  { role: ROLE_STAFF, resource: 'report', action: 'read', conditions: { hotelAssignedToUser: true } },
];

/**
 * Initialize default permissions in Firestore
 */
export const initializePermissions = async (): Promise<void> => {
  try {
    // Check if permissions already exist
    const permissionsQuery = query(collection(db, 'permissions'));
    const permissionsSnapshot = await getDocs(permissionsQuery);
    
    if (!permissionsSnapshot.empty) {
      console.log('Permissions already initialized');
      return;
    }
    
    // Create default permissions
    for (const permission of DEFAULT_PERMISSIONS) {
      const permissionRef = doc(collection(db, 'permissions'));
      await setDoc(permissionRef, {
        ...permission,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }
    
    console.log('Default permissions initialized successfully');
  } catch (error) {
    console.error('Error initializing permissions:', error);
    throw error;
  }
};

/**
 * Create a custom role
 */
export const createRole = async (role: Role): Promise<string> => {
  try {
    const roleRef = doc(collection(db, 'roles'));
    
    await setDoc(roleRef, {
      ...role,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    return roleRef.id;
  } catch (error) {
    console.error('Error creating role:', error);
    throw error;
  }
};

/**
 * Get all roles
 */
export const getRoles = async (): Promise<Role[]> => {
  try {
    const rolesSnapshot = await getDocs(collection(db, 'roles'));
    const roles: Role[] = [];
    
    rolesSnapshot.forEach((doc) => {
      roles.push({ id: doc.id, ...doc.data() } as Role);
    });
    
    return roles;
  } catch (error) {
    console.error('Error getting roles:', error);
    throw error;
  }
};

/**
 * Update a role
 */
export const updateRole = async (roleId: string, role: Partial<Role>): Promise<void> => {
  try {
    const roleRef = doc(db, 'roles', roleId);
    
    await updateDoc(roleRef, {
      ...role,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error updating role:', error);
    throw error;
  }
};

/**
 * Delete a role
 */
export const deleteRole = async (roleId: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, 'roles', roleId));
  } catch (error) {
    console.error('Error deleting role:', error);
    throw error;
  }
};

/**
 * Assign a role to a user
 */
export const assignRoleToUser = async (userRole: UserRole): Promise<void> => {
  try {
    const userRoleRef = doc(collection(db, 'userRoles'));
    
    await setDoc(userRoleRef, {
      ...userRole,
      assignedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error assigning role to user:', error);
    throw error;
  }
};

/**
 * Get roles assigned to a user
 */
export const getUserRoles = async (userId: string): Promise<UserRole[]> => {
  try {
    const userRolesQuery = query(
      collection(db, 'userRoles'),
      where('userId', '==', userId)
    );
    
    const userRolesSnapshot = await getDocs(userRolesQuery);
    const userRoles: UserRole[] = [];
    
    userRolesSnapshot.forEach((doc) => {
      userRoles.push(doc.data() as UserRole);
    });
    
    return userRoles;
  } catch (error) {
    console.error('Error getting user roles:', error);
    throw error;
  }
};

/**
 * Remove a role from a user
 */
export const removeRoleFromUser = async (userId: string, roleId: string): Promise<void> => {
  try {
    const userRolesQuery = query(
      collection(db, 'userRoles'),
      where('userId', '==', userId),
      where('roleId', '==', roleId)
    );
    
    const userRolesSnapshot = await getDocs(userRolesQuery);
    
    if (!userRolesSnapshot.empty) {
      await deleteDoc(userRolesSnapshot.docs[0].ref);
    }
  } catch (error) {
    console.error('Error removing role from user:', error);
    throw error;
  }
};

/**
 * Check if a user has permission to perform an action on a resource
 */
export const checkPermission = async (
  userId: string,
  resource: ResourceType,
  action: ActionType,
  context: Record<string, any> = {}
): Promise<boolean> => {
  try {
    // Get user data
    const userDoc = await getDoc(doc(db, 'users', userId));
    
    if (!userDoc.exists()) {
      return false;
    }
    
    const userData = userDoc.data();
    const userRole = userData.role;
    
    // Get custom roles assigned to the user
    const userRoles = await getUserRoles(userId);
    const customRoleIds = userRoles.map(ur => ur.roleId);
    
    // Get permissions for the user's built-in role
    const builtInPermissionsQuery = query(
      collection(db, 'permissions'),
      where('role', '==', userRole),
      where('resource', '==', resource),
      where('action', '==', action)
    );
    
    const builtInPermissionsSnapshot = await getDocs(builtInPermissionsQuery);
    
    // Check built-in role permissions
    for (const permissionDoc of builtInPermissionsSnapshot.docs) {
      const permission = permissionDoc.data() as Permission;
      
      // If no conditions, permission is granted
      if (!permission.conditions) {
        return true;
      }
      
      // Check conditions
      let conditionsMet = true;
      
      for (const [key, value] of Object.entries(permission.conditions)) {
        if (key === 'ownedByUser' && value === true) {
          if (!context.ownerId || context.ownerId !== userId) {
            conditionsMet = false;
            break;
          }
        } else if (key === 'hotelOwnedByUser' && value === true) {
          if (!context.hotelId) {
            conditionsMet = false;
            break;
          }
          
          // Get hotel data
          const hotelDoc = await getDoc(doc(db, 'hotels', context.hotelId));
          
          if (!hotelDoc.exists() || hotelDoc.data().vendorId !== userId) {
            conditionsMet = false;
            break;
          }
        } else if (key === 'assignedToUser' && value === true) {
          if (!context.hotelId) {
            conditionsMet = false;
            break;
          }
          
          // Check if user is assigned to the hotel
          const userHotelDoc = await getDoc(doc(db, 'users', userId));
          
          if (!userHotelDoc.exists() || userHotelDoc.data().hotelId !== context.hotelId) {
            conditionsMet = false;
            break;
          }
        } else if (key === 'hotelAssignedToUser' && value === true) {
          if (!context.hotelId) {
            conditionsMet = false;
            break;
          }
          
          // Check if user is assigned to the hotel
          const userHotelDoc = await getDoc(doc(db, 'users', userId));
          
          if (!userHotelDoc.exists() || userHotelDoc.data().hotelId !== context.hotelId) {
            conditionsMet = false;
            break;
          }
        } else if (key === 'createdByUser' && value === true) {
          if (!context.createdBy || context.createdBy !== userId) {
            conditionsMet = false;
            break;
          }
        } else if (key === 'role' && value) {
          if (!context.targetRole || context.targetRole !== value) {
            conditionsMet = false;
            break;
          }
        } else if (context[key] !== value) {
          conditionsMet = false;
          break;
        }
      }
      
      if (conditionsMet) {
        return true;
      }
    }
    
    // If no custom roles, permission is denied
    if (customRoleIds.length === 0) {
      return false;
    }
    
    // Check custom role permissions
    for (const roleId of customRoleIds) {
      const roleDoc = await getDoc(doc(db, 'roles', roleId));
      
      if (!roleDoc.exists()) {
        continue;
      }
      
      const role = roleDoc.data() as Role;
      
      // Get permissions for this custom role
      const customPermissionsQuery = query(
        collection(db, 'permissions'),
        where('role', '==', role.id),
        where('resource', '==', resource),
        where('action', '==', action)
      );
      
      const customPermissionsSnapshot = await getDocs(customPermissionsQuery);
      
      // Check custom role permissions
      for (const permissionDoc of customPermissionsSnapshot.docs) {
        const permission = permissionDoc.data() as Permission;
        
        // If no conditions, permission is granted
        if (!permission.conditions) {
          return true;
        }
        
        // Check conditions (same logic as above)
        let conditionsMet = true;
        
        for (const [key, value] of Object.entries(permission.conditions)) {
          if (key === 'ownedByUser' && value === true) {
            if (!context.ownerId || context.ownerId !== userId) {
              conditionsMet = false;
              break;
            }
          } else if (key === 'hotelOwnedByUser' && value === true) {
            if (!context.hotelId) {
              conditionsMet = false;
              break;
            }
            
            // Get hotel data
            const hotelDoc = await getDoc(doc(db, 'hotels', context.hotelId));
            
            if (!hotelDoc.exists() || hotelDoc.data().vendorId !== userId) {
              conditionsMet = false;
              break;
            }
          } else if (key === 'assignedToUser' && value === true) {
            if (!context.hotelId) {
              conditionsMet = false;
              break;
            }
            
            // Check if user is assigned to the hotel
            const userHotelDoc = await getDoc(doc(db, 'users', userId));
            
            if (!userHotelDoc.exists() || userHotelDoc.data().hotelId !== context.hotelId) {
              conditionsMet = false;
              break;
            }
          } else if (key === 'hotelAssignedToUser' && value === true) {
            if (!context.hotelId) {
              conditionsMet = false;
              break;
            }
            
            // Check if user is assigned to the hotel
            const userHotelDoc = await getDoc(doc(db, 'users', userId));
            
            if (!userHotelDoc.exists() || userHotelDoc.data().hotelId !== context.hotelId) {
              conditionsMet = false;
              break;
            }
          } else if (key === 'createdByUser' && value === true) {
            if (!context.createdBy || context.createdBy !== userId) {
              conditionsMet = false;
              break;
            }
          } else if (key === 'role' && value) {
            if (!context.targetRole || context.targetRole !== value) {
              conditionsMet = false;
              break;
            }
          } else if (context[key] !== value) {
            conditionsMet = false;
            break;
          }
        }
        
        if (conditionsMet) {
          return true;
        }
      }
    }
    
    return false;
  } catch (error) {
    console.error('Error checking permission:', error);
    return false;
  }
};
