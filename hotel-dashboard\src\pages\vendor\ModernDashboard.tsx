import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Divider,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  Avatar,
  Button,
  useTheme,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  TextField,
  Badge,
  Tooltip,
  List,
  ListItem,
  ListItemAvatar,
  InputAdornment
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  MoreVert as MoreVertIcon,
  Person as PersonIcon,
  ShoppingCart as ShoppingCartIcon,
  AttachMoney as MoneyIcon,
  AccountBalance as BalanceIcon,
  Refresh as RefreshIcon,
  Hotel as HotelIcon,
  MeetingRoom as RoomIcon,
  Notifications as NotificationsIcon,
  FilterList as FilterListIcon,
  Print as PrintIcon,
  GetApp as DownloadIcon,
  CalendarToday as CalendarIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import {
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianGrid,
  Tooltip as Recharts<PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { auth, db } from '../../firebase/config';
import { doc, getDoc } from 'firebase/firestore';
import {
  getDashboardData,
  RevenueDataPoint,
  Transaction as DashboardTransaction
} from '../../services/dashboardService';
import BookingCalendar from '../../components/dashboard/BookingCalendar';
import QuickActions from '../../components/dashboard/QuickActions';
import SystemStatus from '../../components/dashboard/SystemStatus';
import PerformanceMetrics from '../../components/dashboard/PerformanceMetrics';

// Define interfaces for our data
interface StatCard {
  title: string;
  value: number;
  percentChange: number;
  icon: React.ReactNode;
  linkText: string;
  linkPath: string;
  color: string;
}

// Use the Transaction interface from dashboardService
type Transaction = DashboardTransaction;

// Interface for room type distribution
interface RoomTypeData {
  name: string;
  value: number;
  color: string;
}

// Interface for notification
interface Notification {
  id: string;
  title: string;
  message: string;
  time: string;
  read: boolean;
  type: 'booking' | 'check_in' | 'check_out' | 'payment' | 'system';
}

// Interface for booking event
interface BookingEvent {
  id: string;
  title: string;
  date: Date;
  status: 'confirmed' | 'pending' | 'cancelled' | 'checked_in' | 'checked_out';
  guestName: string;
  roomType: string;
}

const ModernDashboard: React.FC = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for dashboard data
  const [userCount, setUserCount] = useState(0);
  const [bookingCount, setBookingCount] = useState(0);
  const [totalEarnings, setTotalEarnings] = useState(0);
  const [accountBalance, setAccountBalance] = useState(0);
  const [revenueData, setRevenueData] = useState<RevenueDataPoint[]>([]);
  const [salesPercentage, setSalesPercentage] = useState(0);
  const [totalSalesToday, setTotalSalesToday] = useState(0);
  const [transactions, setTransactions] = useState<Transaction[]>([]);

  // State for room type distribution
  const [roomTypeData, setRoomTypeData] = useState<RoomTypeData[]>([
    { name: 'Standard', value: 35, color: '#8884d8' },
    { name: 'Deluxe', value: 25, color: '#82ca9d' },
    { name: 'Suite', value: 20, color: '#ffc658' },
    { name: 'Executive', value: 15, color: '#ff8042' },
    { name: 'Presidential', value: 5, color: '#0088FE' }
  ]);

  // State for notifications
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      title: 'New Booking',
      message: 'John Smith booked a Deluxe Room',
      time: '10 minutes ago',
      read: false,
      type: 'booking'
    },
    {
      id: '2',
      title: 'Check-in',
      message: 'Jane Doe checked in to Room 101',
      time: '1 hour ago',
      read: false,
      type: 'check_in'
    },
    {
      id: '3',
      title: 'Payment Received',
      message: 'Payment of ₹350 received for booking #1142157',
      time: '3 hours ago',
      read: true,
      type: 'payment'
    }
  ]);

  // State for date range filter
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');

  // State for menus
  const [chartMenuAnchor, setChartMenuAnchor] = useState<null | HTMLElement>(null);
  const [notificationMenuAnchor, setNotificationMenuAnchor] = useState<null | HTMLElement>(null);

  // State for user data
  const [userName, setUserName] = useState<string>('');
  const [userRole, setUserRole] = useState<string>('');

  // State for booking events
  const [bookingEvents, setBookingEvents] = useState<BookingEvent[]>([]);

  // Fetch user data
  const fetchUserData = async () => {
    try {
      if (!auth.currentUser) return;

      const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
      if (userDoc.exists()) {
        const userData = userDoc.data();
        setUserName(userData.name || userData.displayName || '');
        setUserRole(userData.role || '');
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };

  // Fetch dashboard data on component mount
  useEffect(() => {
    fetchDashboardData();
    fetchUserData();
  }, []);

  // Function to fetch all dashboard data
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      if (!auth.currentUser) {
        setError('You must be logged in to view the dashboard');
        setLoading(false);
        return;
      }

      // Fetch all dashboard data in one call
      const dashboardData = await getDashboardData(auth.currentUser.uid);

      // Update state with fetched data
      setUserCount(dashboardData.stats.userCount);
      setBookingCount(dashboardData.stats.bookingCount);
      setTotalEarnings(dashboardData.stats.totalEarnings);
      setAccountBalance(dashboardData.stats.accountBalance);
      setSalesPercentage(dashboardData.stats.salesPercentage);
      setTotalSalesToday(dashboardData.stats.totalSalesToday);
      setRevenueData(dashboardData.revenueData);
      setTransactions(dashboardData.transactions);

      setLoading(false);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data. Please try again later.');
      setLoading(false);
    }
  };

  // Handle refresh button click
  const handleRefresh = () => {
    fetchDashboardData();
  };

  // Handle chart menu open
  const handleChartMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setChartMenuAnchor(event.currentTarget);
  };

  // Handle chart menu close
  const handleChartMenuClose = () => {
    setChartMenuAnchor(null);
  };

  // Handle notification menu open
  const handleNotificationMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationMenuAnchor(event.currentTarget);
  };

  // Handle notification menu close
  const handleNotificationMenuClose = () => {
    setNotificationMenuAnchor(null);
  };

  // Handle date range change
  const handleDateRangeChange = () => {
    // In a real implementation, you would filter the revenue data based on the date range
    handleChartMenuClose();
  };

  // Handle notification read
  const handleNotificationRead = (id: string) => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'booking':
        return <ShoppingCartIcon color="primary" />;
      case 'check_in':
        return <HotelIcon color="success" />;
      case 'check_out':
        return <RoomIcon color="warning" />;
      case 'payment':
        return <MoneyIcon color="info" />;
      default:
        return <NotificationsIcon color="action" />;
    }
  };

  // Count unread notifications
  const unreadNotificationsCount = notifications.filter(notification => !notification.read).length;

  // Prepare stat cards data
  const statCards: StatCard[] = [
    {
      title: 'USERS',
      value: userCount,
      percentChange: 20,
      icon: <PersonIcon />,
      linkText: 'View all users',
      linkPath: '/vendor/users',
      color: '#FF9F9F'
    },
    {
      title: 'ORDERS',
      value: bookingCount,
      percentChange: 20,
      icon: <ShoppingCartIcon />,
      linkText: 'View all orders',
      linkPath: '/vendor/bookings',
      color: '#FFE8B9'
    },
    {
      title: 'EARNINGS',
      value: totalEarnings,
      percentChange: 20,
      icon: <MoneyIcon />,
      linkText: 'View net earnings',
      linkPath: '/vendor/earnings',
      color: '#D8F5D8'
    },
    {
      title: 'BALANCE',
      value: accountBalance,
      percentChange: 20,
      icon: <BalanceIcon />,
      linkText: 'See details',
      linkPath: '/vendor/balance',
      color: '#E8D8F5'
    }
  ];

  // Get status chip color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'success';
      case 'pending':
        return 'warning';
      case 'rejected':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="h4">
            Dashboard
          </Typography>
          <Box>
            <Tooltip title="Notifications">
              <IconButton onClick={handleNotificationMenuOpen}>
                <Badge badgeContent={unreadNotificationsCount} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>
            <Tooltip title="Refresh Data">
              <IconButton onClick={handleRefresh} disabled={loading}>
                {loading ? <CircularProgress size={24} /> : <RefreshIcon />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 'normal' }}>
          Welcome back, {userName || 'Vendor'}! Here's what's happening with your hotels today.
        </Typography>
      </Box>

      {/* Notifications Menu */}
      <Menu
        anchorEl={notificationMenuAnchor}
        open={Boolean(notificationMenuAnchor)}
        onClose={handleNotificationMenuClose}
        PaperProps={{
          sx: { width: 320, maxHeight: 400 }
        }}
      >
        <Box sx={{ p: 2, borderBottom: '1px solid #eee' }}>
          <Typography variant="h6">Notifications</Typography>
        </Box>
        <List sx={{ p: 0 }}>
          {notifications.length === 0 ? (
            <ListItem>
              <ListItemText primary="No notifications" />
            </ListItem>
          ) : (
            notifications.map(notification => (
              <ListItem
                key={notification.id}
                sx={{
                  borderBottom: '1px solid #f5f5f5',
                  backgroundColor: notification.read ? 'transparent' : 'rgba(0, 0, 0, 0.04)'
                }}
                onClick={() => handleNotificationRead(notification.id)}
                button
              >
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: 'background.paper' }}>
                    {getNotificationIcon(notification.type)}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={notification.title}
                  secondary={
                    <>
                      <Typography variant="body2" component="span">
                        {notification.message}
                      </Typography>
                      <Typography variant="caption" component="div" color="text.secondary">
                        {notification.time}
                      </Typography>
                    </>
                  }
                />
              </ListItem>
            ))
          )}
        </List>
        <Box sx={{ p: 1, textAlign: 'center', borderTop: '1px solid #eee' }}>
          <Button size="small" color="primary">
            View All Notifications
          </Button>
        </Box>
      </Menu>

      {loading && !error ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box sx={{ p: 2 }}>
          <Typography color="error">{error}</Typography>
        </Box>
      ) : (
        <>
          {/* Stat Cards */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            {statCards.map((card, index) => (
              <Grid item xs={6} sm={6} md={3} key={index}>
                <Paper sx={{ p: { xs: 1, sm: 2 }, position: 'relative', overflow: 'hidden' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2" color="text.secondary">
                      {card.title}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="caption" color={card.percentChange >= 0 ? 'success.main' : 'error.main'} sx={{ mr: 0.5 }}>
                        {card.percentChange}%
                      </Typography>
                      {card.percentChange >= 0 ? <TrendingUpIcon fontSize="small" color="success" /> : <TrendingDownIcon fontSize="small" color="error" />}
                    </Box>
                  </Box>
                  <Typography variant="h4" sx={{ mb: 1 }}>
                    {card.title === 'EARNINGS' || card.title === 'BALANCE' ? `₹ ${card.value}` : card.value}
                  </Typography>
                  <Button size="small" sx={{ textTransform: 'none', p: 0 }}>
                    {card.linkText}
                  </Button>
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 10,
                      right: 10,
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: card.color,
                      opacity: 0.8
                    }}
                  >
                    {card.icon}
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>

          {/* Charts and Data */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            {/* Total Revenue with Circular Progress */}
            <Grid item xs={12} sm={6} md={4}>
              <Paper sx={{ p: 2, height: '100%' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">Total Revenue</Typography>
                  <IconButton size="small">
                    <MoreVertIcon fontSize="small" />
                  </IconButton>
                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', py: 2 }}>
                  <Box sx={{ position: 'relative', display: 'inline-flex', mb: 2 }}>
                    <CircularProgress
                      variant="determinate"
                      value={salesPercentage}
                      size={120}
                      thickness={5}
                      sx={{ color: theme.palette.primary.main }}
                    />
                    <Box
                      sx={{
                        top: 0,
                        left: 0,
                        bottom: 0,
                        right: 0,
                        position: 'absolute',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant="h5" component="div" color="text.secondary">
                        {`${Math.round(salesPercentage)}%`}
                      </Typography>
                    </Box>
                  </Box>
                  <Typography variant="subtitle1" gutterBottom>
                    Total sales made today
                  </Typography>
                  <Typography variant="h4" sx={{ mb: 1 }}>
                    ₹{totalSalesToday}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" align="center">
                    Previous transactions processing. Last payments may not be included.
                  </Typography>
                </Box>
                <Divider sx={{ my: 2 }} />
                <Grid container spacing={2}>
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary" align="center">
                      Target
                    </Typography>
                    <Typography variant="body2" color="error" align="center">
                      <TrendingDownIcon fontSize="small" /> ₹12.4k
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary" align="center">
                      Last Week
                    </Typography>
                    <Typography variant="body2" color="success.main" align="center">
                      <TrendingUpIcon fontSize="small" /> ₹12.4k
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary" align="center">
                      Last Month
                    </Typography>
                    <Typography variant="body2" color="success.main" align="center">
                      <TrendingUpIcon fontSize="small" /> ₹12.4k
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>
            </Grid>

            {/* Room Type Distribution */}
            <Grid item xs={12} sm={6} md={3}>
              <Paper sx={{ p: 2, height: '100%' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">Room Distribution</Typography>
                  <IconButton size="small">
                    <MoreVertIcon fontSize="small" />
                  </IconButton>
                </Box>
                <Box sx={{ height: 300, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <ResponsiveContainer width="100%" height="80%">
                    <PieChart>
                      <Pie
                        data={roomTypeData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {roomTypeData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                  <Typography variant="caption" color="text.secondary" align="center">
                    Distribution of bookings by room type
                  </Typography>
                </Box>
              </Paper>
            </Grid>

            {/* Revenue Chart */}
            <Grid item xs={12} md={5}>
              <Paper sx={{ p: 2, height: '100%' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">Revenue Trend</Typography>
                  <Box>
                    <Tooltip title="Filter">
                      <IconButton size="small" onClick={handleChartMenuOpen}>
                        <FilterListIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Download">
                      <IconButton size="small">
                        <DownloadIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>

                  {/* Chart Menu */}
                  <Menu
                    anchorEl={chartMenuAnchor}
                    open={Boolean(chartMenuAnchor)}
                    onClose={handleChartMenuClose}
                  >
                    <MenuItem>
                      <ListItemIcon>
                        <CalendarIcon fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary="Last 7 days" />
                    </MenuItem>
                    <MenuItem>
                      <ListItemIcon>
                        <CalendarIcon fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary="Last 30 days" />
                    </MenuItem>
                    <MenuItem>
                      <ListItemIcon>
                        <CalendarIcon fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary="Last 90 days" />
                    </MenuItem>
                    <Divider />
                    <Box sx={{ p: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Custom Range
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <TextField
                            label="Start Date"
                            type="date"
                            size="small"
                            fullWidth
                            value={startDate}
                            onChange={(e) => setStartDate(e.target.value)}
                            InputLabelProps={{ shrink: true }}
                          />
                        </Grid>
                        <Grid item xs={6}>
                          <TextField
                            label="End Date"
                            type="date"
                            size="small"
                            fullWidth
                            value={endDate}
                            onChange={(e) => setEndDate(e.target.value)}
                            InputLabelProps={{ shrink: true }}
                          />
                        </Grid>
                      </Grid>
                      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                        <Button
                          size="small"
                          variant="contained"
                          onClick={handleDateRangeChange}
                          disabled={!startDate || !endDate}
                        >
                          Apply
                        </Button>
                      </Box>
                    </Box>
                  </Menu>
                </Box>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={revenueData}
                      margin={{
                        top: 10,
                        right: 30,
                        left: 0,
                        bottom: 0,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <RechartsTooltip formatter={(value) => [`₹${value}`, 'Revenue']} />
                      <Area type="monotone" dataKey="value" stroke="#8884d8" fill="#8884d8" fillOpacity={0.3} />
                    </AreaChart>
                  </ResponsiveContainer>
                </Box>
              </Paper>
            </Grid>
          </Grid>

          {/* Quick Actions */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12}>
              <QuickActions />
            </Grid>
          </Grid>

          {/* Booking Calendar and System Status */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={8}>
              <BookingCalendar bookings={bookingEvents} />
            </Grid>
            <Grid item xs={12} md={4}>
              <SystemStatus />
            </Grid>
          </Grid>

          {/* Performance Metrics */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12}>
              <PerformanceMetrics />
            </Grid>
          </Grid>

          {/* Recent Transactions */}
          <Paper sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Latest Transactions
              </Typography>
              <Box>
                <Tooltip title="Print">
                  <IconButton size="small">
                    <PrintIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Export">
                  <IconButton size="small">
                    <DownloadIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Refresh">
                  <IconButton size="small" onClick={handleRefresh} disabled={loading}>
                    <RefreshIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>

            <Box sx={{ mb: 2 }}>
              <TextField
                placeholder="Search transactions..."
                size="small"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" />
                    </InputAdornment>
                  ),
                }}
                sx={{ width: 300 }}
              />
            </Box>

            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Tracking ID</TableCell>
                    <TableCell>Product</TableCell>
                    <TableCell>Customer</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Payment Method</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {transactions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} align="center">
                        No transactions found
                      </TableCell>
                    </TableRow>
                  ) : (
                    transactions.map((transaction) => (
                      <TableRow key={transaction.id} hover>
                        <TableCell>{transaction.trackingId}</TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Avatar sx={{ mr: 1, bgcolor: '#f5f5f5' }}>
                              {transaction.productName.charAt(0)}
                            </Avatar>
                            {transaction.productName}
                          </Box>
                        </TableCell>
                        <TableCell>{transaction.customerName}</TableCell>
                        <TableCell>{transaction.date}</TableCell>
                        <TableCell>₹{transaction.amount}</TableCell>
                        <TableCell>{transaction.paymentMethod}</TableCell>
                        <TableCell>
                          <Chip
                            label={transaction.status}
                            color={getStatusColor(transaction.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Tooltip title="View Details">
                            <IconButton size="small">
                              <VisibilityIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button size="small" color="primary">
                View All Transactions
              </Button>
            </Box>
          </Paper>
        </>
      )}
    </Box>
  );
};

export default ModernDashboard;
