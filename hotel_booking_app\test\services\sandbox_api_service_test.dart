import 'package:flutter_test/flutter_test.dart';
import 'package:hotel_booking_app/data/services/sandbox_api_service.dart';
import 'package:hotel_booking_app/utils/network_checker.dart';

void main() {
  late SandboxApiService sandboxApiService;

  setUp(() {
    // Set up network checker overrides for testing
    NetworkChecker.hasInternetConnectionOverride = () async => true;
    NetworkChecker.isSandboxApiReachableOverride = () async => true;
    NetworkChecker.isApiReachableOverride = (_) async => true;

    // Create the service
    sandboxApiService = SandboxApiService();
  });

  group('SandboxApiService', () {
    test('should be initialized correctly', () {
      // Assert
      expect(sandboxApiService, isNotNull);
    });

    test('generateOtp should return a response', () async {
      // Act
      final result = await sandboxApiService.generateOtp('123456789012');

      // Assert
      expect(result, isA<Map<String, dynamic>>());
      expect(result.containsKey('success'), isTrue);
    });

    test('verifyOtp should return a response', () async {
      // Act
      final result =
          await sandboxApiService.verifyOtp('test-reference', '123456');

      // Assert
      expect(result, isA<Map<String, dynamic>>());
      expect(result.containsKey('success'), isTrue);
    });
  });
}
