import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Switch,
  FormControlLabel,
  Snackbar
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Hotel as HotelIcon,
  BedOutlined as BedIcon,
  People as PeopleIcon,
  SquareFoot as SquareFootIcon,
  Wifi as WifiIcon,
  AcUnit as AcUnitIcon,
  Tv as TvIcon,
  LocalParking as ParkingIcon,
  Bathtub as BathtubIcon,
  SmokeFree as NoSmokingIcon,
  Pets as PetsIcon,
  Refresh as RefreshIcon,
  Check as CheckIcon,
  Block as BlockIcon
} from '@mui/icons-material';
import { auth } from '../../firebase/config';
import { getHotelsForVendor, HotelData } from '../../firebase/hotelService';
import {
  getRoomsForHotel,
  getRoomTypes,
  createRoom,
  updateRoom,
  deleteRoom,
  getRoomAvailability,
  updateRoomAvailability,
  updateRoomAvailabilityRange,
  createRoomType,
  updateRoomType,
  deleteRoomType,
  RoomData,
  RoomType,
  RoomAvailability
} from '../../services/roomService';

// Define TabPanel component for tab content
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`room-tabpanel-${index}`}
      aria-labelledby={`room-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

// Room form data interface
interface RoomFormData {
  name: string;
  type: string;
  description: string;
  basePrice: number;
  capacity: number;
  beds: {
    single: number;
    double: number;
    queen: number;
    king: number;
  };
  size: number;
  amenities: string[];
  status: 'active' | 'maintenance' | 'inactive';
  floor: number;
  roomNumber: string;
  isAccessible: boolean;
  maxAdults: number;
  maxChildren: number;
  smokingAllowed: boolean;
  petsAllowed: boolean;
}

const Rooms: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hotels, setHotels] = useState<HotelData[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');

  // Room state
  const [rooms, setRooms] = useState<RoomData[]>([]);
  const [roomTypes, setRoomTypes] = useState<RoomType[]>([]);
  const [openRoomDialog, setOpenRoomDialog] = useState(false);
  const [editingRoom, setEditingRoom] = useState<RoomData | null>(null);
  const [viewingRoom, setViewingRoom] = useState<RoomData | null>(null);

  // Room type state
  const [openRoomTypeDialog, setOpenRoomTypeDialog] = useState(false);
  const [editingRoomType, setEditingRoomType] = useState<RoomType | null>(null);
  const [roomTypeFormData, setRoomTypeFormData] = useState<{
    name: string;
    description: string;
    basePrice: number;
    capacity: number;
    amenities: string[];
  }>({
    name: '',
    description: '',
    basePrice: 100,
    capacity: 2,
    amenities: []
  });

  // Availability calendar state
  const [selectedRoomId, setSelectedRoomId] = useState<string>('');
  const [availabilityStartDate, setAvailabilityStartDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  const [availabilityEndDate, setAvailabilityEndDate] = useState<string>(
    new Date(new Date().setDate(new Date().getDate() + 30)).toISOString().split('T')[0]
  );
  const [availabilityStatus, setAvailabilityStatus] = useState<'available' | 'unavailable'>('available');
  const [priceOverride, setPriceOverride] = useState<string>('');
  const [roomAvailability, setRoomAvailability] = useState<RoomAvailability[]>([]);
  const [calendarLoading, setCalendarLoading] = useState(false);

  // Snackbar state
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'success'
  });

  const [roomFormData, setRoomFormData] = useState<RoomFormData>({
    name: '',
    type: '',
    description: '',
    basePrice: 0,
    capacity: 2,
    beds: {
      single: 0,
      double: 1,
      queen: 0,
      king: 0
    },
    size: 0,
    amenities: [],
    status: 'active',
    floor: 1,
    roomNumber: '',
    isAccessible: false,
    maxAdults: 2,
    maxChildren: 1,
    smokingAllowed: false,
    petsAllowed: false
  });

  // Room images state
  const [roomImages, setRoomImages] = useState<File[]>([]);
  const [previewImages, setPreviewImages] = useState<string[]>([]);
  const [deleteImages, setDeleteImages] = useState<string[]>([]);

  // Load data on component mount
  useEffect(() => {
    fetchHotels();
  }, []);

  // Load rooms when selected hotel changes
  useEffect(() => {
    if (selectedHotel) {
      fetchRooms();
      fetchRoomTypes();
    }
  }, [selectedHotel]);

  // Fetch hotels
  const fetchHotels = async () => {
    try {
      setLoading(true);
      if (!auth.currentUser) return;

      const hotelsList = await getHotelsForVendor(auth.currentUser.uid);
      setHotels(hotelsList);

      if (hotelsList.length > 0) {
        setSelectedHotel(hotelsList[0].id || '');
      }

      setLoading(false);
    } catch (err) {
      console.error('Error fetching hotels:', err);
      setError('Failed to load hotels');
      setLoading(false);
    }
  };

  // Fetch rooms
  const fetchRooms = async () => {
    try {
      setLoading(true);
      if (!selectedHotel) return;

      const roomsList = await getRoomsForHotel(selectedHotel);
      setRooms(roomsList);

      setLoading(false);
    } catch (err) {
      console.error('Error fetching rooms:', err);
      setError('Failed to load rooms');
      setLoading(false);
    }
  };

  // Fetch room types
  const fetchRoomTypes = async () => {
    try {
      if (!selectedHotel) return;

      const roomTypesList = await getRoomTypes(selectedHotel);
      setRoomTypes(roomTypesList);
    } catch (err) {
      console.error('Error fetching room types:', err);
      setError('Failed to load room types');
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle hotel change
  const handleHotelChange = (event: any) => {
    setSelectedHotel(event.target.value);
  };

  // Open room dialog
  const handleOpenRoomDialog = (room?: RoomData) => {
    if (room) {
      // Edit mode
      setEditingRoom(room);
      setRoomFormData({
        name: room.name,
        type: room.type,
        description: room.description || '',
        basePrice: room.basePrice,
        capacity: room.capacity,
        beds: room.beds || {
          single: 0,
          double: 1,
          queen: 0,
          king: 0
        },
        size: room.size || 0,
        amenities: room.amenities || [],
        status: room.status,
        floor: room.floor || 1,
        roomNumber: room.roomNumber || '',
        isAccessible: room.isAccessible || false,
        maxAdults: room.maxAdults || 2,
        maxChildren: room.maxChildren || 1,
        smokingAllowed: room.smokingAllowed || false,
        petsAllowed: room.petsAllowed || false
      });

      // Reset image states
      setRoomImages([]);
      setPreviewImages([]);
      setDeleteImages([]);
    } else {
      // Create mode
      setEditingRoom(null);
      setRoomFormData({
        name: '',
        type: roomTypes.length > 0 ? roomTypes[0].id || '' : '',
        description: '',
        basePrice: roomTypes.length > 0 ? roomTypes[0].basePrice : 100,
        capacity: 2,
        beds: {
          single: 0,
          double: 1,
          queen: 0,
          king: 0
        },
        size: 0,
        amenities: [],
        status: 'active',
        floor: 1,
        roomNumber: '',
        isAccessible: false,
        maxAdults: 2,
        maxChildren: 1,
        smokingAllowed: false,
        petsAllowed: false
      });

      // Reset image states
      setRoomImages([]);
      setPreviewImages([]);
      setDeleteImages([]);
    }

    setOpenRoomDialog(true);
  };

  // Close room dialog
  const handleCloseRoomDialog = () => {
    setOpenRoomDialog(false);
  };

  // Open room details view
  const handleViewRoom = (room: RoomData) => {
    setViewingRoom(room);
  };

  // Close room details view
  const handleCloseRoomView = () => {
    setViewingRoom(null);
  };

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRoomFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle number input change
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRoomFormData(prev => ({
      ...prev,
      [name]: Number(value)
    }));
  };

  // Handle select change
  const handleSelectChange = (e: any) => {
    const name = e.target.name;
    const value = e.target.value;
    setRoomFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle beds change
  const handleBedsChange = (bedType: 'single' | 'double' | 'queen' | 'king', value: number) => {
    setRoomFormData(prev => ({
      ...prev,
      beds: {
        ...prev.beds,
        [bedType]: value
      }
    }));
  };

  // Handle amenity toggle
  const handleAmenityToggle = (amenity: string) => {
    setRoomFormData(prev => {
      const amenities = [...prev.amenities];
      if (amenities.includes(amenity)) {
        return {
          ...prev,
          amenities: amenities.filter(a => a !== amenity)
        };
      } else {
        return {
          ...prev,
          amenities: [...amenities, amenity]
        };
      }
    });
  };

  // Handle boolean toggle
  const handleBooleanToggle = (name: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setRoomFormData(prev => ({
      ...prev,
      [name]: e.target.checked
    }));
  };

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setRoomImages(prev => [...prev, ...files]);

      // Create preview URLs
      const newPreviews = files.map(file => URL.createObjectURL(file));
      setPreviewImages(prev => [...prev, ...newPreviews]);
    }
  };

  // Handle image delete from preview
  const handleRemovePreviewImage = (index: number) => {
    setRoomImages(prev => {
      const newImages = [...prev];
      newImages.splice(index, 1);
      return newImages;
    });

    setPreviewImages(prev => {
      const newPreviews = [...prev];
      URL.revokeObjectURL(newPreviews[index]);
      newPreviews.splice(index, 1);
      return newPreviews;
    });
  };

  // Handle existing image delete
  const handleRemoveExistingImage = (url: string) => {
    setDeleteImages(prev => [...prev, url]);
  };

  // Get room type name
  const getRoomTypeName = (typeId: string) => {
    const roomType = roomTypes.find(rt => rt.id === typeId);
    return roomType ? roomType.name : 'Unknown Type';
  };

  // Get total beds count
  const getTotalBeds = (beds: { single: number; double: number; queen: number; king: number }) => {
    return beds.single + beds.double + beds.queen + beds.king;
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Handle save room
  const handleSaveRoom = async () => {
    try {
      setLoading(true);

      // Validate form
      if (!roomFormData.name || !roomFormData.type || roomFormData.basePrice <= 0) {
        setError('Please fill in all required fields');
        setLoading(false);
        return;
      }

      // Check if at least one bed is selected
      const totalBeds = getTotalBeds(roomFormData.beds);
      if (totalBeds === 0) {
        setError('Please add at least one bed');
        setLoading(false);
        return;
      }

      // Create room data object
      const roomData: Partial<RoomData> = {
        hotelId: selectedHotel,
        name: roomFormData.name,
        type: roomFormData.type,
        description: roomFormData.description,
        basePrice: roomFormData.basePrice,
        capacity: roomFormData.capacity,
        beds: roomFormData.beds,
        size: roomFormData.size > 0 ? roomFormData.size : undefined,
        amenities: roomFormData.amenities,
        status: roomFormData.status,
        floor: roomFormData.floor,
        roomNumber: roomFormData.roomNumber,
        isAccessible: roomFormData.isAccessible,
        maxAdults: roomFormData.maxAdults,
        maxChildren: roomFormData.maxChildren,
        smokingAllowed: roomFormData.smokingAllowed,
        petsAllowed: roomFormData.petsAllowed
      };

      if (editingRoom) {
        // Update existing room
        await updateRoom(
          editingRoom.id!,
          roomData,
          roomImages.length > 0 ? roomImages : undefined,
          deleteImages.length > 0 ? deleteImages : undefined
        );
      } else {
        // Create new room
        await createRoom(roomData as RoomData, roomImages.length > 0 ? roomImages : undefined);
      }

      // Close dialog and refresh rooms
      handleCloseRoomDialog();
      fetchRooms();

      // Show success message
      setError(null);
    } catch (err: any) {
      console.error('Error saving room:', err);
      setError(err.message || 'Failed to save room');
    } finally {
      setLoading(false);
    }
  };

  // Handle delete room
  const handleDeleteRoom = async (roomId: string) => {
    if (window.confirm('Are you sure you want to delete this room? This action cannot be undone.')) {
      try {
        setLoading(true);
        await deleteRoom(roomId);
        fetchRooms();
      } catch (err: any) {
        console.error('Error deleting room:', err);
        setError(err.message || 'Failed to delete room');
      } finally {
        setLoading(false);
      }
    }
  };

  // Room Type Management Functions

  // Open room type dialog
  const handleOpenRoomTypeDialog = (roomType?: RoomType) => {
    if (roomType) {
      // Edit mode
      setEditingRoomType(roomType);
      setRoomTypeFormData({
        name: roomType.name,
        description: roomType.description || '',
        basePrice: roomType.basePrice,
        capacity: roomType.capacity,
        amenities: roomType.amenities || []
      });
    } else {
      // Create mode
      setEditingRoomType(null);
      setRoomTypeFormData({
        name: '',
        description: '',
        basePrice: 100,
        capacity: 2,
        amenities: []
      });
    }

    setOpenRoomTypeDialog(true);
  };

  // Close room type dialog
  const handleCloseRoomTypeDialog = () => {
    setOpenRoomTypeDialog(false);
  };

  // Handle room type form input change
  const handleRoomTypeInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRoomTypeFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle room type number input change
  const handleRoomTypeNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRoomTypeFormData(prev => ({
      ...prev,
      [name]: Number(value)
    }));
  };

  // Handle room type amenity toggle
  const handleRoomTypeAmenityToggle = (amenity: string) => {
    setRoomTypeFormData(prev => {
      const amenities = [...prev.amenities];
      if (amenities.includes(amenity)) {
        return {
          ...prev,
          amenities: amenities.filter(a => a !== amenity)
        };
      } else {
        return {
          ...prev,
          amenities: [...amenities, amenity]
        };
      }
    });
  };

  // Handle save room type
  const handleSaveRoomType = async () => {
    try {
      setLoading(true);

      // Validate form
      if (!roomTypeFormData.name || roomTypeFormData.basePrice <= 0 || roomTypeFormData.capacity <= 0) {
        setError('Please fill in all required fields with valid values');
        setLoading(false);
        return;
      }

      // Create room type data object
      const roomTypeData: RoomType = {
        hotelId: selectedHotel,
        name: roomTypeFormData.name,
        description: roomTypeFormData.description,
        basePrice: roomTypeFormData.basePrice,
        capacity: roomTypeFormData.capacity,
        amenities: roomTypeFormData.amenities
      };

      if (editingRoomType) {
        // Update existing room type
        await updateRoomType(editingRoomType.id!, roomTypeData);
      } else {
        // Create new room type
        await createRoomType(roomTypeData);
      }

      // Close dialog and refresh room types
      handleCloseRoomTypeDialog();
      fetchRoomTypes();

      // Show success message
      setError(null);
    } catch (err: any) {
      console.error('Error saving room type:', err);
      setError(err.message || 'Failed to save room type');
    } finally {
      setLoading(false);
    }
  };

  // Handle delete room type
  const handleDeleteRoomType = async (roomTypeId: string) => {
    // First check if any rooms are using this room type
    const roomsUsingType = rooms.filter(room => room.type === roomTypeId);

    if (roomsUsingType.length > 0) {
      setError(`Cannot delete this room type because it is used by ${roomsUsingType.length} room(s). Please change the type of those rooms first.`);
      return;
    }

    if (window.confirm('Are you sure you want to delete this room type? This action cannot be undone.')) {
      try {
        setLoading(true);
        await deleteRoomType(roomTypeId);
        fetchRoomTypes();
      } catch (err: any) {
        console.error('Error deleting room type:', err);
        setError(err.message || 'Failed to delete room type');
      } finally {
        setLoading(false);
      }
    }
  };

  // Availability Calendar Functions

  // Handle room selection for availability
  const handleRoomChange = (event: any) => {
    const roomId = event.target.value;
    setSelectedRoomId(roomId);

    if (roomId) {
      fetchRoomAvailability(roomId);
    } else {
      setRoomAvailability([]);
    }
  };

  // Handle start date change
  const handleStartDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newStartDate = event.target.value;
    setAvailabilityStartDate(newStartDate);

    // If end date is before start date, update end date
    if (new Date(newStartDate) > new Date(availabilityEndDate)) {
      setAvailabilityEndDate(newStartDate);
    }

    // Refresh availability data if a room is selected
    if (selectedRoomId) {
      fetchRoomAvailability(selectedRoomId);
    }
  };

  // Handle end date change
  const handleEndDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newEndDate = event.target.value;
    setAvailabilityEndDate(newEndDate);

    // If start date is after end date, update start date
    if (new Date(newEndDate) < new Date(availabilityStartDate)) {
      setAvailabilityStartDate(newEndDate);
    }

    // Refresh availability data if a room is selected
    if (selectedRoomId) {
      fetchRoomAvailability(selectedRoomId);
    }
  };

  // Handle availability status change
  const handleAvailabilityChange = (event: any) => {
    setAvailabilityStatus(event.target.value as 'available' | 'unavailable');
  };

  // Handle price override change
  const handlePriceOverrideChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setPriceOverride(event.target.value);
  };

  // Fetch room availability
  const fetchRoomAvailability = async (roomId: string) => {
    try {
      setCalendarLoading(true);

      const startDate = new Date(availabilityStartDate);
      const endDate = new Date(availabilityEndDate);

      const availability = await getRoomAvailability(roomId, startDate, endDate);
      setRoomAvailability(availability);
    } catch (err: any) {
      console.error('Error fetching room availability:', err);
      setError(err.message || 'Failed to fetch room availability');
    } finally {
      setCalendarLoading(false);
    }
  };

  // Apply bulk action to selected date range
  const handleApplyBulkAction = async () => {
    try {
      if (!selectedRoomId) {
        setError('Please select a room first');
        return;
      }

      setCalendarLoading(true);

      const startDate = new Date(availabilityStartDate);
      const endDate = new Date(availabilityEndDate);

      // Calculate price override
      const priceValue = priceOverride ? parseFloat(priceOverride) : undefined;

      // Update availability for the date range
      await updateRoomAvailabilityRange(
        selectedRoomId,
        startDate,
        endDate,
        availabilityStatus === 'available',
        priceValue,
        undefined,
        availabilityStatus === 'unavailable' ? 'Manually blocked' : undefined
      );

      // Refresh availability data
      fetchRoomAvailability(selectedRoomId);

      setSnackbar({
        open: true,
        message: 'Availability updated successfully',
        severity: 'success'
      });
    } catch (err: any) {
      console.error('Error updating availability:', err);
      setError(err.message || 'Failed to update availability');
    } finally {
      setCalendarLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };

  // Generate an array of dates between start and end dates
  const generateDateRange = (startDateStr: string, endDateStr: string): string[] => {
    const dates: string[] = [];
    const currentDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);

    // Add one day to include the end date
    endDate.setDate(endDate.getDate() + 1);

    while (currentDate < endDate) {
      dates.push(currentDate.toISOString().split('T')[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
  };

  // Toggle availability for a specific date
  const handleToggleDateAvailability = async (dateStr: string) => {
    try {
      if (!selectedRoomId) {
        setError('Please select a room first');
        return;
      }

      setCalendarLoading(true);

      const date = new Date(dateStr);

      // Find availability record for this date
      const availabilityRecord = roomAvailability.find(
        a => a.date.toDate().toDateString() === date.toDateString()
      );

      // Toggle availability
      const newAvailability = availabilityRecord ? !availabilityRecord.isAvailable : false;

      // Update availability for the date
      await updateRoomAvailability(
        selectedRoomId,
        date,
        newAvailability,
        undefined,
        undefined,
        newAvailability ? undefined : 'Manually blocked'
      );

      // Refresh availability data
      fetchRoomAvailability(selectedRoomId);

      setSnackbar({
        open: true,
        message: `Date ${formatDate(dateStr)} is now ${newAvailability ? 'available' : 'unavailable'}`,
        severity: 'success'
      });
    } catch (err: any) {
      console.error('Error toggling availability:', err);
      setError(err.message || 'Failed to update availability');
    } finally {
      setCalendarLoading(false);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Room Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          disabled={!selectedHotel}
          onClick={() => handleOpenRoomDialog()}
        >
          Add Room
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Hotel selector */}
      {hotels.length > 0 && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <FormControl fullWidth>
            <InputLabel id="hotel-select-label">Select Hotel</InputLabel>
            <Select
              labelId="hotel-select-label"
              value={selectedHotel}
              label="Select Hotel"
              onChange={handleHotelChange}
            >
              {hotels.map((hotel) => (
                <MenuItem key={hotel.id} value={hotel.id}>
                  {hotel.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Paper>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : hotels.length === 0 ? (
        <Alert severity="info">
          You need to create at least one hotel before you can manage rooms.
        </Alert>
      ) : (
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab label="Rooms" />
            <Tab label="Room Types" />
            <Tab label="Availability" />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Box>
                <Typography variant="h6" gutterBottom>
                  Room List
                </Typography>
                <Typography variant="body1">
                  Manage your hotel rooms here.
                </Typography>
              </Box>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => handleOpenRoomDialog()}
              >
                Add Room
              </Button>
            </Box>

            {rooms.length === 0 ? (
              <Alert severity="info">
                No rooms found. Click the "Add Room" button to create your first room.
              </Alert>
            ) : (
              <Grid container spacing={3}>
                {rooms.map((room) => (
                  <Grid item xs={12} sm={6} md={4} key={room.id}>
                    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                      <CardMedia
                        component="img"
                        height="140"
                        image={room.images && room.images.length > 0
                          ? room.images[0]
                          : 'https://via.placeholder.com/300x140?text=No+Image'}
                        alt={room.name}
                      />
                      <CardContent sx={{ flexGrow: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <Typography variant="h6" component="div">
                            {room.name}
                          </Typography>
                          <Chip
                            label={room.status}
                            size="small"
                            color={
                              room.status === 'active' ? 'success' :
                              room.status === 'maintenance' ? 'warning' : 'default'
                            }
                          />
                        </Box>
                        <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                          {getRoomTypeName(room.type)}
                        </Typography>
                        <Typography variant="h6" color="primary" gutterBottom>
                          {formatCurrency(room.basePrice)}/night
                        </Typography>

                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                          <Chip
                            icon={<PeopleIcon />}
                            label={`${room.capacity} guests`}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            icon={<BedIcon />}
                            label={`${getTotalBeds(room.beds)} beds`}
                            size="small"
                            variant="outlined"
                          />
                          {room.size && (
                            <Chip
                              icon={<SquareFootIcon />}
                              label={`${room.size} m²`}
                              size="small"
                              variant="outlined"
                            />
                          )}
                        </Box>

                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {room.amenities && room.amenities.slice(0, 3).map((amenity, index) => (
                            <Chip key={index} label={amenity} size="small" />
                          ))}
                          {room.amenities && room.amenities.length > 3 && (
                            <Chip label={`+${room.amenities.length - 3} more`} size="small" />
                          )}
                        </Box>
                      </CardContent>
                      <CardActions>
                        <Button
                          size="small"
                          startIcon={<VisibilityIcon />}
                          onClick={() => handleViewRoom(room)}
                        >
                          View
                        </Button>
                        <Button
                          size="small"
                          startIcon={<EditIcon />}
                          onClick={() => handleOpenRoomDialog(room)}
                        >
                          Edit
                        </Button>
                        <Box sx={{ flexGrow: 1 }} />
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => room.id && handleDeleteRoom(room.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Box>
                <Typography variant="h6" gutterBottom>
                  Room Types
                </Typography>
                <Typography variant="body1">
                  Manage room types and categories.
                </Typography>
              </Box>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => handleOpenRoomTypeDialog()}
              >
                Add Room Type
              </Button>
            </Box>

            {roomTypes.length === 0 ? (
              <Alert severity="info">
                No room types found. Click the "Add Room Type" button to create your first room type.
              </Alert>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell>Base Price</TableCell>
                      <TableCell>Capacity</TableCell>
                      <TableCell>Amenities</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {roomTypes.map((type) => (
                      <TableRow key={type.id}>
                        <TableCell>{type.name}</TableCell>
                        <TableCell>{type.description || '-'}</TableCell>
                        <TableCell>{formatCurrency(type.basePrice)}</TableCell>
                        <TableCell>{type.capacity}</TableCell>
                        <TableCell>
                          {type.amenities && type.amenities.length > 0 ? (
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                              {type.amenities.slice(0, 2).map((amenity, index) => (
                                <Chip key={index} label={amenity} size="small" />
                              ))}
                              {type.amenities.length > 2 && (
                                <Chip label={`+${type.amenities.length - 2} more`} size="small" />
                              )}
                            </Box>
                          ) : (
                            '-'
                          )}
                        </TableCell>
                        <TableCell align="right">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleOpenRoomTypeDialog(type)}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => type.id && handleDeleteRoomType(type.id)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Box>
                <Typography variant="h6" gutterBottom>
                  Availability Calendar
                </Typography>
                <Typography variant="body1">
                  Manage room availability and pricing by date.
                </Typography>
              </Box>
            </Box>

            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Paper sx={{ p: 2, height: '100%' }}>
                  <Typography variant="h6" gutterBottom>
                    Select Room
                  </Typography>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Room</InputLabel>
                    <Select
                      value={selectedRoomId}
                      label="Room"
                      onChange={handleRoomChange}
                    >
                      {rooms.map((room) => (
                        <MenuItem key={room.id} value={room.id}>
                          {room.name} ({getRoomTypeName(room.type)})
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                    Date Range
                  </Typography>
                  <TextField
                    fullWidth
                    label="Start Date"
                    type="date"
                    InputLabelProps={{ shrink: true }}
                    sx={{ mb: 2 }}
                    value={availabilityStartDate}
                    onChange={handleStartDateChange}
                  />
                  <TextField
                    fullWidth
                    label="End Date"
                    type="date"
                    InputLabelProps={{ shrink: true }}
                    sx={{ mb: 2 }}
                    value={availabilityEndDate}
                    onChange={handleEndDateChange}
                  />

                  <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                    Bulk Actions
                  </Typography>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Availability</InputLabel>
                    <Select
                      value={availabilityStatus}
                      label="Availability"
                      onChange={handleAvailabilityChange}
                    >
                      <MenuItem value="available">Available</MenuItem>
                      <MenuItem value="unavailable">Unavailable</MenuItem>
                    </Select>
                  </FormControl>

                  <TextField
                    fullWidth
                    label="Price Override"
                    type="number"
                    InputProps={{ startAdornment: '$' }}
                    sx={{ mb: 2 }}
                    value={priceOverride}
                    onChange={handlePriceOverrideChange}
                    helperText="Leave empty to use default pricing"
                  />

                  <Button
                    variant="contained"
                    fullWidth
                    onClick={handleApplyBulkAction}
                    disabled={!selectedRoomId}
                  >
                    Apply to Selected Dates
                  </Button>
                </Paper>
              </Grid>

              <Grid item xs={12} md={8}>
                <Paper sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    Availability Calendar
                  </Typography>
                  {!selectedRoomId ? (
                    <Alert severity="info" sx={{ mb: 3 }}>
                      Select a room to view and manage its availability.
                    </Alert>
                  ) : calendarLoading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
                      <CircularProgress />
                    </Box>
                  ) : (
                    <>
                      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="subtitle1">
                          Showing availability from {formatDate(availabilityStartDate)} to {formatDate(availabilityEndDate)}
                        </Typography>
                        <Button
                          size="small"
                          onClick={() => fetchRoomAvailability(selectedRoomId)}
                          startIcon={<RefreshIcon />}
                        >
                          Refresh
                        </Button>
                      </Box>

                      <TableContainer component={Paper} variant="outlined">
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Date</TableCell>
                              <TableCell>Status</TableCell>
                              <TableCell>Price</TableCell>
                              <TableCell>Notes</TableCell>
                              <TableCell align="right">Actions</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {generateDateRange(availabilityStartDate, availabilityEndDate).map((date) => {
                              // Find availability record for this date
                              const availabilityRecord = roomAvailability.find(
                                a => a.date.toDate().toDateString() === new Date(date).toDateString()
                              );

                              // Get room data to show default price
                              const room = rooms.find(r => r.id === selectedRoomId);

                              return (
                                <TableRow key={date}>
                                  <TableCell>{formatDate(date)}</TableCell>
                                  <TableCell>
                                    <Chip
                                      label={availabilityRecord ? (availabilityRecord.isAvailable ? 'Available' : 'Unavailable') : 'Available'}
                                      color={availabilityRecord ? (availabilityRecord.isAvailable ? 'success' : 'error') : 'success'}
                                      size="small"
                                    />
                                  </TableCell>
                                  <TableCell>
                                    {availabilityRecord && availabilityRecord.price ? (
                                      <Typography variant="body2" fontWeight="bold">
                                        {formatCurrency(availabilityRecord.price)}
                                      </Typography>
                                    ) : room ? (
                                      formatCurrency(room.basePrice)
                                    ) : (
                                      '-'
                                    )}
                                  </TableCell>
                                  <TableCell>
                                    {availabilityRecord && availabilityRecord.blockedReason ? (
                                      availabilityRecord.blockedReason
                                    ) : (
                                      '-'
                                    )}
                                  </TableCell>
                                  <TableCell align="right">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleToggleDateAvailability(date)}
                                    >
                                      {availabilityRecord && !availabilityRecord.isAvailable ? (
                                        <CheckIcon fontSize="small" color="success" />
                                      ) : (
                                        <BlockIcon fontSize="small" color="error" />
                                      )}
                                    </IconButton>
                                  </TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </>
                  )}
                </Paper>
              </Grid>
            </Grid>
          </TabPanel>
        </Paper>
      )}

      {/* Room Details Dialog */}
      <Dialog
        open={viewingRoom !== null}
        onClose={handleCloseRoomView}
        maxWidth="md"
        fullWidth
      >
        {viewingRoom && (
          <>
            <DialogTitle>
              {viewingRoom.name} - Room Details
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={3}>
                {/* Room Images */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 1, overflowX: 'auto', pb: 1 }}>
                    {viewingRoom.images && viewingRoom.images.length > 0 ? (
                      viewingRoom.images.map((image, index) => (
                        <Box
                          key={index}
                          component="img"
                          src={image}
                          alt={`Room image ${index + 1}`}
                          sx={{
                            height: 200,
                            width: 300,
                            objectFit: 'cover',
                            borderRadius: 1
                          }}
                        />
                      ))
                    ) : (
                      <Box
                        component="img"
                        src="https://via.placeholder.com/300x200?text=No+Images"
                        alt="No images"
                        sx={{
                          height: 200,
                          width: 300,
                          objectFit: 'cover',
                          borderRadius: 1
                        }}
                      />
                    )}
                  </Box>
                </Grid>

                {/* Room Info */}
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Room Information
                  </Typography>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableBody>
                        <TableRow>
                          <TableCell component="th" scope="row">Type</TableCell>
                          <TableCell>{getRoomTypeName(viewingRoom.type)}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Price</TableCell>
                          <TableCell>{formatCurrency(viewingRoom.basePrice)}/night</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Status</TableCell>
                          <TableCell>
                            <Chip
                              label={viewingRoom.status}
                              size="small"
                              color={
                                viewingRoom.status === 'active' ? 'success' :
                                viewingRoom.status === 'maintenance' ? 'warning' : 'default'
                              }
                            />
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Room Number</TableCell>
                          <TableCell>{viewingRoom.roomNumber || '-'}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Floor</TableCell>
                          <TableCell>{viewingRoom.floor || '-'}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Size</TableCell>
                          <TableCell>{viewingRoom.size ? `${viewingRoom.size} m²` : '-'}</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>

                {/* Capacity Info */}
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Capacity & Beds
                  </Typography>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableBody>
                        <TableRow>
                          <TableCell component="th" scope="row">Max Guests</TableCell>
                          <TableCell>{viewingRoom.capacity}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Max Adults</TableCell>
                          <TableCell>{viewingRoom.maxAdults || viewingRoom.capacity}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Max Children</TableCell>
                          <TableCell>{viewingRoom.maxChildren || 0}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Single Beds</TableCell>
                          <TableCell>{viewingRoom.beds?.single || 0}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Double Beds</TableCell>
                          <TableCell>{viewingRoom.beds?.double || 0}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Queen Beds</TableCell>
                          <TableCell>{viewingRoom.beds?.queen || 0}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">King Beds</TableCell>
                          <TableCell>{viewingRoom.beds?.king || 0}</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>

                {/* Amenities */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Amenities & Features
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Grid container spacing={1}>
                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {viewingRoom.amenities && viewingRoom.amenities.length > 0 ? (
                            viewingRoom.amenities.map((amenity, index) => (
                              <Chip key={index} label={amenity} />
                            ))
                          ) : (
                            <Typography variant="body2" color="text.secondary">
                              No amenities specified
                            </Typography>
                          )}
                        </Box>
                      </Grid>
                      <Grid item xs={12} sx={{ mt: 2 }}>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <NoSmokingIcon color={viewingRoom.smokingAllowed ? 'disabled' : 'success'} sx={{ mr: 1 }} />
                            <Typography variant="body2">
                              {viewingRoom.smokingAllowed ? 'Smoking Allowed' : 'Non-Smoking'}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <PetsIcon color={viewingRoom.petsAllowed ? 'success' : 'disabled'} sx={{ mr: 1 }} />
                            <Typography variant="body2">
                              {viewingRoom.petsAllowed ? 'Pets Allowed' : 'No Pets'}
                            </Typography>
                          </Box>
                          {viewingRoom.isAccessible && (
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Typography variant="body2">
                                Wheelchair Accessible
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>

                {/* Description */}
                {viewingRoom.description && (
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      Description
                    </Typography>
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Typography variant="body1">
                        {viewingRoom.description}
                      </Typography>
                    </Paper>
                  </Grid>
                )}
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseRoomView}>Close</Button>
              <Button
                variant="contained"
                color="primary"
                onClick={() => {
                  handleCloseRoomView();
                  handleOpenRoomDialog(viewingRoom);
                }}
              >
                Edit Room
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Room Form Dialog */}
      <Dialog
        open={openRoomDialog}
        onClose={handleCloseRoomDialog}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          {editingRoom ? 'Edit Room' : 'Add New Room'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 0 }}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                label="Room Name"
                name="name"
                value={roomFormData.name}
                onChange={handleInputChange}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Room Type</InputLabel>
                <Select
                  name="type"
                  value={roomFormData.type}
                  label="Room Type"
                  onChange={handleSelectChange}
                >
                  {roomTypes.map((type) => (
                    <MenuItem key={type.id} value={type.id}>
                      {type.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                label="Base Price per Night"
                name="basePrice"
                type="number"
                value={roomFormData.basePrice}
                onChange={handleNumberChange}
                InputProps={{
                  startAdornment: '$',
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Status</InputLabel>
                <Select
                  name="status"
                  value={roomFormData.status}
                  label="Status"
                  onChange={handleSelectChange}
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="maintenance">Maintenance</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Description"
                name="description"
                value={roomFormData.description}
                onChange={handleInputChange}
              />
            </Grid>

            {/* Room Details */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Room Details
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Room Number"
                name="roomNumber"
                value={roomFormData.roomNumber}
                onChange={handleInputChange}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Floor"
                name="floor"
                type="number"
                value={roomFormData.floor}
                onChange={handleNumberChange}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Size (m²)"
                name="size"
                type="number"
                value={roomFormData.size}
                onChange={handleNumberChange}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <FormControlLabel
                control={
                  <Switch
                    checked={roomFormData.isAccessible}
                    onChange={handleBooleanToggle('isAccessible')}
                    color="primary"
                  />
                }
                label="Wheelchair Accessible"
              />
            </Grid>

            {/* Capacity */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Capacity & Beds
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                required
                label="Max Capacity"
                name="capacity"
                type="number"
                value={roomFormData.capacity}
                onChange={handleNumberChange}
                InputProps={{ inputProps: { min: 1 } }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Max Adults"
                name="maxAdults"
                type="number"
                value={roomFormData.maxAdults}
                onChange={handleNumberChange}
                InputProps={{ inputProps: { min: 1 } }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Max Children"
                name="maxChildren"
                type="number"
                value={roomFormData.maxChildren}
                onChange={handleNumberChange}
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={roomFormData.smokingAllowed}
                      onChange={handleBooleanToggle('smokingAllowed')}
                      color="primary"
                    />
                  }
                  label="Smoking Allowed"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={roomFormData.petsAllowed}
                      onChange={handleBooleanToggle('petsAllowed')}
                      color="primary"
                    />
                  }
                  label="Pets Allowed"
                />
              </Box>
            </Grid>

            {/* Beds */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Beds Configuration
              </Typography>
            </Grid>

            <Grid item xs={6} sm={3}>
              <TextField
                fullWidth
                label="Single Beds"
                type="number"
                value={roomFormData.beds.single}
                onChange={(e) => handleBedsChange('single', parseInt(e.target.value))}
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>

            <Grid item xs={6} sm={3}>
              <TextField
                fullWidth
                label="Double Beds"
                type="number"
                value={roomFormData.beds.double}
                onChange={(e) => handleBedsChange('double', parseInt(e.target.value))}
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>

            <Grid item xs={6} sm={3}>
              <TextField
                fullWidth
                label="Queen Beds"
                type="number"
                value={roomFormData.beds.queen}
                onChange={(e) => handleBedsChange('queen', parseInt(e.target.value))}
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>

            <Grid item xs={6} sm={3}>
              <TextField
                fullWidth
                label="King Beds"
                type="number"
                value={roomFormData.beds.king}
                onChange={(e) => handleBedsChange('king', parseInt(e.target.value))}
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>

            {/* Amenities */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Amenities
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Select Room Amenities
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                {[
                  'WiFi', 'TV', 'Air Conditioning', 'Heating', 'Balcony',
                  'Mini Bar', 'Refrigerator', 'Coffee Machine', 'Safe',
                  'Desk', 'Bathtub', 'Shower', 'Hairdryer', 'Iron',
                  'Wardrobe', 'Telephone', 'Wake-up Service', 'Room Service',
                  'Sea View', 'Mountain View', 'Garden View', 'City View'
                ].map((amenity) => (
                  <Chip
                    key={amenity}
                    label={amenity}
                    onClick={() => handleAmenityToggle(amenity)}
                    color={roomFormData.amenities.includes(amenity) ? 'primary' : 'default'}
                    variant={roomFormData.amenities.includes(amenity) ? 'filled' : 'outlined'}
                  />
                ))}
              </Box>
            </Grid>

            {/* Images */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Room Images
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12}>
              <Button
                variant="outlined"
                component="label"
                startIcon={<AddIcon />}
              >
                Add Images
                <input
                  type="file"
                  hidden
                  accept="image/*"
                  multiple
                  onChange={handleImageUpload}
                />
              </Button>
            </Grid>

            {/* Preview of new images */}
            {previewImages.length > 0 && (
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  New Images
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {previewImages.map((preview, index) => (
                    <Box key={index} sx={{ position: 'relative' }}>
                      <img
                        src={preview}
                        alt={`Preview ${index}`}
                        style={{ width: 100, height: 100, objectFit: 'cover', borderRadius: 4 }}
                      />
                      <IconButton
                        size="small"
                        sx={{
                          position: 'absolute',
                          top: -10,
                          right: -10,
                          bgcolor: 'background.paper',
                          boxShadow: 1,
                          '&:hover': { bgcolor: 'error.light' }
                        }}
                        onClick={() => handleRemovePreviewImage(index)}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  ))}
                </Box>
              </Grid>
            )}

            {/* Existing images */}
            {editingRoom && editingRoom.images && editingRoom.images.length > 0 && (
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  Existing Images
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {editingRoom.images.map((image, index) => (
                    <Box key={index} sx={{ position: 'relative' }}>
                      <img
                        src={image}
                        alt={`Room image ${index}`}
                        style={{
                          width: 100,
                          height: 100,
                          objectFit: 'cover',
                          borderRadius: 4,
                          opacity: deleteImages.includes(image) ? 0.3 : 1
                        }}
                      />
                      <IconButton
                        size="small"
                        sx={{
                          position: 'absolute',
                          top: -10,
                          right: -10,
                          bgcolor: 'background.paper',
                          boxShadow: 1,
                          '&:hover': { bgcolor: 'error.light' }
                        }}
                        onClick={() => {
                          if (deleteImages.includes(image)) {
                            setDeleteImages(prev => prev.filter(url => url !== image));
                          } else {
                            handleRemoveExistingImage(image);
                          }
                        }}
                      >
                        {deleteImages.includes(image) ? (
                          <AddIcon fontSize="small" />
                        ) : (
                          <DeleteIcon fontSize="small" />
                        )}
                      </IconButton>
                    </Box>
                  ))}
                </Box>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseRoomDialog}>Cancel</Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSaveRoom}
          >
            {editingRoom ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Room Type Dialog */}
      <Dialog
        open={openRoomTypeDialog}
        onClose={handleCloseRoomTypeDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editingRoomType ? 'Edit Room Type' : 'Add New Room Type'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 0 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                label="Room Type Name"
                name="name"
                value={roomTypeFormData.name}
                onChange={handleRoomTypeInputChange}
                helperText="E.g., Standard, Deluxe, Suite, etc."
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                label="Base Price per Night"
                name="basePrice"
                type="number"
                value={roomTypeFormData.basePrice}
                onChange={handleRoomTypeNumberChange}
                InputProps={{
                  startAdornment: '$',
                }}
                helperText="Default price for this room type"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                label="Capacity"
                name="capacity"
                type="number"
                value={roomTypeFormData.capacity}
                onChange={handleRoomTypeNumberChange}
                helperText="Maximum number of guests"
                InputProps={{ inputProps: { min: 1 } }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Description"
                name="description"
                value={roomTypeFormData.description}
                onChange={handleRoomTypeInputChange}
                helperText="Detailed description of this room type"
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Standard Amenities for this Room Type
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                {[
                  'WiFi', 'TV', 'Air Conditioning', 'Heating', 'Balcony',
                  'Mini Bar', 'Refrigerator', 'Coffee Machine', 'Safe',
                  'Desk', 'Bathtub', 'Shower', 'Hairdryer', 'Iron',
                  'Wardrobe', 'Telephone', 'Wake-up Service', 'Room Service',
                  'Sea View', 'Mountain View', 'Garden View', 'City View'
                ].map((amenity) => (
                  <Chip
                    key={amenity}
                    label={amenity}
                    onClick={() => handleRoomTypeAmenityToggle(amenity)}
                    color={roomTypeFormData.amenities.includes(amenity) ? 'primary' : 'default'}
                    variant={roomTypeFormData.amenities.includes(amenity) ? 'filled' : 'outlined'}
                  />
                ))}
              </Box>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                These amenities will be suggested as defaults when creating rooms of this type
              </Typography>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseRoomTypeDialog}>Cancel</Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSaveRoomType}
          >
            {editingRoomType ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Rooms;
