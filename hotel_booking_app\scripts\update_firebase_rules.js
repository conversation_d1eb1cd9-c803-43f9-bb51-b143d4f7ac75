// <PERSON>ript to update Firebase security rules
// Run this script using Node.js: node update_firebase_rules.js

const { initializeApp } = require('firebase/app');
const { getFirestore } = require('firebase/firestore');
const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

// Initialize Firebase Admin SDK with your service account
// You need to download your service account key from Firebase Console
// and save it as serviceAccountKey.json in the same directory as this script
try {
  const serviceAccount = require('./serviceAccountKey.json');
  
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
  
  console.log('Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  console.log('Make sure you have a valid serviceAccountKey.json file in the same directory as this script');
  process.exit(1);
}

// Define the security rules
const securityRules = `
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Base rules
    match /{document=**} {
      allow read, write: if false; // <PERSON><PERSON>ult deny all
    }
    
    // User profiles
    match /users/{userId} {
      allow read: if request.auth != null && (request.auth.uid == userId || isAdmin());
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Aadhaar verifications
    match /aadhaar_verifications/{verificationId} {
      // Allow users to read their own verification
      allow read: if request.auth != null;
      
      // Allow users to create/update their own verification
      allow create: if request.auth != null;
      allow update: if request.auth != null;
      
      // Only admins can delete verifications
      allow delete: if isAdmin();
    }
    
    // Hotels
    match /hotels/{hotelId} {
      allow read: if true; // Public read access
      allow write: if isAdmin() || isHotelOwner(hotelId);
    }
    
    // Bookings
    match /bookings/{bookingId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null;
      allow update: if request.auth != null;
      allow delete: if isAdmin();
    }
    
    // Helper functions
    function isAdmin() {
      return request.auth != null && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isHotelOwner(hotelId) {
      return request.auth != null && 
             exists(/databases/$(database)/documents/hotels/$(hotelId)) &&
             get(/databases/$(database)/documents/hotels/$(hotelId)).data.ownerId == request.auth.uid;
    }
  }
}
`;

// Update the security rules
async function updateSecurityRules() {
  try {
    // For demonstration purposes, we'll just write the rules to a file
    // In a real scenario, you would use the Firebase Admin SDK to update the rules
    fs.writeFileSync(path.join(__dirname, 'firestore.rules'), securityRules);
    console.log('Security rules written to firestore.rules');
    console.log('To apply these rules, go to the Firebase Console > Firestore Database > Rules');
    console.log('and paste the contents of the firestore.rules file');
    
    // If you want to update the rules programmatically, you would use:
    // await admin.firestore().updateSecurityRules(securityRules);
    // console.log('Security rules updated successfully');
  } catch (error) {
    console.error('Error updating security rules:', error);
  }
}

// Run the function
updateSecurityRules();
