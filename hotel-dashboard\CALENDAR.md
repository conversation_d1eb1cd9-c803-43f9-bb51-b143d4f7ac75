# Staff Scheduling Calendar

The staff scheduling system includes a calendar view for visualizing staff shifts and time off requests. This document explains how to use and troubleshoot the calendar feature.

## Features

- **Calendar View**: Interactive calendar for visualizing shifts and time off
- **Basic Calendar Fallback**: Simplified grid view that works without FullCalendar dependencies
- **Staff Filtering**: Filter the calendar to show shifts for specific staff members
- **Shift Management**: Add, edit, and delete shifts directly from the calendar
- **Time Off Integration**: View approved time off requests in the calendar

## Installation

The calendar view requires FullCalendar dependencies to be installed. You can install them using one of the following methods:

### Method 1: Run the Fix Script

Run the provided fix script to automatically install the dependencies and update the calendar component:

```
npm run fix-calendar
```

Or run the batch file:

```
fix-calendar.bat
```

### Method 2: Manual Installation

Install the FullCalendar dependencies manually:

```
npm install @fullcalendar/core @fullcalendar/daygrid @fullcalendar/interaction @fullcalendar/react @fullcalendar/timegrid
```

## Troubleshooting

If you encounter issues with the calendar view, try the following:

1. **Check Dependencies**: Make sure the FullCalendar dependencies are installed correctly.
2. **Run Fix Script**: Run the fix script to automatically fix common issues.
3. **Use Basic Calendar**: If the FullCalendar view doesn't work, the system will automatically fall back to a basic calendar view.
4. **Check Console Errors**: Look for any errors in the browser console related to FullCalendar.

## How It Works

The staff scheduling system uses a wrapper component (`StaffCalendarWrapper`) that checks if the FullCalendar dependencies are available:

- If the dependencies are available, it renders the full-featured calendar view (`StaffCalendar`).
- If the dependencies are not available, it renders a simplified calendar view (`BasicCalendar`).

This approach ensures that the staff scheduling system works even if the FullCalendar dependencies are not installed, providing a graceful fallback.

## Calendar Views

The calendar supports three different views:

1. **Month View**: Shows a traditional month calendar with all shifts and time off.
2. **Week View**: Shows a detailed week view with time slots for each day.
3. **Day View**: Shows a detailed view of a single day with all shifts.

## Adding Shifts

To add a new shift:

1. Click on a time slot in the week or day view.
2. Select the staff member from the dropdown.
3. Choose the shift type (morning, afternoon, night, or custom).
4. If you selected custom, set the start and end times.
5. Add any notes if needed.
6. Click "Add Shift" to save.

## Editing Shifts

To edit an existing shift:

1. Click on the shift in the calendar.
2. Modify the details in the dialog.
3. Click "Save Changes" to update the shift.

## Deleting Shifts

To delete a shift:

1. Click on the shift in the calendar.
2. Click the "Delete" button in the dialog.

## Time Off Requests

Approved time off requests appear as all-day events in the calendar with a red background. This helps prevent scheduling conflicts with approved time off.
