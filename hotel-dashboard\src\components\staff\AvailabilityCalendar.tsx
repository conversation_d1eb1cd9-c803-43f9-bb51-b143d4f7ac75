import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Chip
} from '@mui/material';
import {
  NavigateBefore as PrevIcon,
  NavigateNext as NextIcon,
  Today as TodayIcon,
  Person as PersonIcon,
  ThumbUp as ThumbUpIcon,
  Check as CheckIcon,
  Block as BlockIcon
} from '@mui/icons-material';
import {
  startOfWeek,
  endOfWeek,
  addDays,
  format,
  addWeeks,
  subWeeks,
  isSameDay
} from 'date-fns';
import { 
  StaffAvailability,
  getHotelStaffAvailability,
  AVAILABILITY_TYPE_PREFERRED,
  AVAILABILITY_TYPE_AVAILABLE,
  AVAILABILITY_TYPE_UNAVAILABLE
} from '../../services/staffAvailabilityService';
import { StaffMember } from '../../services/staffManagementService';

// Day of week options
const DAYS_OF_WEEK = [
  { value: 0, label: 'Monday' },
  { value: 1, label: 'Tuesday' },
  { value: 2, label: 'Wednesday' },
  { value: 3, label: 'Thursday' },
  { value: 4, label: 'Friday' },
  { value: 5, label: 'Saturday' },
  { value: 6, label: 'Sunday' }
];

// Availability type options
const AVAILABILITY_TYPES = [
  { value: AVAILABILITY_TYPE_PREFERRED, label: 'Preferred', color: 'success', icon: <ThumbUpIcon /> },
  { value: AVAILABILITY_TYPE_AVAILABLE, label: 'Available', color: 'primary', icon: <CheckIcon /> },
  { value: AVAILABILITY_TYPE_UNAVAILABLE, label: 'Unavailable', color: 'error', icon: <BlockIcon /> }
];

interface AvailabilityCalendarProps {
  hotelId: string;
  staffList: StaffMember[];
}

const AvailabilityCalendar: React.FC<AvailabilityCalendarProps> = ({
  hotelId,
  staffList
}) => {
  // State for calendar
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(startOfWeek(new Date(), { weekStartsOn: 1 }));
  const [weekDays, setWeekDays] = useState<Date[]>([]);
  
  // State for availability
  const [availabilityList, setAvailabilityList] = useState<StaffAvailability[]>([]);
  
  // State for filters
  const [selectedStaff, setSelectedStaff] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  
  // State for loading and errors
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Generate week days when current week start changes
  useEffect(() => {
    const days = [];
    for (let i = 0; i < 7; i++) {
      days.push(addDays(currentWeekStart, i));
    }
    setWeekDays(days);
    
    // Fetch availability for the current week
    fetchAvailability();
  }, [currentWeekStart, hotelId]);
  
  // Fetch availability
  const fetchAvailability = async () => {
    try {
      setLoading(true);
      const fetchedAvailability = await getHotelStaffAvailability(hotelId);
      setAvailabilityList(fetchedAvailability);
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching availability:', err);
      setError(err.message || 'Failed to fetch availability');
      setLoading(false);
    }
  };
  
  // Handle week navigation
  const handlePrevWeek = () => {
    setCurrentWeekStart(subWeeks(currentWeekStart, 1));
  };
  
  const handleNextWeek = () => {
    setCurrentWeekStart(addWeeks(currentWeekStart, 1));
  };
  
  const handleCurrentWeek = () => {
    setCurrentWeekStart(startOfWeek(new Date(), { weekStartsOn: 1 }));
  };
  
  // Handle filter changes
  const handleStaffFilterChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedStaff(event.target.value as string);
  };
  
  const handleTypeFilterChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedType(event.target.value as string);
  };
  
  // Get availability for a specific day and staff member
  const getAvailabilityForDay = (day: Date, staffId: string): StaffAvailability[] => {
    // Get day of week (0-6, Monday-Sunday)
    const dayOfWeek = (day.getDay() + 6) % 7; // Convert from Sunday-Saturday (0-6) to Monday-Sunday (0-6)
    
    // Filter availability
    return availabilityList.filter(a => {
      // Filter by staff if selected
      if (staffId !== 'all' && a.staffId !== staffId) {
        return false;
      }
      
      // Filter by type if selected
      if (selectedType !== 'all' && a.type !== selectedType) {
        return false;
      }
      
      // Check if it's a weekly recurring availability for this day
      if (a.recurring === 'weekly' && a.dayOfWeek === dayOfWeek) {
        return true;
      }
      
      // Check if it's a one-time availability for this specific date
      if (a.recurring === 'none' && a.specificDate && isSameDay(a.specificDate.toDate(), day)) {
        return true;
      }
      
      return false;
    });
  };
  
  // Get availability type details
  const getAvailabilityTypeDetails = (type: string) => {
    const availabilityType = AVAILABILITY_TYPES.find(t => t.value === type);
    return availabilityType || AVAILABILITY_TYPES[0];
  };
  
  // Get staff member by ID
  const getStaffMember = (staffId: string): StaffMember | undefined => {
    return staffList.find(staff => staff.id === staffId);
  };
  
  // Get filtered staff list
  const getFilteredStaffList = (): StaffMember[] => {
    if (selectedStaff === 'all') {
      return staffList;
    } else {
      return staffList.filter(staff => staff.id === selectedStaff);
    }
  };
  
  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handlePrevWeek}>
            <PrevIcon />
          </IconButton>
          <Typography variant="h6" sx={{ mx: 2 }}>
            {format(currentWeekStart, 'MMM d')} - {format(addDays(currentWeekStart, 6), 'MMM d, yyyy')}
          </Typography>
          <IconButton onClick={handleNextWeek}>
            <NextIcon />
          </IconButton>
          <Button 
            variant="outlined" 
            startIcon={<TodayIcon />} 
            onClick={handleCurrentWeek}
            sx={{ ml: 2 }}
          >
            Current Week
          </Button>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <FormControl sx={{ minWidth: 200, mr: 2 }}>
            <InputLabel id="staff-filter-label">Staff Member</InputLabel>
            <Select
              labelId="staff-filter-label"
              value={selectedStaff}
              label="Staff Member"
              onChange={handleStaffFilterChange as any}
              startAdornment={
                <PersonIcon sx={{ mr: 1, color: 'action.active' }} />
              }
            >
              <MenuItem value="all">All Staff</MenuItem>
              {staffList.map((staff) => (
                <MenuItem key={staff.id} value={staff.id || ''}>
                  {staff.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel id="type-filter-label">Availability Type</InputLabel>
            <Select
              labelId="type-filter-label"
              value={selectedType}
              label="Availability Type"
              onChange={handleTypeFilterChange as any}
            >
              <MenuItem value="all">All Types</MenuItem>
              {AVAILABILITY_TYPES.map((type) => (
                <MenuItem key={type.value} value={type.value}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {React.cloneElement(type.icon, { fontSize: 'small', sx: { mr: 1 } })}
                    {type.label}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      <Paper sx={{ p: 2 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Box>
            {/* Week header */}
            <Grid container spacing={1}>
              {weekDays.map((day, index) => (
                <Grid item xs={12/7} key={index}>
                  <Box sx={{ 
                    textAlign: 'center', 
                    p: 1, 
                    bgcolor: 'primary.light', 
                    color: 'primary.contrastText',
                    borderRadius: 1
                  }}>
                    <Typography variant="subtitle1">
                      {format(day, 'EEE')}
                    </Typography>
                    <Typography variant="body2">
                      {format(day, 'MMM d')}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
            
            {/* Staff rows */}
            {getFilteredStaffList().map((staff) => (
              <Grid container spacing={1} key={staff.id} sx={{ mt: 1 }}>
                <Grid item xs={12}>
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    p: 1, 
                    bgcolor: 'grey.100',
                    borderRadius: 1
                  }}>
                    <PersonIcon sx={{ mr: 1 }} />
                    <Typography variant="subtitle2">
                      {staff.name} ({staff.role?.replace(/_/g, ' ') || 'Staff'})
                    </Typography>
                  </Box>
                </Grid>
                
                {weekDays.map((day, dayIndex) => (
                  <Grid item xs={12/7} key={dayIndex}>
                    <Paper 
                      variant="outlined" 
                      sx={{ 
                        p: 1, 
                        minHeight: 100,
                        maxHeight: 200,
                        overflow: 'auto'
                      }}
                    >
                      {getAvailabilityForDay(day, staff.id || '').length === 0 ? (
                        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', textAlign: 'center', p: 2 }}>
                          No availability set
                        </Typography>
                      ) : (
                        getAvailabilityForDay(day, staff.id || '').map((availability, index) => {
                          const typeDetails = getAvailabilityTypeDetails(availability.type);
                          return (
                            <Box 
                              key={index}
                              sx={{ 
                                p: 1, 
                                mb: 1, 
                                borderRadius: 1,
                                bgcolor: `${typeDetails.color}.light`,
                                border: '1px solid',
                                borderColor: `${typeDetails.color}.main`,
                                '&:last-child': { mb: 0 }
                              }}
                            >
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                                {React.cloneElement(typeDetails.icon, { fontSize: 'small', sx: { mr: 0.5 } })}
                                <Typography variant="caption" fontWeight="bold">
                                  {typeDetails.label}
                                </Typography>
                              </Box>
                              <Typography variant="caption" display="block">
                                {availability.startTime} - {availability.endTime}
                              </Typography>
                              {availability.notes && (
                                <Typography variant="caption" display="block" sx={{ mt: 0.5, fontStyle: 'italic' }}>
                                  {availability.notes}
                                </Typography>
                              )}
                            </Box>
                          );
                        })
                      )}
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            ))}
            
            {getFilteredStaffList().length === 0 && (
              <Box sx={{ p: 5, textAlign: 'center' }}>
                <Typography variant="h6" gutterBottom>
                  No staff members found
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {selectedStaff === 'all'
                    ? 'There are no staff members in the system.'
                    : 'The selected staff member was not found.'}
                </Typography>
              </Box>
            )}
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default AvailabilityCalendar;
