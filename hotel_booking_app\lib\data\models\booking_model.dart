import 'package:cloud_firestore/cloud_firestore.dart';

class Booking {
  final String id;
  final String userId;
  final String hotelId;
  final String hotelName;
  final String roomId;
  final String roomName;
  final DateTime checkIn;
  final DateTime checkOut;
  final int guests;
  final double totalPrice;
  final String status;
  final String paymentStatus;
  final Map<String, dynamic>? guestDetails;
  final bool isAadhaarVerified;
  final DateTime? aadhaarVerifiedAt;
  final String? aadhaarVerificationId;
  final DateTime? checkedInAt;
  final DateTime? checkedOutAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Booking({
    required this.id,
    required this.userId,
    required this.hotelId,
    required this.hotelName,
    required this.roomId,
    required this.roomName,
    required this.checkIn,
    required this.checkOut,
    required this.guests,
    required this.totalPrice,
    required this.status,
    required this.paymentStatus,
    this.guestDetails,
    this.isAadhaarVerified = false,
    this.aadhaarVerifiedAt,
    this.aadhaarVerificationId,
    this.checkedInAt,
    this.checkedOutAt,
    this.createdAt,
    this.updatedAt,
  });

  factory Booking.fromMap(Map<String, dynamic> map) {
    return Booking(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      hotelId: map['hotelId'] ?? '',
      hotelName: map['hotelName'] ?? '',
      roomId: map['roomId'] ?? '',
      roomName: map['roomName'] ?? '',
      checkIn: map['checkIn'] != null
          ? (map['checkIn'] as Timestamp).toDate()
          : DateTime.now(),
      checkOut: map['checkOut'] != null
          ? (map['checkOut'] as Timestamp).toDate()
          : DateTime.now().add(const Duration(days: 1)),
      guests: map['guests'] ?? 1,
      totalPrice: (map['totalPrice'] ?? 0).toDouble(),
      status: map['status'] ?? 'pending',
      paymentStatus: map['paymentStatus'] ?? 'pending',
      guestDetails: map['guestDetails'],
      isAadhaarVerified: map['isAadhaarVerified'] ?? false,
      aadhaarVerifiedAt: map['aadhaarVerifiedAt'] != null
          ? (map['aadhaarVerifiedAt'] as Timestamp).toDate()
          : null,
      aadhaarVerificationId: map['aadhaarVerificationId'],
      checkedInAt: map['checkedInAt'] != null
          ? (map['checkedInAt'] as Timestamp).toDate()
          : null,
      checkedOutAt: map['checkedOutAt'] != null
          ? (map['checkedOutAt'] as Timestamp).toDate()
          : null,
      createdAt: map['createdAt'] != null
          ? (map['createdAt'] as Timestamp).toDate()
          : null,
      updatedAt: map['updatedAt'] != null
          ? (map['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'hotelId': hotelId,
      'hotelName': hotelName,
      'roomId': roomId,
      'roomName': roomName,
      'checkIn': Timestamp.fromDate(checkIn),
      'checkOut': Timestamp.fromDate(checkOut),
      'guests': guests,
      'totalPrice': totalPrice,
      'status': status,
      'paymentStatus': paymentStatus,
      'guestDetails': guestDetails,
      'isAadhaarVerified': isAadhaarVerified,
      'aadhaarVerifiedAt': aadhaarVerifiedAt != null
          ? Timestamp.fromDate(aadhaarVerifiedAt!)
          : null,
      'aadhaarVerificationId': aadhaarVerificationId,
      'checkedInAt':
          checkedInAt != null ? Timestamp.fromDate(checkedInAt!) : null,
      'checkedOutAt':
          checkedOutAt != null ? Timestamp.fromDate(checkedOutAt!) : null,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  Booking copyWith({
    String? id,
    String? userId,
    String? hotelId,
    String? hotelName,
    String? roomId,
    String? roomName,
    DateTime? checkIn,
    DateTime? checkOut,
    int? guests,
    double? totalPrice,
    String? status,
    String? paymentStatus,
    Map<String, dynamic>? guestDetails,
    bool? isAadhaarVerified,
    DateTime? aadhaarVerifiedAt,
    String? aadhaarVerificationId,
    DateTime? checkedInAt,
    DateTime? checkedOutAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Booking(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      hotelId: hotelId ?? this.hotelId,
      hotelName: hotelName ?? this.hotelName,
      roomId: roomId ?? this.roomId,
      roomName: roomName ?? this.roomName,
      checkIn: checkIn ?? this.checkIn,
      checkOut: checkOut ?? this.checkOut,
      guests: guests ?? this.guests,
      totalPrice: totalPrice ?? this.totalPrice,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      guestDetails: guestDetails ?? this.guestDetails,
      isAadhaarVerified: isAadhaarVerified ?? this.isAadhaarVerified,
      aadhaarVerifiedAt: aadhaarVerifiedAt ?? this.aadhaarVerifiedAt,
      aadhaarVerificationId:
          aadhaarVerificationId ?? this.aadhaarVerificationId,
      checkedInAt: checkedInAt ?? this.checkedInAt,
      checkedOutAt: checkedOutAt ?? this.checkedOutAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
