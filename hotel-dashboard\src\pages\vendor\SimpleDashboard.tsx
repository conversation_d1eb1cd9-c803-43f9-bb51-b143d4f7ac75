import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Divider,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  Avatar,
  Button,
  useTheme,
  Card,
  CardContent,
  CardHeader
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Refresh as RefreshIcon,
  Person as PersonIcon,
  ShoppingCart as ShoppingCartIcon,
  AttachMoney as MoneyIcon,
  AccountBalance as BalanceIcon
} from '@mui/icons-material';
import { auth } from '../../firebase/config';

// Define interfaces for our data
interface StatCard {
  title: string;
  value: number;
  percentChange: number;
  icon: React.ReactNode;
  color: string;
}

interface Transaction {
  id: string;
  trackingId: string;
  productName: string;
  customerName: string;
  date: string;
  amount: number;
  paymentMethod: string;
  status: string;
}

const SimpleDashboard: React.FC = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for dashboard data
  const [userCount, setUserCount] = useState(0);
  const [bookingCount, setBookingCount] = useState(0);
  const [totalEarnings, setTotalEarnings] = useState(0);
  const [accountBalance, setAccountBalance] = useState(0);
  const [transactions, setTransactions] = useState<Transaction[]>([]);

  // Handle refresh button click
  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  // Get status chip color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'success';
      case 'pending':
        return 'warning';
      case 'rejected':
        return 'error';
      default:
        return 'default';
    }
  };

  // Prepare stat cards data
  const statCards: StatCard[] = [
    {
      title: 'USERS',
      value: userCount,
      percentChange: 20,
      icon: <PersonIcon />,
      color: '#FF9F9F'
    },
    {
      title: 'ORDERS',
      value: bookingCount,
      percentChange: 15,
      icon: <ShoppingCartIcon />,
      color: '#FFE8B9'
    },
    {
      title: 'EARNINGS',
      value: totalEarnings,
      percentChange: 25,
      icon: <MoneyIcon />,
      color: '#D8F5D8'
    },
    {
      title: 'BALANCE',
      value: accountBalance,
      percentChange: 18,
      icon: <BalanceIcon />,
      color: '#E8D8F5'
    }
  ];

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="h4">
            Dashboard
          </Typography>
          <Box>
            <IconButton onClick={handleRefresh} disabled={loading}>
              {loading ? <CircularProgress size={24} /> : <RefreshIcon />}
            </IconButton>
          </Box>
        </Box>

        <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 'normal' }}>
          Welcome back, Vendor! Here's what's happening with your hotels today.
        </Typography>
      </Box>

      {loading && !error ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box sx={{ p: 2 }}>
          <Typography color="error">{error}</Typography>
        </Box>
      ) : (
        <>
          {/* Stat Cards */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            {statCards.map((card, index) => (
              <Grid item xs={6} sm={6} md={3} key={index}>
                <Paper sx={{ p: { xs: 1, sm: 2 }, position: 'relative', overflow: 'hidden' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2" color="text.secondary">
                      {card.title}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="caption" color={card.percentChange >= 0 ? 'success.main' : 'error.main'} sx={{ mr: 0.5 }}>
                        {card.percentChange}%
                      </Typography>
                      {card.percentChange >= 0 ? <TrendingUpIcon fontSize="small" color="success" /> : <TrendingDownIcon fontSize="small" color="error" />}
                    </Box>
                  </Box>
                  <Typography variant="h4" sx={{ mb: 1 }}>
                    {card.title === 'EARNINGS' || card.title === 'BALANCE' ? `$ ${card.value}` : card.value}
                  </Typography>
                  <Button size="small" sx={{ textTransform: 'none', p: 0 }}>
                    View Details
                  </Button>
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 10,
                      right: 10,
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: card.color,
                      opacity: 0.8
                    }}
                  >
                    {card.icon}
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>

          {/* Recent Transactions */}
          <Paper sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Latest Transactions
              </Typography>
            </Box>

            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Tracking ID</TableCell>
                    <TableCell>Product</TableCell>
                    <TableCell>Customer</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Payment Method</TableCell>
                    <TableCell>Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id} hover>
                      <TableCell>{transaction.trackingId}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar sx={{ mr: 1, bgcolor: '#f5f5f5' }}>
                            {transaction.productName.charAt(0)}
                          </Avatar>
                          {transaction.productName}
                        </Box>
                      </TableCell>
                      <TableCell>{transaction.customerName}</TableCell>
                      <TableCell>{transaction.date}</TableCell>
                      <TableCell>${transaction.amount}</TableCell>
                      <TableCell>{transaction.paymentMethod}</TableCell>
                      <TableCell>
                        <Chip
                          label={transaction.status}
                          color={getStatusColor(transaction.status) as any}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </>
      )}
    </Box>
  );
};

export default SimpleDashboard;
