import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CurrencyService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  List<Map<String, dynamic>> _currencies = [];
  String _selectedCurrency = 'INR'; // Default currency is Indian Rupee
  bool _isLoading = false;
  String? _error;

  CurrencyService() {
    _loadSelectedCurrency();
    fetchCurrencies();
  }

  // Getters
  List<Map<String, dynamic>> get currencies => _currencies;
  String get selectedCurrency => _selectedCurrency;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Load selected currency from SharedPreferences
  Future<void> _loadSelectedCurrency() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedCurrency = prefs.getString('selectedCurrency');
      if (savedCurrency != null) {
        _selectedCurrency = savedCurrency;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading selected currency: $e');
    }
  }

  // Save selected currency to SharedPreferences
  Future<void> _saveSelectedCurrency(String currencyCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selectedCurrency', currencyCode);
    } catch (e) {
      debugPrint('Error saving selected currency: $e');
    }
  }

  // Fetch currencies from Firestore
  Future<void> fetchCurrencies() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final snapshot = await _firestore.collection('currencies').get();

      if (snapshot.docs.isEmpty) {
        // If no currencies exist in Firestore, create default ones
        await _createDefaultCurrencies();
        final newSnapshot = await _firestore.collection('currencies').get();
        _currencies = newSnapshot.docs
            .map((doc) => {
                  'id': doc.id,
                  ...doc.data(),
                })
            .toList();
      } else {
        _currencies = snapshot.docs
            .map((doc) => {
                  'id': doc.id,
                  ...doc.data(),
                })
            .toList();
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Create default currencies in Firestore if none exist
  Future<void> _createDefaultCurrencies() async {
    final batch = _firestore.batch();

    final defaultCurrencies = [
      {
        'code': 'INR',
        'name': 'Indian Rupee',
        'symbol': '₹',
        'flag': '🇮🇳',
        'isActive': true,
      },
      {
        'code': 'USD',
        'name': 'US Dollar',
        'symbol': '\$',
        'flag': '🇺🇸',
        'isActive': true,
      },
      {
        'code': 'EUR',
        'name': 'Euro',
        'symbol': '€',
        'flag': '🇪🇺',
        'isActive': true,
      },
      {
        'code': 'GBP',
        'name': 'British Pound',
        'symbol': '£',
        'flag': '🇬🇧',
        'isActive': true,
      },
      {
        'code': 'JPY',
        'name': 'Japanese Yen',
        'symbol': '¥',
        'flag': '🇯🇵',
        'isActive': true,
      },
    ];

    for (final currency in defaultCurrencies) {
      final docRef =
          _firestore.collection('currencies').doc(currency['code'] as String);
      batch.set(docRef, currency);
    }

    await batch.commit();
  }

  // Set selected currency
  Future<void> setCurrency(String currencyCode) async {
    _selectedCurrency = currencyCode;
    await _saveSelectedCurrency(currencyCode);
    notifyListeners();
  }

  // Add a new currency (admin function)
  Future<void> addCurrency(Map<String, dynamic> currency) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _firestore
          .collection('currencies')
          .doc(currency['code'] as String)
          .set(currency);
      await fetchCurrencies();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Update a currency (admin function)
  Future<void> updateCurrency(String code, Map<String, dynamic> data) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _firestore.collection('currencies').doc(code).update(data);
      await fetchCurrencies();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Toggle currency active status (admin function)
  Future<void> toggleCurrencyStatus(String code, bool isActive) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _firestore.collection('currencies').doc(code).update({
        'isActive': isActive,
      });
      await fetchCurrencies();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Get currency symbol
  String getCurrencySymbol(String code) {
    final currency = _currencies.firstWhere(
      (currency) => currency['code'] == code,
      orElse: () => {'symbol': '₹'}, // Default to INR symbol
    );
    return currency['symbol'];
  }
}
