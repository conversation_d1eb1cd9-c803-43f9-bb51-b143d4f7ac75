import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  Divider,
  CircularProgress,
  Alert,
  Chip
} from '@mui/material';
import {
  Schedule as ScheduleIcon,
  CalendarMonth as CalendarIcon,
  Person as PersonIcon,
  Work as WorkIcon,
  Check as CheckIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format, startOfWeek, addDays } from 'date-fns';
import {
  ScheduleTemplate,
  TemplateShift,
  getTemplatesForHotel,
  getTemplateById,
  getShiftsForTemplate,
  applyTemplate
} from '../../services/scheduleTemplateService';
import { StaffMember, getStaffForHotel } from '../../services/staffManagementService';
import { createStaffShift } from '../../services/staffSchedulingService';
import { checkAllConflicts, ScheduleConflict } from '../../services/scheduleConflictService';
import ConflictAlert from './ConflictAlert';

interface ApplyTemplateProps {
  hotelId: string;
  vendorId: string;
  onSuccess?: () => void;
}

const ApplyTemplate: React.FC<ApplyTemplateProps> = ({ hotelId, vendorId, onSuccess }) => {
  // State for templates
  const [templates, setTemplates] = useState<ScheduleTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ScheduleTemplate | null>(null);
  const [templateShifts, setTemplateShifts] = useState<TemplateShift[]>([]);

  // State for staff
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([]);
  const [staffAssignments, setStaffAssignments] = useState<{ [role: string]: string[] }>({});

  // State for date
  const [startDate, setStartDate] = useState<Date>(startOfWeek(new Date(), { weekStartsOn: 1 }));

  // State for conflicts
  const [conflicts, setConflicts] = useState<ScheduleConflict[]>([]);
  const [showConflictDialog, setShowConflictDialog] = useState<boolean>(false);

  // State for loading and errors
  const [loading, setLoading] = useState<boolean>(true);
  const [applying, setApplying] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Fetch templates and staff on component mount
  useEffect(() => {
    fetchTemplates();
    fetchStaff();
  }, [hotelId]);

  // Fetch templates
  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const fetchedTemplates = await getTemplatesForHotel(hotelId);
      setTemplates(fetchedTemplates);

      // Select default template if available
      const defaultTemplate = fetchedTemplates.find(t => t.isDefault);
      if (defaultTemplate) {
        setSelectedTemplate(defaultTemplate);
        fetchTemplateShifts(defaultTemplate.id!);
      }

      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching templates:', err);
      setError(err.message || 'Failed to fetch templates');
      setLoading(false);
    }
  };

  // Fetch staff
  const fetchStaff = async () => {
    try {
      setLoading(true);
      const fetchedStaff = await getStaffForHotel(hotelId, { status: 'active' });
      setStaffMembers(fetchedStaff);

      // Initialize staff assignments
      const initialAssignments: { [role: string]: string[] } = {};
      fetchedStaff.forEach(staff => {
        if (!initialAssignments[staff.role]) {
          initialAssignments[staff.role] = [];
        }
        if (staff.id) {
          initialAssignments[staff.role].push(staff.id);
        }
      });

      setStaffAssignments(initialAssignments);
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching staff:', err);
      setError(err.message || 'Failed to fetch staff');
      setLoading(false);
    }
  };

  // Fetch template shifts
  const fetchTemplateShifts = async (templateId: string) => {
    try {
      setLoading(true);
      const fetchedShifts = await getShiftsForTemplate(templateId);
      setTemplateShifts(fetchedShifts);
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching template shifts:', err);
      setError(err.message || 'Failed to fetch template shifts');
      setLoading(false);
    }
  };

  // Handle template selection
  const handleTemplateChange = async (event: React.ChangeEvent<{ value: unknown }>) => {
    const templateId = event.target.value as string;

    if (templateId) {
      const template = templates.find(t => t.id === templateId);
      if (template) {
        setSelectedTemplate(template);
        await fetchTemplateShifts(templateId);
      }
    } else {
      setSelectedTemplate(null);
      setTemplateShifts([]);
    }
  };

  // Handle date change
  const handleDateChange = (date: Date | null) => {
    if (date) {
      setStartDate(startOfWeek(date, { weekStartsOn: 1 }));
    }
  };

  // Type-safe wrapper for DatePicker onChange
  const handleDatePickerChange = (value: unknown) => {
    handleDateChange(value as Date | null);
  };

  // Handle staff assignment change
  const handleStaffAssignmentChange = (role: string, staffId: string, checked: boolean) => {
    setStaffAssignments(prev => {
      const updatedAssignments = { ...prev };

      if (!updatedAssignments[role]) {
        updatedAssignments[role] = [];
      }

      if (checked) {
        // Add staff to role
        if (!updatedAssignments[role].includes(staffId)) {
          updatedAssignments[role] = [...updatedAssignments[role], staffId];
        }
      } else {
        // Remove staff from role
        updatedAssignments[role] = updatedAssignments[role].filter(id => id !== staffId);
      }

      return updatedAssignments;
    });
  };

  // Apply template
  const handleApplyTemplate = async () => {
    try {
      if (!selectedTemplate) {
        setError('Please select a template');
        return;
      }

      setApplying(true);
      setError(null);
      setSuccess(null);

      // Get staff names for assignments
      const staffMap: { [id: string]: StaffMember } = {};
      staffMembers.forEach(staff => {
        if (staff.id) {
          staffMap[staff.id] = staff;
        }
      });

      // Apply template to create shifts
      const shifts = await applyTemplate(
        selectedTemplate.id!,
        startDate,
        staffAssignments,
        hotelId,
        vendorId
      );

      // Add staff names to shifts
      const shiftsWithNames = shifts.map(shift => ({
        ...shift,
        staffName: staffMap[shift.staffId]?.name || 'Unknown'
      }));

      // Check for conflicts
      let allConflicts: ScheduleConflict[] = [];
      for (const shift of shiftsWithNames) {
        const shiftConflicts = await checkAllConflicts(shift, hotelId);
        allConflicts = [...allConflicts, ...shiftConflicts];
      }

      if (allConflicts.length > 0) {
        setConflicts(allConflicts);
        setShowConflictDialog(true);
        setApplying(false);
        return;
      }

      // Create shifts in database
      for (const shift of shiftsWithNames) {
        await createStaffShift(shift);
      }

      setSuccess(`Successfully applied template "${selectedTemplate.name}" with ${shiftsWithNames.length} shifts.`);
      setApplying(false);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error('Error applying template:', err);
      setError(err.message || 'Failed to apply template');
      setApplying(false);
    }
  };

  // Handle proceed despite conflicts
  const handleProceedDespiteConflicts = async () => {
    try {
      setShowConflictDialog(false);
      setApplying(true);

      if (!selectedTemplate) {
        setError('Please select a template');
        setApplying(false);
        return;
      }

      // Get staff names for assignments
      const staffMap: { [id: string]: StaffMember } = {};
      staffMembers.forEach(staff => {
        if (staff.id) {
          staffMap[staff.id] = staff;
        }
      });

      // Apply template to create shifts
      const shifts = await applyTemplate(
        selectedTemplate.id!,
        startDate,
        staffAssignments,
        hotelId,
        vendorId
      );

      // Add staff names to shifts
      const shiftsWithNames = shifts.map(shift => ({
        ...shift,
        staffName: staffMap[shift.staffId]?.name || 'Unknown'
      }));

      // Create shifts in database
      for (const shift of shiftsWithNames) {
        await createStaffShift(shift);
      }

      setSuccess(`Successfully applied template "${selectedTemplate.name}" with ${shiftsWithNames.length} shifts.`);
      setApplying(false);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error('Error applying template:', err);
      setError(err.message || 'Failed to apply template');
      setApplying(false);
    }
  };

  // Handle cancel due to conflicts
  const handleCancelDueToConflicts = () => {
    setShowConflictDialog(false);
    setConflicts([]);
  };

  // Get staff for role
  const getStaffForRole = (role: string): StaffMember[] => {
    return staffMembers.filter(staff => staff.role === role);
  };

  // Get roles used in template
  const getRolesInTemplate = (): string[] => {
    const roles = new Set<string>();
    templateShifts.forEach(shift => {
      roles.add(shift.role);
    });
    return Array.from(roles);
  };

  // Get role name
  const getRoleName = (role: string): string => {
    switch (role) {
      case 'housekeeping':
        return 'Housekeeping';
      case 'food_service':
        return 'Food Service';
      case 'maintenance':
        return 'Maintenance';
      case 'front_desk':
        return 'Front Desk';
      case 'manager':
        return 'Manager';
      default:
        return role.charAt(0).toUpperCase() + role.slice(1);
    }
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Apply Schedule Template
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel id="template-label">Schedule Template</InputLabel>
              <Select
                labelId="template-label"
                value={selectedTemplate?.id || ''}
                label="Schedule Template"
                onChange={handleTemplateChange as any}
                disabled={loading || applying}
              >
                {templates.map((template) => (
                  <MenuItem key={template.id} value={template.id}>
                    {template.name}
                    {template.isDefault && ' (Default)'}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Week Starting"
                value={startDate}
                onChange={handleDatePickerChange}
                disabled={loading || applying}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    helperText="Schedule will start from Monday of this week"
                  />
                )}
              />
            </LocalizationProvider>
          </Grid>
        </Grid>

        {selectedTemplate && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              Template Details
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <ScheduleIcon sx={{ mr: 1 }} />
                  <Typography variant="body1">
                    {selectedTemplate.name}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <CalendarIcon sx={{ mr: 1 }} />
                  <Typography variant="body2">
                    Week of {format(startDate, 'MMMM d, yyyy')}
                  </Typography>
                </Box>

                <Typography variant="body2" color="text.secondary">
                  {selectedTemplate.description || 'No description provided.'}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <WorkIcon sx={{ mr: 1 }} />
                  <Typography variant="body2">
                    {templateShifts.length} shifts in template
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <PersonIcon sx={{ mr: 1 }} />
                  <Typography variant="body2">
                    {staffMembers.length} staff members available
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>
        )}
      </Paper>

      {selectedTemplate && templateShifts.length > 0 && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Staff Assignments
          </Typography>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Select staff members to assign to each role in the template.
          </Typography>

          {getRolesInTemplate().map((role) => (
            <Box key={role} sx={{ mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                {getRoleName(role)}
              </Typography>

              <List dense>
                {getStaffForRole(role).length === 0 ? (
                  <ListItem>
                    <ListItemText
                      primary="No staff members found for this role"
                      secondary="Add staff members with this role to assign them to the schedule"
                    />
                  </ListItem>
                ) : (
                  getStaffForRole(role).map((staff) => (
                    <ListItem key={staff.id}>
                      <ListItemIcon>
                        <Checkbox
                          edge="start"
                          checked={staffAssignments[role]?.includes(staff.id || '') || false}
                          onChange={(e) => handleStaffAssignmentChange(role, staff.id || '', e.target.checked)}
                          disabled={loading || applying}
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={staff.name}
                        secondary={staff.email}
                      />
                    </ListItem>
                  ))
                )}
              </List>

              <Divider sx={{ my: 2 }} />
            </Box>
          ))}

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={applying ? <CircularProgress size={20} color="inherit" /> : <CheckIcon />}
              onClick={handleApplyTemplate}
              disabled={loading || applying}
            >
              {applying ? 'Applying...' : 'Apply Template'}
            </Button>
          </Box>
        </Paper>
      )}

      {/* Conflict Dialog */}
      <Dialog
        open={showConflictDialog}
        onClose={handleCancelDueToConflicts}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Schedule Conflicts Detected</DialogTitle>
        <DialogContent>
          <Typography variant="body1" paragraph>
            The following conflicts were detected when applying the template:
          </Typography>

          <ConflictAlert conflicts={conflicts} />

          <Typography variant="body1" paragraph>
            Do you want to proceed with applying the template despite these conflicts?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDueToConflicts}>
            Cancel
          </Button>
          <Button
            onClick={handleProceedDespiteConflicts}
            color="warning"
            variant="contained"
          >
            Proceed Anyway
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ApplyTemplate;
