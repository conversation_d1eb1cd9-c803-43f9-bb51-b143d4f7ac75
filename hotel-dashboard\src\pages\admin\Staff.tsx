import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Snackbar,
  Alert,
  CircularProgress,
  InputAdornment,
  Grid,
  Chip,
  Tooltip,
  TablePagination,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
  Hotel as HotelIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Work as WorkIcon
} from '@mui/icons-material';
import { getStaff, createUser, updateUser, deleteUser, UserData, getVendors } from '../../firebase/userService';
import { ROLE_STAFF } from '../../firebase/auth';
import { auth } from '../../firebase/config';
import { getHotelsByVendor, HotelData } from '../../firebase/hotelService';

const Staff: React.FC = () => {
  // Data state
  const [staff, setStaff] = useState<UserData[]>([]);
  const [vendors, setVendors] = useState<UserData[]>([]);
  const [hotels, setHotels] = useState<{ [vendorId: string]: HotelData[] }>({});
  const [loading, setLoading] = useState(true);

  // UI state
  const [openDialog, setOpenDialog] = useState(false);
  const [editingStaff, setEditingStaff] = useState<UserData | null>(null);

  // Search and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Form state
  const [formData, setFormData] = useState({
    email: '',
    displayName: '',
    phone: '',
    address: '',
    password: '',
    vendorId: '',
    hotelId: ''
  });

  // Notifications
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  // Load staff and vendors on component mount
  useEffect(() => {
    fetchStaff();
    fetchVendors();
  }, []);

  // Fetch hotels when selected vendor changes
  useEffect(() => {
    if (formData.vendorId) {
      fetchHotelsForVendor(formData.vendorId);
    }
  }, [formData.vendorId]);

  const fetchStaff = async () => {
    try {
      setLoading(true);
      const staffList = await getStaff();
      setStaff(staffList);
    } catch (error) {
      console.error('Error fetching staff:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load staff',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchVendors = async () => {
    try {
      const vendorsList = await getVendors();
      setVendors(vendorsList);
    } catch (error) {
      console.error('Error fetching vendors:', error);
    }
  };

  const fetchHotelsForVendor = async (vendorId: string) => {
    try {
      if (hotels[vendorId]) {
        return hotels[vendorId];
      }

      const { hotels: hotelsList } = await getHotelsByVendor(vendorId);
      setHotels(prev => ({
        ...prev,
        [vendorId]: hotelsList
      }));
      return hotelsList;
    } catch (error) {
      console.error(`Error fetching hotels for vendor ${vendorId}:`, error);
      return [];
    }
  };

  const handleOpenDialog = (staffMember?: UserData) => {
    if (staffMember) {
      // Edit mode
      setEditingStaff(staffMember);
      setFormData({
        email: staffMember.email,
        displayName: staffMember.displayName,
        phone: staffMember.phone || '',
        address: staffMember.address || '',
        password: '', // Don't set password when editing
        vendorId: staffMember.vendorId || '',
        hotelId: staffMember.hotelId || ''
      });

      // Fetch hotels for the vendor if needed
      if (staffMember.vendorId && !hotels[staffMember.vendorId]) {
        fetchHotelsForVendor(staffMember.vendorId);
      }
    } else {
      // Create mode
      setEditingStaff(null);
      setFormData({
        email: '',
        displayName: '',
        phone: '',
        address: '',
        password: '',
        vendorId: vendors.length > 0 ? vendors[0].id! : '',
        hotelId: ''
      });

      // Fetch hotels for the default vendor if needed
      if (vendors.length > 0 && !hotels[vendors[0].id!]) {
        fetchHotelsForVendor(vendors[0].id!);
      }
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (e: SelectChangeEvent) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
      // Reset hotelId if vendorId changes
      ...(name === 'vendorId' ? { hotelId: '' } : {})
    }));
  };

  const handleSubmit = async () => {
    try {
      if (!auth.currentUser) {
        throw new Error('You must be logged in to perform this action');
      }

      if (editingStaff) {
        // Update existing staff
        await updateUser(editingStaff.id!, {
          displayName: formData.displayName,
          phone: formData.phone,
          address: formData.address,
          vendorId: formData.vendorId,
          hotelId: formData.hotelId
        });

        setSnackbar({
          open: true,
          message: 'Staff updated successfully',
          severity: 'success'
        });
      } else {
        // Create new staff
        if (!formData.email || !formData.displayName || !formData.vendorId) {
          throw new Error('Email, name, and vendor are required');
        }

        const userData: UserData = {
          email: formData.email,
          displayName: formData.displayName,
          role: ROLE_STAFF,
          phone: formData.phone,
          address: formData.address,
          vendorId: formData.vendorId,
          hotelId: formData.hotelId || undefined
        };

        // Get the vendor as the creator
        const vendor = vendors.find(v => v.id === formData.vendorId);
        if (!vendor) {
          throw new Error('Selected vendor not found');
        }

        await createUser(userData, formData.password, vendor);

        setSnackbar({
          open: true,
          message: 'Staff created successfully',
          severity: 'success'
        });
      }

      handleCloseDialog();
      fetchStaff(); // Refresh the list
    } catch (error: any) {
      console.error('Error saving staff:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to save staff',
        severity: 'error'
      });
    }
  };

  const handleDeleteStaff = async (staffId: string) => {
    if (window.confirm('Are you sure you want to delete this staff member?')) {
      try {
        await deleteUser(staffId);
        setSnackbar({
          open: true,
          message: 'Staff deleted successfully',
          severity: 'success'
        });
        fetchStaff(); // Refresh the list
      } catch (error) {
        console.error('Error deleting staff:', error);
        setSnackbar({
          open: true,
          message: 'Failed to delete staff',
          severity: 'error'
        });
      }
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setPage(0); // Reset to first page when searching
  };

  const handleVendorFilter = (e: SelectChangeEvent) => {
    setSelectedVendor(e.target.value);
    setPage(0); // Reset to first page when filtering
  };

  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Filter staff based on search term and selected vendor
  const filteredStaff = staff.filter(staffMember => {
    const matchesSearch =
      staffMember.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staffMember.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (staffMember.phone && staffMember.phone.includes(searchTerm)) ||
      (staffMember.address && staffMember.address.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesVendor = selectedVendor ? staffMember.vendorId === selectedVendor : true;

    return matchesSearch && matchesVendor;
  });

  // Get vendor name by ID
  const getVendorName = (vendorId: string) => {
    const vendor = vendors.find(v => v.id === vendorId);
    return vendor ? vendor.displayName : 'Unknown Vendor';
  };

  // Get hotel name by ID
  const getHotelName = (vendorId: string, hotelId: string) => {
    if (!hotelId) return 'Not Assigned';

    const hotelsList = hotels[vendorId];
    if (!hotelsList) return 'Loading...';

    const hotel = hotelsList.find(h => h.id === hotelId);
    return hotel ? hotel.name : 'Unknown Hotel';
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Staff Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Add Staff
        </Button>
      </Box>

      {/* Search and Filter Bar */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search staff by name, email, phone or address..."
              value={searchTerm}
              onChange={handleSearch}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Filter by Vendor</InputLabel>
              <Select
                value={selectedVendor}
                onChange={handleVendorFilter}
                label="Filter by Vendor"
              >
                <MenuItem value="">All Vendors</MenuItem>
                {vendors.map((vendor) => (
                  <MenuItem key={vendor.id} value={vendor.id}>
                    {vendor.displayName}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchStaff}
            >
              Refresh
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <Paper>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Phone</TableCell>
                  <TableCell>Vendor</TableCell>
                  <TableCell>Hotel</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredStaff.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      No staff found
                    </TableCell>
                  </TableRow>
                ) : (
                  // Apply pagination to filtered staff
                  filteredStaff
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((staffMember) => (
                      <TableRow key={staffMember.id}>
                        <TableCell>
                          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                              {staffMember.displayName}
                            </Typography>
                            {staffMember.id && (
                              <Typography variant="caption" color="text.secondary">
                                ID: {staffMember.id.substring(0, 8)}...
                              </Typography>
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <EmailIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            {staffMember.email}
                          </Box>
                        </TableCell>
                        <TableCell>
                          {staffMember.phone ? (
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <PhoneIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                              {staffMember.phone}
                            </Box>
                          ) : (
                            '-'
                          )}
                        </TableCell>
                        <TableCell>
                          {staffMember.vendorId ? (
                            <Chip
                              icon={<PersonIcon />}
                              label={getVendorName(staffMember.vendorId)}
                              color="primary"
                              size="small"
                            />
                          ) : (
                            '-'
                          )}
                        </TableCell>
                        <TableCell>
                          {staffMember.hotelId && staffMember.vendorId ? (
                            <Chip
                              icon={<HotelIcon />}
                              label={getHotelName(staffMember.vendorId, staffMember.hotelId)}
                              color="secondary"
                              size="small"
                            />
                          ) : (
                            <Chip
                              label="Not Assigned"
                              variant="outlined"
                              size="small"
                            />
                          )}
                        </TableCell>
                        <TableCell align="right">
                          <Tooltip title="Edit Staff">
                            <IconButton
                              color="primary"
                              size="small"
                              onClick={() => handleOpenDialog(staffMember)}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete Staff">
                            <IconButton
                              color="error"
                              size="small"
                              onClick={() => staffMember.id && handleDeleteStaff(staffMember.id)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredStaff.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>

      {/* Add/Edit Staff Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingStaff ? 'Edit Staff Member' : 'Add New Staff Member'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Email Address"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  disabled={!!editingStaff} // Disable email editing for existing staff
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Name"
                  name="displayName"
                  value={formData.displayName}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  fullWidth
                  label="Phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  fullWidth
                  label="Address"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                />
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 1 }} />
                <Typography variant="subtitle1" gutterBottom>
                  Assignment
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal" required>
                  <InputLabel>Vendor</InputLabel>
                  <Select
                    name="vendorId"
                    value={formData.vendorId}
                    onChange={handleSelectChange}
                    label="Vendor"
                  >
                    {vendors.map((vendor) => (
                      <MenuItem key={vendor.id} value={vendor.id}>
                        {vendor.displayName}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Hotel</InputLabel>
                  <Select
                    name="hotelId"
                    value={formData.hotelId}
                    onChange={handleSelectChange}
                    label="Hotel"
                    disabled={!formData.vendorId || !hotels[formData.vendorId]?.length}
                  >
                    <MenuItem value="">Not Assigned</MenuItem>
                    {formData.vendorId && hotels[formData.vendorId]?.map((hotel) => (
                      <MenuItem key={hotel.id} value={hotel.id}>
                        {hotel.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {!editingStaff && (
                <Grid item xs={12}>
                  <TextField
                    margin="normal"
                    fullWidth
                    label="Password (leave empty to send reset email)"
                    name="password"
                    type="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    helperText="If left empty, a password reset email will be sent to the user"
                  />
                </Grid>
              )}
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingStaff ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Staff;
