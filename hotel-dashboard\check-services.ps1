Write-Host "Checking for required service files..." -ForegroundColor Green

$servicesDir = Join-Path $PSScriptRoot "src\services"
$requiredServices = @(
    "staffManagementService.ts",
    "staffSchedulingService.ts"
)

$missingServices = @()

foreach ($service in $requiredServices) {
    $servicePath = Join-Path $servicesDir $service
    if (-not (Test-Path $servicePath)) {
        $missingServices += $service
    }
}

if ($missingServices.Count -eq 0) {
    Write-Host "All required service files are present." -ForegroundColor Green
} else {
    Write-Host "The following service files are missing:" -ForegroundColor Red
    foreach ($service in $missingServices) {
        Write-Host "  - $service" -ForegroundColor Red
    }
    
    Write-Host "`nWould you like to create the missing service files? (Y/N)" -ForegroundColor Yellow
    $response = Read-Host
    
    if ($response -eq "Y" -or $response -eq "y") {
        foreach ($service in $missingServices) {
            $servicePath = Join-Path $servicesDir $service
            
            if ($service -eq "staffManagementService.ts") {
                Write-Host "Creating staffManagementService.ts..." -ForegroundColor Green
                $content = @"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit as firestoreLimit,
  Timestamp,
  serverTimestamp,
  DocumentSnapshot
} from 'firebase/firestore';
import { db } from '../firebase/config';

// Collection name
const STAFF_COLLECTION = 'staff';

// Staff roles
export const STAFF_ROLE_HOUSEKEEPING = 'housekeeping';
export const STAFF_ROLE_FOOD_SERVICE = 'food_service';
export const STAFF_ROLE_MAINTENANCE = 'maintenance';
export const STAFF_ROLE_FRONT_DESK = 'front_desk';
export const STAFF_ROLE_MANAGER = 'manager';
export const STAFF_ROLE_OTHER = 'other';

// Staff status
export const STAFF_STATUS_ACTIVE = 'active';
export const STAFF_STATUS_INACTIVE = 'inactive';
export const STAFF_STATUS_ON_LEAVE = 'on_leave';

// Staff member interface
export interface StaffMember {
  id?: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  skills?: string[];
  status: string;
  hotelId: string;
  vendorId: string;
  notes?: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

/**
 * Get all staff members for a hotel
 */
export const getStaffForHotel = async (
  hotelId: string,
  options?: {
    role?: string;
    status?: string;
    limit?: number;
  }
): Promise<StaffMember[]> => {
  try {
    let staffQuery = query(
      collection(db, STAFF_COLLECTION),
      where('hotelId', '==', hotelId),
      orderBy('name', 'asc')
    );
    
    // Apply role filter if provided
    if (options?.role) {
      staffQuery = query(
        staffQuery,
        where('role', '==', options.role)
      );
    }
    
    // Apply status filter if provided
    if (options?.status) {
      staffQuery = query(
        staffQuery,
        where('status', '==', options.status)
      );
    }
    
    // Apply limit if provided
    if (options?.limit) {
      staffQuery = query(
        staffQuery,
        firestoreLimit(options.limit)
      );
    }
    
    const querySnapshot = await getDocs(staffQuery);
    const staff: StaffMember[] = [];
    
    querySnapshot.forEach((doc) => {
      staff.push({
        id: doc.id,
        ...doc.data() as Omit<StaffMember, 'id'>
      });
    });
    
    return staff;
  } catch (error) {
    console.error('Error getting staff for hotel:', error);
    throw error;
  }
};

/**
 * Get a staff member by ID
 */
export const getStaffMemberById = async (staffId: string): Promise<StaffMember | null> => {
  try {
    const docRef = doc(db, STAFF_COLLECTION, staffId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data() as Omit<StaffMember, 'id'>
      };
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error getting staff member by ID:', error);
    throw error;
  }
};

/**
 * Create a new staff member
 */
export const createStaffMember = async (
  staffMember: Omit<StaffMember, 'id' | 'createdAt' | 'updatedAt'>
): Promise<StaffMember> => {
  try {
    const now = Timestamp.now();
    const newStaffMember: Omit<StaffMember, 'id'> = {
      ...staffMember,
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await addDoc(collection(db, STAFF_COLLECTION), newStaffMember);
    
    return {
      ...newStaffMember,
      id: docRef.id
    };
  } catch (error) {
    console.error('Error creating staff member:', error);
    throw error;
  }
};

/**
 * Update a staff member
 */
export const updateStaffMember = async (
  staffId: string,
  updates: Partial<Omit<StaffMember, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<boolean> => {
  try {
    await updateDoc(doc(db, STAFF_COLLECTION, staffId), {
      ...updates,
      updatedAt: serverTimestamp()
    });
    
    return true;
  } catch (error) {
    console.error('Error updating staff member:', error);
    throw error;
  }
};

/**
 * Delete a staff member
 */
export const deleteStaffMember = async (staffId: string): Promise<boolean> => {
  try {
    await deleteDoc(doc(db, STAFF_COLLECTION, staffId));
    return true;
  } catch (error) {
    console.error('Error deleting staff member:', error);
    throw error;
  }
};

/**
 * Get staff members by role
 */
export const getStaffByRole = async (
  hotelId: string,
  role: string,
  options?: {
    status?: string;
    limit?: number;
  }
): Promise<StaffMember[]> => {
  try {
    let staffQuery = query(
      collection(db, STAFF_COLLECTION),
      where('hotelId', '==', hotelId),
      where('role', '==', role),
      orderBy('name', 'asc')
    );
    
    // Apply status filter if provided
    if (options?.status) {
      staffQuery = query(
        staffQuery,
        where('status', '==', options.status)
      );
    }
    
    // Apply limit if provided
    if (options?.limit) {
      staffQuery = query(
        staffQuery,
        firestoreLimit(options.limit)
      );
    }
    
    const querySnapshot = await getDocs(staffQuery);
    const staff: StaffMember[] = [];
    
    querySnapshot.forEach((doc) => {
      staff.push({
        id: doc.id,
        ...doc.data() as Omit<StaffMember, 'id'>
      });
    });
    
    return staff;
  } catch (error) {
    console.error('Error getting staff by role:', error);
    throw error;
  }
};

/**
 * Update staff status
 */
export const updateStaffStatus = async (
  staffId: string,
  status: string
): Promise<boolean> => {
  try {
    await updateDoc(doc(db, STAFF_COLLECTION, staffId), {
      status,
      updatedAt: serverTimestamp()
    });
    
    return true;
  } catch (error) {
    console.error('Error updating staff status:', error);
    throw error;
  }
};

export default {
  getStaffForHotel,
  getStaffMemberById,
  createStaffMember,
  updateStaffMember,
  deleteStaffMember,
  getStaffByRole,
  updateStaffStatus
};
"@
                Set-Content -Path $servicePath -Value $content
            }
            
            if ($service -eq "staffSchedulingService.ts") {
                Write-Host "Creating staffSchedulingService.ts..." -ForegroundColor Green
                # Add staffSchedulingService.ts content here
                # (Content omitted for brevity)
            }
        }
        
        Write-Host "`nMissing service files have been created." -ForegroundColor Green
    } else {
        Write-Host "`nNo files were created." -ForegroundColor Yellow
    }
}

Read-Host -Prompt "Press Enter to exit"
