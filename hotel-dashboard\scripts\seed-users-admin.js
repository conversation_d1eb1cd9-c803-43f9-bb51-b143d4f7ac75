/**
 * Seed script to create initial users in Firebase Authentication and Firestore
 * using Firebase Admin SDK
 * 
 * This script creates:
 * 1. A super admin user
 * 2. A vendor user
 * 3. A staff user for the vendor
 * 
 * Run this script with: node scripts/seed-users-admin.js
 */

const admin = require('firebase-admin');
const serviceAccount = require('./service-account-key.json');

// Initialize Firebase Admin
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const auth = admin.auth();
const db = admin.firestore();

// User roles
const ROLE_SUPER_ADMIN = 'super_admin';
const ROLE_VENDOR = 'vendor';
const ROLE_STAFF = 'staff';

// Sample users to create
const users = [
  {
    email: '<EMAIL>',
    password: 'Admin123!',
    displayName: 'Super Admin',
    role: ROLE_SUPER_ADMIN,
    phone: '+**********',
    address: '123 Admin Street, Admin City'
  },
  {
    email: '<EMAIL>',
    password: 'Vendor123!',
    displayName: 'Hotel Vendor',
    role: ROLE_VENDOR,
    phone: '+**********',
    address: '456 Vendor Avenue, Vendor City'
  },
  {
    email: '<EMAIL>',
    password: 'Staff123!',
    displayName: 'Hotel Staff',
    role: ROLE_STAFF,
    phone: '+**********',
    address: '789 Staff Road, Staff City'
  }
];

/**
 * Create a user in Firebase Authentication and Firestore
 */
async function createUser(userData, createdBy = null) {
  try {
    console.log(`Creating user: ${userData.email} (${userData.role})...`);
    
    // Check if user already exists
    try {
      const userRecord = await auth.getUserByEmail(userData.email);
      console.log(`User ${userData.email} already exists with ID: ${userRecord.uid}. Skipping...`);
      return userRecord.uid;
    } catch (error) {
      // User doesn't exist, continue with creation
      if (error.code !== 'auth/user-not-found') {
        throw error;
      }
    }
    
    // Create user in Firebase Authentication
    const userRecord = await auth.createUser({
      email: userData.email,
      password: userData.password,
      displayName: userData.displayName,
      disabled: false
    });
    
    const uid = userRecord.uid;
    
    // Add user data to Firestore
    await db.collection('users').doc(uid).set({
      email: userData.email,
      displayName: userData.displayName,
      role: userData.role,
      phone: userData.phone || '',
      address: userData.address || '',
      createdBy: createdBy,
      vendorId: userData.role === ROLE_STAFF ? userData.vendorId : (userData.role === ROLE_VENDOR ? uid : null),
      hotelId: userData.role === ROLE_STAFF ? userData.hotelId : null,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });
    
    console.log(`User ${userData.email} created successfully with ID: ${uid}`);
    
    return uid;
  } catch (error) {
    console.error(`Error creating user ${userData.email}:`, error.message);
    throw error;
  }
}

/**
 * Main function to seed users
 */
async function seedUsers() {
  try {
    console.log('Starting user seeding process...');
    
    // Create super admin
    const adminId = await createUser(users[0]);
    
    // Create vendor
    const vendorId = await createUser(users[1], adminId);
    
    // Create staff with reference to vendor
    const staffData = {
      ...users[2],
      vendorId: vendorId,
      hotelId: 'default-hotel' // This will be updated when hotels are created
    };
    
    await createUser(staffData, vendorId);
    
    console.log('User seeding completed successfully!');
    
    console.log('Seed script completed. You can now log in with any of these accounts:');
    users.forEach(user => {
      console.log(`- ${user.email} (${user.role}) - Password: ${user.password}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error seeding users:', error.message);
    process.exit(1);
  }
}

// Run the seed function
seedUsers();
