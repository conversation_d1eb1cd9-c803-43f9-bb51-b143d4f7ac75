import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/data/models/service_request_model.dart';
import 'package:hotel_booking_app/data/services/service_request_notification_service.dart';

class ServiceRequestService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Service request types
  static const String serviceTypeCleaning = 'cleaning';
  static const String serviceTypeFood = 'food';
  static const String serviceTypeMaintenance = 'maintenance';

  // Service request statuses
  static const String statusPending = 'pending';
  static const String statusInProgress = 'in_progress';
  static const String statusCompleted = 'completed';
  static const String statusCancelled = 'cancelled';

  // Create a new service request
  Future<String> createServiceRequest(BaseServiceRequest request) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.serviceRequestsCollection)
          .add(request.toMap());

      return docRef.id;
    } catch (e) {
      debugPrint('Error creating service request: $e');
      rethrow;
    }
  }

  // Get service requests for a user
  Future<List<BaseServiceRequest>> getServiceRequestsForUser(
      String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppConstants.serviceRequestsCollection)
          .where('guestId', isEqualTo: userId)
          .orderBy('requestTime', descending: true)
          .get();

      return _parseServiceRequests(querySnapshot.docs);
    } catch (e) {
      debugPrint('Error getting service requests for user: $e');
      rethrow;
    }
  }

  // Get service requests for a booking
  Future<List<BaseServiceRequest>> getServiceRequestsForBooking(
      String bookingId) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppConstants.serviceRequestsCollection)
          .where('bookingId', isEqualTo: bookingId)
          .orderBy('requestTime', descending: true)
          .get();

      return _parseServiceRequests(querySnapshot.docs);
    } catch (e) {
      debugPrint('Error getting service requests for booking: $e');
      rethrow;
    }
  }

  // Get service requests for a hotel
  Future<List<BaseServiceRequest>> getServiceRequestsForHotel(
    String hotelId, {
    String? type,
    String? status,
  }) async {
    try {
      Query query = _firestore
          .collection(AppConstants.serviceRequestsCollection)
          .where('hotelId', isEqualTo: hotelId);

      if (type != null) {
        query = query.where('type', isEqualTo: type);
      }

      if (status != null) {
        query = query.where('status', isEqualTo: status);
      }

      final querySnapshot =
          await query.orderBy('requestTime', descending: true).get();

      return _parseServiceRequests(querySnapshot.docs);
    } catch (e) {
      debugPrint('Error getting service requests for hotel: $e');
      rethrow;
    }
  }

  // Get a service request by ID
  Future<BaseServiceRequest?> getServiceRequestById(String id) async {
    try {
      final docSnapshot = await _firestore
          .collection(AppConstants.serviceRequestsCollection)
          .doc(id)
          .get();

      if (!docSnapshot.exists) {
        return null;
      }

      return _parseServiceRequest(docSnapshot);
    } catch (e) {
      debugPrint('Error getting service request by ID: $e');
      rethrow;
    }
  }

  // Update a service request
  Future<void> updateServiceRequest(
      String id, Map<String, dynamic> updates) async {
    try {
      // Get the current service request to check for status changes
      final currentRequest = await getServiceRequestById(id);
      final String? previousStatus = currentRequest?.status;

      // Add updatedAt timestamp
      updates['updatedAt'] = FieldValue.serverTimestamp();

      // Update the service request
      await _firestore
          .collection(AppConstants.serviceRequestsCollection)
          .doc(id)
          .update(updates);

      // If status has changed, send notification to the user
      if (updates.containsKey('status') &&
          previousStatus != null &&
          updates['status'] != previousStatus) {
        // Get the updated service request
        final updatedRequest = await getServiceRequestById(id);

        if (updatedRequest != null) {
          // Import and use the notification service
          final notificationService = ServiceRequestNotificationService();

          // Send notification about status change
          await notificationService.sendServiceRequestStatusUpdateToUser(
            request: updatedRequest,
            previousStatus: previousStatus,
          );
        }
      }
    } catch (e) {
      debugPrint('Error updating service request: $e');
      rethrow;
    }
  }

  // Cancel a service request
  Future<void> cancelServiceRequest(String id) async {
    try {
      await _firestore
          .collection(AppConstants.serviceRequestsCollection)
          .doc(id)
          .update({
        'status': statusCancelled,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error cancelling service request: $e');
      rethrow;
    }
  }

  // Parse service requests from query snapshot
  List<BaseServiceRequest> _parseServiceRequests(List<DocumentSnapshot> docs) {
    final requests = <BaseServiceRequest>[];

    for (final doc in docs) {
      final request = _parseServiceRequest(doc);
      if (request != null) {
        requests.add(request);
      }
    }

    return requests;
  }

  // Parse a single service request from document snapshot
  BaseServiceRequest? _parseServiceRequest(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>? ?? {};
    final type = data['type'] as String? ?? '';

    switch (type) {
      case serviceTypeCleaning:
        return CleaningRequest.fromDocument(doc);
      case serviceTypeFood:
        return FoodOrder.fromDocument(doc);
      case serviceTypeMaintenance:
        return MaintenanceRequest.fromDocument(doc);
      default:
        debugPrint('Unknown service request type: $type');
        return null;
    }
  }

  // Stream of service requests for a user
  Stream<List<BaseServiceRequest>> streamServiceRequestsForUser(String userId) {
    return _firestore
        .collection(AppConstants.serviceRequestsCollection)
        .where('guestId', isEqualTo: userId)
        .orderBy('requestTime', descending: true)
        .snapshots()
        .map((snapshot) => _parseServiceRequests(snapshot.docs));
  }

  // Stream of service requests for a hotel
  Stream<List<BaseServiceRequest>> streamServiceRequestsForHotel(
    String hotelId, {
    String? type,
    String? status,
  }) {
    Query query = _firestore
        .collection(AppConstants.serviceRequestsCollection)
        .where('hotelId', isEqualTo: hotelId);

    if (type != null) {
      query = query.where('type', isEqualTo: type);
    }

    if (status != null) {
      query = query.where('status', isEqualTo: status);
    }

    return query
        .orderBy('requestTime', descending: true)
        .snapshots()
        .map((snapshot) => _parseServiceRequests(snapshot.docs));
  }

  // Get active service requests count for a user
  Future<int> getActiveServiceRequestsCountForUser(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppConstants.serviceRequestsCollection)
          .where('guestId', isEqualTo: userId)
          .where('status', whereIn: [statusPending, statusInProgress])
          .count()
          .get();

      return querySnapshot.count ?? 0;
    } catch (e) {
      debugPrint('Error getting active service requests count: $e');
      return 0;
    }
  }

  // Get pending service requests count for a hotel
  Future<int> getPendingServiceRequestsCountForHotel(String hotelId) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppConstants.serviceRequestsCollection)
          .where('hotelId', isEqualTo: hotelId)
          .where('status', isEqualTo: statusPending)
          .count()
          .get();

      return querySnapshot.count ?? 0;
    } catch (e) {
      debugPrint('Error getting pending service requests count: $e');
      return 0;
    }
  }
}
