import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Snackbar,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { getStaffForVendor, createUser, updateUser, deleteUser, UserData } from '../../firebase/userService';
import { ROLE_STAFF } from '../../firebase/auth';
import { auth } from '../../firebase/config';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../../firebase/config';

interface Hotel {
  id: string;
  name: string;
}

const Staff: React.FC = () => {
  const [staff, setStaff] = useState<UserData[]>([]);
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingStaff, setEditingStaff] = useState<UserData | null>(null);
  const [formData, setFormData] = useState({
    email: '',
    displayName: '',
    phone: '',
    hotelId: '',
    password: ''
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  // Load staff and hotels on component mount
  useEffect(() => {
    fetchHotels();
    fetchStaff();
  }, []);

  const fetchHotels = async () => {
    try {
      if (!auth.currentUser) return;

      // Get hotels owned by the current vendor
      const hotelsQuery = query(
        collection(db, 'hotels'),
        where('vendorId', '==', auth.currentUser.uid)
      );

      const hotelsSnapshot = await getDocs(hotelsQuery);
      const hotelsList: Hotel[] = [];

      hotelsSnapshot.forEach((doc) => {
        hotelsList.push({
          id: doc.id,
          name: doc.data().name
        });
      });

      setHotels(hotelsList);
    } catch (error) {
      console.error('Error fetching hotels:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load hotels',
        severity: 'error'
      });
    }
  };

  const fetchStaff = async () => {
    try {
      setLoading(true);
      if (!auth.currentUser) return;

      const staffList = await getStaffForVendor(auth.currentUser.uid);
      setStaff(staffList);
    } catch (error) {
      console.error('Error fetching staff:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load staff',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (staffMember?: UserData) => {
    if (staffMember) {
      // Edit mode
      setEditingStaff(staffMember);
      setFormData({
        email: staffMember.email,
        displayName: staffMember.displayName,
        phone: staffMember.phone || '',
        hotelId: staffMember.hotelId || '',
        password: '' // Don't set password when editing
      });
    } else {
      // Create mode
      setEditingStaff(null);
      setFormData({
        email: '',
        displayName: '',
        phone: '',
        hotelId: hotels.length > 0 ? hotels[0].id : '',
        password: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (e: SelectChangeEvent) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async () => {
    try {
      if (!auth.currentUser) {
        throw new Error('You must be logged in to perform this action');
      }

      if (editingStaff) {
        // Update existing staff
        await updateUser(editingStaff.id!, {
          displayName: formData.displayName,
          phone: formData.phone,
          hotelId: formData.hotelId
        });

        setSnackbar({
          open: true,
          message: 'Staff updated successfully',
          severity: 'success'
        });
      } else {
        // Create new staff
        if (!formData.email || !formData.displayName || !formData.hotelId) {
          throw new Error('Email, name, and hotel are required');
        }

        const userData: UserData = {
          email: formData.email,
          displayName: formData.displayName,
          role: ROLE_STAFF,
          phone: formData.phone,
          vendorId: auth.currentUser.uid,
          hotelId: formData.hotelId
        };

        await createUser(userData, formData.password, auth.currentUser);

        setSnackbar({
          open: true,
          message: 'Staff created successfully',
          severity: 'success'
        });
      }

      handleCloseDialog();
      fetchStaff(); // Refresh the list
    } catch (error: any) {
      console.error('Error saving staff:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to save staff',
        severity: 'error'
      });
    }
  };

  const handleDeleteStaff = async (staffId: string) => {
    if (window.confirm('Are you sure you want to delete this staff member?')) {
      try {
        await deleteUser(staffId);
        setSnackbar({
          open: true,
          message: 'Staff deleted successfully',
          severity: 'success'
        });
        fetchStaff(); // Refresh the list
      } catch (error) {
        console.error('Error deleting staff:', error);
        setSnackbar({
          open: true,
          message: 'Failed to delete staff',
          severity: 'error'
        });
      }
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Get hotel name by ID
  const getHotelName = (hotelId: string) => {
    const hotel = hotels.find(h => h.id === hotelId);
    return hotel ? hotel.name : 'Unknown Hotel';
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Staff Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          disabled={hotels.length === 0}
        >
          Add Staff
        </Button>
      </Box>

      {hotels.length === 0 && (
        <Paper sx={{ p: 3, mb: 3, bgcolor: 'warning.light' }}>
          <Typography variant="body1">
            You need to create at least one hotel before you can add staff members.
          </Typography>
        </Paper>
      )}

      <Paper>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Phone</TableCell>
                  <TableCell>Hotel</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {staff.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      No staff found
                    </TableCell>
                  </TableRow>
                ) : (
                  staff.map((staffMember) => (
                    <TableRow key={staffMember.id}>
                      <TableCell>{staffMember.displayName}</TableCell>
                      <TableCell>{staffMember.email}</TableCell>
                      <TableCell>{staffMember.phone || '-'}</TableCell>
                      <TableCell>{staffMember.hotelId ? getHotelName(staffMember.hotelId) : '-'}</TableCell>
                      <TableCell align="right">
                        <IconButton
                          color="primary"
                          onClick={() => handleOpenDialog(staffMember)}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          color="error"
                          onClick={() => staffMember.id && handleDeleteStaff(staffMember.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* Add/Edit Staff Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingStaff ? 'Edit Staff' : 'Add New Staff'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              label="Email Address"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              disabled={!!editingStaff} // Disable email editing for existing staff
            />
            <TextField
              margin="normal"
              required
              fullWidth
              label="Name"
              name="displayName"
              value={formData.displayName}
              onChange={handleInputChange}
            />
            <TextField
              margin="normal"
              fullWidth
              label="Phone"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
            />
            <FormControl fullWidth margin="normal" required>
              <InputLabel id="hotel-select-label">Hotel</InputLabel>
              <Select
                labelId="hotel-select-label"
                name="hotelId"
                value={formData.hotelId}
                label="Hotel"
                onChange={handleSelectChange}
              >
                {hotels.map((hotel) => (
                  <MenuItem key={hotel.id} value={hotel.id}>
                    {hotel.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            {!editingStaff && (
              <TextField
                margin="normal"
                fullWidth
                label="Password (leave empty to send reset email)"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                helperText="If left empty, a password reset email will be sent to the user"
              />
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingStaff ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Staff;
