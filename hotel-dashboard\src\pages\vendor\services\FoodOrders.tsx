import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Button,
  CircularProgress,
  Alert,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Chip,
  Badge,
  InputAdornment,
  List,
  ListItem,
  ListItemText
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  CheckCircle as CompletedIcon,
  Pending as PendingIcon,
  Restaurant as FoodIcon,
  Schedule as ScheduleIcon,
  LocalDining as DiningIcon
} from '@mui/icons-material';
import { collection, query, where, orderBy, getDocs, updateDoc, doc, Timestamp } from 'firebase/firestore';
import { db, auth } from '../../../firebase/config';

// Service request statuses
const STATUS_PENDING = 'pending';
const STATUS_IN_PROGRESS = 'in_progress';
const STATUS_COMPLETED = 'completed';
const STATUS_CANCELLED = 'cancelled';

// Food order item interface
interface FoodItem {
  name: string;
  quantity: number;
  price: number;
  specialInstructions?: string;
}

// Food order model
interface FoodOrder {
  id: string;
  roomNumber: string;
  orderTime: Timestamp;
  status: string;
  notes: string;
  assignedTo?: string;
  completedTime?: Timestamp;
  guestName?: string;
  priority: 'low' | 'medium' | 'high';
  hotelId: string;
  items: FoodItem[];
  totalAmount: number;
}

const FoodOrders: React.FC = () => {
  const [orders, setOrders] = useState<FoodOrder[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<FoodOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Load food orders on component mount
  useEffect(() => {
    fetchFoodOrders();
  }, []);

  // Filter orders when filter or search changes
  useEffect(() => {
    filterOrders();
  }, [statusFilter, searchTerm, orders]);

  const fetchFoodOrders = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!auth.currentUser) {
        throw new Error('You must be logged in to view food orders');
      }

      // In a real app, we would get the hotel ID from the vendor's profile
      // For now, we'll use a mock hotel ID
      const vendorId = auth.currentUser.uid;
      const hotelId = 'hotel123'; // This would come from the vendor's profile

      const ordersRef = collection(db, 'serviceRequests');
      const ordersQuery = query(
        ordersRef,
        where('type', '==', 'food'),
        where('hotelId', '==', hotelId),
        orderBy('orderTime', 'desc')
      );

      const querySnapshot = await getDocs(ordersQuery);

      const ordersList: FoodOrder[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data() as Omit<FoodOrder, 'id'>;
        ordersList.push({
          id: doc.id,
          ...data
        });
      });
      setOrders(ordersList);
    } catch (err: any) {
      console.error('Error fetching food orders:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const filterOrders = () => {
    let filtered = [...orders];

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter);
    }

    // Apply search filter
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(
        order =>
          order.roomNumber.toLowerCase().includes(search) ||
          (order.guestName && order.guestName.toLowerCase().includes(search)) ||
          order.notes.toLowerCase().includes(search) ||
          order.items.some(item => item.name.toLowerCase().includes(search))
      );
    }

    setFilteredOrders(filtered);
  };

  const handleStatusFilterChange = (event: SelectChangeEvent) => {
    setStatusFilter(event.target.value);
    setPage(0); // Reset to first page when filter changes
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0); // Reset to first page when search changes
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case STATUS_PENDING:
        return <Chip icon={<PendingIcon />} label="Pending" color="warning" size="small" />;
      case STATUS_IN_PROGRESS:
        return <Chip icon={<ScheduleIcon />} label="In Progress" color="info" size="small" />;
      case STATUS_COMPLETED:
        return <Chip icon={<CompletedIcon />} label="Completed" color="success" size="small" />;
      case STATUS_CANCELLED:
        return <Chip label="Cancelled" color="error" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const getPriorityChip = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Chip label="High" color="error" size="small" />;
      case 'medium':
        return <Chip label="Medium" color="warning" size="small" />;
      case 'low':
        return <Chip label="Low" color="info" size="small" />;
      default:
        return <Chip label={priority} size="small" />;
    }
  };

  const formatDate = (timestamp: Timestamp) => {
    return new Date(timestamp.seconds * 1000).toLocaleString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };



  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Food Orders
        </Typography>
        <Button
          variant="contained"
          startIcon={<RefreshIcon />}
          onClick={fetchFoodOrders}
        >
          Refresh
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Total Orders
              </Typography>
              <Typography variant="h3">
                {orders.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'warning.light' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Pending
              </Typography>
              <Typography variant="h3">
                {orders.filter(r => r.status === STATUS_PENDING).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'info.light' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                In Progress
              </Typography>
              <Typography variant="h3">
                {orders.filter(r => r.status === STATUS_IN_PROGRESS).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'success.light' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Completed
              </Typography>
              <Typography variant="h3">
                {orders.filter(r => r.status === STATUS_COMPLETED).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search by room number, guest name, or food items"
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                label="Status"
                onChange={handleStatusFilterChange}
                startAdornment={
                  <InputAdornment position="start">
                    <FilterIcon />
                  </InputAdornment>
                }
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value={STATUS_PENDING}>Pending</MenuItem>
                <MenuItem value={STATUS_IN_PROGRESS}>In Progress</MenuItem>
                <MenuItem value={STATUS_COMPLETED}>Completed</MenuItem>
                <MenuItem value={STATUS_CANCELLED}>Cancelled</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Orders Table */}
      <Paper>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Room</TableCell>
                    <TableCell>Guest</TableCell>
                    <TableCell>Order Time</TableCell>
                    <TableCell>Items</TableCell>
                    <TableCell>Total</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredOrders.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        No food orders found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredOrders
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((order) => (
                        <TableRow key={order.id}>
                          <TableCell>
                            <Typography variant="body1" fontWeight="bold">
                              {order.roomNumber}
                            </Typography>
                          </TableCell>
                          <TableCell>{order.guestName || 'N/A'}</TableCell>
                          <TableCell>{formatDate(order.orderTime)}</TableCell>
                          <TableCell>
                            <List dense disablePadding>
                              {order.items.map((item, index) => (
                                <ListItem key={index} disablePadding>
                                  <ListItemText
                                    primary={`${item.quantity}x ${item.name}`}
                                    secondary={formatCurrency(item.price * item.quantity)}
                                  />
                                </ListItem>
                              ))}
                            </List>
                          </TableCell>
                          <TableCell>
                            <Typography fontWeight="bold">
                              {formatCurrency(order.totalAmount)}
                            </Typography>
                          </TableCell>
                          <TableCell>{getStatusChip(order.status)}</TableCell>
                          <TableCell>
                            <Button
                              variant="outlined"
                              size="small"
                              disabled={order.status === STATUS_COMPLETED || order.status === STATUS_CANCELLED}
                            >
                              Manage
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={filteredOrders.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Paper>
    </Box>
  );
};

export default FoodOrders;
