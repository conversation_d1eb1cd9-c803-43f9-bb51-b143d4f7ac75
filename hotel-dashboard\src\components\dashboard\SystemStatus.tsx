import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  CircularProgress,
  Tooltip,
  IconButton,
  useTheme
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as <PERSON>rrorIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  Storage as StorageIcon,
  Cloud as CloudIcon,
  Security as SecurityIcon,
  Sync as SyncIcon,
  Payments as PaymentsIcon
} from '@mui/icons-material';

interface SystemService {
  name: string;
  status: 'operational' | 'degraded' | 'outage' | 'maintenance';
  icon: React.ReactNode;
  lastUpdated: Date;
  details?: string;
}

const SystemStatus: React.FC = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [services, setServices] = useState<SystemService[]>([
    {
      name: 'Database',
      status: 'operational',
      icon: <StorageIcon />,
      lastUpdated: new Date(),
      details: 'Firebase Firestore is operating normally'
    },
    {
      name: 'Authentication',
      status: 'operational',
      icon: <SecurityIcon />,
      lastUpdated: new Date(),
      details: 'Firebase Auth is operating normally'
    },
    {
      name: 'Cloud Storage',
      status: 'operational',
      icon: <CloudIcon />,
      lastUpdated: new Date(),
      details: 'Firebase Storage is operating normally'
    },
    {
      name: 'Sync Service',
      status: 'operational',
      icon: <SyncIcon />,
      lastUpdated: new Date(),
      details: 'Real-time sync is operating normally'
    },
    {
      name: 'Payment Gateway',
      status: 'operational',
      icon: <PaymentsIcon />,
      lastUpdated: new Date(),
      details: 'Payment processing is operating normally'
    }
  ]);

  // Simulate checking system status
  const checkSystemStatus = () => {
    setLoading(true);
    
    // Simulate API call with timeout
    setTimeout(() => {
      // Randomly set some services to different statuses for demo purposes
      const updatedServices = services.map(service => {
        const random = Math.random();
        let status: 'operational' | 'degraded' | 'outage' | 'maintenance' = 'operational';
        
        // 80% chance of operational, 10% degraded, 5% outage, 5% maintenance
        if (random > 0.9) {
          status = 'outage';
        } else if (random > 0.8) {
          status = 'degraded';
        } else if (random > 0.7) {
          status = 'maintenance';
        }
        
        return {
          ...service,
          status,
          lastUpdated: new Date()
        };
      });
      
      setServices(updatedServices);
      setLoading(false);
    }, 1500);
  };

  // Check status on component mount
  useEffect(() => {
    checkSystemStatus();
  }, []);

  // Get status icon and color
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'operational':
        return {
          icon: <CheckCircleIcon sx={{ color: theme.palette.success.main }} />,
          color: theme.palette.success.main,
          text: 'Operational'
        };
      case 'degraded':
        return {
          icon: <WarningIcon sx={{ color: theme.palette.warning.main }} />,
          color: theme.palette.warning.main,
          text: 'Degraded'
        };
      case 'outage':
        return {
          icon: <ErrorIcon sx={{ color: theme.palette.error.main }} />,
          color: theme.palette.error.main,
          text: 'Outage'
        };
      case 'maintenance':
        return {
          icon: <SyncIcon sx={{ color: theme.palette.info.main }} />,
          color: theme.palette.info.main,
          text: 'Maintenance'
        };
      default:
        return {
          icon: <CheckCircleIcon sx={{ color: theme.palette.success.main }} />,
          color: theme.palette.success.main,
          text: 'Operational'
        };
    }
  };

  // Calculate overall system status
  const getOverallStatus = () => {
    if (services.some(service => service.status === 'outage')) {
      return 'outage';
    } else if (services.some(service => service.status === 'degraded')) {
      return 'degraded';
    } else if (services.some(service => service.status === 'maintenance')) {
      return 'maintenance';
    } else {
      return 'operational';
    }
  };

  const overallStatus = getOverallStatus();
  const statusInfo = getStatusInfo(overallStatus);

  return (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">System Status</Typography>
        <Tooltip title="Refresh Status">
          <IconButton size="small" onClick={checkSystemStatus} disabled={loading}>
            {loading ? <CircularProgress size={20} /> : <RefreshIcon fontSize="small" />}
          </IconButton>
        </Tooltip>
      </Box>
      
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Box sx={{ mr: 2 }}>
          {statusInfo.icon}
        </Box>
        <Box>
          <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
            Overall System Status: {statusInfo.text}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Last checked: {new Date().toLocaleTimeString()}
          </Typography>
        </Box>
      </Box>
      
      <Divider sx={{ mb: 2 }} />
      
      <List disablePadding>
        {services.map((service, index) => {
          const { icon, color, text } = getStatusInfo(service.status);
          return (
            <ListItem 
              key={index}
              sx={{ 
                py: 1,
                borderBottom: index < services.length - 1 ? '1px solid #f0f0f0' : 'none'
              }}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>
                {service.icon}
              </ListItemIcon>
              <ListItemText 
                primary={service.name}
                secondary={service.details}
                primaryTypographyProps={{ variant: 'body2' }}
                secondaryTypographyProps={{ variant: 'caption' }}
              />
              <Chip 
                size="small"
                label={text}
                sx={{ 
                  bgcolor: `${color}20`,
                  color: color,
                  fontWeight: 'bold',
                  fontSize: '0.7rem'
                }}
                icon={icon}
              />
            </ListItem>
          );
        })}
      </List>
    </Paper>
  );
};

export default SystemStatus;
