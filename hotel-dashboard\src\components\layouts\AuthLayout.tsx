import React from 'react';
import { Outlet } from 'react-router-dom';
import { Box, Container, Paper, Typography, useTheme, useMediaQuery } from '@mui/material';

const AuthLayout: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <Box
      sx={{
        display: 'flex',
        minHeight: '100vh',
        bgcolor: 'background.default',
        flexDirection: { xs: 'column', md: 'row' },
      }}
    >
      {/* Left side - Image */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          bgcolor: 'primary.main',
          backgroundImage: 'url(https://source.unsplash.com/featured/?luxury,hotel)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          position: 'relative',
          minHeight: { xs: '30vh', md: '100vh' },
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            bgcolor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: { xs: 2, md: 4 },
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
            <img
              src="/link_in_blink_white.png"
              alt="Link In Blink Logo"
              style={{
                height: isMobile ? '200px' : '300px', // Smaller logo on mobile
                marginBottom: isMobile ? '20px' : '40px', // Less margin on mobile
                filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.5))',
                maxWidth: '100%' // Ensure logo doesn't overflow container
              }}
            />
          </Box>
          <Typography
            variant={isMobile ? "h3" : "h2"}
            component="h1"
            sx={{
              color: 'white',
              fontWeight: 700,
              textAlign: 'center',
              mb: 2,
              textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
            }}
          >
            Link In Blink Hotel
          </Typography>
          <Typography
            variant={isMobile ? "body1" : "h5"}
            sx={{
              color: 'white',
              textAlign: 'center',
              maxWidth: 600,
              textShadow: '1px 1px 2px rgba(0,0,0,0.5)',
            }}
          >
            Comprehensive multi-vendor hotel booking solution for the modern hospitality industry
          </Typography>
        </Box>
      </Box>

      {/* Right side - Auth Form */}
      <Box
        sx={{
          flex: { xs: 1, md: 0.7 },
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          p: { xs: 2, md: 4 },
          bgcolor: '#f8f9fa',
        }}
      >
        <Container maxWidth="sm">
          <Paper
            elevation={3}
            sx={{
              p: { xs: 2, md: 4 },
              borderRadius: 2,
              boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
              background: 'white',
            }}
          >
            <Outlet />
          </Paper>
        </Container>
      </Box>
    </Box>
  );
};

export default AuthLayout;
