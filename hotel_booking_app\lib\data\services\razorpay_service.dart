import 'package:flutter/foundation.dart';
import 'package:hotel_booking_app/data/models/booking_model.dart';
import 'package:hotel_booking_app/data/models/payment_model.dart';
import 'package:hotel_booking_app/data/services/payment_service.dart';
import 'package:hotel_booking_app/data/services/mock_razorpay.dart';
import 'dart:math' as math;

/// Service to handle Razorpay payment gateway integration
/// This is a mock implementation for testing purposes
class RazorpayService extends ChangeNotifier {
  // Razorpay instance
  final Razorpay _razorpay = Razorpay();

  // Payment service for database operations
  final PaymentService _paymentService = PaymentService();

  // Razorpay API keys
  // In a real app, these would be stored securely and not hardcoded
  static const String _keyId = 'rzp_test_1DP5mmOlF5G5ag';

  // State variables
  bool _isLoading = false;
  String? _error;

  // Current booking being processed
  Booking? _currentBooking;

  // Callback functions
  Function(PaymentDetails?)? onPaymentSuccess;
  Function(String)? onPaymentError;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize the service
  RazorpayService() {
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  // Generate a mock payment ID
  String _generateMockPaymentId() {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = math.Random();
    final result = StringBuffer('mock_pay_');
    for (var i = 0; i < 14; i++) {
      result.write(chars[random.nextInt(chars.length)]);
    }
    return result.toString();
  }

  // Handle payment success - using mock data
  void _handlePaymentSuccess(PaymentSuccessResponse response) async {
    // Generate mock payment ID instead of using real one
    final mockPaymentId = _generateMockPaymentId();
    debugPrint('MOCK Payment success: $mockPaymentId');

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Create payment metadata with mock transaction details
      final metadata = {
        'razorpay_payment_id': mockPaymentId,
        'razorpay_order_id':
            'mock_order_${DateTime.now().millisecondsSinceEpoch}',
        'razorpay_signature':
            'mock_signature_${DateTime.now().millisecondsSinceEpoch}',
        'payment_timestamp': DateTime.now().millisecondsSinceEpoch,
        'payment_gateway': 'razorpay_mock',
        'is_mock': true,
        'mock_created_at': DateTime.now().toIso8601String(),
      };

      // Create a payment record in the database with mock transaction data
      final payment = await _paymentService.processPayment(
        booking: _currentBooking!,
        paymentMethod: PaymentMethod.creditCard,
        savedPaymentMethodId: null,
        transactionId: mockPaymentId,
        gatewayResponse: 'MOCK Payment successful via Razorpay',
        metadata: metadata,
      );

      if (payment != null) {
        // Call the success callback if provided
        if (onPaymentSuccess != null) {
          onPaymentSuccess!(payment);
        }
      } else {
        // Handle error
        _error = 'Failed to process payment';
        if (onPaymentError != null) {
          onPaymentError!(_error!);
        }
      }
    } catch (e) {
      _error = e.toString();
      if (onPaymentError != null) {
        onPaymentError!(_error!);
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Handle payment error - using mock data
  void _handlePaymentError(PaymentFailureResponse response) {
    final mockErrorCode = response.code ?? 1000;
    final mockErrorMessage = response.message ?? 'Mock payment failed';

    debugPrint('MOCK Payment error: $mockErrorCode - $mockErrorMessage');

    _error = 'MOCK Payment failed: $mockErrorMessage';
    if (onPaymentError != null) {
      onPaymentError!(_error!);
    }

    // Log mock error details
    debugPrint('MOCK Payment error details:');
    debugPrint('  Error code: $mockErrorCode');
    debugPrint('  Error message: $mockErrorMessage');
    debugPrint('  Timestamp: ${DateTime.now().toIso8601String()}');

    notifyListeners();
  }

  // Handle external wallet
  void _handleExternalWallet(ExternalWalletResponse response) {
    debugPrint('External wallet: ${response.walletName}');
  }

  // Process payment for a booking - MOCK IMPLEMENTATION
  void processPayment({
    required Booking booking,
    required String customerName,
    required String customerEmail,
    required String customerPhone,
  }) {
    _currentBooking = booking;

    // Add mock indicator to the description
    final description =
        'MOCK PAYMENT - Booking for ${booking.hotelName} - ${booking.roomName}';

    // Create payment options with mock indicator
    final options = {
      'key': _keyId,
      'amount': (booking.totalPrice * 100).toInt(), // Amount in paise
      'name': 'Link In Blink (TEST MODE)',
      'description': description,
      'prefill': {
        'contact': customerPhone,
        'email': customerEmail,
        'name': customerName,
      },
      'external': {
        'wallets': ['paytm', 'gpay']
      },
      'theme': {
        'color': '#3f51b5',
      },
      'currency': 'INR',
      'notes': {
        'is_mock': 'true',
        'mock_mode':
            'This is a test payment and no actual charges will be made',
        'booking_id': booking.id,
      },
    };

    try {
      _razorpay.open(options);
    } catch (e) {
      _error = 'Error opening Razorpay: $e';
      if (onPaymentError != null) {
        onPaymentError!(_error!);
      }
      notifyListeners();
    }
  }

  // Process payment with UPI - MOCK IMPLEMENTATION
  void processUpiPayment({
    required Booking booking,
    required String customerName,
    required String customerEmail,
    required String customerPhone,
    required String upiId,
  }) {
    _currentBooking = booking;

    // Add mock indicator to the description
    final description =
        'MOCK UPI PAYMENT - Booking for ${booking.hotelName} - ${booking.roomName}';

    // Create payment options with mock indicator
    final options = {
      'key': _keyId,
      'amount': (booking.totalPrice * 100).toInt(), // Amount in paise
      'name': 'Link In Blink (TEST MODE)',
      'description': description,
      'prefill': {
        'contact': customerPhone,
        'email': customerEmail,
        'name': customerName,
      },
      'method': {
        'upi': true,
        'vpa': upiId,
      },
      'theme': {
        'color': '#3f51b5',
      },
      'currency': 'INR',
      'notes': {
        'is_mock': 'true',
        'mock_mode':
            'This is a test UPI payment and no actual charges will be made',
        'booking_id': booking.id,
      },
    };

    try {
      _razorpay.open(options);
    } catch (e) {
      _error = 'Error opening Razorpay UPI: $e';
      if (onPaymentError != null) {
        onPaymentError!(_error!);
      }
      notifyListeners();
    }
  }

  // Dispose the service
  @override
  void dispose() {
    _razorpay.clear();
    _razorpay.dispose();
    super.dispose();
  }
}
