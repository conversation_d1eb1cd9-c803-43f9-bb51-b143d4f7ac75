const admin = require('firebase-admin');
const serviceAccount = require('../serviceAccountKey.json');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

// Constants
const STAFF_COLLECTION = 'staff';
const STAFF_ASSIGNMENTS_COLLECTION = 'staffAssignments';

// Staff roles
const STAFF_ROLE_HOUSEKEEPING = 'housekeeping';
const STAFF_ROLE_FOOD_SERVICE = 'food_service';
const STAFF_ROLE_MAINTENANCE = 'maintenance';
const STAFF_ROLE_FRONT_DESK = 'front_desk';
const STAFF_ROLE_MANAGER = 'manager';

// Staff status
const STAFF_STATUS_ACTIVE = 'active';
const STAFF_STATUS_INACTIVE = 'inactive';
const STAFF_STATUS_ON_LEAVE = 'on_leave';

// Sample hotel and vendor IDs (replace with actual IDs)
const HOTEL_ID = 'hotel123';
const VENDOR_ID = 'vendor123';

// Sample staff members
const sampleStaffMembers = [
  // Housekeeping staff
  {
    name: 'Maria Garcia',
    email: '<EMAIL>',
    phone: '+**********',
    role: STAFF_ROLE_HOUSEKEEPING,
    status: STAFF_STATUS_ACTIVE,
    hotelId: HOTEL_ID,
    vendorId: VENDOR_ID,
    skills: ['regular cleaning', 'deep cleaning', 'turndown service'],
    profileImage: 'https://randomuser.me/api/portraits/women/1.jpg',
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  {
    name: 'John Smith',
    email: '<EMAIL>',
    phone: '+1234567891',
    role: STAFF_ROLE_HOUSEKEEPING,
    status: STAFF_STATUS_ACTIVE,
    hotelId: HOTEL_ID,
    vendorId: VENDOR_ID,
    skills: ['regular cleaning', 'deep cleaning'],
    profileImage: 'https://randomuser.me/api/portraits/men/1.jpg',
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  {
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '+1234567892',
    role: STAFF_ROLE_HOUSEKEEPING,
    status: STAFF_STATUS_ON_LEAVE,
    hotelId: HOTEL_ID,
    vendorId: VENDOR_ID,
    skills: ['regular cleaning', 'turndown service'],
    profileImage: 'https://randomuser.me/api/portraits/women/2.jpg',
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  
  // Food service staff
  {
    name: 'David Lee',
    email: '<EMAIL>',
    phone: '+1234567893',
    role: STAFF_ROLE_FOOD_SERVICE,
    status: STAFF_STATUS_ACTIVE,
    hotelId: HOTEL_ID,
    vendorId: VENDOR_ID,
    skills: ['room service', 'food preparation', 'bartending'],
    profileImage: 'https://randomuser.me/api/portraits/men/2.jpg',
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  {
    name: 'Emily Chen',
    email: '<EMAIL>',
    phone: '+1234567894',
    role: STAFF_ROLE_FOOD_SERVICE,
    status: STAFF_STATUS_ACTIVE,
    hotelId: HOTEL_ID,
    vendorId: VENDOR_ID,
    skills: ['room service', 'food preparation'],
    profileImage: 'https://randomuser.me/api/portraits/women/3.jpg',
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  {
    name: 'Michael Brown',
    email: '<EMAIL>',
    phone: '+1234567895',
    role: STAFF_ROLE_FOOD_SERVICE,
    status: STAFF_STATUS_INACTIVE,
    hotelId: HOTEL_ID,
    vendorId: VENDOR_ID,
    skills: ['room service', 'bartending'],
    profileImage: 'https://randomuser.me/api/portraits/men/3.jpg',
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  
  // Maintenance staff
  {
    name: 'Robert Wilson',
    email: '<EMAIL>',
    phone: '+1234567896',
    role: STAFF_ROLE_MAINTENANCE,
    status: STAFF_STATUS_ACTIVE,
    hotelId: HOTEL_ID,
    vendorId: VENDOR_ID,
    skills: ['plumbing', 'electrical', 'hvac'],
    profileImage: 'https://randomuser.me/api/portraits/men/4.jpg',
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  {
    name: 'Jennifer Martinez',
    email: '<EMAIL>',
    phone: '+1234567897',
    role: STAFF_ROLE_MAINTENANCE,
    status: STAFF_STATUS_ACTIVE,
    hotelId: HOTEL_ID,
    vendorId: VENDOR_ID,
    skills: ['electrical', 'furniture repair'],
    profileImage: 'https://randomuser.me/api/portraits/women/4.jpg',
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  {
    name: 'Thomas Anderson',
    email: '<EMAIL>',
    phone: '+1234567898',
    role: STAFF_ROLE_MAINTENANCE,
    status: STAFF_STATUS_ACTIVE,
    hotelId: HOTEL_ID,
    vendorId: VENDOR_ID,
    skills: ['plumbing', 'hvac', 'general repairs'],
    profileImage: 'https://randomuser.me/api/portraits/men/5.jpg',
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  
  // Front desk staff
  {
    name: 'Lisa Taylor',
    email: '<EMAIL>',
    phone: '+1234567899',
    role: STAFF_ROLE_FRONT_DESK,
    status: STAFF_STATUS_ACTIVE,
    hotelId: HOTEL_ID,
    vendorId: VENDOR_ID,
    skills: ['check-in', 'check-out', 'customer service'],
    profileImage: 'https://randomuser.me/api/portraits/women/5.jpg',
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  
  // Manager
  {
    name: 'James Williams',
    email: '<EMAIL>',
    phone: '+1234567900',
    role: STAFF_ROLE_MANAGER,
    status: STAFF_STATUS_ACTIVE,
    hotelId: HOTEL_ID,
    vendorId: VENDOR_ID,
    skills: ['staff management', 'customer service', 'operations'],
    profileImage: 'https://randomuser.me/api/portraits/men/6.jpg',
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  }
];

// Seed staff members
async function seedStaffMembers() {
  try {
    console.log('Seeding staff members...');
    
    // Clear existing staff members
    const existingStaff = await db.collection(STAFF_COLLECTION)
      .where('hotelId', '==', HOTEL_ID)
      .get();
    
    const batch = db.batch();
    
    existingStaff.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
    console.log(`Deleted ${existingStaff.size} existing staff members.`);
    
    // Add new staff members
    const staffPromises = sampleStaffMembers.map(staff => 
      db.collection(STAFF_COLLECTION).add(staff)
    );
    
    await Promise.all(staffPromises);
    
    console.log(`Added ${sampleStaffMembers.length} new staff members.`);
    console.log('Staff members seeded successfully!');
  } catch (error) {
    console.error('Error seeding staff members:', error);
  }
}

// Run the seed function
seedStaffMembers()
  .then(() => {
    console.log('Script completed successfully!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
