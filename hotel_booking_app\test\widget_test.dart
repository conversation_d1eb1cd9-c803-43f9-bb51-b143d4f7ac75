// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Skip main app test since it requires Firebase initialization
void main() {
  testWidgets('App smoke test', (WidgetTester tester) async {
    // Build a simple MaterialApp for testing
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          appBar: AppBar(
            title: const Text('Hotel Booking App'),
          ),
          body: const Center(
            child: Text('Welcome to Link In Blink'),
          ),
        ),
      ),
    );

    // Verify that the welcome text appears
    expect(find.text('Welcome to Link In Blink'), findsOneWidget);
    expect(find.text('Hotel Booking App'), findsOneWidget);
  });
}
