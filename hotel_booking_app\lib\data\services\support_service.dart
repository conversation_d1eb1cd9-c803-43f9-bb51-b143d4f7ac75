import 'package:flutter/material.dart';
import 'package:hotel_booking_app/data/services/auth_service.dart';

class SupportService extends ChangeNotifier {
  final AuthService _authService;

  List<Map<String, dynamic>> _supportOptions = [];
  List<Map<String, dynamic>> _supportTickets = [];
  bool _isLoading = false;
  String? _error;

  SupportService(this._authService) {
    fetchSupportOptions();
    if (_authService.isAuthenticated) {
      fetchUserSupportTickets();
    }
  }

  // Getters
  List<Map<String, dynamic>> get supportOptions => _supportOptions;
  List<Map<String, dynamic>> get supportTickets => _supportTickets;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Fetch support options - now uses local data to avoid Firestore permission issues
  Future<void> fetchSupportOptions() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Instead of fetching from Firestore, use local data
      await Future.delayed(
          const Duration(milliseconds: 500)); // Simulate network delay
      _supportOptions = _getLocalSupportOptions();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Get local support options
  List<Map<String, dynamic>> _getLocalSupportOptions() {
    return [
      {
        'id': 'email_support',
        'title': 'Email Support',
        'subtitle': '<EMAIL>',
        'icon': 'email',
        'type': 'email',
        'value': '<EMAIL>',
        'order': 1,
      },
      {
        'id': 'phone_support',
        'title': 'Phone Support',
        'subtitle': '+91 1234567890',
        'icon': 'phone',
        'type': 'phone',
        'value': '+911234567890',
        'order': 2,
      },
      {
        'id': 'live_chat',
        'title': 'Live Chat',
        'subtitle': 'Available 24/7',
        'icon': 'chat',
        'type': 'chat',
        'value': 'chat',
        'order': 3,
      },
    ];
  }

  // Fetch user's support tickets - now uses local data
  Future<void> fetchUserSupportTickets() async {
    if (!_authService.isAuthenticated) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Instead of fetching from Firestore, use local data
      await Future.delayed(
          const Duration(milliseconds: 500)); // Simulate network delay
      _supportTickets = _getLocalSupportTickets();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Get local support tickets
  List<Map<String, dynamic>> _getLocalSupportTickets() {
    if (!_authService.isAuthenticated) return [];

    // Return empty list for now - can be populated with sample data if needed
    return [];
  }

  // Create a new support ticket - now just simulates the operation
  Future<String?> createSupportTicket({
    required String subject,
    required String message,
    required String category,
    List<String>? attachmentUrls,
  }) async {
    if (!_authService.isAuthenticated) return null;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 800));

      // Generate a fake ticket ID
      final ticketId = 'ticket_${DateTime.now().millisecondsSinceEpoch}';

      // Add the ticket to local tickets
      final userData = _authService.userData;
      final newTicket = {
        'id': ticketId,
        'userId': _authService.user!.uid,
        'userName': userData?['displayName'] ?? 'User',
        'userEmail': userData?['email'] ?? '',
        'subject': subject,
        'message': message,
        'category': category,
        'attachmentUrls': attachmentUrls ?? [],
        'status': 'open',
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      _supportTickets = [newTicket, ..._supportTickets];

      _isLoading = false;
      notifyListeners();

      return ticketId;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  // Add a message to an existing support ticket - now just simulates the operation
  Future<bool> addTicketMessage(String ticketId, String message) async {
    if (!_authService.isAuthenticated) return false;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 500));

      // Log the message (in a real app, this would be saved to Firestore)
      debugPrint('Added message to ticket $ticketId: $message');

      // Update the ticket's updated timestamp
      final ticketIndex =
          _supportTickets.indexWhere((ticket) => ticket['id'] == ticketId);
      if (ticketIndex >= 0) {
        _supportTickets[ticketIndex] = {
          ..._supportTickets[ticketIndex],
          'updatedAt': DateTime.now().toIso8601String(),
          'status': 'open',
        };
      }

      _isLoading = false;
      notifyListeners();

      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Close a support ticket - now just simulates the operation
  Future<bool> closeTicket(String ticketId) async {
    if (!_authService.isAuthenticated) return false;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 500));

      // Update the ticket status
      final ticketIndex =
          _supportTickets.indexWhere((ticket) => ticket['id'] == ticketId);
      if (ticketIndex >= 0) {
        _supportTickets[ticketIndex] = {
          ..._supportTickets[ticketIndex],
          'status': 'closed',
          'updatedAt': DateTime.now().toIso8601String(),
        };
      }

      _isLoading = false;
      notifyListeners();

      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Reopen a support ticket - now just simulates the operation
  Future<bool> reopenTicket(String ticketId) async {
    if (!_authService.isAuthenticated) return false;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 500));

      // Update the ticket status
      final ticketIndex =
          _supportTickets.indexWhere((ticket) => ticket['id'] == ticketId);
      if (ticketIndex >= 0) {
        _supportTickets[ticketIndex] = {
          ..._supportTickets[ticketIndex],
          'status': 'open',
          'updatedAt': DateTime.now().toIso8601String(),
        };
      }

      _isLoading = false;
      notifyListeners();

      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
}
