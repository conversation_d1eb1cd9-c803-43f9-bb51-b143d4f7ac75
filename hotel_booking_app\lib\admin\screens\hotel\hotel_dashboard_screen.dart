import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'package:hotel_booking_app/admin/screens/verification/aadhaar_verification_list_screen.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/data/models/aadhaar_verification_model.dart';

class HotelDashboardScreen extends StatefulWidget {
  final String hotelId;
  final String hotelName;

  const HotelDashboardScreen({
    super.key,
    required this.hotelId,
    required this.hotelName,
  });

  @override
  State<HotelDashboardScreen> createState() => _HotelDashboardScreenState();
}

class _HotelDashboardScreenState extends State<HotelDashboardScreen> {
  bool _isLoading = true;
  Map<String, dynamic> _stats = {};
  List<Map<String, dynamic>> _todayCheckIns = [];
  List<AadhaarVerification> _recentVerifications = [];

  @override
  void initState() {
    super.initState();
    _fetchDashboardData();
  }

  Future<void> _fetchDashboardData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Fetch hotel stats
      final DateTime today = DateTime.now();
      final DateTime startOfDay = DateTime(today.year, today.month, today.day);
      final DateTime endOfDay = startOfDay.add(const Duration(days: 1));

      // Fetch total rooms
      final roomsSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.hotelsCollection)
          .doc(widget.hotelId)
          .collection(AppConstants.roomsCollection)
          .count()
          .get();

      // Fetch total bookings
      final bookingsSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.bookingsCollection)
          .where('hotelId', isEqualTo: widget.hotelId)
          .count()
          .get();

      // Fetch today's check-ins
      final checkInsSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.bookingsCollection)
          .where('hotelId', isEqualTo: widget.hotelId)
          .where('checkIn', isGreaterThanOrEqualTo: startOfDay)
          .where('checkIn', isLessThan: endOfDay)
          .get();

      // Fetch today's check-outs
      final checkOutsSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.bookingsCollection)
          .where('hotelId', isEqualTo: widget.hotelId)
          .where('checkOut', isGreaterThanOrEqualTo: startOfDay)
          .where('checkOut', isLessThan: endOfDay)
          .count()
          .get();

      // Process check-ins to get user IDs
      final List<Map<String, dynamic>> checkIns = [];
      final List<String> userIds = [];

      for (final doc in checkInsSnapshot.docs) {
        final data = doc.data();
        checkIns.add({
          'id': doc.id,
          ...data,
        });

        if (data['userId'] != null) {
          userIds.add(data['userId']);
        }
      }

      // Fetch recent Aadhaar verifications for users with bookings at this hotel
      final verificationsSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.aadhaarVerificationsCollection)
          .where('userId', whereIn: userIds.isEmpty ? [''] : userIds)
          .orderBy('verifiedAt', descending: true)
          .limit(5)
          .get();

      final List<AadhaarVerification> verifications = verificationsSnapshot.docs
          .map((doc) => AadhaarVerification.fromFirestore(doc))
          .toList();

      setState(() {
        _stats = {
          'rooms': roomsSnapshot.count,
          'bookings': bookingsSnapshot.count,
          'checkIns': checkInsSnapshot.docs.length,
          'checkOuts': checkOutsSnapshot.count,
        };
        _todayCheckIns = checkIns;
        _recentVerifications = verifications;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error fetching hotel dashboard data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.hotelName),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchDashboardData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Welcome message
                  Text(
                    'Welcome to ${widget.hotelName} Dashboard',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Here\'s what\'s happening at your hotel today',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Stats cards
                  Row(
                    children: [
                      _buildStatCard(
                        'Total Rooms',
                        _stats['rooms']?.toString() ?? '0',
                        Colors.blue,
                        Icons.hotel,
                      ),
                      const SizedBox(width: 16),
                      _buildStatCard(
                        'Total Bookings',
                        _stats['bookings']?.toString() ?? '0',
                        Colors.orange,
                        Icons.book_online,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      _buildStatCard(
                        'Today\'s Check-ins',
                        _stats['checkIns']?.toString() ?? '0',
                        Colors.green,
                        Icons.login,
                      ),
                      const SizedBox(width: 16),
                      _buildStatCard(
                        'Today\'s Check-outs',
                        _stats['checkOuts']?.toString() ?? '0',
                        Colors.purple,
                        Icons.logout,
                      ),
                    ],
                  ),
                  const SizedBox(height: 32),

                  // Today's check-ins
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Today\'s Check-ins',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          // Navigate to all check-ins
                        },
                        child: const Text('View All'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  _todayCheckIns.isEmpty
                      ? _buildEmptyState(
                          'No check-ins scheduled for today',
                          Icons.event_busy,
                        )
                      : _buildCheckInsList(),
                  const SizedBox(height: 32),

                  // Recent Aadhaar verifications
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Recent Aadhaar Verifications',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  AadhaarVerificationListScreen(
                                hotelId: widget.hotelId,
                              ),
                            ),
                          );
                        },
                        child: const Text('View All'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  _recentVerifications.isEmpty
                      ? _buildEmptyState(
                          'No recent verifications',
                          Icons.verified_user,
                        )
                      : _buildVerificationsList(),
                ],
              ),
            ),
    );
  }

  Widget _buildStatCard(
      String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                value,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCheckInsList() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _todayCheckIns.length,
        separatorBuilder: (context, index) => const Divider(),
        itemBuilder: (context, index) {
          final checkIn = _todayCheckIns[index];
          final DateTime checkInDate =
              (checkIn['checkIn'] as Timestamp).toDate();
          final DateTime checkOutDate =
              (checkIn['checkOut'] as Timestamp).toDate();

          return ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.green.shade100,
              child: const Icon(
                Icons.person,
                color: Colors.green,
              ),
            ),
            title: Text(checkIn['guestDetails']?['name'] ?? 'Guest'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Room: ${checkIn['roomName']}'),
                Text(
                  'Check-in: ${DateFormat('hh:mm a').format(checkInDate)}',
                  style: const TextStyle(color: Colors.green),
                ),
              ],
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'ID: ${checkIn['id'].substring(0, 6)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(checkIn['status']).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(checkIn['status']),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getStatusColor(checkIn['status']),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            onTap: () {
              // Navigate to check-in details
            },
          );
        },
      ),
    );
  }

  Widget _buildVerificationsList() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _recentVerifications.length,
        separatorBuilder: (context, index) => const Divider(),
        itemBuilder: (context, index) {
          final verification = _recentVerifications[index];

          return ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.blue.shade100,
              child: const Icon(
                Icons.verified_user,
                color: Colors.blue,
              ),
            ),
            title: Text(verification.fullName),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Aadhaar: ${verification.maskedAadhaarNumber}'),
                if (verification.verifiedAt != null)
                  Text(
                    'Verified: ${DateFormat('dd MMM yyyy, hh:mm a').format(verification.verifiedAt!)}',
                    style: const TextStyle(color: Colors.blue),
                  ),
              ],
            ),
            trailing: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getVerificationStatusColor(verification.status)
                    .withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _getVerificationStatusText(verification.status),
                style: TextStyle(
                  fontSize: 12,
                  color: _getVerificationStatusColor(verification.status),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            onTap: () {
              _showVerificationDetails(verification);
            },
          );
        },
      ),
    );
  }

  void _showVerificationDetails(AadhaarVerification verification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Verification Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Name', verification.fullName),
              const SizedBox(height: 8),
              _buildDetailRow('Aadhaar', verification.maskedAadhaarNumber),
              const SizedBox(height: 8),
              _buildDetailRow(
                'Status',
                _getVerificationStatusText(verification.status),
                valueColor: _getVerificationStatusColor(verification.status),
              ),
              if (verification.dateOfBirth != null) ...[
                const SizedBox(height: 8),
                _buildDetailRow(
                  'Date of Birth',
                  DateFormat('dd MMM yyyy').format(verification.dateOfBirth!),
                ),
              ],
              if (verification.gender != null) ...[
                const SizedBox(height: 8),
                _buildDetailRow('Gender', verification.gender!),
              ],
              if (verification.address != null) ...[
                const SizedBox(height: 8),
                _buildDetailRow('Address', verification.address!),
              ],
              if (verification.verifiedAt != null) ...[
                const SizedBox(height: 8),
                _buildDetailRow(
                  'Verified On',
                  DateFormat('dd MMM yyyy, hh:mm a')
                      .format(verification.verifiedAt!),
                ),
              ],
              if (verification.verificationMethod != null) ...[
                const SizedBox(height: 8),
                _buildDetailRow('Method', verification.verificationMethod!),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {Color? valueColor}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            '$label:',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(color: valueColor),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String? status) {
    switch (status) {
      case AppConstants.statusConfirmed:
        return Colors.blue;
      case AppConstants.statusCheckedIn:
        return Colors.green;
      case AppConstants.statusCheckedOut:
        return Colors.purple;
      case AppConstants.statusCancelled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String? status) {
    switch (status) {
      case AppConstants.statusPending:
        return 'Pending';
      case AppConstants.statusConfirmed:
        return 'Confirmed';
      case AppConstants.statusCheckedIn:
        return 'Checked In';
      case AppConstants.statusCheckedOut:
        return 'Checked Out';
      case AppConstants.statusCancelled:
        return 'Cancelled';
      default:
        return status ?? 'Unknown';
    }
  }

  Color _getVerificationStatusColor(AadhaarVerificationStatus status) {
    switch (status) {
      case AadhaarVerificationStatus.verified:
        return Colors.green;
      case AadhaarVerificationStatus.pending:
        return Colors.orange;
      case AadhaarVerificationStatus.failed:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getVerificationStatusText(AadhaarVerificationStatus status) {
    switch (status) {
      case AadhaarVerificationStatus.verified:
        return 'Verified';
      case AadhaarVerificationStatus.pending:
        return 'Pending';
      case AadhaarVerificationStatus.failed:
        return 'Failed';
      case AadhaarVerificationStatus.notVerified:
        return 'Not Verified';
      default:
        return 'Unknown';
    }
  }
}
