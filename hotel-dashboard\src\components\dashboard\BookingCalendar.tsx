import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Grid,
  Tooltip,
  useTheme
} from '@mui/material';
import {
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Event as EventIcon,
  Today as TodayIcon
} from '@mui/icons-material';

// Interface for booking event
interface BookingEvent {
  id: string;
  title: string;
  date: Date;
  status: 'confirmed' | 'pending' | 'cancelled' | 'checked_in' | 'checked_out';
  guestName: string;
  roomType: string;
}

interface BookingCalendarProps {
  bookings: BookingEvent[];
}

const BookingCalendar: React.FC<BookingCalendarProps> = ({ bookings }) => {
  const theme = useTheme();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [currentView, setCurrentView] = useState<'month' | 'week'>('month');

  // Get current month and year
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  // Get days in current month
  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();

  // Get first day of month (0 = Sunday, 1 = Monday, etc.)
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1).getDay();

  // Get month name
  const monthName = currentDate.toLocaleString('default', { month: 'long' });

  // Navigate to previous month
  const handlePrevMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth - 1, 1));
  };

  // Navigate to next month
  const handleNextMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth + 1, 1));
  };

  // Navigate to today
  const handleToday = () => {
    setCurrentDate(new Date());
  };

  // Toggle between month and week view
  const toggleView = () => {
    setCurrentView(currentView === 'month' ? 'week' : 'month');
  };

  // Get bookings for a specific day
  const getBookingsForDay = (day: number) => {
    const date = new Date(currentYear, currentMonth, day);
    return filteredBookings.filter(booking => {
      const bookingDate = booking.date;
      return (
        bookingDate.getDate() === date.getDate() &&
        bookingDate.getMonth() === date.getMonth() &&
        bookingDate.getFullYear() === date.getFullYear()
      );
    });
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return theme.palette.success.main;
      case 'pending':
        return theme.palette.warning.main;
      case 'cancelled':
        return theme.palette.error.main;
      case 'checked_in':
        return theme.palette.info.main;
      case 'checked_out':
        return theme.palette.primary.main;
      default:
        return theme.palette.grey[500];
    }
  };

  // Render month view
  const renderMonthView = () => {
    const days = [];
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    // Add day names
    for (let i = 0; i < 7; i++) {
      days.push(
        <Box
          key={`header-${i}`}
          sx={{
            p: 1,
            textAlign: 'center',
            fontWeight: 'bold',
            bgcolor: 'background.paper',
            borderBottom: 1,
            borderColor: 'divider'
          }}
        >
          {dayNames[i]}
        </Box>
      );
    }

    // Add empty cells for days before first day of month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(
        <Box
          key={`empty-${i}`}
          sx={{
            p: 1,
            height: 100,
            bgcolor: 'background.paper',
            border: 1,
            borderColor: 'divider'
          }}
        />
      );
    }

    // Add days of month
    for (let day = 1; day <= daysInMonth; day++) {
      const dayBookings = getBookingsForDay(day);
      const isToday =
        day === new Date().getDate() &&
        currentMonth === new Date().getMonth() &&
        currentYear === new Date().getFullYear();

      days.push(
        <Box
          key={`day-${day}`}
          sx={{
            p: 1,
            height: 100,
            bgcolor: isToday ? 'primary.light' : 'background.paper',
            border: 1,
            borderColor: 'divider',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          <Typography
            variant="body2"
            sx={{
              fontWeight: isToday ? 'bold' : 'normal',
              color: isToday ? 'primary.contrastText' : 'text.primary'
            }}
          >
            {day}
          </Typography>
          <Box sx={{ mt: 1 }}>
            {dayBookings.slice(0, 2).map((booking, index) => (
              <Box
                key={booking.id}
                sx={{
                  p: 0.5,
                  mb: 0.5,
                  borderRadius: 1,
                  bgcolor: getStatusColor(booking.status),
                  color: '#fff',
                  fontSize: '0.75rem',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}
              >
                {booking.title}
              </Box>
            ))}
            {dayBookings.length > 2 && (
              <Typography variant="caption" sx={{ display: 'block', textAlign: 'center' }}>
                +{dayBookings.length - 2} more
              </Typography>
            )}
          </Box>
        </Box>
      );
    }

    return (
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: 0 }}>
        {days}
      </Box>
    );
  };

  // Render week view
  const renderWeekView = () => {
    // Get current week start and end dates
    const currentDay = currentDate.getDay();
    const weekStart = new Date(currentDate);
    weekStart.setDate(currentDate.getDate() - currentDay);

    const weekDays = [];

    // Add days of the week
    for (let i = 0; i < 7; i++) {
      const day = new Date(weekStart);
      day.setDate(weekStart.getDate() + i);

      const dayBookings = filteredBookings.filter(booking => {
        const bookingDate = booking.date;
        return (
          bookingDate.getDate() === day.getDate() &&
          bookingDate.getMonth() === day.getMonth() &&
          bookingDate.getFullYear() === day.getFullYear()
        );
      });

      const isToday =
        day.getDate() === new Date().getDate() &&
        day.getMonth() === new Date().getMonth() &&
        day.getFullYear() === new Date().getFullYear();

      weekDays.push(
        <Box key={`week-day-${i}`} sx={{ mb: 2 }}>
          <Box
            sx={{
              p: 1,
              textAlign: 'center',
              fontWeight: 'bold',
              bgcolor: isToday ? 'primary.light' : 'background.paper',
              color: isToday ? 'primary.contrastText' : 'text.primary',
              borderRadius: '4px 4px 0 0',
              borderTop: 1,
              borderLeft: 1,
              borderRight: 1,
              borderColor: 'divider'
            }}
          >
            <Typography variant="body2">
              {day.toLocaleDateString('en-US', { weekday: 'short' })}
            </Typography>
            <Typography variant="h6">
              {day.getDate()}
            </Typography>
          </Box>
          <Box
            sx={{
              p: 1,
              height: 200,
              bgcolor: 'background.paper',
              borderBottom: 1,
              borderLeft: 1,
              borderRight: 1,
              borderColor: 'divider',
              borderRadius: '0 0 4px 4px',
              overflow: 'auto'
            }}
          >
            {dayBookings.map((booking) => (
              <Box
                key={booking.id}
                sx={{
                  p: 1,
                  mb: 1,
                  borderRadius: 1,
                  bgcolor: getStatusColor(booking.status),
                  color: '#fff',
                  fontSize: '0.75rem'
                }}
              >
                <Typography variant="caption" sx={{ display: 'block', fontWeight: 'bold' }}>
                  {booking.title}
                </Typography>
                <Typography variant="caption" sx={{ display: 'block' }}>
                  {booking.guestName} - {booking.roomType}
                </Typography>
              </Box>
            ))}
            {dayBookings.length === 0 && (
              <Typography variant="caption" sx={{ display: 'block', textAlign: 'center', color: 'text.secondary', mt: 2 }}>
                No bookings
              </Typography>
            )}
          </Box>
        </Box>
      );
    }

    return (
      <Grid container spacing={1}>
        {weekDays.map((day, index) => (
          <Grid item key={`grid-day-${index}`} xs={12} sm={6} md={3} lg={12/7}>
            {day}
          </Grid>
        ))}
      </Grid>
    );
  };

  // State for search
  const [searchQuery, setSearchQuery] = useState('');

  // Filter bookings based on search query
  const filteredBookings = searchQuery.trim() === ''
    ? bookings
    : bookings.filter(booking =>
        booking.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.guestName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        booking.roomType.toLowerCase().includes(searchQuery.toLowerCase())
      );

  return (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Booking Calendar</Typography>
        <Box>
          <Tooltip title="Today">
            <IconButton size="small" onClick={handleToday}>
              <TodayIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title={currentView === 'month' ? 'Week View' : 'Month View'}>
            <IconButton size="small" onClick={toggleView}>
              <EventIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Search box temporarily removed due to TextField issue */}
      <Box sx={{ mb: 2 }}>
        <input
          type="text"
          placeholder="Search bookings..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          style={{
            width: '100%',
            padding: '8px 12px',
            border: '1px solid #ccc',
            borderRadius: '4px'
          }}
        />
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <IconButton onClick={handlePrevMonth}>
          <ChevronLeftIcon />
        </IconButton>
        <Typography variant="h6">
          {monthName} {currentYear}
        </Typography>
        <IconButton onClick={handleNextMonth}>
          <ChevronRightIcon />
        </IconButton>
      </Box>

      {currentView === 'month' ? renderMonthView() : renderWeekView()}
    </Paper>
  );
};

export default BookingCalendar;
