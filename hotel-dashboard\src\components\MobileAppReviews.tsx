import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Rating,
  Chip,
  IconButton,
  Button,
  CircularProgress,
  Alert,
  Tooltip,
  Badge,
  Divider,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Comment as CommentIcon,
  PhoneAndroid as PhoneIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Reply as ReplyIcon
} from '@mui/icons-material';
import { getHotelReviews, submitHotelReview, MobileReview } from '../services/mobileAppService';
import { formatDateDDMMMYYYY } from '../utils/bookingUtils';

interface MobileAppReviewsProps {
  hotelId: string;
  vendorId: string;
  limit?: number;
}

const MobileAppReviews: React.FC<MobileAppReviewsProps> = ({
  hotelId,
  vendorId,
  limit = 5
}) => {
  const [reviews, setReviews] = useState<MobileReview[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pendingReviewsCount, setPendingReviewsCount] = useState(0);
  
  // Reply dialog state
  const [replyDialogOpen, setReplyDialogOpen] = useState(false);
  const [selectedReview, setSelectedReview] = useState<MobileReview | null>(null);
  const [replyText, setReplyText] = useState('');
  const [replying, setReplying] = useState(false);

  // Fetch reviews on component mount and when hotelId changes
  useEffect(() => {
    if (hotelId) {
      fetchReviews();
    }
  }, [hotelId]);

  // Fetch hotel reviews
  const fetchReviews = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get all reviews (including pending ones)
      const allReviews = await getHotelReviews(hotelId, false);
      
      // Count pending reviews
      const pendingReviews = allReviews.filter(review => review.status === 'pending');
      setPendingReviewsCount(pendingReviews.length);
      
      // Sort reviews by date (newest first)
      const sortedReviews = allReviews.sort((a, b) => {
        if (!a.createdAt || !b.createdAt) return 0;
        return b.createdAt.toDate().getTime() - a.createdAt.toDate().getTime();
      });
      
      // Limit the number of reviews to display
      setReviews(sortedReviews.slice(0, limit));
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching reviews:', err);
      setError(err.message || 'Failed to load reviews');
      setLoading(false);
    }
  };

  // Handle approve review
  const handleApproveReview = async (review: MobileReview) => {
    try {
      if (!review.id) return;
      
      // Update review status to approved
      await updateReviewStatus(review.id, 'approved');
      
      // Refresh reviews
      fetchReviews();
    } catch (err: any) {
      console.error('Error approving review:', err);
      setError(err.message || 'Failed to approve review');
    }
  };

  // Handle reject review
  const handleRejectReview = async (review: MobileReview) => {
    try {
      if (!review.id) return;
      
      // Update review status to rejected
      await updateReviewStatus(review.id, 'rejected');
      
      // Refresh reviews
      fetchReviews();
    } catch (err: any) {
      console.error('Error rejecting review:', err);
      setError(err.message || 'Failed to reject review');
    }
  };

  // Update review status
  const updateReviewStatus = async (reviewId: string, status: 'approved' | 'rejected') => {
    // This would be implemented in the mobileAppService
    // For now, we'll just simulate it
    console.log(`Updating review ${reviewId} status to ${status}`);
    
    // In a real implementation, you would call a function like:
    // await updateReviewStatus(reviewId, status);
  };

  // Open reply dialog
  const handleOpenReplyDialog = (review: MobileReview) => {
    setSelectedReview(review);
    setReplyText(review.vendorResponse?.comment || '');
    setReplyDialogOpen(true);
  };

  // Close reply dialog
  const handleCloseReplyDialog = () => {
    setReplyDialogOpen(false);
    setSelectedReview(null);
    setReplyText('');
  };

  // Submit reply
  const handleSubmitReply = async () => {
    try {
      if (!selectedReview || !selectedReview.id) return;
      
      setReplying(true);
      
      // This would be implemented in the mobileAppService
      // For now, we'll just simulate it
      console.log(`Submitting reply to review ${selectedReview.id}: ${replyText}`);
      
      // In a real implementation, you would call a function like:
      // await submitReviewResponse(selectedReview.id, replyText, vendorId);
      
      setReplying(false);
      handleCloseReplyDialog();
      
      // Refresh reviews
      fetchReviews();
    } catch (err: any) {
      console.error('Error submitting reply:', err);
      setError(err.message || 'Failed to submit reply');
      setReplying(false);
    }
  };

  // Get review status chip
  const getStatusChip = (status: string) => {
    switch (status) {
      case 'approved':
        return <Chip label="Approved" color="success" size="small" />;
      case 'rejected':
        return <Chip label="Rejected" color="error" size="small" />;
      case 'pending':
        return <Chip label="Pending" color="warning" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  return (
    <Paper sx={{ p: 3, height: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <PhoneIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6">
            Mobile App Reviews
            {pendingReviewsCount > 0 && (
              <Badge
                badgeContent={pendingReviewsCount}
                color="error"
                sx={{ ml: 1 }}
              />
            )}
          </Typography>
        </Box>
        <Button
          startIcon={<RefreshIcon />}
          size="small"
          onClick={fetchReviews}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>
      
      <Divider sx={{ mb: 2 }} />

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : reviews.length === 0 ? (
        <Alert severity="info">
          No reviews found for this hotel.
        </Alert>
      ) : (
        <List>
          {reviews.map((review) => (
            <ListItem
              key={review.id}
              alignItems="flex-start"
              sx={{
                mb: 2,
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1,
                p: 2
              }}
            >
              <ListItemAvatar>
                <Avatar>
                  <CommentIcon />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Typography variant="subtitle1" component="span">
                        {review.title || 'Review'}
                      </Typography>
                      <Rating value={review.rating} readOnly size="small" sx={{ ml: 1 }} />
                    </Box>
                    {getStatusChip(review.status)}
                  </Box>
                }
                secondary={
                  <React.Fragment>
                    <Typography
                      variant="body2"
                      color="text.primary"
                      sx={{ mt: 1 }}
                    >
                      {review.comment}
                    </Typography>
                    
                    {review.categories && (
                      <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {Object.entries(review.categories).map(([category, rating]) => (
                          <Chip
                            key={category}
                            label={`${category}: ${rating}/5`}
                            size="small"
                            variant="outlined"
                          />
                        ))}
                      </Box>
                    )}
                    
                    {review.vendorResponse && (
                      <Box sx={{ mt: 2, ml: 2, p: 1, bgcolor: 'action.hover', borderRadius: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                          <strong>Your response:</strong> {review.vendorResponse.comment}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {review.vendorResponse.timestamp && 
                            formatDateDDMMMYYYY(review.vendorResponse.timestamp.toDate())}
                        </Typography>
                      </Box>
                    )}
                    
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                      {review.createdAt && formatDateDDMMMYYYY(review.createdAt.toDate())}
                    </Typography>
                    
                    <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end' }}>
                      {review.status === 'pending' && (
                        <>
                          <Tooltip title="Approve">
                            <IconButton
                              size="small"
                              color="success"
                              onClick={() => handleApproveReview(review)}
                            >
                              <CheckCircleIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Reject">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleRejectReview(review)}
                            >
                              <CancelIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </>
                      )}
                      <Tooltip title="Reply">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleOpenReplyDialog(review)}
                        >
                          <ReplyIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </React.Fragment>
                }
              />
            </ListItem>
          ))}
        </List>
      )}
      
      {reviews.length > 0 && (
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
          <Button
            variant="outlined"
            size="small"
          >
            View All Reviews
          </Button>
        </Box>
      )}
      
      {/* Reply Dialog */}
      <Dialog
        open={replyDialogOpen}
        onClose={handleCloseReplyDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Reply to Review</DialogTitle>
        <DialogContent>
          {selectedReview && (
            <>
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1">
                  Original Review:
                </Typography>
                <Box sx={{ p: 2, bgcolor: 'action.hover', borderRadius: 1 }}>
                  <Rating value={selectedReview.rating} readOnly size="small" />
                  <Typography variant="body1" sx={{ mt: 1 }}>
                    {selectedReview.title}
                  </Typography>
                  <Typography variant="body2">
                    {selectedReview.comment}
                  </Typography>
                </Box>
              </Box>
              
              <TextField
                fullWidth
                multiline
                rows={4}
                label="Your Reply"
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
                placeholder="Thank the guest for their feedback and address their comments..."
              />
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseReplyDialog}>Cancel</Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmitReply}
            disabled={!replyText.trim() || replying}
          >
            {replying ? 'Submitting...' : 'Submit Reply'}
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default MobileAppReviews;
