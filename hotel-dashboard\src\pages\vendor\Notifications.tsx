import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  IconButton,
  Divider,
  Toolt<PERSON>,
  Badge
} from '@mui/material';
import {
  Add as AddIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  CheckCircle as ReadIcon,
  MarkChatRead as MarkReadIcon,
  RoomService as RoomServiceIcon,
  BookOnline as BookingIcon,
  VerifiedUser as VerificationIcon,
  Info as SystemIcon,
  Notifications as NotificationsIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { auth } from '../../firebase/config';
import { getHotelsForVendor, HotelData } from '../../firebase/hotelService';
import NotificationCampaignsList from '../../components/NotificationCampaignsList';
import NotificationCampaignForm from '../../components/NotificationCampaignForm';
import {
  getVendorNotifications,
  markVendorNotificationAsRead,
  markAllVendorNotificationsAsRead,
  deleteVendorNotification,
  VendorNotification,
  NOTIFICATION_TYPE_SERVICE_REQUEST,
  NOTIFICATION_TYPE_BOOKING,
  NOTIFICATION_TYPE_VERIFICATION,
  NOTIFICATION_TYPE_SYSTEM,
  NOTIFICATION_STATUS_UNREAD
} from '../../services/vendorNotificationService';

// Define TabPanel component for tab content
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`notification-tabpanel-${index}`}
      aria-labelledby={`notification-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const Notifications: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hotels, setHotels] = useState<HotelData[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Vendor notifications state
  const [vendorNotifications, setVendorNotifications] = useState<VendorNotification[]>([]);
  const [notificationsLoading, setNotificationsLoading] = useState(false);
  const [notificationsError, setNotificationsError] = useState<string | null>(null);
  const [notificationTypeFilter, setNotificationTypeFilter] = useState<string>('all');
  const [markingAllAsRead, setMarkingAllAsRead] = useState(false);

  // Load data on component mount
  useEffect(() => {
    fetchHotels();
  }, []);

  // Fetch vendor notifications when hotel is selected or tab changes
  useEffect(() => {
    if (tabValue === 0 && selectedHotel) {
      fetchVendorNotifications();
    }
  }, [tabValue, selectedHotel, notificationTypeFilter]);

  // Fetch hotels
  const fetchHotels = async () => {
    try {
      setLoading(true);
      if (!auth.currentUser) return;

      const hotelsList = await getHotelsForVendor(auth.currentUser.uid);
      setHotels(hotelsList);

      if (hotelsList.length > 0) {
        setSelectedHotel(hotelsList[0].id || '');
      }

      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching hotels:', err);
      setError('Failed to load hotels');
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle hotel change
  const handleHotelChange = (event: any) => {
    setSelectedHotel(event.target.value);
  };

  // Handle create new campaign
  const handleCreateNew = () => {
    setShowCreateForm(true);
  };

  // Handle campaign created
  const handleCampaignCreated = (campaignId: string) => {
    setShowCreateForm(false);
    // Optionally, you could switch to the campaigns tab
    setTabValue(0);
  };

  // Handle cancel create
  const handleCancelCreate = () => {
    setShowCreateForm(false);
  };

  // Fetch vendor notifications
  const fetchVendorNotifications = async () => {
    try {
      setNotificationsLoading(true);
      setNotificationsError(null);

      if (!auth.currentUser) return;

      const options: any = {
        hotelId: selectedHotel
      };

      // Apply type filter if not 'all'
      if (notificationTypeFilter !== 'all') {
        options.type = notificationTypeFilter;
      }

      const notifications = await getVendorNotifications(auth.currentUser.uid, options);
      setVendorNotifications(notifications);
    } catch (error: any) {
      console.error('Error fetching vendor notifications:', error);
      setNotificationsError(error.message || 'Failed to load notifications');
    } finally {
      setNotificationsLoading(false);
    }
  };

  // Handle notification type filter change
  const handleNotificationTypeFilterChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNotificationTypeFilter(event.target.value);
  };

  // Mark notification as read
  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markVendorNotificationAsRead(notificationId);

      // Update local state
      setVendorNotifications(prevNotifications =>
        prevNotifications.map(n =>
          n.id === notificationId ? { ...n, status: 'read', readAt: new Date() as any } : n
        )
      );
    } catch (error) {
      console.error('Error marking notification as read:', error);
      setNotificationsError('Failed to mark notification as read');
    }
  };

  // Mark all notifications as read
  const handleMarkAllAsRead = async () => {
    try {
      setMarkingAllAsRead(true);

      if (!auth.currentUser) return;

      await markAllVendorNotificationsAsRead(auth.currentUser.uid, selectedHotel);

      // Update local state
      setVendorNotifications(prevNotifications =>
        prevNotifications.map(n => ({
          ...n,
          status: 'read',
          readAt: n.readAt || new Date() as any
        }))
      );
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      setNotificationsError('Failed to mark all notifications as read');
    } finally {
      setMarkingAllAsRead(false);
    }
  };

  // Delete notification
  const handleDeleteNotification = async (notificationId: string) => {
    try {
      await deleteVendorNotification(notificationId);

      // Update local state
      setVendorNotifications(prevNotifications =>
        prevNotifications.filter(n => n.id !== notificationId)
      );
    } catch (error) {
      console.error('Error deleting notification:', error);
      setNotificationsError('Failed to delete notification');
    }
  };

  // Get notification icon
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case NOTIFICATION_TYPE_SERVICE_REQUEST:
        return <RoomServiceIcon color="primary" />;
      case NOTIFICATION_TYPE_BOOKING:
        return <BookingIcon color="info" />;
      case NOTIFICATION_TYPE_VERIFICATION:
        return <VerificationIcon color="success" />;
      case NOTIFICATION_TYPE_SYSTEM:
        return <SystemIcon color="warning" />;
      default:
        return <NotificationsIcon />;
    }
  };

  // Get priority chip
  const getPriorityChip = (priority?: string) => {
    if (!priority) return null;

    switch (priority) {
      case 'high':
        return <Chip label="High" color="error" size="small" sx={{ ml: 1 }} />;
      case 'medium':
        return <Chip label="Medium" color="warning" size="small" sx={{ ml: 1 }} />;
      case 'low':
        return <Chip label="Low" color="info" size="small" sx={{ ml: 1 }} />;
      default:
        return null;
    }
  };

  // Format date
  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';

    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleString();
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Notifications
        </Typography>
        {!showCreateForm && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateNew}
            disabled={!selectedHotel}
          >
            New Campaign
          </Button>
        )}
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Hotel selector */}
      {hotels.length > 0 && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <FormControl fullWidth>
            <InputLabel id="hotel-select-label">Select Hotel</InputLabel>
            <Select
              labelId="hotel-select-label"
              value={selectedHotel}
              label="Select Hotel"
              onChange={handleHotelChange}
            >
              {hotels.map((hotel) => (
                <MenuItem key={hotel.id} value={hotel.id}>
                  {hotel.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Paper>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : hotels.length === 0 ? (
        <Alert severity="info">
          You need to create at least one hotel before you can manage notifications.
        </Alert>
      ) : showCreateForm ? (
        // Show create form
        <NotificationCampaignForm
          hotelId={selectedHotel}
          userId={auth.currentUser?.uid || ''}
          onSuccess={handleCampaignCreated}
          onCancel={handleCancelCreate}
        />
      ) : (
        // Show tabs
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <span>Notifications</span>
                {vendorNotifications.filter(n => n.status === NOTIFICATION_STATUS_UNREAD).length > 0 && (
                  <Badge
                    color="error"
                    badgeContent={vendorNotifications.filter(n => n.status === NOTIFICATION_STATUS_UNREAD).length}
                    sx={{ ml: 1 }}
                  />
                )}
              </Box>
            } />
            <Tab label="Campaigns" />
            <Tab label="Templates" />
            <Tab label="Settings" />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">
                Notifications for Your Hotel
              </Typography>
              <Box>
                {vendorNotifications.filter(n => n.status === NOTIFICATION_STATUS_UNREAD).length > 0 && (
                  <Button
                    variant="outlined"
                    startIcon={<MarkReadIcon />}
                    onClick={handleMarkAllAsRead}
                    disabled={markingAllAsRead}
                    sx={{ mr: 2 }}
                  >
                    Mark All as Read
                  </Button>
                )}
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={fetchVendorNotifications}
                  disabled={notificationsLoading}
                >
                  Refresh
                </Button>
              </Box>
            </Box>

            {/* Notification type filter */}
            <Paper sx={{ p: 2, mb: 3 }}>
              <FormControl fullWidth>
                <InputLabel id="notification-type-filter-label">Filter by Type</InputLabel>
                <Select
                  labelId="notification-type-filter-label"
                  value={notificationTypeFilter}
                  label="Filter by Type"
                  onChange={handleNotificationTypeFilterChange as any}
                  startAdornment={
                    <FilterIcon sx={{ mr: 1, color: 'action.active' }} />
                  }
                >
                  <MenuItem value="all">All Notifications</MenuItem>
                  <MenuItem value={NOTIFICATION_TYPE_SERVICE_REQUEST}>Service Requests</MenuItem>
                  <MenuItem value={NOTIFICATION_TYPE_BOOKING}>Bookings</MenuItem>
                  <MenuItem value={NOTIFICATION_TYPE_VERIFICATION}>Verifications</MenuItem>
                  <MenuItem value={NOTIFICATION_TYPE_SYSTEM}>System</MenuItem>
                </Select>
              </FormControl>
            </Paper>

            {notificationsError && (
              <Alert severity="error" sx={{ mb: 3 }} onClose={() => setNotificationsError(null)}>
                {notificationsError}
              </Alert>
            )}

            {notificationsLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
                <CircularProgress />
              </Box>
            ) : vendorNotifications.length === 0 ? (
              <Paper sx={{ p: 5, textAlign: 'center' }}>
                <NotificationsIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  No notifications found
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {notificationTypeFilter === 'all'
                    ? 'You don\'t have any notifications yet.'
                    : `You don't have any ${notificationTypeFilter.replace('_', ' ')} notifications.`}
                </Typography>
              </Paper>
            ) : (
              <Paper>
                <List sx={{ width: '100%', bgcolor: 'background.paper' }}>
                  {vendorNotifications.map((notification) => (
                    <React.Fragment key={notification.id}>
                      <ListItem
                        alignItems="flex-start"
                        sx={{
                          bgcolor: notification.status === NOTIFICATION_STATUS_UNREAD ? 'action.hover' : 'inherit',
                          transition: 'background-color 0.3s'
                        }}
                        secondaryAction={
                          <Box>
                            {notification.status === NOTIFICATION_STATUS_UNREAD && (
                              <Tooltip title="Mark as read">
                                <IconButton
                                  edge="end"
                                  onClick={() => notification.id && handleMarkAsRead(notification.id)}
                                  sx={{ mr: 1 }}
                                >
                                  <ReadIcon />
                                </IconButton>
                              </Tooltip>
                            )}
                            <Tooltip title="Delete">
                              <IconButton
                                edge="end"
                                onClick={() => notification.id && handleDeleteNotification(notification.id)}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        }
                      >
                        <ListItemAvatar>
                          <Avatar>
                            {getNotificationIcon(notification.type)}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Typography variant="subtitle1" component="span" fontWeight={notification.status === NOTIFICATION_STATUS_UNREAD ? 'bold' : 'normal'}>
                                {notification.title}
                              </Typography>
                              {getPriorityChip(notification.priority)}
                            </Box>
                          }
                          secondary={
                            <>
                              <Typography
                                sx={{ display: 'block' }}
                                component="span"
                                variant="body2"
                                color="text.primary"
                              >
                                {notification.message}
                              </Typography>
                              <Typography
                                sx={{ display: 'block', mt: 1 }}
                                component="span"
                                variant="caption"
                                color="text.secondary"
                              >
                                {formatDate(notification.createdAt)}
                              </Typography>
                              {notification.actions && notification.actions.length > 0 && (
                                <Box sx={{ mt: 1 }}>
                                  {notification.actions.map((action, index) => (
                                    <Button
                                      key={index}
                                      size="small"
                                      variant="outlined"
                                      onClick={() => {
                                        if (notification.id && notification.status === NOTIFICATION_STATUS_UNREAD) {
                                          handleMarkAsRead(notification.id);
                                        }
                                        window.location.href = action.url;
                                      }}
                                      sx={{ mr: 1, mt: 1 }}
                                    >
                                      {action.label}
                                    </Button>
                                  ))}
                                </Box>
                              )}
                            </>
                          }
                        />
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))}
                </List>
              </Paper>
            )}
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <NotificationCampaignsList
              hotelId={selectedHotel}
              onCreateNew={handleCreateNew}
            />
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Typography variant="h6" gutterBottom>
              Notification Templates
            </Typography>
            <Typography variant="body1">
              Create and manage notification templates to use in your campaigns.
            </Typography>
            <Alert severity="info" sx={{ mt: 3 }}>
              Notification templates feature will be implemented in the next phase.
            </Alert>
          </TabPanel>

          <TabPanel value={tabValue} index={3}>
            <Typography variant="h6" gutterBottom>
              Notification Settings
            </Typography>
            <Typography variant="body1">
              Configure notification settings for your hotel.
            </Typography>
            <Alert severity="info" sx={{ mt: 3 }}>
              Notification settings feature will be implemented in the next phase.
            </Alert>
          </TabPanel>
        </Paper>
      )}
    </Box>
  );
};

export default Notifications;
