import {
  createUserWithEmailAndPassword,
  sendPasswordResetEmail,
  getAuth
} from 'firebase/auth';
import {
  doc,
  setDoc,
  collection,
  query,
  where,
  getDocs,
  updateDoc,
  deleteDoc,
  serverTimestamp,
  getDoc
} from 'firebase/firestore';
import { db } from './config';
import { ROLE_SUPER_ADMIN, ROLE_VENDOR, ROLE_STAFF } from './auth';

// Interface for user data
export interface UserData {
  id?: string;
  email: string;
  displayName: string;
  role: string;
  phone?: string;
  address?: string;
  createdBy?: string;
  vendorId?: string; // For staff members, references their vendor
  hotelId?: string;  // For staff members, references their hotel
  createdAt?: any;
  updatedAt?: any;
}

/**
 * Create a new user with the specified role
 * This function is called by the admin panel when creating new vendors or staff
 */
export const createUser = async (userData: UserData, password: string, currentUser: any): Promise<string> => {
  try {
    // Generate a temporary password if none provided
    const tempPassword = password || Math.random().toString(36).slice(-8);

    // Create the user in Firebase Authentication
    const auth = getAuth();
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      userData.email,
      tempPassword
    );

    const uid = userCredential.user.uid;

    // Add additional user data to Firestore
    await setDoc(doc(db, 'users', uid), {
      email: userData.email,
      displayName: userData.displayName,
      role: userData.role,
      phone: userData.phone || '',
      address: userData.address || '',
      createdBy: currentUser.uid,
      vendorId: userData.role === ROLE_STAFF ? userData.vendorId : (userData.role === ROLE_VENDOR ? uid : null),
      hotelId: userData.role === ROLE_STAFF ? userData.hotelId : null,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    // If a temporary password was generated, send a password reset email
    if (!password) {
      await sendPasswordResetEmail(auth, userData.email);
    }

    return uid;
  } catch (error: any) {
    console.error('Error creating user:', error);

    // Check for specific Firebase error codes
    if (error.code === 'auth/email-already-in-use') {
      throw new Error('This email is already registered. Please use a different email address.');
    } else if (error.code === 'auth/invalid-email') {
      throw new Error('The email address is not valid. Please check and try again.');
    } else if (error.code === 'auth/weak-password') {
      throw new Error('The password is too weak. Please use a stronger password.');
    } else {
      throw new Error(`Failed to create user: ${error.message}`);
    }
  }
};

/**
 * Get all vendors (for Super Admin)
 */
export const getVendors = async () => {
  try {
    const vendorsQuery = query(
      collection(db, 'users'),
      where('role', '==', ROLE_VENDOR)
    );

    const vendorsSnapshot = await getDocs(vendorsQuery);
    const vendors: UserData[] = [];

    vendorsSnapshot.forEach((doc) => {
      vendors.push({ id: doc.id, ...doc.data() } as UserData);
    });

    return vendors;
  } catch (error) {
    console.error('Error getting vendors:', error);
    throw error;
  }
};

/**
 * Get all staff for a specific vendor
 */
export const getStaffForVendor = async (vendorId: string) => {
  try {
    const staffQuery = query(
      collection(db, 'users'),
      where('role', '==', ROLE_STAFF),
      where('vendorId', '==', vendorId)
    );

    const staffSnapshot = await getDocs(staffQuery);
    const staff: UserData[] = [];

    staffSnapshot.forEach((doc) => {
      staff.push({ id: doc.id, ...doc.data() } as UserData);
    });

    return staff;
  } catch (error) {
    console.error('Error getting staff:', error);
    throw error;
  }
};

/**
 * Update user data
 */
export const updateUser = async (userId: string, userData: Partial<UserData>) => {
  try {
    const userRef = doc(db, 'users', userId);

    await updateDoc(userRef, {
      ...userData,
      updatedAt: serverTimestamp(),
    });

    return true;
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
};

/**
 * Delete a user
 */
export const deleteUser = async (userId: string) => {
  try {
    await deleteDoc(doc(db, 'users', userId));
    // Note: This doesn't delete the user from Firebase Authentication
    // In a production app, you would use Firebase Admin SDK or Cloud Functions
    return true;
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
};

/**
 * Get all staff (for Super Admin)
 */
export const getStaff = async () => {
  try {
    const staffQuery = query(
      collection(db, 'users'),
      where('role', '==', ROLE_STAFF)
    );

    const staffSnapshot = await getDocs(staffQuery);
    const staff: UserData[] = [];

    staffSnapshot.forEach((doc) => {
      staff.push({ id: doc.id, ...doc.data() } as UserData);
    });

    return staff;
  } catch (error) {
    console.error('Error getting staff:', error);
    throw error;
  }
};

/**
 * Get all users (for Super Admin)
 */
export const getAllUsers = async () => {
  try {
    // First try to get real users from Firestore
    try {
      const usersSnapshot = await getDocs(collection(db, 'users'));
      const users: UserData[] = [];

      usersSnapshot.forEach((doc) => {
        try {
          const data = doc.data();
          users.push({
            id: doc.id,
            ...data,
            // Ensure these fields have default values if missing
            email: data.email || '',
            displayName: data.displayName || 'Unknown User',
            role: data.role || 'user',
          } as UserData);
        } catch (err) {
          console.error('Error processing user document:', err);
        }
      });

      // If we have real users, return them
      if (users.length > 0) {
        return users;
      }
    } catch (err) {
      console.error('Error fetching real users:', err);
    }

    // If no real users or error occurred, return sample data
    console.log('Returning sample user data');

    return [
      {
        id: 'sample-user-1',
        email: '<EMAIL>',
        displayName: 'John Doe',
        role: 'user',
        phone: '+1234567890',
        address: '123 Main St, New York, NY',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      },
      {
        id: 'sample-user-2',
        email: '<EMAIL>',
        displayName: 'Jane Smith',
        role: 'user',
        phone: '+1987654321',
        address: '456 Park Ave, Los Angeles, CA',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      },
      {
        id: 'sample-user-3',
        email: '<EMAIL>',
        displayName: 'Robert Johnson',
        role: 'user',
        phone: '+1122334455',
        address: '789 Broadway, Chicago, IL',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      },
      {
        id: 'sample-user-4',
        email: '<EMAIL>',
        displayName: 'Emily Davis',
        role: 'user',
        phone: '+1567891234',
        address: '321 Oak St, Miami, FL',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      },
      {
        id: 'sample-user-5',
        email: '<EMAIL>',
        displayName: 'Michael Wilson',
        role: 'user',
        phone: '+1654987321',
        address: '654 Pine St, Seattle, WA',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      }
    ];
  } catch (error) {
    console.error('Error getting all users:', error);
    // Return empty array instead of throwing
    return [];
  }
};
