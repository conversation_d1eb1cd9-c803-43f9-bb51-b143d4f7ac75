import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Menu,
  MenuItem,
  Popover,
  Tooltip,
  Typography
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  RoomService as RoomServiceIcon,
  CleaningServices as CleaningIcon,
  Build as BuildIcon,
  Restaurant as RestaurantIcon,
  MoreVert as MoreVertIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  AccessTime as AccessTimeIcon
} from '@mui/icons-material';
import { collection, query, where, orderBy, onSnapshot, updateDoc, doc } from 'firebase/firestore';
import { db } from '../../firebase/config';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  read: boolean;
  hotelId: string;
  actionUrl?: string;
  createdAt: any;
}

const ServiceRequestNotification: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);
  const { currentUser } = useAuth();
  const navigate = useNavigate();

  const open = Boolean(anchorEl);
  const menuOpen = Boolean(menuAnchorEl);

  useEffect(() => {
    if (!currentUser) return;

    // Query notifications for the current vendor
    const notificationsQuery = query(
      collection(db, 'mobileNotifications'),
      where('userId', '==', currentUser.uid),
      where('type', '==', 'serviceRequest'),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(notificationsQuery, (snapshot) => {
      const notificationsList: Notification[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        notificationsList.push({
          id: doc.id,
          title: data.title || 'Service Request',
          message: data.message || '',
          type: data.type || 'serviceRequest',
          read: data.read || false,
          hotelId: data.hotelId || '',
          actionUrl: data.actionUrl || '',
          createdAt: data.createdAt,
        });
      });

      setNotifications(notificationsList);
      setUnreadCount(notificationsList.filter(n => !n.read).length);
    });

    return () => unsubscribe();
  }, [currentUser]);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, notification: Notification) => {
    event.stopPropagation();
    setSelectedNotification(notification);
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleNotificationClick = async (notification: Notification) => {
    // Mark as read
    if (!notification.read) {
      await updateDoc(doc(db, 'mobileNotifications', notification.id), {
        read: true
      });
    }

    // Navigate to the action URL if provided
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }

    handleClose();
  };

  const markAsRead = async (notification: Notification) => {
    await updateDoc(doc(db, 'mobileNotifications', notification.id), {
      read: true
    });
    handleMenuClose();
  };

  const markAllAsRead = async () => {
    const promises = notifications
      .filter(n => !n.read)
      .map(n => updateDoc(doc(db, 'mobileNotifications', n.id), { read: true }));

    await Promise.all(promises);
    handleMenuClose();
  };

  const getNotificationIcon = (type: string) => {
    if (type.includes('food')) {
      return <RestaurantIcon color="primary" />;
    } else if (type.includes('cleaning')) {
      return <CleaningIcon color="secondary" />;
    } else if (type.includes('maintenance')) {
      return <BuildIcon color="warning" />;
    } else {
      return <RoomServiceIcon color="info" />;
    }
  };

  const getTimeAgo = (timestamp: any) => {
    if (!timestamp) return 'Just now';

    try {
      const date = timestamp.toDate();
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
      return 'Just now';
    }
  };

  return (
    <>
      <Tooltip title="Service Requests">
        <IconButton
          color="inherit"
          onClick={handleClick}
          sx={{ ml: 1 }}
        >
          <Badge badgeContent={unreadCount} color="error">
            <RoomServiceIcon />
          </Badge>
        </IconButton>
      </Tooltip>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <Card sx={{ width: 360, maxHeight: 480 }}>
          <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Service Requests</Typography>
            {unreadCount > 0 && (
              <Button size="small" onClick={markAllAsRead}>
                Mark all as read
              </Button>
            )}
          </Box>
          <Divider />

          {notifications.length === 0 ? (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Typography color="textSecondary">No service requests</Typography>
            </Box>
          ) : (
            <List sx={{ maxHeight: 360, overflow: 'auto' }}>
              {notifications.map((notification) => (
                <ListItem
                  key={notification.id}
                  alignItems="flex-start"
                  sx={{
                    bgcolor: notification.read ? 'transparent' : 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' },
                    cursor: 'pointer',
                  }}
                  onClick={() => handleNotificationClick(notification)}
                  secondaryAction={
                    <IconButton edge="end" onClick={(e) => handleMenuClick(e, notification)}>
                      <MoreVertIcon />
                    </IconButton>
                  }
                >
                  <ListItemAvatar>
                    {getNotificationIcon(notification.title.toLowerCase())}
                  </ListItemAvatar>
                  <ListItemText
                    primary={notification.title}
                    secondary={
                      <>
                        <Typography
                          component="span"
                          variant="body2"
                          color="textPrimary"
                          sx={{ display: 'block' }}
                        >
                          {notification.message}
                        </Typography>
                        <Typography
                          component="span"
                          variant="caption"
                          color="textSecondary"
                        >
                          {getTimeAgo(notification.createdAt)}
                        </Typography>
                      </>
                    }
                  />
                </ListItem>
              ))}
            </List>
          )}

          <Divider />
          <CardContent>
            <Button
              fullWidth
              variant="text"
              onClick={() => {
                navigate('/vendor/services');
                handleClose();
              }}
            >
              View All Service Requests
            </Button>
          </CardContent>
        </Card>
      </Popover>

      <Menu
        anchorEl={menuAnchorEl}
        open={menuOpen}
        onClose={handleMenuClose}
      >
        {selectedNotification && !selectedNotification.read && (
          <MenuItem onClick={() => markAsRead(selectedNotification)}>
            <CheckCircleIcon fontSize="small" sx={{ mr: 1 }} />
            Mark as read
          </MenuItem>
        )}
        <MenuItem onClick={handleMenuClose}>
          <AccessTimeIcon fontSize="small" sx={{ mr: 1 }} />
          Snooze notification
        </MenuItem>
      </Menu>
    </>
  );
};

export default ServiceRequestNotification;
