import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit as firestoreLimit,
  Timestamp,
  serverTimestamp,
  DocumentSnapshot,
  writeBatch
} from 'firebase/firestore';
import { db } from '../firebase/config';

// Collection name
const VENDOR_NOTIFICATIONS_COLLECTION = 'vendorNotifications';

// Notification types
export const NOTIFICATION_TYPE_SERVICE_REQUEST = 'service_request';
export const NOTIFICATION_TYPE_BOOKING = 'booking';
export const NOTIFICATION_TYPE_VERIFICATION = 'verification';
export const NOTIFICATION_TYPE_SYSTEM = 'system';

// Notification status
export const NOTIFICATION_STATUS_UNREAD = 'unread';
export const NOTIFICATION_STATUS_READ = 'read';

// Notification model
export interface VendorNotification {
  id?: string;
  title: string;
  message: string;
  type: string;
  status: string;
  vendorId: string;
  hotelId: string;
  relatedId?: string; // ID of related entity (booking, service request, etc.)
  relatedType?: string; // Type of related entity
  createdAt: Timestamp;
  readAt?: Timestamp;
  priority?: 'low' | 'medium' | 'high';
  actions?: {
    label: string;
    url: string;
  }[];
}

/**
 * Create a new vendor notification
 */
export const createVendorNotification = async (
  notificationData: Omit<VendorNotification, 'createdAt' | 'status'>
): Promise<VendorNotification> => {
  try {
    const notification: Omit<VendorNotification, 'id'> = {
      ...notificationData,
      status: NOTIFICATION_STATUS_UNREAD,
      createdAt: Timestamp.now()
    };

    const docRef = await addDoc(collection(db, VENDOR_NOTIFICATIONS_COLLECTION), notification);
    return { id: docRef.id, ...notification };
  } catch (error) {
    console.error('Error creating vendor notification:', error);
    throw error;
  }
};

/**
 * Get notifications for a vendor
 */
export const getVendorNotifications = async (
  vendorId: string,
  options?: {
    status?: string;
    type?: string;
    limit?: number;
    hotelId?: string;
  }
): Promise<VendorNotification[]> => {
  try {
    let notificationsQuery = query(
      collection(db, VENDOR_NOTIFICATIONS_COLLECTION),
      where('vendorId', '==', vendorId),
      orderBy('createdAt', 'desc')
    );

    // Apply status filter if provided
    if (options?.status) {
      notificationsQuery = query(
        notificationsQuery,
        where('status', '==', options.status)
      );
    }

    // Apply type filter if provided
    if (options?.type) {
      notificationsQuery = query(
        notificationsQuery,
        where('type', '==', options.type)
      );
    }

    // Apply hotel filter if provided
    if (options?.hotelId) {
      notificationsQuery = query(
        notificationsQuery,
        where('hotelId', '==', options.hotelId)
      );
    }

    // Apply limit if provided
    if (options?.limit) {
      notificationsQuery = query(notificationsQuery, firestoreLimit(options.limit));
    }

    const querySnapshot = await getDocs(notificationsQuery);
    const notifications: VendorNotification[] = [];

    querySnapshot.forEach((doc) => {
      notifications.push({
        id: doc.id,
        ...doc.data() as Omit<VendorNotification, 'id'>
      });
    });

    return notifications;
  } catch (error) {
    console.error('Error getting vendor notifications:', error);
    throw error;
  }
};

/**
 * Mark a notification as read
 */
export const markVendorNotificationAsRead = async (notificationId: string): Promise<boolean> => {
  try {
    const notificationRef = doc(db, VENDOR_NOTIFICATIONS_COLLECTION, notificationId);
    await updateDoc(notificationRef, {
      status: NOTIFICATION_STATUS_READ,
      readAt: Timestamp.now()
    });
    return true;
  } catch (error) {
    console.error('Error marking vendor notification as read:', error);
    throw error;
  }
};

/**
 * Mark all notifications as read for a vendor
 */
export const markAllVendorNotificationsAsRead = async (vendorId: string, hotelId?: string): Promise<boolean> => {
  try {
    let notificationsQuery = query(
      collection(db, VENDOR_NOTIFICATIONS_COLLECTION),
      where('vendorId', '==', vendorId),
      where('status', '==', NOTIFICATION_STATUS_UNREAD)
    );

    // Apply hotel filter if provided
    if (hotelId) {
      notificationsQuery = query(
        notificationsQuery,
        where('hotelId', '==', hotelId)
      );
    }

    const querySnapshot = await getDocs(notificationsQuery);
    const batch = writeBatch(db);

    querySnapshot.forEach((document) => {
      const notificationRef = doc(db, VENDOR_NOTIFICATIONS_COLLECTION, document.id);
      batch.update(notificationRef, {
        status: NOTIFICATION_STATUS_READ,
        readAt: Timestamp.now()
      });
    });

    await batch.commit();
    return true;
  } catch (error) {
    console.error('Error marking all vendor notifications as read:', error);
    throw error;
  }
};

/**
 * Delete a notification
 */
export const deleteVendorNotification = async (notificationId: string): Promise<boolean> => {
  try {
    await deleteDoc(doc(db, VENDOR_NOTIFICATIONS_COLLECTION, notificationId));
    return true;
  } catch (error) {
    console.error('Error deleting vendor notification:', error);
    throw error;
  }
};

/**
 * Get unread notification count for a vendor
 */
export const getUnreadVendorNotificationCount = async (vendorId: string, hotelId?: string): Promise<number> => {
  try {
    let notificationsQuery = query(
      collection(db, VENDOR_NOTIFICATIONS_COLLECTION),
      where('vendorId', '==', vendorId),
      where('status', '==', NOTIFICATION_STATUS_UNREAD)
    );

    // Apply hotel filter if provided
    if (hotelId) {
      notificationsQuery = query(
        notificationsQuery,
        where('hotelId', '==', hotelId)
      );
    }

    const querySnapshot = await getDocs(notificationsQuery);
    return querySnapshot.size;
  } catch (error) {
    console.error('Error getting unread vendor notification count:', error);
    throw error;
  }
};

/**
 * Create a service request notification for vendor
 */
export const createServiceRequestNotification = async (
  vendorId: string,
  hotelId: string,
  serviceRequest: {
    id: string;
    type: string; // 'cleaning', 'food', 'maintenance'
    roomNumber: string;
    guestName?: string;
    priority: 'low' | 'medium' | 'high';
  }
): Promise<VendorNotification> => {
  try {
    const serviceTypeMap: Record<string, string> = {
      cleaning: 'Cleaning',
      food: 'Food Order',
      maintenance: 'Maintenance'
    };
    
    const serviceType = serviceTypeMap[serviceRequest.type] || 'Service';
    const guestInfo = serviceRequest.guestName ? ` from ${serviceRequest.guestName}` : '';
    
    const notification: Omit<VendorNotification, 'createdAt' | 'status'> = {
      title: `New ${serviceType} Request`,
      message: `Room ${serviceRequest.roomNumber} has requested ${serviceType.toLowerCase()} service${guestInfo}.`,
      type: NOTIFICATION_TYPE_SERVICE_REQUEST,
      vendorId,
      hotelId,
      relatedId: serviceRequest.id,
      relatedType: serviceRequest.type,
      priority: serviceRequest.priority,
      actions: [
        {
          label: 'View Request',
          url: `/vendor/services/${serviceRequest.type}?id=${serviceRequest.id}`
        }
      ]
    };

    return await createVendorNotification(notification);
  } catch (error) {
    console.error('Error creating service request notification:', error);
    throw error;
  }
};

/**
 * Create a booking notification for vendor
 */
export const createBookingNotification = async (
  vendorId: string,
  hotelId: string,
  booking: {
    id: string;
    bookingNumber: string;
    guestName: string;
    checkInDate: Timestamp;
    roomNumber?: string;
    status: string;
  }
): Promise<VendorNotification> => {
  try {
    const statusMap: Record<string, string> = {
      confirmed: 'New Booking',
      cancelled: 'Booking Cancelled',
      checked_in: 'Guest Checked In',
      checked_out: 'Guest Checked Out'
    };
    
    const title = statusMap[booking.status] || 'Booking Update';
    const roomInfo = booking.roomNumber ? ` for Room ${booking.roomNumber}` : '';
    
    const notification: Omit<VendorNotification, 'createdAt' | 'status'> = {
      title,
      message: `${booking.guestName} has ${booking.status === 'confirmed' ? 'made a new booking' : 
                booking.status === 'cancelled' ? 'cancelled their booking' : 
                booking.status === 'checked_in' ? 'checked in' : 
                booking.status === 'checked_out' ? 'checked out' : 
                'updated their booking'}${roomInfo}. Booking #${booking.bookingNumber}.`,
      type: NOTIFICATION_TYPE_BOOKING,
      vendorId,
      hotelId,
      relatedId: booking.id,
      relatedType: 'booking',
      priority: booking.status === 'confirmed' || booking.status === 'cancelled' ? 'high' : 'medium',
      actions: [
        {
          label: 'View Booking',
          url: `/vendor/bookings?id=${booking.id}`
        }
      ]
    };

    return await createVendorNotification(notification);
  } catch (error) {
    console.error('Error creating booking notification:', error);
    throw error;
  }
};

export default {
  createVendorNotification,
  getVendorNotifications,
  markVendorNotificationAsRead,
  markAllVendorNotificationsAsRead,
  deleteVendorNotification,
  getUnreadVendorNotificationCount,
  createServiceRequestNotification,
  createBookingNotification
};
