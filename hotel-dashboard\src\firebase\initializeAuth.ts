import { createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';
import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db } from './config';
import { ROLE_SUPER_ADMIN, ROLE_VENDOR, R<PERSON>E_STAFF } from './auth';

// Sample users for initializing authentication
const sampleUsers = [
  {
    email: '<EMAIL>',
    password: 'admin123',
    displayName: 'Admin User',
    role: ROLE_SUPER_ADMIN,
  },
  {
    email: '<EMAIL>',
    password: 'vendor123',
    displayName: 'Vendor User',
    role: ROLE_VENDOR,
  },
  {
    email: '<EMAIL>',
    password: 'staff123',
    displayName: 'Staff User',
    role: ROLE_STAFF,
  },
];

// Function to initialize authentication with sample users
export const initializeAuth = async () => {
  try {
    for (const user of sampleUsers) {
      try {
        // Create user with email and password
        const userCredential = await createUserWithEmailAndPassword(
          auth,
          user.email,
          user.password
        );

        // Update user profile with display name
        await updateProfile(userCredential.user, {
          displayName: user.displayName,
        });

        // Add user data to Firestore
        await setDoc(doc(db, 'users', userCredential.user.uid), {
          email: user.email,
          displayName: user.displayName,
          role: user.role,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });

        console.log(`User ${user.email} created successfully`);
      } catch (error: any) {
        // Skip if user already exists
        if (error.code === 'auth/email-already-in-use') {
          console.log(`User ${user.email} already exists`);
        } else {
          console.error(`Error creating user ${user.email}:`, error);
        }
      }
    }

    console.log('Authentication initialized successfully');
    return true;
  } catch (error) {
    console.error('Error initializing authentication:', error);
    return false;
  }
};
