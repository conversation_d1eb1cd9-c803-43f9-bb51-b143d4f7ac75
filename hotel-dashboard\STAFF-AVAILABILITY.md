# Staff Availability

The hotel booking system includes a staff availability feature that allows staff members to set their preferred working hours and days. This document explains how to use the staff availability feature to create more effective schedules that respect staff preferences.

## What Is Staff Availability?

Staff availability refers to the times when staff members are available to work. By setting their availability preferences, staff members can indicate:

1. **Preferred Times**: Times when they prefer to work
2. **Available Times**: Times when they are available to work if needed
3. **Unavailable Times**: Times when they are not available to work

## Types of Availability

The system supports three types of availability:

1. **Preferred**: Times when the staff member prefers to work. These are the ideal times for scheduling.
2. **Available**: Times when the staff member is available to work if needed, but not their preferred times.
3. **Unavailable**: Times when the staff member is not available to work. Scheduling during these times should be avoided.

## Recurring vs. One-time Availability

Staff availability can be set as:

1. **Weekly Recurring**: Applies to the same day of the week every week (e.g., every Monday from 9 AM to 5 PM).
2. **One-time**: Applies to a specific date only (e.g., unavailable on December 25).

## Managing Staff Availability

### Setting Staff Availability

To set availability for a staff member:

1. Navigate to the Staff Scheduling page
2. Click on the "Staff Availability" tab
3. Select the staff member from the dropdown
4. Click "Add Availability"
5. Select the recurring type (weekly or one-time)
6. For weekly recurring, select the day of the week
7. For one-time, select the specific date
8. Select the availability type (preferred, available, unavailable)
9. Set the start and end times
10. Add any notes
11. Click "Add Availability"

### Editing Staff Availability

To edit existing availability:

1. Navigate to the staff member's availability
2. Click the edit icon next to the availability you want to edit
3. Update the availability details
4. Click "Save Changes"

### Deleting Staff Availability

To delete availability:

1. Navigate to the staff member's availability
2. Click the delete icon next to the availability you want to delete
3. Confirm the deletion

## Viewing Staff Availability

### Availability Calendar

The Availability Calendar provides a visual representation of staff availability:

1. Navigate to the Staff Scheduling page
2. Click on the "Staff Availability" tab
3. Select the "Availability Calendar" sub-tab

The calendar shows:
- Preferred times in green
- Available times in blue
- Unavailable times in red

You can filter the calendar by:
- Staff member
- Availability type

## Using Staff Availability in Scheduling

When creating or editing shifts, the system checks if the staff member is available during the scheduled time:

1. If the shift is during a staff member's unavailable time, a conflict is detected
2. If the shift is outside a staff member's available or preferred times, a warning is shown
3. If the shift is during a staff member's preferred time, no conflicts are detected

## Benefits of Using Staff Availability

Using staff availability provides several benefits:

1. **Staff Satisfaction**: Respects staff preferences and personal commitments
2. **Reduced Conflicts**: Prevents scheduling during unavailable times
3. **Optimized Scheduling**: Helps create schedules that match staff preferences
4. **Improved Communication**: Provides clear visibility of staff availability
5. **Reduced No-shows**: Decreases the likelihood of staff not showing up for shifts

## Best Practices

1. **Regular Updates**: Encourage staff to keep their availability up to date
2. **Advance Notice**: Set expectations for how far in advance availability should be updated
3. **Balance Preferences**: Try to balance staff preferences with business needs
4. **Communicate Changes**: Notify staff when schedules are created that don't match their preferences
5. **Review Regularly**: Periodically review availability patterns to identify trends

## Technical Details

Staff availability is stored in the `staffAvailability` collection in Firestore with the following fields:

- `staffId`: ID of the staff member
- `staffName`: Name of the staff member
- `hotelId`: ID of the hotel
- `dayOfWeek`: Day of the week (0-6, Monday-Sunday) for weekly recurring availability
- `specificDate`: Specific date for one-time availability
- `startTime`: Start time in 24-hour format (HH:MM)
- `endTime`: End time in 24-hour format (HH:MM)
- `type`: Type of availability (preferred, available, unavailable)
- `recurring`: Recurring type (weekly, none)
- `notes`: Additional notes about the availability

## Integration with Other Features

Staff availability integrates with other features of the hotel booking system:

1. **Schedule Conflict Detection**: Checks for conflicts with staff availability when creating or editing shifts
2. **Schedule Templates**: Can be used to create templates that respect staff availability
3. **Time Off Requests**: Automatically marks staff as unavailable during approved time off

By using staff availability effectively, you can create schedules that balance business needs with staff preferences, leading to higher staff satisfaction and better operational efficiency.
