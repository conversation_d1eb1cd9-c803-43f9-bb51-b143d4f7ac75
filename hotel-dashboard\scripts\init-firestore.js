const { initializeApp } = require('firebase/app');
const {
  getFirestore,
  collection,
  doc,
  setDoc,
  serverTimestamp
} = require('firebase/firestore');
const fs = require('fs');
const path = require('path');

// Load Firebase config from the config file
const loadFirebaseConfig = () => {
  try {
    // Try to load from environment variables first
    if (process.env.FIREBASE_CONFIG) {
      return JSON.parse(process.env.FIREBASE_CONFIG);
    }

    // Otherwise, load from the config file
    const configPath = path.join(__dirname, '..', 'src', 'firebase', 'config.ts');
    const configFile = fs.readFileSync(configPath, 'utf8');

    // Hardcoded config based on the file content
    // This is more reliable than regex parsing
    return {
      apiKey: "AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI",
      authDomain: "linkinblink-hotel.firebaseapp.com",
      projectId: "linkinblink-hotel",
      storageBucket: "linkinblink-hotel.firebasestorage.app",
      messagingSenderId: "132613661307",
      appId: "1:132613661307:web:a9e5c8f4c2b8e9a9e5c8f4",
      measurementId: "G-MEASUREMENT_ID"
    };
  } catch (error) {
    console.error('Error loading Firebase config:', error);
    console.log('Please provide Firebase config as a JSON string in FIREBASE_CONFIG environment variable');
    process.exit(1);
  }
};

// Initialize Firebase
const initializeFirebase = () => {
  const firebaseConfig = loadFirebaseConfig();
  const app = initializeApp(firebaseConfig);
  const db = getFirestore(app);
  return { app, db };
};

// Create initial collections and documents
const initializeFirestore = async () => {
  console.log('Initializing Firestore collections...');

  const { db } = initializeFirebase();

  try {
    // Create super admin user
    await createSuperAdmin(db);

    // Create sample hotel
    const hotelId = await createSampleHotel(db);

    // Create room types
    const roomTypeIds = await createSampleRoomTypes(db, hotelId);

    // Create rooms
    await createSampleRooms(db, hotelId, roomTypeIds);

    // Create pricing rules
    await createSamplePricingRules(db, hotelId);

    // Create permissions
    await createDefaultPermissions(db);

    console.log('Firestore initialization completed successfully!');
  } catch (error) {
    console.error('Error initializing Firestore:', error);
  }
};

// Create super admin user
const createSuperAdmin = async (db) => {
  console.log('Creating super admin user...');

  const superAdminId = 'superadmin123'; // This would normally be the Firebase Auth UID

  await setDoc(doc(db, 'users', superAdminId), {
    displayName: 'Super Admin',
    email: '<EMAIL>',
    role: 'super_admin',
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  });

  console.log('Super admin user created with ID:', superAdminId);
  return superAdminId;
};

// Create sample hotel
const createSampleHotel = async (db) => {
  console.log('Creating sample hotel...');

  const vendorId = 'vendor123'; // This would normally be the Firebase Auth UID

  // Create vendor user
  await setDoc(doc(db, 'users', vendorId), {
    displayName: 'Sample Vendor',
    email: '<EMAIL>',
    role: 'vendor',
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  });

  // Create hotel
  const hotelId = 'hotel123';
  await setDoc(doc(db, 'hotels', hotelId), {
    name: 'Grand Hotel',
    description: 'A luxurious hotel in the heart of the city',
    address: '123 Main Street',
    city: 'New York',
    country: 'USA',
    zipCode: '10001',
    phone: '******-456-7890',
    email: '<EMAIL>',
    website: 'https://www.grandhotel.com',
    vendorId: vendorId,
    amenities: ['WiFi', 'Pool', 'Gym', 'Restaurant', 'Spa'],
    images: [],
    rating: 4.5,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  });

  console.log('Sample hotel created with ID:', hotelId);
  return hotelId;
};

// Create sample room types
const createSampleRoomTypes = async (db, hotelId) => {
  console.log('Creating sample room types...');

  const roomTypes = [
    {
      id: 'roomtype1',
      name: 'Standard',
      description: 'Comfortable room with all basic amenities',
      basePrice: 100,
      capacity: 2,
      amenities: ['WiFi', 'TV', 'Air Conditioning', 'Desk', 'Shower']
    },
    {
      id: 'roomtype2',
      name: 'Deluxe',
      description: 'Spacious room with premium amenities',
      basePrice: 150,
      capacity: 2,
      amenities: ['WiFi', 'TV', 'Air Conditioning', 'Mini Bar', 'Bathtub', 'Safe']
    },
    {
      id: 'roomtype3',
      name: 'Suite',
      description: 'Luxury suite with separate living area',
      basePrice: 250,
      capacity: 4,
      amenities: ['WiFi', 'TV', 'Air Conditioning', 'Mini Bar', 'Bathtub', 'Safe', 'Living Room', 'Kitchen']
    }
  ];

  const roomTypeIds = [];

  for (const roomType of roomTypes) {
    await setDoc(doc(db, 'roomTypes', roomType.id), {
      ...roomType,
      hotelId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    roomTypeIds.push(roomType.id);
    console.log(`Room type created: ${roomType.name} with ID: ${roomType.id}`);
  }

  return roomTypeIds;
};

// Create sample rooms
const createSampleRooms = async (db, hotelId, roomTypeIds) => {
  console.log('Creating sample rooms...');

  const rooms = [
    {
      id: 'room101',
      name: 'Room 101',
      type: roomTypeIds[0], // Standard
      description: 'Standard room with city view',
      basePrice: 100,
      capacity: 2,
      beds: { single: 0, double: 1, queen: 0, king: 0 },
      size: 25,
      amenities: ['WiFi', 'TV', 'Air Conditioning', 'Desk', 'Shower'],
      status: 'active',
      floor: 1,
      roomNumber: '101',
      isAccessible: false,
      maxAdults: 2,
      maxChildren: 1,
      smokingAllowed: false,
      petsAllowed: false
    },
    {
      id: 'room102',
      name: 'Room 102',
      type: roomTypeIds[0], // Standard
      description: 'Standard room with garden view',
      basePrice: 110,
      capacity: 2,
      beds: { single: 2, double: 0, queen: 0, king: 0 },
      size: 25,
      amenities: ['WiFi', 'TV', 'Air Conditioning', 'Desk', 'Shower'],
      status: 'active',
      floor: 1,
      roomNumber: '102',
      isAccessible: true,
      maxAdults: 2,
      maxChildren: 1,
      smokingAllowed: false,
      petsAllowed: false
    },
    {
      id: 'room201',
      name: 'Room 201',
      type: roomTypeIds[1], // Deluxe
      description: 'Deluxe room with city view',
      basePrice: 150,
      capacity: 2,
      beds: { single: 0, double: 0, queen: 1, king: 0 },
      size: 35,
      amenities: ['WiFi', 'TV', 'Air Conditioning', 'Mini Bar', 'Bathtub', 'Safe'],
      status: 'active',
      floor: 2,
      roomNumber: '201',
      isAccessible: false,
      maxAdults: 2,
      maxChildren: 2,
      smokingAllowed: false,
      petsAllowed: false
    },
    {
      id: 'room301',
      name: 'Room 301',
      type: roomTypeIds[2], // Suite
      description: 'Luxury suite with panoramic view',
      basePrice: 250,
      capacity: 4,
      beds: { single: 0, double: 0, queen: 0, king: 1 },
      size: 50,
      amenities: ['WiFi', 'TV', 'Air Conditioning', 'Mini Bar', 'Bathtub', 'Safe', 'Living Room', 'Kitchen'],
      status: 'active',
      floor: 3,
      roomNumber: '301',
      isAccessible: false,
      maxAdults: 4,
      maxChildren: 2,
      smokingAllowed: false,
      petsAllowed: true
    }
  ];

  for (const room of rooms) {
    await setDoc(doc(db, 'rooms', room.id), {
      ...room,
      hotelId,
      images: [],
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    console.log(`Room created: ${room.name} with ID: ${room.id}`);
  }
};

// Create sample pricing rules
const createSamplePricingRules = async (db, hotelId) => {
  console.log('Creating sample pricing rules...');

  const pricingRules = [
    {
      id: 'rule1',
      name: 'Weekend Premium',
      description: 'Higher rates for Friday and Saturday nights',
      startDate: new Date('2023-01-01'),
      endDate: new Date('2023-12-31'),
      dayOfWeek: [5, 6], // Friday and Saturday
      adjustmentType: 'percentage',
      adjustmentValue: 20,
      priority: 10,
      status: 'active'
    },
    {
      id: 'rule2',
      name: 'Summer Season',
      description: 'Higher rates during summer months',
      startDate: new Date('2023-06-01'),
      endDate: new Date('2023-08-31'),
      adjustmentType: 'percentage',
      adjustmentValue: 15,
      priority: 5,
      status: 'active'
    },
    {
      id: 'rule3',
      name: 'Long Stay Discount',
      description: 'Discount for stays longer than 7 nights',
      startDate: new Date('2023-01-01'),
      endDate: new Date('2023-12-31'),
      adjustmentType: 'percentage',
      adjustmentValue: -10,
      minStayLength: 7,
      priority: 3,
      status: 'active'
    }
  ];

  for (const rule of pricingRules) {
    await setDoc(doc(db, 'pricingRules', rule.id), {
      ...rule,
      hotelId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    console.log(`Pricing rule created: ${rule.name} with ID: ${rule.id}`);
  }
};

// Create default permissions
const createDefaultPermissions = async (db) => {
  console.log('Creating default permissions...');

  const permissions = [
    // Super Admin permissions
    { id: 'perm1', role: 'super_admin', resource: 'hotel', action: 'manage' },
    { id: 'perm2', role: 'super_admin', resource: 'room', action: 'manage' },
    { id: 'perm3', role: 'super_admin', resource: 'booking', action: 'manage' },
    { id: 'perm4', role: 'super_admin', resource: 'user', action: 'manage' },

    // Vendor permissions
    { id: 'perm5', role: 'vendor', resource: 'hotel', action: 'read', conditions: { ownedByUser: true } },
    { id: 'perm6', role: 'vendor', resource: 'hotel', action: 'update', conditions: { ownedByUser: true } },
    { id: 'perm7', role: 'vendor', resource: 'room', action: 'manage', conditions: { hotelOwnedByUser: true } },
    { id: 'perm8', role: 'vendor', resource: 'booking', action: 'manage', conditions: { hotelOwnedByUser: true } },
    { id: 'perm9', role: 'vendor', resource: 'user', action: 'manage', conditions: { role: 'staff', createdByUser: true } },

    // Staff permissions
    { id: 'perm10', role: 'staff', resource: 'booking', action: 'read', conditions: { hotelAssignedToUser: true } },
    { id: 'perm11', role: 'staff', resource: 'booking', action: 'create', conditions: { hotelAssignedToUser: true } },
    { id: 'perm12', role: 'staff', resource: 'booking', action: 'update', conditions: { hotelAssignedToUser: true } },
    { id: 'perm13', role: 'staff', resource: 'room', action: 'read', conditions: { hotelAssignedToUser: true } }
  ];

  for (const permission of permissions) {
    await setDoc(doc(db, 'permissions', permission.id), {
      ...permission,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    console.log(`Permission created: ${permission.role} - ${permission.resource} - ${permission.action}`);
  }
};

// Run the initialization
initializeFirestore();
