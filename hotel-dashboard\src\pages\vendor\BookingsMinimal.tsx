import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Alert,
  Button,
} from '@mui/material';

const BookingsMinimal: React.FC = () => {
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Bookings Management
        </Typography>
      </Box>

      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h5" gutterBottom>
          Bookings Module Under Maintenance
        </Typography>
        <Typography variant="body1" paragraph>
          We're currently updating the bookings management system to provide you with improved features and performance.
        </Typography>
        <Typography variant="body2" paragraph color="text.secondary">
          Please check back soon. In the meantime, you can access other features of the dashboard.
        </Typography>
        <Box sx={{ mt: 3 }}>
          <Button 
            variant="contained" 
            color="primary" 
            href="/vendor"
            sx={{ mx: 1 }}
          >
            Return to Dashboard
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default BookingsMinimal;
