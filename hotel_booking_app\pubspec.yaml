name: hotel_booking_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # Firebase packages
  firebase_core: ^2.32.0
  firebase_auth: ^4.17.4
  cloud_firestore: ^4.15.4
  firebase_storage: ^11.6.5
  firebase_analytics: ^10.8.5
  firebase_messaging: ^14.7.14

  # State management
  provider: ^6.1.1
  flutter_bloc: ^8.1.3

  # UI packages
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.1
  flutter_carousel_widget: ^2.2.0
  shimmer: ^3.0.0
  flutter_rating_bar: ^4.0.1

  # Utilities
  intl: ^0.19.0
  http: ^1.2.0
  shared_preferences: ^2.2.2
  geolocator: ^10.1.0
  google_maps_flutter: ^2.5.3
  url_launcher: ^6.2.4

  # Payment Gateway - Using custom mock implementation
  # razorpay_flutter: ^1.3.5 (removed due to compatibility issues)
  # Mock payment implementation for testing
  mockito: ^5.4.4

  # PDF Generation
  pdf: ^3.10.7
  path_provider: ^2.1.2
  printing: ^5.11.1
  qr_flutter: ^4.1.0

  # Internationalization
  flutter_localizations:
    sdk: flutter

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # App icon generator
  flutter_launcher_icons: ^0.13.1

  # Testing utilities
  # Note: mockito and build_runner are commented out due to compatibility issues
  # mockito: ^5.4.4
  # build_runner: ^2.4.8

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/link_in_blink_logo.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/images/link_in_blink_logo.png"
  min_sdk_android: 21
  remove_alpha_ios: true
  web:
    generate: true
    image_path: "assets/images/link_in_blink_logo.png"
    background_color: "#FFFFFF"
    theme_color: "#FFFFFF"
  windows:
    generate: true
    image_path: "assets/images/link_in_blink_logo.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/images/link_in_blink_logo.png"
