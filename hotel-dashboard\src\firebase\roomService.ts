import {
  collection,
  doc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  getDocs,
  getDoc,
  serverTimestamp,
  orderBy,
  limit
} from 'firebase/firestore';
import { db } from './config';

// Interface for room data
export interface RoomData {
  id?: string;
  hotelId: string;
  name: string;
  description: string;
  price: number;
  capacity: number;
  amenities?: string[];
  images?: string[];
  status: 'available' | 'booked' | 'maintenance';
  roomNumber?: string;
  floor?: number;
  size?: number; // in square meters/feet
  bedType?: string;
  createdAt?: any;
  updatedAt?: any;
}

/**
 * Create a new room
 */
export const createRoom = async (roomData: RoomData): Promise<string> => {
  try {
    // Create a new document reference
    const roomRef = doc(collection(db, 'rooms'));

    // Add the room data with timestamps
    await setDoc(roomRef, {
      ...roomData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return roomRef.id;
  } catch (error) {
    console.error('Error creating room:', error);
    throw error;
  }
};

/**
 * Get all rooms for a specific hotel
 */
export const getRoomsForHotel = async (hotelId: string) => {
  try {
    // First try to get real rooms from Firestore
    try {
      const roomsQuery = query(
        collection(db, 'rooms'),
        where('hotelId', '==', hotelId),
        orderBy('createdAt', 'desc')
      );

      const roomsSnapshot = await getDocs(roomsQuery);
      const rooms: RoomData[] = [];

      roomsSnapshot.forEach((doc) => {
        rooms.push({ id: doc.id, ...doc.data() } as RoomData);
      });

      // If we have real rooms, return them
      if (rooms.length > 0) {
        return rooms;
      }
    } catch (err) {
      console.error('Error fetching real rooms:', err);
    }

    // If no real rooms or error occurred, return sample data
    console.log('Returning sample room data for hotel:', hotelId);

    // Sample rooms for each hotel
    if (hotelId === 'sample-hotel-1') {
      return [
        {
          id: 'sample-room-1',
          hotelId: 'sample-hotel-1',
          name: 'Deluxe King Room',
          description: 'Spacious room with king-size bed and city view',
          price: 250,
          capacity: 2,
          amenities: ['King Bed', 'City View', 'Mini Bar', 'Free WiFi', 'Flat-screen TV', 'Air Conditioning'],
          images: [
            'https://images.unsplash.com/photo-1590490360182-c33d57733427?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80'
          ],
          status: 'available' as 'available' | 'booked' | 'maintenance',
          roomNumber: '501',
          floor: 5,
          size: 35,
          bedType: 'King'
        },
        {
          id: 'sample-room-2',
          hotelId: 'sample-hotel-1',
          name: 'Executive Suite',
          description: 'Luxury suite with separate living area and panoramic views',
          price: 450,
          capacity: 3,
          amenities: ['King Bed', 'Sofa Bed', 'Panoramic View', 'Mini Bar', 'Free WiFi', 'Flat-screen TV', 'Air Conditioning', 'Jacuzzi'],
          images: [
            'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80'
          ],
          status: 'available' as 'available' | 'booked' | 'maintenance',
          roomNumber: '601',
          floor: 6,
          size: 55,
          bedType: 'King'
        }
      ];
    } else if (hotelId === 'sample-hotel-2') {
      return [
        {
          id: 'sample-room-3',
          hotelId: 'sample-hotel-2',
          name: 'Ocean View Room',
          description: 'Beautiful room with stunning ocean views',
          price: 320,
          capacity: 2,
          amenities: ['Queen Bed', 'Ocean View', 'Mini Bar', 'Free WiFi', 'Flat-screen TV', 'Air Conditioning', 'Balcony'],
          images: [
            'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80'
          ],
          status: 'available' as 'available' | 'booked' | 'maintenance',
          roomNumber: '301',
          floor: 3,
          size: 30,
          bedType: 'Queen'
        },
        {
          id: 'sample-room-4',
          hotelId: 'sample-hotel-2',
          name: 'Beachfront Suite',
          description: 'Luxurious suite with direct beach access',
          price: 550,
          capacity: 4,
          amenities: ['King Bed', 'Twin Beds', 'Beach Access', 'Mini Bar', 'Free WiFi', 'Flat-screen TV', 'Air Conditioning', 'Private Terrace'],
          images: [
            'https://images.unsplash.com/photo-1602002418082-dd4a8f7d317f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80'
          ],
          status: 'available' as 'available' | 'booked' | 'maintenance',
          roomNumber: '101',
          floor: 1,
          size: 65,
          bedType: 'King and Twin'
        }
      ];
    } else if (hotelId === 'sample-hotel-3') {
      return [
        {
          id: 'sample-room-5',
          hotelId: 'sample-hotel-3',
          name: 'Mountain View Room',
          description: 'Cozy room with beautiful mountain views',
          price: 180,
          capacity: 2,
          amenities: ['Queen Bed', 'Mountain View', 'Free WiFi', 'Flat-screen TV', 'Air Conditioning', 'Fireplace'],
          images: [
            'https://images.unsplash.com/photo-1566665797739-1674de7a421a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80'
          ],
          status: 'available' as 'available' | 'booked' | 'maintenance',
          roomNumber: '201',
          floor: 2,
          size: 28,
          bedType: 'Queen'
        },
        {
          id: 'sample-room-6',
          hotelId: 'sample-hotel-3',
          name: 'Family Cabin',
          description: 'Spacious cabin perfect for families with outdoor patio',
          price: 320,
          capacity: 6,
          amenities: ['King Bed', 'Bunk Beds', 'Sofa Bed', 'Patio', 'Free WiFi', 'Flat-screen TV', 'Air Conditioning', 'Fireplace', 'Kitchenette'],
          images: [
            'https://images.unsplash.com/photo-1587985064135-0366536eab42?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80'
          ],
          status: 'available' as 'available' | 'booked' | 'maintenance',
          roomNumber: 'C1',
          floor: 1,
          size: 75,
          bedType: 'Multiple'
        }
      ];
    }

    // Default sample rooms for any other hotel ID
    return [
      {
        id: `sample-room-${hotelId}-1`,
        hotelId: hotelId,
        name: 'Standard Room',
        description: 'Comfortable standard room with all amenities',
        price: 150,
        capacity: 2,
        amenities: ['Queen Bed', 'Free WiFi', 'Flat-screen TV', 'Air Conditioning'],
        images: [
          'https://images.unsplash.com/photo-1505693416388-ac5ce068fe85?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80'
        ],
        status: 'available' as 'available' | 'booked' | 'maintenance',
        roomNumber: '101',
        floor: 1,
        size: 25,
        bedType: 'Queen'
      },
      {
        id: `sample-room-${hotelId}-2`,
        hotelId: hotelId,
        name: 'Deluxe Room',
        description: 'Spacious deluxe room with premium amenities',
        price: 220,
        capacity: 2,
        amenities: ['King Bed', 'Mini Bar', 'Free WiFi', 'Flat-screen TV', 'Air Conditioning'],
        images: [
          'https://images.unsplash.com/photo-1618773928121-c32242e63f39?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80'
        ],
        status: 'available' as 'available' | 'booked' | 'maintenance',
        roomNumber: '201',
        floor: 2,
        size: 30,
        bedType: 'King'
      }
    ];
  } catch (error) {
    console.error('Error getting rooms:', error);
    // Return empty array instead of throwing
    return [];
  }
};

/**
 * Get all rooms (for admin)
 */
export const getAllRooms = async () => {
  try {
    const roomsSnapshot = await getDocs(collection(db, 'rooms'));
    const rooms: RoomData[] = [];

    roomsSnapshot.forEach((doc) => {
      try {
        const data = doc.data();
        rooms.push({
          id: doc.id,
          ...data,
          // Ensure these fields have default values if missing
          hotelId: data.hotelId || '',
          name: data.name || 'Unnamed Room',
          description: data.description || '',
          price: data.price || 0,
          capacity: data.capacity || 1,
          status: (data.status as 'available' | 'booked' | 'maintenance') || 'available',
          amenities: data.amenities || [],
          images: data.images || []
        } as RoomData);
      } catch (err) {
        console.error('Error processing room document:', err);
      }
    });

    return rooms;
  } catch (error) {
    console.error('Error getting all rooms:', error);
    // Return empty array instead of throwing
    return [];
  }
};

/**
 * Get a room by ID
 */
export const getRoomById = async (roomId: string) => {
  try {
    const roomDoc = await getDoc(doc(db, 'rooms', roomId));

    if (roomDoc.exists()) {
      return { id: roomDoc.id, ...roomDoc.data() } as RoomData;
    } else {
      throw new Error('Room not found');
    }
  } catch (error) {
    console.error('Error getting room:', error);
    throw error;
  }
};

/**
 * Update a room
 */
export const updateRoom = async (roomId: string, roomData: Partial<RoomData>) => {
  try {
    const roomRef = doc(db, 'rooms', roomId);

    await updateDoc(roomRef, {
      ...roomData,
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('Error updating room:', error);
    throw error;
  }
};

/**
 * Delete a room
 */
export const deleteRoom = async (roomId: string) => {
  try {
    await deleteDoc(doc(db, 'rooms', roomId));
    return true;
  } catch (error) {
    console.error('Error deleting room:', error);
    throw error;
  }
};

/**
 * Get available rooms for a specific hotel
 */
export const getAvailableRoomsForHotel = async (hotelId: string) => {
  try {
    const roomsQuery = query(
      collection(db, 'rooms'),
      where('hotelId', '==', hotelId),
      where('status', '==', 'available')
    );

    const roomsSnapshot = await getDocs(roomsQuery);
    const rooms: RoomData[] = [];

    roomsSnapshot.forEach((doc) => {
      rooms.push({ id: doc.id, ...doc.data() } as RoomData);
    });

    return rooms;
  } catch (error) {
    console.error('Error getting available rooms:', error);
    throw error;
  }
};

/**
 * Get room count for a specific hotel
 */
export const getRoomCountForHotel = async (hotelId: string): Promise<number> => {
  try {
    const roomsQuery = query(
      collection(db, 'rooms'),
      where('hotelId', '==', hotelId)
    );

    const roomsSnapshot = await getDocs(roomsQuery);
    return roomsSnapshot.size;
  } catch (error) {
    console.error('Error getting room count:', error);
    throw error;
  }
};

/**
 * Get total room count
 */
export const getTotalRoomCount = async (): Promise<number> => {
  try {
    const roomsSnapshot = await getDocs(collection(db, 'rooms'));
    return roomsSnapshot.size;
  } catch (error) {
    console.error('Error getting total room count:', error);
    throw error;
  }
};
