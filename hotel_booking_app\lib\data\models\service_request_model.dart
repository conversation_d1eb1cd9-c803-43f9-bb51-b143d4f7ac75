import 'package:cloud_firestore/cloud_firestore.dart';

// Base service request class
abstract class BaseServiceRequest {
  final String? id;
  final String type;
  final String status;
  final String roomNumber;
  final String hotelId;
  final String? guestId;
  final String? guestName;
  final Timestamp requestTime;
  final String notes;
  final String priority;
  final String? assignedTo;
  final Timestamp? completedTime;
  final Timestamp? createdAt;
  final Timestamp? updatedAt;

  BaseServiceRequest({
    this.id,
    required this.type,
    required this.status,
    required this.roomNumber,
    required this.hotelId,
    this.guestId,
    this.guestName,
    required this.requestTime,
    required this.notes,
    required this.priority,
    this.assignedTo,
    this.completedTime,
    this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap();

  @override
  String toString() {
    return 'ServiceRequest(id: $id, type: $type, status: $status, roomNumber: $roomNumber, hotelId: $hotelId)';
  }
}

// Food item class for food orders
class FoodItem {
  final String name;
  final int quantity;
  final double price;
  final String? specialInstructions;

  FoodItem({
    required this.name,
    required this.quantity,
    required this.price,
    this.specialInstructions,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'quantity': quantity,
      'price': price,
      'specialInstructions': specialInstructions,
    };
  }

  factory FoodItem.fromMap(Map<String, dynamic> map) {
    return FoodItem(
      name: map['name'] ?? '',
      quantity: map['quantity'] ?? 1,
      price: (map['price'] ?? 0.0).toDouble(),
      specialInstructions: map['specialInstructions'],
    );
  }

  @override
  String toString() {
    return 'FoodItem(name: $name, quantity: $quantity, price: $price)';
  }
}

// Cleaning request class
class CleaningRequest extends BaseServiceRequest {
  final String? cleaningType;
  final List<String>? items;

  CleaningRequest({
    super.id,
    required super.type,
    required super.status,
    required super.roomNumber,
    required super.hotelId,
    super.guestId,
    super.guestName,
    required super.requestTime,
    required super.notes,
    required super.priority,
    super.assignedTo,
    super.completedTime,
    super.createdAt,
    super.updatedAt,
    this.cleaningType,
    this.items,
  });

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type,
      'status': status,
      'roomNumber': roomNumber,
      'hotelId': hotelId,
      'guestId': guestId,
      'guestName': guestName,
      'requestTime': requestTime,
      'notes': notes,
      'priority': priority,
      'assignedTo': assignedTo,
      'completedTime': completedTime,
      'cleaningType': cleaningType,
      'items': items,
      'createdAt': createdAt ?? FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }

  factory CleaningRequest.fromMap(Map<String, dynamic> map, String id) {
    return CleaningRequest(
      id: id,
      type: map['type'] ?? 'cleaning',
      status: map['status'] ?? 'pending',
      roomNumber: map['roomNumber'] ?? '',
      hotelId: map['hotelId'] ?? '',
      guestId: map['guestId'],
      guestName: map['guestName'],
      requestTime: map['requestTime'] ?? Timestamp.now(),
      notes: map['notes'] ?? '',
      priority: map['priority'] ?? 'medium',
      assignedTo: map['assignedTo'],
      completedTime: map['completedTime'],
      cleaningType: map['cleaningType'],
      items: map['items'] != null ? List<String>.from(map['items']) : null,
      createdAt: map['createdAt'],
      updatedAt: map['updatedAt'],
    );
  }

  factory CleaningRequest.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>? ?? {};
    return CleaningRequest.fromMap(data, doc.id);
  }

  @override
  String toString() {
    return 'CleaningRequest(${super.toString()}, cleaningType: $cleaningType, items: $items)';
  }
}

// Food order class
class FoodOrder extends BaseServiceRequest {
  final List<FoodItem> items;
  final double totalAmount;
  final Timestamp? deliveryTime;
  final String? paymentMethod;

  FoodOrder({
    super.id,
    required super.type,
    required super.status,
    required super.roomNumber,
    required super.hotelId,
    super.guestId,
    super.guestName,
    required super.requestTime,
    required super.notes,
    required super.priority,
    super.assignedTo,
    super.completedTime,
    super.createdAt,
    super.updatedAt,
    required this.items,
    required this.totalAmount,
    this.deliveryTime,
    this.paymentMethod,
  });

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type,
      'status': status,
      'roomNumber': roomNumber,
      'hotelId': hotelId,
      'guestId': guestId,
      'guestName': guestName,
      'requestTime': requestTime,
      'notes': notes,
      'priority': priority,
      'assignedTo': assignedTo,
      'completedTime': completedTime,
      'items': items.map((item) => item.toMap()).toList(),
      'totalAmount': totalAmount,
      'deliveryTime': deliveryTime,
      'paymentMethod': paymentMethod,
      'createdAt': createdAt ?? FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }

  factory FoodOrder.fromMap(Map<String, dynamic> map, String id) {
    return FoodOrder(
      id: id,
      type: map['type'] ?? 'food',
      status: map['status'] ?? 'pending',
      roomNumber: map['roomNumber'] ?? '',
      hotelId: map['hotelId'] ?? '',
      guestId: map['guestId'],
      guestName: map['guestName'],
      requestTime: map['requestTime'] ?? Timestamp.now(),
      notes: map['notes'] ?? '',
      priority: map['priority'] ?? 'medium',
      assignedTo: map['assignedTo'],
      completedTime: map['completedTime'],
      items: map['items'] != null
          ? List<FoodItem>.from(
              (map['items'] as List).map((item) => FoodItem.fromMap(item)))
          : [],
      totalAmount: (map['totalAmount'] ?? 0.0).toDouble(),
      deliveryTime: map['deliveryTime'],
      paymentMethod: map['paymentMethod'],
      createdAt: map['createdAt'],
      updatedAt: map['updatedAt'],
    );
  }

  factory FoodOrder.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>? ?? {};
    return FoodOrder.fromMap(data, doc.id);
  }

  @override
  String toString() {
    return 'FoodOrder(${super.toString()}, items: ${items.length}, totalAmount: $totalAmount)';
  }
}

// Maintenance request class
class MaintenanceRequest extends BaseServiceRequest {
  final String issueType;
  final String description;
  final Timestamp? estimatedCompletionTime;
  final List<String>? partsRequired;

  MaintenanceRequest({
    super.id,
    required super.type,
    required super.status,
    required super.roomNumber,
    required super.hotelId,
    super.guestId,
    super.guestName,
    required super.requestTime,
    required super.notes,
    required super.priority,
    super.assignedTo,
    super.completedTime,
    super.createdAt,
    super.updatedAt,
    required this.issueType,
    required this.description,
    this.estimatedCompletionTime,
    this.partsRequired,
  });

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type,
      'status': status,
      'roomNumber': roomNumber,
      'hotelId': hotelId,
      'guestId': guestId,
      'guestName': guestName,
      'requestTime': requestTime,
      'notes': notes,
      'priority': priority,
      'assignedTo': assignedTo,
      'completedTime': completedTime,
      'issueType': issueType,
      'description': description,
      'estimatedCompletionTime': estimatedCompletionTime,
      'partsRequired': partsRequired,
      'createdAt': createdAt ?? FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }

  factory MaintenanceRequest.fromMap(Map<String, dynamic> map, String id) {
    return MaintenanceRequest(
      id: id,
      type: map['type'] ?? 'maintenance',
      status: map['status'] ?? 'pending',
      roomNumber: map['roomNumber'] ?? '',
      hotelId: map['hotelId'] ?? '',
      guestId: map['guestId'],
      guestName: map['guestName'],
      requestTime: map['requestTime'] ?? Timestamp.now(),
      notes: map['notes'] ?? '',
      priority: map['priority'] ?? 'medium',
      assignedTo: map['assignedTo'],
      completedTime: map['completedTime'],
      issueType: map['issueType'] ?? 'other',
      description: map['description'] ?? '',
      estimatedCompletionTime: map['estimatedCompletionTime'],
      partsRequired: map['partsRequired'] != null
          ? List<String>.from(map['partsRequired'])
          : null,
      createdAt: map['createdAt'],
      updatedAt: map['updatedAt'],
    );
  }

  factory MaintenanceRequest.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>? ?? {};
    return MaintenanceRequest.fromMap(data, doc.id);
  }

  @override
  String toString() {
    return 'MaintenanceRequest(${super.toString()}, issueType: $issueType)';
  }
}
