import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/data/models/hotel_model.dart';
import 'package:hotel_booking_app/data/models/review_model.dart';
import 'package:hotel_booking_app/data/models/room_model.dart';

class HotelRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Fetch all hotels
  Future<List<Hotel>> fetchHotels() async {
    try {
      debugPrint(
          'Fetching hotels from collection: ${AppConstants.hotelsCollection}');

      // Check if Firestore is accessible
      debugPrint('Firestore instance: Available');

      final snapshot =
          await _firestore.collection(AppConstants.hotelsCollection).get();

      debugPrint('Fetched ${snapshot.docs.length} hotels from Firestore');

      // Log the first hotel document if available for debugging
      if (snapshot.docs.isNotEmpty) {
        final firstDoc = snapshot.docs.first;
        debugPrint('First hotel document ID: ${firstDoc.id}');
        debugPrint('First hotel data: ${firstDoc.data()}');
      }

      if (snapshot.docs.isEmpty) {
        debugPrint('No hotels found in Firestore. Creating sample hotels...');
        // Create sample hotels if none exist
        await _createSampleHotels();

        // Fetch again after creating sample hotels
        final newSnapshot =
            await _firestore.collection(AppConstants.hotelsCollection).get();

        debugPrint(
            'Created and fetched ${newSnapshot.docs.length} sample hotels');

        // Log the first created hotel document if available
        if (newSnapshot.docs.isNotEmpty) {
          final firstDoc = newSnapshot.docs.first;
          debugPrint('First created hotel document ID: ${firstDoc.id}');
          debugPrint('First created hotel data: ${firstDoc.data()}');
        }

        final hotels = newSnapshot.docs
            .map((doc) => Hotel.fromMap({...doc.data(), 'id': doc.id}))
            .toList();

        debugPrint('Returning ${hotels.length} hotels after creation');
        return hotels;
      }

      final hotels = snapshot.docs
          .map((doc) => Hotel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();

      debugPrint('Returning ${hotels.length} hotels from existing data');
      return hotels;
    } catch (e) {
      debugPrint('Error fetching hotels: $e');
      debugPrint('Error details: ${e.toString()}');
      return [];
    }
  }

  // Create sample hotels for testing
  Future<void> _createSampleHotels() async {
    try {
      // Sample hotel data
      final sampleHotels = [
        {
          'name': 'Grand Hotel Luxury',
          'description':
              'A luxurious 5-star hotel in the heart of the city with stunning views and world-class amenities.',
          'address': '123 Main Street',
          'city': 'Mumbai',
          'country': 'India',
          'zipCode': '400001',
          'phone': '+91-22-12345678',
          'email': '<EMAIL>',
          'website': 'www.grandhotelluxury.com',
          'rating': 4.8,
          'price': 12500.0,
          'amenities': [
            'Swimming Pool',
            'Spa',
            'Gym',
            'Restaurant',
            'Bar',
            'Room Service',
            'Free WiFi',
            'Parking'
          ],
          'images': [
            'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
            'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
          ],
          'vendorId': 'sample-vendor-1',
          'createdAt': FieldValue.serverTimestamp(),
          'reviewCount': 120,
          'type': 'Luxury',
        },
        {
          'name': 'Seaside Resort',
          'description':
              'A beautiful beachfront resort with private access to the beach and stunning ocean views.',
          'address': '456 Beach Road',
          'city': 'Goa',
          'country': 'India',
          'zipCode': '403001',
          'phone': '+91-832-12345678',
          'email': '<EMAIL>',
          'website': 'www.seasideresort.com',
          'rating': 4.5,
          'price': 8500.0,
          'amenities': [
            'Beach Access',
            'Swimming Pool',
            'Spa',
            'Restaurant',
            'Bar',
            'Water Sports',
            'Free WiFi'
          ],
          'images': [
            'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
            'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
          ],
          'vendorId': 'sample-vendor-2',
          'createdAt': FieldValue.serverTimestamp(),
          'reviewCount': 85,
          'type': 'Resort',
        },
        {
          'name': 'Mountain View Lodge',
          'description':
              'A cozy mountain lodge with breathtaking views of the Himalayas and outdoor activities.',
          'address': '789 Mountain Road',
          'city': 'Shimla',
          'country': 'India',
          'zipCode': '171001',
          'phone': '+91-177-12345678',
          'email': '<EMAIL>',
          'website': 'www.mountainviewlodge.com',
          'rating': 4.3,
          'price': 6500.0,
          'amenities': [
            'Mountain Views',
            'Fireplace',
            'Restaurant',
            'Hiking Trails',
            'Free WiFi',
            'Parking'
          ],
          'images': [
            'https://images.unsplash.com/photo-1517320964276-a002fa203177?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
            'https://images.unsplash.com/photo-1506059612708-99d6c258160e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1169&q=80',
          ],
          'vendorId': 'sample-vendor-3',
          'createdAt': FieldValue.serverTimestamp(),
          'reviewCount': 62,
          'type': 'Lodge',
        },
      ];

      // Add sample hotels to Firestore
      for (final hotel in sampleHotels) {
        await _firestore.collection(AppConstants.hotelsCollection).add(hotel);
      }

      debugPrint('Successfully created ${sampleHotels.length} sample hotels');
    } catch (e) {
      debugPrint('Error creating sample hotels: $e');
    }
  }

  // Fetch popular hotels
  Future<List<Hotel>> fetchPopularHotels() async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.hotelsCollection)
          .orderBy('rating', descending: true)
          .limit(10)
          .get();

      return snapshot.docs
          .map((doc) => Hotel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      debugPrint('Error fetching popular hotels: $e');
      return [];
    }
  }

  // Fetch hotel by ID
  Future<Hotel?> fetchHotelById(String hotelId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.hotelsCollection)
          .doc(hotelId)
          .get();

      if (doc.exists) {
        return Hotel.fromMap({...doc.data()!, 'id': doc.id});
      }
      return null;
    } catch (e) {
      debugPrint('Error fetching hotel by ID: $e');
      return null;
    }
  }

  // Fetch rooms by hotel ID
  Future<List<Room>> fetchRoomsByHotelId(String hotelId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.roomsCollection)
          .where('hotelId', isEqualTo: hotelId)
          .get();

      return snapshot.docs
          .map((doc) => Room.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      debugPrint('Error fetching rooms by hotel ID: $e');
      return [];
    }
  }

  // Fetch reviews by hotel ID
  Future<List<Review>> fetchReviewsByHotelId(String hotelId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.reviewsCollection)
          .where('hotelId', isEqualTo: hotelId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => Review.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      debugPrint('Error fetching reviews by hotel ID: $e');
      return [];
    }
  }

  // Add review
  Future<String?> addReview(Review review) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.reviewsCollection)
          .add(review.toMap());

      // Update hotel rating
      await updateHotelRating(review.hotelId);

      return docRef.id;
    } catch (e) {
      debugPrint('Error adding review: $e');
      return null;
    }
  }

  // Update hotel rating
  Future<void> updateHotelRating(String hotelId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.reviewsCollection)
          .where('hotelId', isEqualTo: hotelId)
          .get();

      if (snapshot.docs.isEmpty) return;

      final reviews = snapshot.docs
          .map((doc) => Review.fromMap({...doc.data(), 'id': doc.id}))
          .toList();

      final totalRating =
          reviews.fold(0.0, (total, review) => total + review.rating);
      final averageRating = totalRating / reviews.length;

      await _firestore
          .collection(AppConstants.hotelsCollection)
          .doc(hotelId)
          .update({'rating': averageRating});
    } catch (e) {
      debugPrint('Error updating hotel rating: $e');
    }
  }

  // Search hotels
  Future<List<Hotel>> searchHotels(String query) async {
    try {
      // Search by name
      final nameSnapshot = await _firestore
          .collection(AppConstants.hotelsCollection)
          .where('name', isGreaterThanOrEqualTo: query)
          .where('name', isLessThanOrEqualTo: '$query\uf8ff')
          .get();

      // Search by city
      final citySnapshot = await _firestore
          .collection(AppConstants.hotelsCollection)
          .where('city', isGreaterThanOrEqualTo: query)
          .where('city', isLessThanOrEqualTo: '$query\uf8ff')
          .get();

      // Combine results
      final Set<String> hotelIds = {};
      final List<Hotel> results = [];

      for (final doc in nameSnapshot.docs) {
        if (!hotelIds.contains(doc.id)) {
          hotelIds.add(doc.id);
          results.add(Hotel.fromMap({...doc.data(), 'id': doc.id}));
        }
      }

      for (final doc in citySnapshot.docs) {
        if (!hotelIds.contains(doc.id)) {
          hotelIds.add(doc.id);
          results.add(Hotel.fromMap({...doc.data(), 'id': doc.id}));
        }
      }

      return results;
    } catch (e) {
      debugPrint('Error searching hotels: $e');
      return [];
    }
  }

  // Toggle favorite
  Future<void> toggleFavorite(String userId, String hotelId) async {
    try {
      final docRef =
          _firestore.collection(AppConstants.usersCollection).doc(userId);

      final doc = await docRef.get();
      if (!doc.exists) return;

      final userData = doc.data()!;
      final List<dynamic> favorites = userData['favorites'] ?? [];

      if (favorites.contains(hotelId)) {
        favorites.remove(hotelId);
      } else {
        favorites.add(hotelId);
      }

      await docRef.update({'favorites': favorites});
    } catch (e) {
      debugPrint('Error toggling favorite: $e');
    }
  }

  // Fetch favorite hotels
  Future<List<Hotel>> fetchFavoriteHotels(String userId) async {
    try {
      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (!userDoc.exists) return [];

      final userData = userDoc.data()!;
      final List<dynamic> favorites = userData['favorites'] ?? [];

      if (favorites.isEmpty) return [];

      final List<Hotel> hotels = [];
      for (final hotelId in favorites) {
        final hotel = await fetchHotelById(hotelId);
        if (hotel != null) {
          hotels.add(hotel);
        }
      }

      return hotels;
    } catch (e) {
      debugPrint('Error fetching favorite hotels: $e');
      return [];
    }
  }
}
