import {
  collection,
  doc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  getDocs,
  getDoc,
  serverTimestamp,
  orderBy,
  limit,
  Timestamp,
  onSnapshot
} from 'firebase/firestore';
import { db } from './config';

// Interface for notification data
export interface NotificationData {
  id?: string;
  userId: string;
  title: string;
  message: string;
  type: 'booking' | 'system' | 'alert' | 'info';
  isRead: boolean;
  relatedId?: string; // ID of related entity (booking, hotel, etc.)
  createdAt?: any;
}

/**
 * Create a new notification
 */
export const createNotification = async (notificationData: NotificationData): Promise<string> => {
  try {
    // Create a new document reference
    const notificationRef = doc(collection(db, 'notifications'));

    // Add the notification data with timestamps
    await setDoc(notificationRef, {
      ...notificationData,
      isRead: false,
      createdAt: serverTimestamp()
    });

    return notificationRef.id;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
};

/**
 * Get notifications for a specific user
 */
export const getNotificationsForUser = async (userId: string, unreadOnly: boolean = false, limitCount: number = 20) => {
  try {
    if (!userId) {
      console.error('No userId provided to getNotificationsForUser');
      return [];
    }

    let notificationsQuery;

    try {
      if (unreadOnly) {
        notificationsQuery = query(
          collection(db, 'notifications'),
          where('userId', '==', userId),
          where('isRead', '==', false),
          orderBy('createdAt', 'desc'),
          limit(limitCount)
        );
      } else {
        notificationsQuery = query(
          collection(db, 'notifications'),
          where('userId', '==', userId),
          orderBy('createdAt', 'desc'),
          limit(limitCount)
        );
      }
    } catch (queryError) {
      console.error('Error creating notifications query:', queryError);
      return [];
    }

    try {
      const notificationsSnapshot = await getDocs(notificationsQuery);
      const notifications: NotificationData[] = [];

      notificationsSnapshot.forEach((doc) => {
        try {
          const data = doc.data();
          notifications.push({
            id: doc.id,
            ...data,
            // Ensure these fields have default values if missing
            userId: data.userId || userId,
            title: data.title || 'Notification',
            message: data.message || 'No message',
            type: (data.type as 'booking' | 'system' | 'alert' | 'info') || 'info',
            isRead: data.isRead || false
          } as NotificationData);
        } catch (docError) {
          console.error('Error processing notification document:', docError);
        }
      });

      return notifications;
    } catch (snapshotError) {
      console.error('Error getting notifications snapshot:', snapshotError);
      return [];
    }
  } catch (error) {
    console.error('Error getting notifications:', error);
    // Return empty array instead of throwing
    return [];
  }
};

/**
 * Mark a notification as read
 */
export const markNotificationAsRead = async (notificationId: string) => {
  try {
    const notificationRef = doc(db, 'notifications', notificationId);

    await updateDoc(notificationRef, {
      isRead: true
    });

    return true;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
};

/**
 * Mark all notifications as read for a user
 */
export const markAllNotificationsAsRead = async (userId: string) => {
  try {
    const notificationsQuery = query(
      collection(db, 'notifications'),
      where('userId', '==', userId),
      where('isRead', '==', false)
    );

    const notificationsSnapshot = await getDocs(notificationsQuery);

    const updatePromises = notificationsSnapshot.docs.map(doc =>
      updateDoc(doc.ref, { isRead: true })
    );

    await Promise.all(updatePromises);

    return true;
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    throw error;
  }
};

/**
 * Delete a notification
 */
export const deleteNotification = async (notificationId: string) => {
  try {
    await deleteDoc(doc(db, 'notifications', notificationId));
    return true;
  } catch (error) {
    console.error('Error deleting notification:', error);
    throw error;
  }
};

/**
 * Get unread notification count for a user
 */
export const getUnreadNotificationCount = async (userId: string): Promise<number> => {
  try {
    const notificationsQuery = query(
      collection(db, 'notifications'),
      where('userId', '==', userId),
      where('isRead', '==', false)
    );

    const notificationsSnapshot = await getDocs(notificationsQuery);
    return notificationsSnapshot.size;
  } catch (error) {
    console.error('Error getting unread notification count:', error);
    throw error;
  }
};

/**
 * Subscribe to notifications for a user
 * Returns an unsubscribe function
 */
export const subscribeToNotifications = (
  userId: string,
  callback: (notifications: NotificationData[]) => void
): (() => void) => {
  try {
    if (!userId) {
      console.error('No userId provided to subscribeToNotifications');
      callback([]);
      // Return a no-op function
      return () => {};
    }

    let notificationsQuery;
    try {
      notificationsQuery = query(
        collection(db, 'notifications'),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc'),
        limit(20)
      );
    } catch (queryError) {
      console.error('Error creating notifications subscription query:', queryError);
      callback([]);
      // Return a no-op function
      return () => {};
    }

    const unsubscribe = onSnapshot(
      notificationsQuery,
      (snapshot) => {
        try {
          const notifications: NotificationData[] = [];

          snapshot.forEach((doc) => {
            try {
              const data = doc.data();
              notifications.push({
                id: doc.id,
                ...data,
                // Ensure these fields have default values if missing
                userId: data.userId || userId,
                title: data.title || 'Notification',
                message: data.message || 'No message',
                type: (data.type as 'booking' | 'system' | 'alert' | 'info') || 'info',
                isRead: data.isRead || false
              } as NotificationData);
            } catch (docError) {
              console.error('Error processing notification document in subscription:', docError);
            }
          });

          callback(notifications);
        } catch (snapshotError) {
          console.error('Error processing notifications snapshot in subscription:', snapshotError);
          callback([]);
        }
      },
      (error) => {
        console.error('Error in notifications subscription:', error);
        callback([]);
      }
    );

    return unsubscribe;
  } catch (error) {
    console.error('Error setting up notifications subscription:', error);
    callback([]);
    // Return a no-op function
    return () => {};
  }
};

/**
 * Create a booking notification
 */
export const createBookingNotification = async (
  userId: string,
  bookingId: string,
  guestName: string,
  hotelName: string
): Promise<string> => {
  const notification: NotificationData = {
    userId,
    title: 'New Booking',
    message: `New booking from ${guestName} for ${hotelName}`,
    type: 'booking',
    isRead: false,
    relatedId: bookingId
  };

  return createNotification(notification);
};

/**
 * Create a system notification
 */
export const createSystemNotification = async (
  userId: string,
  title: string,
  message: string
): Promise<string> => {
  const notification: NotificationData = {
    userId,
    title,
    message,
    type: 'system',
    isRead: false
  };

  return createNotification(notification);
};
