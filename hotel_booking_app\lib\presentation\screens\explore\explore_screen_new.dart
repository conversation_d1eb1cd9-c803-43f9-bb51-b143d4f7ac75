import 'package:flutter/material.dart';
import 'package:hotel_booking_app/core/theme/app_theme.dart';
import 'package:hotel_booking_app/data/models/hotel_model.dart';
import 'package:hotel_booking_app/data/services/auth_service.dart';
import 'package:hotel_booking_app/data/services/hotel_service.dart';
import 'package:hotel_booking_app/data/services/notification_service.dart';
import 'package:hotel_booking_app/presentation/screens/hotel/hotel_details_screen.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_text_field.dart';
import 'package:hotel_booking_app/presentation/widgets/notification_badge.dart';
import 'package:hotel_booking_app/utils/hotel_seeder.dart';
import 'package:provider/provider.dart';

class ExploreScreen extends StatefulWidget {
  const ExploreScreen({super.key});

  @override
  State<ExploreScreen> createState() => _ExploreScreenState();
}

class _ExploreScreenState extends State<ExploreScreen> {
  final _searchController = TextEditingController();
  List<Hotel> _searchResults = [];
  List<Hotel> _filteredHotels = [];
  bool _isSearching = false;
  bool _isFiltering = false;

  // Filter state
  RangeValues _priceRange = const RangeValues(1000, 20000);
  double _minRating = 0;
  List<String> _selectedAmenities = [];
  String? _selectedPropertyType;

  @override
  void initState() {
    super.initState();

    // Clear any active search and search text
    _isSearching = false;
    _isFiltering = false;
    _searchResults = [];
    _filteredHotels = [];
    _searchController.clear(); // Make sure search text is empty

    // Fetch hotels and popular hotels
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchHotels();
      _initializeNotifications();

      // Force the UI to show all hotels, not search results
      if (mounted) {
        setState(() {
          _isSearching = false;
          _isFiltering = false;
          _searchResults = [];
          _filteredHotels = [];
        });
      }
    });
  }

  void _initializeNotifications() {
    final authService = Provider.of<AuthService>(context, listen: false);
    if (authService.isAuthenticated && authService.user != null) {
      final notificationService =
          Provider.of<NotificationService>(context, listen: false);
      notificationService.initializeNotifications(authService.user!.uid);
    }
  }

  Future<void> _fetchHotels() async {
    debugPrint('ExploreScreen: Starting to fetch hotels');

    // IMPORTANT: Force clear any active search to ensure hotels are displayed
    if (mounted) {
      setState(() {
        _isSearching = false;
        _searchResults = [];
        // Clear search text if there was any
        _searchController.clear();
      });
    }

    // First check if the hotels collection exists and has data
    final checkResult = await HotelSeeder.checkHotelsCollection();
    debugPrint('Hotels collection check result: $checkResult');

    // ALWAYS create hotels to ensure we have data
    debugPrint(
        'ExploreScreen: Creating real hotel data regardless of current state');
    final success = await HotelSeeder.seedHotels();
    debugPrint('ExploreScreen: Hotels seeded successfully: $success');

    // Show a snackbar if mounted
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Added real hotel data to the database'),
          duration: Duration(seconds: 2),
        ),
      );
    }

    // Check if widget is still mounted before accessing context
    if (!mounted) return;

    final hotelService = Provider.of<HotelService>(context, listen: false);

    // Clear any existing hotels to force a fresh fetch
    hotelService.clearHotels();

    // Fetch hotels from Firestore
    await hotelService.fetchHotels();
    await hotelService.fetchPopularHotels();

    // Log the results after fetching
    debugPrint(
        'ExploreScreen: After fetch - Hotels count: ${hotelService.hotels.length}');
    debugPrint(
        'ExploreScreen: After fetch - Popular hotels count: ${hotelService.popularHotels.length}');

    if (hotelService.hotels.isNotEmpty) {
      debugPrint('First hotel: ${hotelService.hotels.first.name}');
    }

    if (hotelService.hotels.isEmpty) {
      debugPrint('ExploreScreen: WARNING - No hotels were fetched');

      // If still no hotels, try creating more hotels and fetching again
      debugPrint('ExploreScreen: Creating more hotels and fetching again');
      await HotelSeeder.seedHotels();

      // Check if widget is still mounted before accessing context
      if (!mounted) return;

      await hotelService.fetchHotels();
      debugPrint(
          'ExploreScreen: After second fetch - Hotels count: ${hotelService.hotels.length}');
    }

    // IMPORTANT: Force clear any active search again to ensure hotels are displayed
    if (mounted) {
      setState(() {
        _isSearching = false;
        _searchResults = [];
        // Clear search text if there was any
        _searchController.clear();
      });
    }

    if (hotelService.error != null) {
      debugPrint('ExploreScreen: Error during fetch: ${hotelService.error}');
    }
  }

  Future<void> _searchHotels(String query) async {
    // If query is empty, show all hotels
    if (query.isEmpty) {
      setState(() {
        _isSearching = false;
        _isFiltering = false;
        _searchResults = [];
        _filteredHotels = [];
        debugPrint('Search query is empty, showing all hotels');
      });
      return;
    }

    debugPrint('Searching for hotels with query: "$query"');
    setState(() {
      _isSearching = true;
      _isFiltering = false;
    });

    final hotelService = Provider.of<HotelService>(context, listen: false);
    final results = await hotelService.searchHotels(query);

    debugPrint('Search results: ${results.length} hotels found');
    if (results.isNotEmpty) {
      debugPrint('First result: ${results.first.name}');
    }

    // Only update state if the search query hasn't changed
    if (_searchController.text == query) {
      setState(() {
        _searchResults = results;
        _filteredHotels = [];
        _isSearching = false;
        _isFiltering = false;
      });
    } else {
      debugPrint('Search query changed during search, ignoring results');
    }
  }

  void _applyFilters() {
    debugPrint(
        'Applying filters: Price range: $_priceRange, Min rating: $_minRating, Amenities: $_selectedAmenities, Property type: $_selectedPropertyType');

    final hotelService = Provider.of<HotelService>(context, listen: false);

    // Apply filters
    final filteredResults = hotelService.filterHotels(
      minPrice: _priceRange.start,
      maxPrice: _priceRange.end,
      minRating: _minRating,
      amenities: _selectedAmenities.isEmpty ? null : _selectedAmenities,
      propertyType: _selectedPropertyType,
    );

    setState(() {
      _filteredHotels = filteredResults;
      _isFiltering = true;
      _isSearching = false;
      _searchResults = [];
      _searchController.clear();
    });

    // Show a snackbar with filter info
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            'Filters applied: Price ₹${_priceRange.start.round()}-₹${_priceRange.end.round()}, '
            'Rating ${_minRating > 0 ? "$_minRating★+" : "Any"}, '
            'Amenities: ${_selectedAmenities.isEmpty ? "Any" : _selectedAmenities.join(", ")}'),
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'Clear',
          onPressed: _clearFilters,
        ),
      ),
    );
  }

  void _clearFilters() {
    setState(() {
      _isFiltering = false;
      _filteredHotels = [];
      _priceRange = const RangeValues(1000, 20000);
      _minRating = 0;
      _selectedAmenities = [];
      _selectedPropertyType = null;
    });
  }

  void _navigateToHotelDetails(String hotelId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HotelDetailsScreen(hotelId: hotelId),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final hotelService = Provider.of<HotelService>(context);
    final authService = Provider.of<AuthService>(context);

    final hotels = hotelService.hotels;
    final popularHotels = hotelService.popularHotels;
    final isLoading = hotelService.isLoading;

    // Debug information during build
    debugPrint('ExploreScreen build: Hotels count: ${hotels.length}');
    debugPrint(
        'ExploreScreen build: Popular hotels count: ${popularHotels.length}');
    debugPrint('ExploreScreen build: isLoading: $isLoading');

    if (hotelService.error != null) {
      debugPrint('ExploreScreen build: Error: ${hotelService.error}');
    }

    // Popular destinations (cities)
    final cities = <String>[];
    for (final hotel in hotels) {
      if (!cities.contains(hotel.city) && cities.length < 10) {
        cities.add(hotel.city);
      }
    }

    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            // App Bar with Gradient Background
            SliverAppBar(
              floating: true,
              pinned: true,
              expandedHeight: 120,
              backgroundColor: AppTheme.primaryColor,
              actions: [
                // Refresh button
                IconButton(
                  icon: const Icon(Icons.refresh),
                  tooltip: 'Refresh Hotels',
                  onPressed: () {
                    _fetchHotels();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Refreshing hotels...'),
                        duration: Duration(seconds: 1),
                      ),
                    );
                  },
                ),
                // Firebase Test button
                IconButton(
                  icon: const Icon(Icons.bug_report),
                  tooltip: 'Firebase Test',
                  onPressed: () {
                    Navigator.pushNamed(context, '/firebase_test');
                  },
                ),
                const NotificationBadge(),
                const SizedBox(width: 8),
              ],
              flexibleSpace: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppTheme.primaryColor,
                      AppTheme.accentColor,
                    ],
                  ),
                ),
                child: FlexibleSpaceBar(
                  titlePadding: const EdgeInsets.only(left: 16, bottom: 16),
                  title: const Text(
                    'Discover',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 22,
                    ),
                  ),
                  background: Stack(
                    children: [
                      // Decorative elements
                      Positioned(
                        top: -20,
                        right: -20,
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: -40,
                        left: -20,
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(70),
                child: Container(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  child: CustomTextField(
                    controller: _searchController,
                    hintText: 'Search for hotels, cities, etc.',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.filter_list),
                      onPressed: () {
                        // Show filter options
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.vertical(
                              top: Radius.circular(20),
                            ),
                          ),
                          builder: (context) => StatefulBuilder(
                            builder: (context, setModalState) {
                              // Use the class state variables
                              return Container(
                                padding: const EdgeInsets.all(20),
                                height:
                                    MediaQuery.of(context).size.height * 0.7,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        const Text(
                                          'Filter Options',
                                          style: TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        IconButton(
                                          icon: const Icon(Icons.close),
                                          onPressed: () =>
                                              Navigator.pop(context),
                                        ),
                                      ],
                                    ),
                                    const Divider(),
                                    Expanded(
                                      child: SingleChildScrollView(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            // Price Range
                                            const Text(
                                              'Price Range (₹)',
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const SizedBox(height: 10),
                                            RangeSlider(
                                              values: _priceRange,
                                              min: 1000,
                                              max: 50000,
                                              divisions: 49,
                                              labels: RangeLabels(
                                                '₹${_priceRange.start.round()}',
                                                '₹${_priceRange.end.round()}',
                                              ),
                                              onChanged: (values) {
                                                setModalState(() {
                                                  _priceRange = values;
                                                });
                                              },
                                            ),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text('₹1,000',
                                                    style: TextStyle(
                                                        color: Colors
                                                            .grey.shade600)),
                                                Text('₹50,000',
                                                    style: TextStyle(
                                                        color: Colors
                                                            .grey.shade600)),
                                              ],
                                            ),
                                            const SizedBox(height: 20),

                                            // Rating
                                            const Text(
                                              'Minimum Rating',
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const SizedBox(height: 10),
                                            Wrap(
                                              spacing: 8,
                                              children: [
                                                for (double i = 0; i <= 5; i++)
                                                  FilterChip(
                                                    label: i == 0
                                                        ? const Text('Any')
                                                        : Row(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            children: [
                                                              Text('$i'),
                                                              const Icon(
                                                                  Icons.star,
                                                                  size: 16,
                                                                  color: Colors
                                                                      .amber),
                                                            ],
                                                          ),
                                                    selected: _minRating == i,
                                                    onSelected: (selected) {
                                                      setModalState(() {
                                                        _minRating =
                                                            selected ? i : 0;
                                                      });
                                                    },
                                                  ),
                                              ],
                                            ),
                                            const SizedBox(height: 20),

                                            // Amenities
                                            const Text(
                                              'Amenities',
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const SizedBox(height: 10),
                                            Wrap(
                                              spacing: 8,
                                              runSpacing: 8,
                                              children: [
                                                for (String amenity in [
                                                  'WiFi',
                                                  'Pool',
                                                  'Gym',
                                                  'Restaurant',
                                                  'Parking',
                                                  'Spa',
                                                  'Room Service',
                                                  'Bar',
                                                  'Air Conditioning',
                                                ])
                                                  FilterChip(
                                                    label: Text(amenity),
                                                    selected: _selectedAmenities
                                                        .contains(amenity),
                                                    onSelected: (selected) {
                                                      setModalState(() {
                                                        if (selected) {
                                                          _selectedAmenities
                                                              .add(amenity);
                                                        } else {
                                                          _selectedAmenities
                                                              .remove(amenity);
                                                        }
                                                      });
                                                    },
                                                  ),
                                              ],
                                            ),
                                            const SizedBox(height: 20),

                                            // Property Type
                                            const Text(
                                              'Property Type',
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const SizedBox(height: 10),
                                            Wrap(
                                              spacing: 8,
                                              runSpacing: 8,
                                              children: [
                                                for (String type in [
                                                  'Hotel',
                                                  'Resort',
                                                  'Villa',
                                                  'Apartment',
                                                  'Homestay',
                                                ])
                                                  ChoiceChip(
                                                    label: Text(type),
                                                    selected:
                                                        _selectedPropertyType ==
                                                            type,
                                                    onSelected: (selected) {
                                                      setModalState(() {
                                                        _selectedPropertyType =
                                                            selected
                                                                ? type
                                                                : null;
                                                      });
                                                    },
                                                  ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    const Divider(),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: OutlinedButton(
                                            onPressed: () {
                                              setModalState(() {
                                                _priceRange = const RangeValues(
                                                    1000, 20000);
                                                _minRating = 0;
                                                _selectedAmenities = [];
                                                _selectedPropertyType = null;
                                              });
                                            },
                                            child: const Text('Reset'),
                                          ),
                                        ),
                                        const SizedBox(width: 16),
                                        Expanded(
                                          child: ElevatedButton(
                                            onPressed: () {
                                              Navigator.pop(context);

                                              // Apply filters
                                              _applyFilters();
                                            },
                                            child: const Text('Apply Filters'),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                    onSubmitted: (value) {
                      _searchHotels(value);
                    },
                  ),
                ),
              ),
            ),

            if (isLoading)
              SliverFillRemaining(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                            AppTheme.secondaryColor),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Loading amazing hotels for you...',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else if (_isSearching)
              SliverFillRemaining(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                            AppTheme.secondaryColor),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Searching for hotels...',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else if (_searchController.text.isNotEmpty)
              // Search Results Header
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.search, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            'Search Results',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _searchResults.isNotEmpty
                            ? 'Found ${_searchResults.length} hotels matching "${_searchController.text}"'
                            : 'No hotels found matching "${_searchController.text}"',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Divider(),
                    ],
                  ),
                ),
              ),

            // Search Results or Filtered Results List
            if (_isSearching)
              SliverFillRemaining(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                            AppTheme.secondaryColor),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Searching for hotels...',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else if (_searchResults.isNotEmpty)
              // Search Results
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final hotel = _searchResults[index];
                    return _buildHotelCard(hotel, authService);
                  },
                  childCount: _searchResults.length,
                ),
              )
            else if (_isFiltering && _filteredHotels.isNotEmpty)
              // Filtered Results
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final hotel = _filteredHotels[index];
                    return _buildHotelCard(hotel, authService);
                  },
                  childCount: _filteredHotels.length,
                ),
              )
            else if ((_isFiltering && _filteredHotels.isEmpty) ||
                (_searchController.text.isNotEmpty && _searchResults.isEmpty))
              // No Results Found
              SliverFillRemaining(
                hasScrollBody: false,
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _isFiltering ? Icons.filter_list_off : Icons.search_off,
                        size: 80,
                        color: Colors.grey.shade300,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No Results Found',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _isFiltering
                            ? 'No hotels match your filter criteria.\nTry adjusting your filters.'
                            : 'We couldn\'t find any hotels matching "${_searchController.text}".\nTry a different search term.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: () {
                          if (_isFiltering) {
                            _clearFilters();
                          } else {
                            _searchController.clear();
                            setState(() {
                              _isSearching = false;
                              _searchResults = [];
                            });
                          }
                        },
                        icon: Icon(_isFiltering
                            ? Icons.filter_alt_off
                            : Icons.refresh),
                        label: Text(
                            _isFiltering ? 'Clear Filters' : 'Clear Search'),
                      ),
                    ],
                  ),
                ),
              ),
            if (!_isSearching || _searchController.text.isEmpty) ...[
              // Content - Popular Destinations with Enhanced UI
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Popular Destinations',
                            style: TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          TextButton(
                            onPressed: () {
                              // View all destinations
                            },
                            child: const Text('View All'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        height: 160,
                        child: cities.isEmpty
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.location_city,
                                      size: 48,
                                      color: Colors.grey.shade400,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'No destinations available',
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'Please check your Firebase connection',
                                      style: TextStyle(
                                        color: Colors.red.shade600,
                                        fontSize: 14,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Project ID: linkinblink-hotel',
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                        fontSize: 12,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    ElevatedButton.icon(
                                      onPressed: () {
                                        _fetchHotels();
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            content:
                                                Text('Refreshing hotels...'),
                                            duration: Duration(seconds: 1),
                                          ),
                                        );
                                      },
                                      icon: const Icon(Icons.refresh),
                                      label: const Text('Refresh'),
                                    ),
                                  ],
                                ),
                              )
                            : ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: cities.length,
                                itemBuilder: (context, index) {
                                  final city = cities[index];
                                  return Container(
                                    width: 140,
                                    margin: const EdgeInsets.only(right: 16),
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade300,
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.1),
                                          blurRadius: 8,
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(16),
                                      child: Stack(
                                        children: [
                                          // City Image
                                          Positioned.fill(
                                            child: Image.network(
                                              'https://source.unsplash.com/random/300x400?city=$city',
                                              fit: BoxFit.cover,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return Container(
                                                  color: Colors.grey.shade300,
                                                  child: const Icon(
                                                    Icons.image_not_supported,
                                                    color: Colors.grey,
                                                    size: 40,
                                                  ),
                                                );
                                              },
                                            ),
                                          ),

                                          // Gradient overlay
                                          Positioned.fill(
                                            child: Container(
                                              decoration: BoxDecoration(
                                                gradient: LinearGradient(
                                                  begin: Alignment.topCenter,
                                                  end: Alignment.bottomCenter,
                                                  colors: [
                                                    Colors.transparent,
                                                    Colors.black
                                                        .withOpacity(0.7),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),

                                          // City name and info
                                          Positioned(
                                            bottom: 0,
                                            left: 0,
                                            right: 0,
                                            child: Container(
                                              padding: const EdgeInsets.all(12),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    city,
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 16,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Row(
                                                    children: [
                                                      const Icon(
                                                        Icons.hotel,
                                                        color: Colors.white70,
                                                        size: 14,
                                                      ),
                                                      const SizedBox(width: 4),
                                                      Text(
                                                        '${hotels.where((h) => h.city == city).length} Hotels',
                                                        style: const TextStyle(
                                                          color: Colors.white70,
                                                          fontSize: 12,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                      ),
                      const SizedBox(height: 32),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Popular Hotels',
                            style: TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          TextButton(
                            onPressed: () {
                              // View all popular hotels
                            },
                            child: const Text('View All'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),

              // Popular Hotels List
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    if (popularHotels.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(height: 20),
                            Icon(
                              Icons.hotel,
                              size: 48,
                              color: Colors.grey.shade400,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'No popular hotels available',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Please check your Firebase connection',
                              style: TextStyle(
                                color: Colors.red.shade600,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Project ID: linkinblink-hotel',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 12,
                              ),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton.icon(
                              onPressed: () {
                                _fetchHotels();
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Refreshing hotels...'),
                                    duration: Duration(seconds: 1),
                                  ),
                                );
                              },
                              icon: const Icon(Icons.refresh),
                              label: const Text('Refresh'),
                            ),
                            const SizedBox(height: 20),
                          ],
                        ),
                      );
                    }

                    final hotel = popularHotels[index];
                    return _buildHotelCard(hotel, authService);
                  },
                  childCount: popularHotels.isEmpty ? 1 : popularHotels.length,
                ),
              ),

              // All Hotels Section
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'All Hotels',
                            style: TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          // Sort button instead of filter
                          TextButton.icon(
                            onPressed: () {
                              // Show sort options
                              showModalBottomSheet(
                                context: context,
                                shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(20),
                                  ),
                                ),
                                builder: (context) => Container(
                                  padding: const EdgeInsets.all(20),
                                  height: 250,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'Sort By',
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 20),
                                      ListTile(
                                        leading: const Icon(Icons.trending_up),
                                        title: const Text('Price: Low to High'),
                                        onTap: () {
                                          Navigator.pop(context);
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            const SnackBar(
                                              content: Text(
                                                  'Sorting will be applied in the next update'),
                                              duration: Duration(seconds: 2),
                                            ),
                                          );
                                        },
                                      ),
                                      ListTile(
                                        leading:
                                            const Icon(Icons.trending_down),
                                        title: const Text('Price: High to Low'),
                                        onTap: () {
                                          Navigator.pop(context);
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            const SnackBar(
                                              content: Text(
                                                  'Sorting will be applied in the next update'),
                                              duration: Duration(seconds: 2),
                                            ),
                                          );
                                        },
                                      ),
                                      ListTile(
                                        leading: const Icon(Icons.star),
                                        title: const Text('Rating'),
                                        onTap: () {
                                          Navigator.pop(context);
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            const SnackBar(
                                              content: Text(
                                                  'Sorting will be applied in the next update'),
                                              duration: Duration(seconds: 2),
                                            ),
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                            icon: const Icon(Icons.sort),
                            label: const Text('Sort'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Discover all our amazing hotels',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),

              // All Hotels List
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    if (hotels.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(height: 20),
                            Icon(
                              Icons.hotel,
                              size: 48,
                              color: Colors.grey.shade400,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'No hotels available',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Please check your Firebase connection',
                              style: TextStyle(
                                color: Colors.red.shade600,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Project ID: linkinblink-hotel',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 12,
                              ),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton.icon(
                              onPressed: () {
                                _fetchHotels();
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Refreshing hotels...'),
                                    duration: Duration(seconds: 1),
                                  ),
                                );
                              },
                              icon: const Icon(Icons.refresh),
                              label: const Text('Refresh'),
                            ),
                            const SizedBox(height: 20),
                          ],
                        ),
                      );
                    }

                    final hotel = hotels[index];
                    return _buildHotelCard(hotel, authService);
                  },
                  childCount: hotels.isEmpty ? 1 : hotels.length,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHotelCard(Hotel hotel, AuthService authService) {
    // Check if hotel is in favorites
    final hotelService = Provider.of<HotelService>(context, listen: false);
    bool isFavorite = false;
    if (authService.isAuthenticated && authService.user != null) {
      // We'll use a synchronous check for now
      isFavorite = hotelService.favoriteHotels.any((h) => h.id == hotel.id);
    }

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 24),
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 15,
              offset: const Offset(0, 5),
              spreadRadius: 1,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _navigateToHotelDetails(hotel.id),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Hotel Image with Overlay
                  Stack(
                    children: [
                      // Hotel Image
                      SizedBox(
                        height: 200,
                        width: double.infinity,
                        child: Hero(
                          tag: 'hotel_image_${hotel.id}',
                          child: Image.network(
                            hotel.images.isNotEmpty
                                ? hotel.images.first
                                : 'https://source.unsplash.com/random/600x400?luxury+hotel',
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                height: 200,
                                color: Colors.grey.shade300,
                                child: Center(
                                  child: Icon(
                                    Icons.hotel,
                                    color: Colors.grey.shade400,
                                    size: 60,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),

                      // Gradient overlay for better text visibility
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        height: 80,
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withOpacity(0.5),
                              ],
                            ),
                          ),
                        ),
                      ),

                      // Favorite button
                      Positioned(
                        top: 12,
                        right: 12,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.9),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: IconButton(
                            icon: Icon(
                              isFavorite
                                  ? Icons.favorite
                                  : Icons.favorite_border,
                              color: isFavorite
                                  ? Colors.red
                                  : Colors.grey.shade600,
                            ),
                            onPressed: () {
                              if (authService.isAuthenticated) {
                                hotelService.toggleFavorite(
                                  authService.user!.uid,
                                  hotel.id,
                                );
                              } else {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content:
                                        Text('Please sign in to save hotels'),
                                    behavior: SnackBarBehavior.floating,
                                  ),
                                );
                              }
                            },
                          ),
                        ),
                      ),

                      // Price tag
                      Positioned(
                        bottom: 12,
                        right: 12,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.secondaryColor,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              Text(
                                '\$${hotel.price.toStringAsFixed(0)}',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              const Text(
                                '/night',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.white70,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Rating badge
                      Positioned(
                        top: 12,
                        left: 12,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.star,
                                color: Colors.amber.shade600,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                hotel.rating.toStringAsFixed(1),
                                style: TextStyle(
                                  color: Colors.grey.shade800,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Hotel Details
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Hotel Name
                        Text(
                          hotel.name,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 8),

                        // Location
                        Row(
                          children: [
                            Icon(
                              Icons.location_on,
                              size: 16,
                              color: Colors.grey.shade600,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                '${hotel.city}, ${hotel.country}',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 14,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),

                        // Hotel Features
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: [
                            _buildFeatureChip(Icons.wifi, 'WiFi'),
                            _buildFeatureChip(Icons.pool, 'Pool'),
                            _buildFeatureChip(Icons.restaurant, 'Restaurant'),
                            _buildFeatureChip(Icons.local_parking, 'Parking'),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // View Details Button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () => _navigateToHotelDetails(hotel.id),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'View Details',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureChip(IconData icon, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: Colors.grey.shade700,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }
}
