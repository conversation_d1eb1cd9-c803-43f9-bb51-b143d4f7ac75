class AppConstants {
  // App Information
  static const String appName = 'Link In Blink';
  static const String appVersion = '1.0.0';

  // API Endpoints
  static const String baseUrl = 'https://api.linkinblink.com';

  // Shared Preferences Keys
  static const String tokenKey = 'auth_token';
  static const String userIdKey = 'user_id';
  static const String userRoleKey = 'user_role';
  static const String languageCodeKey = 'language_code';
  static const String isDarkModeKey = 'is_dark_mode';

  // User Roles
  static const String roleUser = 'user';
  static const String roleAdmin = 'admin';
  static const String roleSuperAdmin = 'super_admin';
  static const String roleHotelAdmin = 'hotel_admin';
  static const String roleStaff = 'staff';
  static const String roleVendor = 'vendor';

  // Pagination
  static const int defaultPageSize = 10;

  // Booking Status
  static const String statusPending = 'pending';
  static const String statusConfirmed = 'confirmed';
  static const String statusCheckedIn = 'checked_in';
  static const String statusCheckedOut = 'checked_out';
  static const String statusCancelled = 'cancelled';

  // Payment Status
  static const String paymentPending = 'pending';
  static const String paymentCompleted = 'completed';
  static const String paymentFailed = 'failed';
  static const String paymentRefunded = 'refunded';

  // Room Status
  static const String roomAvailable = 'available';
  static const String roomBooked = 'booked';
  static const String roomMaintenance = 'maintenance';

  // Firebase Collections
  static const String usersCollection = 'users';
  static const String hotelsCollection = 'hotels';
  static const String roomsCollection = 'rooms';
  static const String bookingsCollection = 'bookings';
  static const String reviewsCollection = 'reviews';
  static const String paymentsCollection = 'payments';
  static const String paymentMethodsCollection = 'paymentMethods';
  static const String aadhaarVerificationsCollection = 'aadhaar_verifications';
  static const String serviceRequestsCollection = 'serviceRequests';
  static const String mobileNotificationsCollection = 'mobileNotifications';
  static const String loyaltyProgramCollection = 'loyaltyPrograms';
  static const String userLoyaltyCollection = 'userLoyalty';
  static const String loyaltyTransactionsCollection = 'loyaltyTransactions';

  // Payment Status Constants
  static const String paymentProcessing = 'processing';
  static const String statusCompleted = 'completed';

  // Assets Paths
  static const String imagePath = 'assets/images/';
  static const String iconPath = 'assets/icons/';

  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 500);
  static const Duration longAnimationDuration = Duration(milliseconds: 800);
}
