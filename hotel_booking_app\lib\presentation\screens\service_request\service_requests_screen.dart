import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:hotel_booking_app/data/models/booking_model.dart';
import 'package:hotel_booking_app/data/models/service_request_model.dart';
import 'package:hotel_booking_app/data/services/auth_service.dart';
import 'package:hotel_booking_app/data/services/service_request_service.dart';
import 'package:hotel_booking_app/presentation/screens/service_request/service_request_form.dart';
import 'package:hotel_booking_app/presentation/screens/service_request/service_request_details_screen.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_app_bar.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_button.dart';
import 'package:hotel_booking_app/presentation/widgets/error_widget.dart';
import 'package:hotel_booking_app/presentation/widgets/loading_widget.dart';

class ServiceRequestsScreen extends StatefulWidget {
  final Booking booking;

  const ServiceRequestsScreen({
    super.key,
    required this.booking,
  });

  @override
  State<ServiceRequestsScreen> createState() => _ServiceRequestsScreenState();
}

class _ServiceRequestsScreenState extends State<ServiceRequestsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  String? _errorMessage;
  List<BaseServiceRequest> _serviceRequests = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _fetchServiceRequests();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _fetchServiceRequests() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final serviceRequestService =
          Provider.of<ServiceRequestService>(context, listen: false);

      final user = authService.user;
      if (user == null) {
        throw Exception('You must be logged in to view service requests');
      }

      final requests =
          await serviceRequestService.getServiceRequestsForUser(user.uid);

      // Filter requests for this booking
      final filteredRequests = requests
          .where((request) =>
              request.roomNumber == widget.booking.roomName &&
              request.hotelId == widget.booking.hotelId)
          .toList();

      setState(() {
        _serviceRequests = filteredRequests;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading service requests: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Room Services',
        showBackButton: true,
      ),
      body: _isLoading
          ? const LoadingWidget()
          : _errorMessage != null
              ? CustomErrorWidget(
                  message: _errorMessage!,
                  onRetry: _fetchServiceRequests,
                )
              : _buildContent(),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showServiceTypeSelectionDialog(),
        icon: const Icon(Icons.add),
        label: const Text('New Request'),
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.cleaning_services),
              text: 'Cleaning',
            ),
            Tab(
              icon: Icon(Icons.restaurant),
              text: 'Food',
            ),
            Tab(
              icon: Icon(Icons.build),
              text: 'Maintenance',
            ),
          ],
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildServiceRequestsList('cleaning'),
              _buildServiceRequestsList('food'),
              _buildServiceRequestsList('maintenance'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildServiceRequestsList(String serviceType) {
    final filteredRequests = _serviceRequests
        .where((request) => request.type == serviceType)
        .toList();

    if (filteredRequests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getServiceTypeIcon(serviceType),
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No ${_getServiceTypeTitle(serviceType)} requests yet',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Tap the + button to create a new request',
              style: TextStyle(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            CustomButton(
              text: 'Request ${_getServiceTypeTitle(serviceType)}',
              onPressed: () => _navigateToServiceRequestForm(serviceType),
              icon: Icons.add,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _fetchServiceRequests,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredRequests.length,
        itemBuilder: (context, index) {
          final request = filteredRequests[index];
          return _buildServiceRequestCard(request);
        },
      ),
    );
  }

  Widget _buildServiceRequestCard(BaseServiceRequest request) {
    final statusColor = _getStatusColor(request.status);
    final formattedDate =
        DateFormat('MMM d, yyyy h:mm a').format(request.requestTime.toDate());

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToServiceRequestDetails(request),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _getServiceTypeIcon(request.type),
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getServiceTypeTitle(request.type),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withAlpha(25),
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: statusColor),
                    ),
                    child: Text(
                      _getStatusText(request.status),
                      style: TextStyle(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const Divider(),
              const SizedBox(height: 8),
              _buildRequestDetails(request),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(
                    Icons.access_time,
                    size: 16,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    formattedDate,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                  const Spacer(),
                  if (request.status == 'pending')
                    TextButton(
                      onPressed: () => _cancelServiceRequest(request),
                      child: const Text('Cancel'),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRequestDetails(BaseServiceRequest request) {
    if (request is CleaningRequest) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Type: ${_formatCleaningType(request.cleaningType)}'),
          if (request.items != null && request.items!.isNotEmpty)
            Text('Items: ${request.items!.join(', ')}'),
          if (request.notes.isNotEmpty) Text('Notes: ${request.notes}'),
        ],
      );
    } else if (request is FoodOrder) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Items: ${request.items.length}'),
          Text('Total: \$${request.totalAmount.toStringAsFixed(2)}'),
          if (request.notes.isNotEmpty) Text('Notes: ${request.notes}'),
        ],
      );
    } else if (request is MaintenanceRequest) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Issue: ${_formatIssueType(request.issueType)}'),
          Text('Description: ${request.description}'),
          if (request.notes.isNotEmpty) Text('Notes: ${request.notes}'),
        ],
      );
    }

    return const SizedBox.shrink();
  }

  void _showServiceTypeSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Select Service Type'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.cleaning_services),
                title: const Text('Cleaning'),
                onTap: () {
                  Navigator.pop(context);
                  _navigateToServiceRequestForm('cleaning');
                },
              ),
              ListTile(
                leading: const Icon(Icons.restaurant),
                title: const Text('Food Service'),
                onTap: () {
                  Navigator.pop(context);
                  _navigateToServiceRequestForm('food');
                },
              ),
              ListTile(
                leading: const Icon(Icons.build),
                title: const Text('Maintenance'),
                onTap: () {
                  Navigator.pop(context);
                  _navigateToServiceRequestForm('maintenance');
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  void _navigateToServiceRequestForm(String serviceType) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ServiceRequestForm(
          booking: widget.booking,
          serviceType: serviceType,
        ),
      ),
    );

    if (result == true) {
      _fetchServiceRequests();
    }
  }

  void _navigateToServiceRequestDetails(BaseServiceRequest request) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ServiceRequestDetailsScreen(
          request: request,
          booking: widget.booking,
        ),
      ),
    );

    if (result == true) {
      _fetchServiceRequests();
    }
  }

  Future<void> _cancelServiceRequest(BaseServiceRequest request) async {
    if (request.id == null) return;
    if (!mounted) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Request'),
        content:
            const Text('Are you sure you want to cancel this service request?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Yes'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;
    if (!mounted) return;

    try {
      final serviceRequestService =
          Provider.of<ServiceRequestService>(context, listen: false);
      await serviceRequestService.cancelServiceRequest(request.id!);

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Service request cancelled successfully'),
          backgroundColor: Colors.green,
        ),
      );

      _fetchServiceRequests();
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error cancelling request: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  IconData _getServiceTypeIcon(String serviceType) {
    switch (serviceType) {
      case 'cleaning':
        return Icons.cleaning_services;
      case 'food':
        return Icons.restaurant;
      case 'maintenance':
        return Icons.build;
      default:
        return Icons.miscellaneous_services;
    }
  }

  String _getServiceTypeTitle(String serviceType) {
    switch (serviceType) {
      case 'cleaning':
        return 'Cleaning';
      case 'food':
        return 'Food Service';
      case 'maintenance':
        return 'Maintenance';
      default:
        return 'Service';
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'in_progress':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatCleaningType(String? cleaningType) {
    if (cleaningType == null) return 'Regular';

    switch (cleaningType) {
      case 'regular':
        return 'Regular Cleaning';
      case 'deep':
        return 'Deep Cleaning';
      case 'turndown':
        return 'Turndown Service';
      case 'special':
        return 'Special Request';
      default:
        return cleaningType;
    }
  }

  String _formatIssueType(String issueType) {
    switch (issueType) {
      case 'plumbing':
        return 'Plumbing';
      case 'electrical':
        return 'Electrical';
      case 'hvac':
        return 'HVAC/Climate Control';
      case 'furniture':
        return 'Furniture';
      case 'appliance':
        return 'Appliance';
      case 'structural':
        return 'Structural';
      case 'other':
        return 'Other';
      default:
        return issueType;
    }
  }
}
