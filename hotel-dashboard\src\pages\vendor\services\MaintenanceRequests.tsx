import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Button,
  CircularProgress,
  Alert,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Chip,
  Badge,
  InputAdornment
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  CheckCircle as CompletedIcon,
  Pending as PendingIcon,
  Build as MaintenanceIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { collection, query, where, orderBy, getDocs, updateDoc, doc, Timestamp } from 'firebase/firestore';
import { db, auth } from '../../../firebase/config';

// Service request statuses
const STATUS_PENDING = 'pending';
const STATUS_IN_PROGRESS = 'in_progress';
const STATUS_COMPLETED = 'completed';
const STATUS_CANCELLED = 'cancelled';

// Maintenance request model
interface MaintenanceRequest {
  id: string;
  roomNumber: string;
  requestTime: Timestamp;
  status: string;
  description: string;
  issueType: string;
  assignedTo?: string;
  completedTime?: Timestamp;
  guestName?: string;
  priority: 'low' | 'medium' | 'high';
  hotelId: string;
  estimatedCompletionTime?: Timestamp;
  partsRequired?: string[];
}

const MaintenanceRequests: React.FC = () => {
  const [requests, setRequests] = useState<MaintenanceRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<MaintenanceRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [issueTypeFilter, setIssueTypeFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Load maintenance requests on component mount
  useEffect(() => {
    fetchMaintenanceRequests();
  }, []);

  // Filter requests when filter or search changes
  useEffect(() => {
    filterRequests();
  }, [statusFilter, issueTypeFilter, searchTerm, requests]);

  const fetchMaintenanceRequests = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!auth.currentUser) {
        throw new Error('You must be logged in to view maintenance requests');
      }

      // In a real app, we would get the hotel ID from the vendor's profile
      // For now, we'll use a mock hotel ID
      const vendorId = auth.currentUser.uid;
      const hotelId = 'hotel123'; // This would come from the vendor's profile

      const requestsRef = collection(db, 'serviceRequests');
      const requestsQuery = query(
        requestsRef,
        where('type', '==', 'maintenance'),
        where('hotelId', '==', hotelId),
        orderBy('requestTime', 'desc')
      );

      const querySnapshot = await getDocs(requestsQuery);

      const requestsList: MaintenanceRequest[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data() as Omit<MaintenanceRequest, 'id'>;
        requestsList.push({
          id: doc.id,
          ...data
        });
      });
      setRequests(requestsList);
    } catch (err: any) {
      console.error('Error fetching maintenance requests:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const filterRequests = () => {
    let filtered = [...requests];

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(request => request.status === statusFilter);
    }

    // Apply issue type filter
    if (issueTypeFilter !== 'all') {
      filtered = filtered.filter(request => request.issueType === issueTypeFilter);
    }

    // Apply search filter
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(
        request =>
          request.roomNumber.toLowerCase().includes(search) ||
          (request.guestName && request.guestName.toLowerCase().includes(search)) ||
          request.description.toLowerCase().includes(search) ||
          request.issueType.toLowerCase().includes(search)
      );
    }

    setFilteredRequests(filtered);
  };

  const handleStatusFilterChange = (event: SelectChangeEvent) => {
    setStatusFilter(event.target.value);
    setPage(0); // Reset to first page when filter changes
  };

  const handleIssueTypeFilterChange = (event: SelectChangeEvent) => {
    setIssueTypeFilter(event.target.value);
    setPage(0); // Reset to first page when filter changes
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0); // Reset to first page when search changes
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case STATUS_PENDING:
        return <Chip icon={<PendingIcon />} label="Pending" color="warning" size="small" />;
      case STATUS_IN_PROGRESS:
        return <Chip icon={<ScheduleIcon />} label="In Progress" color="info" size="small" />;
      case STATUS_COMPLETED:
        return <Chip icon={<CompletedIcon />} label="Completed" color="success" size="small" />;
      case STATUS_CANCELLED:
        return <Chip label="Cancelled" color="error" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const getPriorityChip = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Chip label="High" color="error" size="small" />;
      case 'medium':
        return <Chip label="Medium" color="warning" size="small" />;
      case 'low':
        return <Chip label="Low" color="info" size="small" />;
      default:
        return <Chip label={priority} size="small" />;
    }
  };

  const formatDate = (timestamp: Timestamp) => {
    return new Date(timestamp.seconds * 1000).toLocaleString();
  };

  // Get unique issue types for filter
  const getUniqueIssueTypes = () => {
    const issueTypes = new Set<string>();
    requests.forEach(request => {
      issueTypes.add(request.issueType);
    });
    return Array.from(issueTypes);
  };



  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Maintenance Requests
        </Typography>
        <Button
          variant="contained"
          startIcon={<RefreshIcon />}
          onClick={fetchMaintenanceRequests}
        >
          Refresh
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Total Requests
              </Typography>
              <Typography variant="h3">
                {requests.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'warning.light' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Pending
              </Typography>
              <Typography variant="h3">
                {requests.filter(r => r.status === STATUS_PENDING).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'info.light' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                In Progress
              </Typography>
              <Typography variant="h3">
                {requests.filter(r => r.status === STATUS_IN_PROGRESS).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'success.light' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Completed
              </Typography>
              <Typography variant="h3">
                {requests.filter(r => r.status === STATUS_COMPLETED).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search by room, description, or issue type"
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                label="Status"
                onChange={handleStatusFilterChange}
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value={STATUS_PENDING}>Pending</MenuItem>
                <MenuItem value={STATUS_IN_PROGRESS}>In Progress</MenuItem>
                <MenuItem value={STATUS_COMPLETED}>Completed</MenuItem>
                <MenuItem value={STATUS_CANCELLED}>Cancelled</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Issue Type</InputLabel>
              <Select
                value={issueTypeFilter}
                label="Issue Type"
                onChange={handleIssueTypeFilterChange}
              >
                <MenuItem value="all">All Issue Types</MenuItem>
                {getUniqueIssueTypes().map(type => (
                  <MenuItem key={type} value={type}>{type.charAt(0).toUpperCase() + type.slice(1)}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Requests Table */}
      <Paper>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Room</TableCell>
                    <TableCell>Issue Type</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Request Time</TableCell>
                    <TableCell>Priority</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Assigned To</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredRequests.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} align="center">
                        No maintenance requests found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredRequests
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((request) => (
                        <TableRow key={request.id}>
                          <TableCell>
                            <Typography variant="body1" fontWeight="bold">
                              {request.roomNumber}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={request.issueType.charAt(0).toUpperCase() + request.issueType.slice(1)}
                              size="small"
                              icon={<MaintenanceIcon />}
                            />
                          </TableCell>
                          <TableCell>{request.description}</TableCell>
                          <TableCell>{formatDate(request.requestTime)}</TableCell>
                          <TableCell>{getPriorityChip(request.priority)}</TableCell>
                          <TableCell>{getStatusChip(request.status)}</TableCell>
                          <TableCell>{request.assignedTo || 'Not assigned'}</TableCell>
                          <TableCell>
                            <Button
                              variant="outlined"
                              size="small"
                              disabled={request.status === STATUS_COMPLETED || request.status === STATUS_CANCELLED}
                            >
                              Manage
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={filteredRequests.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Paper>
    </Box>
  );
};

export default MaintenanceRequests;
