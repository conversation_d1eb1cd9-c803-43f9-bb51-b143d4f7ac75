import 'package:flutter/material.dart';
import 'package:hotel_booking_app/core/theme/app_theme.dart';
import 'package:hotel_booking_app/data/services/faq_service.dart';
import 'package:hotel_booking_app/data/services/support_service.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_app_bar.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_button.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

class HelpCenterScreen extends StatefulWidget {
  const HelpCenterScreen({super.key});

  @override
  State<HelpCenterScreen> createState() => _HelpCenterScreenState();
}

class _HelpCenterScreenState extends State<HelpCenterScreen> {
  final TextEditingController _searchController = TextEditingController();
  int _selectedCategoryIndex = 0;
  String _searchQuery = '';
  late FaqService _faqService;
  late SupportService _supportService;

  @override
  void initState() {
    super.initState();
    _faqService = Provider.of<FaqService>(context, listen: false);
    _supportService = Provider.of<SupportService>(context, listen: false);

    // Fetch FAQs if not already loaded
    if (_faqService.faqCategories.isEmpty) {
      _faqService.fetchFaqCategories();
    }

    // Fetch support options if not already loaded
    if (_supportService.supportOptions.isEmpty) {
      _supportService.fetchSupportOptions();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Help Center',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search for help',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey.shade100,
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // Contact Support Button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: CustomButton(
              text: 'Contact Support',
              prefixIcon: const Icon(Icons.support_agent, color: Colors.white),
              onPressed: () {
                _showContactSupportDialog();
              },
              height: 56,
              borderRadius: 12,
            ),
          ),

          const SizedBox(height: 24),

          // FAQ Content
          Expanded(
            child: Consumer<FaqService>(
              builder: (context, faqService, _) {
                if (faqService.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (faqService.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 60,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading FAQs',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade800,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          faqService.error!,
                          style: TextStyle(
                            color: Colors.grey.shade600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            faqService.fetchFaqCategories();
                          },
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                // Filter categories based on search query
                final filteredCategories = _searchQuery.isEmpty
                    ? faqService.faqCategories
                    : faqService.searchFaqs(_searchQuery);

                if (filteredCategories.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.help_outline,
                          size: 60,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No FAQs available',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade800,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                // Ensure selected category index is valid
                if (_selectedCategoryIndex >= filteredCategories.length) {
                  _selectedCategoryIndex = 0;
                }

                return Column(
                  children: [
                    // FAQ Categories
                    SizedBox(
                      height: 100,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: filteredCategories.length,
                        itemBuilder: (context, index) {
                          final category = filteredCategories[index];
                          final isSelected = _selectedCategoryIndex == index;
                          final colorHex = category['color'] as String;
                          final color = Color(int.parse(colorHex.substring(1), radix: 16) + 0xFF000000);

                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedCategoryIndex = index;
                              });
                            },
                            child: Container(
                              width: 100,
                              margin: const EdgeInsets.only(right: 16),
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? color.withAlpha(30)
                                    : Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: isSelected
                                      ? color
                                      : Colors.grey.shade200,
                                  width: 2,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withAlpha(15),
                                    blurRadius: 5,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: color.withAlpha(30),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      _getIconData(category['icon']),
                                      color: color,
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    category['title'],
                                    style: TextStyle(
                                      fontWeight: isSelected
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                      color: isSelected
                                          ? color
                                          : Colors.grey.shade800,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),

                    const SizedBox(height: 16),

                    // FAQ List
                    Expanded(
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: (filteredCategories[_selectedCategoryIndex]['faqs'] as List).length,
                        itemBuilder: (context, index) {
                          final faq = (filteredCategories[_selectedCategoryIndex]['faqs'] as List)[index];
                          final colorHex = filteredCategories[_selectedCategoryIndex]['color'] as String;
                          final color = Color(int.parse(colorHex.substring(1), radix: 16) + 0xFF000000);

                          return _buildFaqItem(
                            faq: faq,
                            color: color,
                            faqService: faqService,
                          );
                        },
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'hotel':
        return Icons.hotel;
      case 'payment':
        return Icons.payment;
      case 'room_service':
        return Icons.room_service;
      case 'account_circle':
        return Icons.account_circle;
      default:
        return Icons.help_outline;
    }

  void _showContactSupportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contact Support'),
        content: Consumer<SupportService>(
          builder: (context, supportService, _) {
            if (supportService.isLoading) {
              return const SizedBox(
                height: 100,
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }

            if (supportService.error != null) {
              return SizedBox(
                height: 100,
                child: Center(
                  child: Text(
                    'Error loading support options',
                    style: TextStyle(color: Colors.red.shade700),
                  ),
                ),
              );
            }

            if (supportService.supportOptions.isEmpty) {
              return const SizedBox(
                height: 100,
                child: Center(
                  child: Text('No support options available'),
                ),
              );
            }

            return Column(
              mainAxisSize: MainAxisSize.min,
              children: supportService.supportOptions.map((option) {
                return Column(
                  children: [
                    _buildContactOption(
                      icon: _getIconData(option['icon']),
                      title: option['title'],
                      subtitle: option['subtitle'],
                      onTap: () {
                        Navigator.pop(context);
                        _handleSupportOptionTap(option);
                      },
                    ),
                    if (supportService.supportOptions.last != option)
                      const SizedBox(height: 16),
                  ],
                );
              }).toList(),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  void _handleSupportOptionTap(Map<String, dynamic> option) {
    final type = option['type'];
    final value = option['value'];

    switch (type) {
      case 'email':
        _launchUrl('mailto:$value');
        break;
      case 'phone':
        _launchUrl('tel:$value');
        break;
      case 'chat':
        // In a real app, you would launch a chat interface
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Opening live chat...'),
          ),
        );
        break;
      default:
        break;
    }
  }

  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url)) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Could not launch $urlString'),
        ),
      );
    }
  }

  Widget _buildContactOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withAlpha(30),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: AppTheme.primaryColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_forward_ios, size: 16),
          ],
        ),
      ),
    );
  }
}
    required Map<String, dynamic> faq,
    required Color color,
    required FaqService faqService,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        title: Text(
          faq['question'],
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 16,
          ),
        ),
        iconColor: color,
        textColor: color,
        childrenPadding: const EdgeInsets.all(16),
        expandedCrossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            faq['answer'],
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton.icon(
                onPressed: () {
                  faqService.submitFaqFeedback(faq['id'], true);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Thank you for your feedback!'),
                    ),
                  );
                },
                icon: const Icon(Icons.thumb_up, size: 16),
                label: const Text('Helpful'),
                style: TextButton.styleFrom(
                  foregroundColor: color,
                ),
              ),
              TextButton.icon(
                onPressed: () {
                  faqService.submitFaqFeedback(faq['id'], false);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Thank you for your feedback!'),
                    ),
                  );
                },
                icon: const Icon(Icons.thumb_down, size: 16),
                label: const Text('Not Helpful'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.grey,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
