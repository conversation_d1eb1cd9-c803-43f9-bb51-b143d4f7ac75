import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Divider,
  CircularProgress,
  Grid,
  <PERSON>,
  <PERSON><PERSON>,
  Al<PERSON>,
  useTheme
} from '@mui/material';
import {
  VerifiedUser as VerifiedUserIcon,
  PendingActions as PendingIcon,
  ErrorOutline as ErrorIcon,
  HelpOutline as HelpIcon,
  Refresh as RefreshIcon,
  Bar<PERSON>hart as BarChartIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { getVerificationStats } from '../services/verificationService';
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';

interface VerificationStats {
  total: number;
  verified: number;
  pending: number;
  failed: number;
  notVerified: number;
}

interface VerificationAnalyticsWidgetProps {
  hotelId?: string;
  title?: string;
  showViewAll?: boolean;
}

const VerificationAnalyticsWidget: React.FC<VerificationAnalyticsWidgetProps> = ({
  hotelId,
  title = 'Aadhaar Verification Analytics',
  showViewAll = true
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [stats, setStats] = useState<VerificationStats>({
    total: 0,
    verified: 0,
    pending: 0,
    failed: 0,
    notVerified: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await getVerificationStats();
      setStats(data);
    } catch (err) {
      console.error('Error fetching verification stats:', err);
      setError('Failed to load verification statistics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, [hotelId]);

  // Prepare data for pie chart
  const chartData = [
    { name: 'Verified', value: stats.verified, color: theme.palette.success.main },
    { name: 'Pending', value: stats.pending, color: theme.palette.warning.main },
    { name: 'Failed', value: stats.failed, color: theme.palette.error.main },
    { name: 'Not Verified', value: stats.notVerified, color: theme.palette.grey[500] }
  ].filter(item => item.value > 0);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Verified':
        return <VerifiedUserIcon fontSize="small" sx={{ color: theme.palette.success.main }} />;
      case 'Pending':
        return <PendingIcon fontSize="small" sx={{ color: theme.palette.warning.main }} />;
      case 'Failed':
        return <ErrorIcon fontSize="small" sx={{ color: theme.palette.error.main }} />;
      case 'Not Verified':
        return <HelpIcon fontSize="small" sx={{ color: theme.palette.grey[500] }} />;
      default:
        return null;
    }
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">{title}</Typography>
          <Box>
            <Button
              size="small"
              startIcon={<RefreshIcon />}
              onClick={fetchStats}
              disabled={loading}
              sx={{ mr: 1 }}
            >
              Refresh
            </Button>
            {showViewAll && (
              <Button
                size="small"
                startIcon={<BarChartIcon />}
                onClick={() => navigate('/vendor/verification-reports')}
              >
                View All
              </Button>
            )}
          </Box>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" height={200}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        ) : (
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Box height={200} display="flex" flexDirection="column" justifyContent="center">
                <Typography variant="subtitle1" gutterBottom>
                  Verification Status
                </Typography>
                <Box mb={2}>
                  <Typography variant="h4" color="primary" fontWeight="bold">
                    {stats.total}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Verifications
                  </Typography>
                </Box>
                <Box>
                  {chartData.map((item) => (
                    <Box key={item.name} display="flex" alignItems="center" mb={1}>
                      {getStatusIcon(item.name)}
                      <Typography variant="body2" sx={{ ml: 1 }}>
                        {item.name}: {item.value} ({Math.round((item.value / stats.total) * 100)}%)
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              {stats.total > 0 ? (
                <Box height={200}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={chartData}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={80}
                        paddingAngle={2}
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        labelLine={false}
                      >
                        {chartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value} users`, 'Count']} />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              ) : (
                <Box
                  height={200}
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  bgcolor={theme.palette.grey[100]}
                  borderRadius={1}
                >
                  <Typography variant="body2" color="text.secondary">
                    No verification data available
                  </Typography>
                </Box>
              )}
            </Grid>
          </Grid>
        )}
      </CardContent>
    </Card>
  );
};

export default VerificationAnalyticsWidget;
