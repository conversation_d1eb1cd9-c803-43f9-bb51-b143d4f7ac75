import 'package:flutter/material.dart';

class FaqService extends ChangeNotifier {
  List<Map<String, dynamic>> _faqCategories = [];
  bool _isLoading = false;
  String? _error;

  FaqService() {
    fetchFaqCategories();
  }

  // Getters
  List<Map<String, dynamic>> get faqCategories => _faqCategories;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Fetch FAQ categories - now uses local data to avoid Firestore permission issues
  Future<void> fetchFaqCategories() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Instead of fetching from Firestore, use local data
      await Future.delayed(
          const Duration(milliseconds: 500)); // Simulate network delay
      _faqCategories = _getLocalFaqData();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Get local FAQ data
  List<Map<String, dynamic>> _getLocalFaqData() {
    // Create category IDs
    final categoryIds = [
      'bookings_category',
      'payments_category',
      'services_category',
      'account_category',
    ];

    // Create categories with their FAQs
    final categories = [
      {
        'id': categoryIds[0],
        'title': 'Bookings',
        'icon': 'hotel',
        'color': '#2196F3', // Blue
        'order': 1,
        'faqs': [
          {
            'id': 'booking_faq_1',
            'categoryId': categoryIds[0],
            'question': 'How do I cancel my booking?',
            'answer':
                'To cancel your booking, go to the Bookings tab, select the booking you want to cancel, and tap on the "Cancel Booking" button. Please note that cancellation policies vary by hotel and booking type.',
            'order': 1,
          },
          {
            'id': 'booking_faq_2',
            'categoryId': categoryIds[0],
            'question': 'Can I modify my booking dates?',
            'answer':
                'Yes, you can modify your booking dates if the hotel\'s policy allows it. Go to the Bookings tab, select the booking you want to modify, and tap on the "Modify Booking" button.',
            'order': 2,
          },
          {
            'id': 'booking_faq_3',
            'categoryId': categoryIds[0],
            'question': 'How do I get a receipt for my booking?',
            'answer':
                'You can find your booking receipt in the Bookings tab. Select the booking and tap on the "View Receipt" button. You can also download or share the receipt from there.',
            'order': 3,
          },
        ],
      },
      {
        'id': categoryIds[1],
        'title': 'Payments',
        'icon': 'payment',
        'color': '#4CAF50', // Green
        'order': 2,
        'faqs': [
          {
            'id': 'payment_faq_1',
            'categoryId': categoryIds[1],
            'question': 'What payment methods are accepted?',
            'answer':
                'We accept all major credit and debit cards, including Visa, Mastercard, American Express, and Discover. Some hotels also accept digital wallets like Google Pay and Apple Pay.',
            'order': 1,
          },
          {
            'id': 'payment_faq_2',
            'categoryId': categoryIds[1],
            'question': 'When will I be charged for my booking?',
            'answer':
                'Payment policies vary by hotel. Some hotels require full payment at the time of booking, while others may only require a deposit or allow payment at check-in.',
            'order': 2,
          },
          {
            'id': 'payment_faq_3',
            'categoryId': categoryIds[1],
            'question': 'How do I get a refund for a cancelled booking?',
            'answer':
                'Refunds are processed according to the hotel\'s cancellation policy. If you\'re eligible for a refund, it will be processed to your original payment method within 5-10 business days.',
            'order': 3,
          },
        ],
      },
      {
        'id': categoryIds[2],
        'title': 'Services',
        'icon': 'room_service',
        'color': '#FF9800', // Orange
        'order': 3,
        'faqs': [
          {
            'id': 'service_faq_1',
            'categoryId': categoryIds[2],
            'question': 'How do I request room service?',
            'answer':
                'You can request room service through the app by going to the Service Requests tab and selecting "Room Service". You can also call the hotel directly using the contact information provided in your booking details.',
            'order': 1,
          },
          {
            'id': 'service_faq_2',
            'categoryId': categoryIds[2],
            'question': 'Can I request special accommodations?',
            'answer':
                'Yes, you can request special accommodations like extra beds, cribs, or accessibility features. Go to your booking details and tap on "Special Requests" to make your request.',
            'order': 2,
          },
          {
            'id': 'service_faq_3',
            'categoryId': categoryIds[2],
            'question': 'How do I report an issue with my room?',
            'answer':
                'To report an issue with your room, go to the Service Requests tab and select "Report an Issue". You can also contact the hotel front desk directly.',
            'order': 3,
          },
        ],
      },
      {
        'id': categoryIds[3],
        'title': 'Account',
        'icon': 'account_circle',
        'color': '#9C27B0', // Purple
        'order': 4,
        'faqs': [
          {
            'id': 'account_faq_1',
            'categoryId': categoryIds[3],
            'question': 'How do I reset my password?',
            'answer':
                'To reset your password, go to the login screen and tap on "Forgot Password". Follow the instructions sent to your email to create a new password.',
            'order': 1,
          },
          {
            'id': 'account_faq_2',
            'categoryId': categoryIds[3],
            'question': 'How do I update my profile information?',
            'answer':
                'You can update your profile information by going to the Profile tab and tapping on "Personal Information". From there, you can edit your name, email, phone number, and other details.',
            'order': 2,
          },
          {
            'id': 'account_faq_3',
            'categoryId': categoryIds[3],
            'question': 'How do I delete my account?',
            'answer':
                'To delete your account, go to the Profile tab, tap on "Security", and then select "Delete Account". Please note that this action is irreversible and will permanently delete all your data.',
            'order': 3,
          },
        ],
      },
    ];

    return categories;
  }

  // Search FAQs
  List<Map<String, dynamic>> searchFaqs(String query) {
    if (query.isEmpty) {
      return _faqCategories;
    }

    final searchResults = <Map<String, dynamic>>[];

    for (final category in _faqCategories) {
      final faqs = (category['faqs'] as List<dynamic>)
          .where((faq) =>
              faq['question']
                  .toString()
                  .toLowerCase()
                  .contains(query.toLowerCase()) ||
              faq['answer']
                  .toString()
                  .toLowerCase()
                  .contains(query.toLowerCase()))
          .toList();

      if (faqs.isNotEmpty) {
        searchResults.add({
          ...category,
          'faqs': faqs,
        });
      }
    }

    return searchResults;
  }

  // Submit feedback for a FAQ - now just logs locally instead of using Firestore
  Future<void> submitFaqFeedback(String faqId, bool isHelpful) async {
    try {
      // Instead of writing to Firestore, just log the feedback
      debugPrint('FAQ Feedback: FAQ ID: $faqId, Helpful: $isHelpful');
    } catch (e) {
      debugPrint('Error submitting FAQ feedback: $e');
    }
  }
}
