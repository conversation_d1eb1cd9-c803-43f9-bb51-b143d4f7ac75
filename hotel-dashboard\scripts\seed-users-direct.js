/**
 * Seed script to create initial users directly in Firebase
 * 
 * This script creates:
 * 1. A super admin user
 * 2. A vendor user
 * 3. A staff user for the vendor
 * 
 * Run this script with: node scripts/seed-users-direct.js
 */

const { initializeApp } = require('firebase/app');
const { 
  getAuth, 
  createUserWithEmailAndPassword,
  signOut
} = require('firebase/auth');
const { 
  getFirestore,
  doc, 
  setDoc, 
  collection, 
  query, 
  where, 
  getDocs, 
  serverTimestamp
} = require('firebase/firestore');

// Firebase configuration - same as in config.ts
const firebaseConfig = {
  apiKey: "AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI",
  authDomain: "linkinblink-hotel.firebaseapp.com",
  projectId: "linkinblink-hotel",
  storageBucket: "linkinblink-hotel.firebasestorage.app",
  messagingSenderId: "132613661307",
  appId: "1:132613661307:web:a9e5c8f4c2b8e9a9e5c8f4",
  measurementId: "G-MEASUREMENT_ID"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// User roles
const ROLE_SUPER_ADMIN = 'super_admin';
const ROLE_VENDOR = 'vendor';
const ROLE_STAFF = 'staff';

// Sample users to create
const users = [
  {
    email: '<EMAIL>',
    password: 'Admin123!',
    displayName: 'Super Admin',
    role: ROLE_SUPER_ADMIN,
    phone: '+1234567890',
    address: '123 Admin Street, Admin City'
  }
];

/**
 * Create a user in Firebase Authentication and Firestore
 */
async function createUser(userData) {
  try {
    console.log(`Creating user: ${userData.email} (${userData.role})...`);
    
    // Check if user already exists in Firebase Auth
    try {
      // Try to sign in with the credentials
      await createUserWithEmailAndPassword(auth, userData.email, userData.password);
      console.log(`User ${userData.email} created in Firebase Auth`);
      
      // Get the current user
      const user = auth.currentUser;
      
      if (user) {
        const uid = user.uid;
        
        // Add user data to Firestore
        await setDoc(doc(db, 'users', uid), {
          email: userData.email,
          displayName: userData.displayName,
          role: userData.role,
          phone: userData.phone || '',
          address: userData.address || '',
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });
        
        console.log(`User ${userData.email} data added to Firestore with ID: ${uid}`);
        return uid;
      } else {
        throw new Error('User created but not signed in');
      }
    } catch (error) {
      if (error.code === 'auth/email-already-in-use') {
        console.log(`User ${userData.email} already exists. Skipping...`);
        
        // Check if user exists in Firestore
        const usersQuery = query(
          collection(db, 'users'),
          where('email', '==', userData.email)
        );
        
        const usersSnapshot = await getDocs(usersQuery);
        
        if (!usersSnapshot.empty) {
          return usersSnapshot.docs[0].id;
        } else {
          console.log(`User ${userData.email} exists in Auth but not in Firestore. Please check manually.`);
          return null;
        }
      } else {
        throw error;
      }
    }
  } catch (error) {
    console.error(`Error creating user ${userData.email}:`, error.message);
    throw error;
  }
}

/**
 * Main function to seed users
 */
async function seedUsers() {
  try {
    console.log('Starting user seeding process...');
    
    // Create super admin
    const adminId = await createUser(users[0]);
    
    console.log('User seeding completed successfully!');
    
    // Sign out after creating users
    await signOut(auth);
    
    console.log('Seed script completed. You can now log in with:');
    users.forEach(user => {
      console.log(`- ${user.email} (${user.role}) - Password: ${user.password}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error seeding users:', error.message);
    process.exit(1);
  }
}

// Run the seed function
seedUsers();
