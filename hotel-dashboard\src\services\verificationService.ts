import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit as firestoreLimit,
  Timestamp,
  updateDoc
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { AadhaarVerificationData } from '../components/AadhaarVerificationCard';

// Collection name
const AADHAAR_VERIFICATIONS_COLLECTION = 'aadhaarVerifications';

/**
 * Convert Firestore data to AadhaarVerificationData
 */
const convertVerificationData = (doc: any): AadhaarVerificationData => {
  const data = doc.data();

  return {
    id: doc.id,
    userId: data.userId || '',
    maskedAadhaarNumber: data.maskedAadhaarNumber || '',
    fullName: data.fullName || '',
    dateOfBirth: data.dateOfBirth ? data.dateOfBirth.toDate() : undefined,
    gender: data.gender,
    address: data.address,
    status: data.status || 'notVerified',
    verifiedAt: data.verifiedAt ? data.verifiedAt.toDate() : undefined,
    verificationMethod: data.verificationMethod,
    referenceId: data.referenceId,
    verificationDetails: data.verificationDetails,
    createdAt: data.createdAt ? data.createdAt.toDate() : undefined,
    updatedAt: data.updatedAt ? data.updatedAt.toDate() : undefined,
  };
};

/**
 * Get Aadhaar verification by user ID
 */
export const getVerificationByUserId = async (userId: string): Promise<AadhaarVerificationData | null> => {
  try {
    const verificationQuery = query(
      collection(db, AADHAAR_VERIFICATIONS_COLLECTION),
      where('userId', '==', userId),
      firestoreLimit(1)
    );

    const verificationSnapshot = await getDocs(verificationQuery);

    if (verificationSnapshot.empty) {
      return null;
    }

    return convertVerificationData(verificationSnapshot.docs[0]);
  } catch (error) {
    console.error('Error getting verification by user ID:', error);
    throw error;
  }
};

/**
 * Get Aadhaar verification by ID
 */
export const getVerificationById = async (id: string): Promise<AadhaarVerificationData | null> => {
  try {
    const verificationDoc = await getDoc(doc(db, AADHAAR_VERIFICATIONS_COLLECTION, id));

    if (!verificationDoc.exists()) {
      return null;
    }

    return convertVerificationData(verificationDoc);
  } catch (error) {
    console.error('Error getting verification by ID:', error);
    throw error;
  }
};

/**
 * Get all verifications with optional filters
 */
export const getVerifications = async (
  status?: string,
  hotelId?: string,
  limit?: number
): Promise<AadhaarVerificationData[]> => {
  try {
    let verificationQuery: any = collection(db, AADHAAR_VERIFICATIONS_COLLECTION);

    // Build query with filters
    const queryFilters = [];

    if (status && status !== 'all') {
      queryFilters.push(where('status', '==', status));
    }

    // If hotelId is provided, we need to get bookings for that hotel
    // and then filter verifications by those users
    // This is a simplified approach - in a real app, you might need a more complex query
    if (hotelId) {
      // This would require a different approach since Firestore doesn't support
      // queries across collections. You might need to store hotelId in the verification
      // or use a Cloud Function to handle this.
      console.warn('Filtering by hotelId is not implemented in this simplified version');
    }

    // Apply filters and ordering
    verificationQuery = query(
      verificationQuery,
      ...queryFilters,
      orderBy('updatedAt', 'desc')
    );

    // Apply limit if provided
    if (limit) {
      verificationQuery = query(verificationQuery, firestoreLimit(limit));
    }

    const verificationSnapshot = await getDocs(verificationQuery);

    return verificationSnapshot.docs.map(convertVerificationData);
  } catch (error) {
    console.error('Error getting verifications:', error);
    throw error;
  }
};

/**
 * Get recent verifications
 */
export const getRecentVerifications = async (count: number = 5): Promise<AadhaarVerificationData[]> => {
  try {
    const verificationQuery = query(
      collection(db, AADHAAR_VERIFICATIONS_COLLECTION),
      orderBy('updatedAt', 'desc'),
      firestoreLimit(count)
    );

    const verificationSnapshot = await getDocs(verificationQuery);

    return verificationSnapshot.docs.map(convertVerificationData);
  } catch (error) {
    console.error('Error getting recent verifications:', error);
    throw error;
  }
};

/**
 * Get verification statistics
 */
export const getVerificationStats = async () => {
  try {
    const verificationSnapshot = await getDocs(collection(db, AADHAAR_VERIFICATIONS_COLLECTION));

    let totalVerifications = 0;
    let verifiedCount = 0;
    let pendingCount = 0;
    let failedCount = 0;

    verificationSnapshot.forEach((doc) => {
      totalVerifications++;
      const status = doc.data().status;

      if (status === 'verified') {
        verifiedCount++;
      } else if (status === 'pending') {
        pendingCount++;
      } else if (status === 'failed') {
        failedCount++;
      }
    });

    return {
      total: totalVerifications,
      verified: verifiedCount,
      pending: pendingCount,
      failed: failedCount,
      notVerified: totalVerifications - verifiedCount - pendingCount - failedCount
    };
  } catch (error) {
    console.error('Error getting verification stats:', error);
    throw error;
  }
};

/**
 * Update verification status
 */
export const updateVerificationStatus = async (
  id: string,
  status: string,
  comment?: string
): Promise<void> => {
  try {
    const verificationRef = doc(db, AADHAAR_VERIFICATIONS_COLLECTION, id);

    // Get the current document
    const verificationDoc = await getDoc(verificationRef);

    if (!verificationDoc.exists()) {
      throw new Error('Verification not found');
    }

    // Update the document with new status
    await updateDoc(verificationRef, {
      status,
      adminComment: comment || '',
      verifiedAt: status === 'verified' ? Timestamp.now() : null,
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error updating verification status:', error);
    throw error;
  }
};
