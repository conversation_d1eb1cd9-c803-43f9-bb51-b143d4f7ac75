import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Divider,
  CircularProgress,
  Alert,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  AccessTime as TimeIcon,
  Event as EventIcon,
  EventRepeat as RepeatIcon,
  ThumbUp as ThumbUpIcon,
  Check as CheckIcon,
  Block as BlockIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format } from 'date-fns';
import {
  StaffAvailability as StaffAvailabilityType,
  getStaffAvailability,
  createStaffAvailability,
  updateStaffAvailability,
  deleteStaffAvailability,
  AVAILABILITY_TYPE_PREFERRED,
  AVAILABILITY_TYPE_AVAILABLE,
  AVAILABILITY_TYPE_UNAVAILABLE,
  RECURRING_WEEKLY,
  RECURRING_MONTHLY,
  RECURRING_NONE
} from '../../services/staffAvailabilityService';
import { Timestamp } from 'firebase/firestore';

// Day of week options
const DAYS_OF_WEEK = [
  { value: 0, label: 'Monday' },
  { value: 1, label: 'Tuesday' },
  { value: 2, label: 'Wednesday' },
  { value: 3, label: 'Thursday' },
  { value: 4, label: 'Friday' },
  { value: 5, label: 'Saturday' },
  { value: 6, label: 'Sunday' }
];

// Availability type options
const AVAILABILITY_TYPES = [
  { value: AVAILABILITY_TYPE_PREFERRED, label: 'Preferred', color: 'success', icon: <ThumbUpIcon /> },
  { value: AVAILABILITY_TYPE_AVAILABLE, label: 'Available', color: 'primary', icon: <CheckIcon /> },
  { value: AVAILABILITY_TYPE_UNAVAILABLE, label: 'Unavailable', color: 'error', icon: <BlockIcon /> }
];

// Recurring options
const RECURRING_OPTIONS = [
  { value: RECURRING_WEEKLY, label: 'Weekly' },
  { value: RECURRING_NONE, label: 'One-time' }
];

interface StaffAvailabilityProps {
  staffId: string;
  staffName: string;
  hotelId: string;
  readOnly?: boolean;
}

const StaffAvailability: React.FC<StaffAvailabilityProps> = ({
  staffId,
  staffName,
  hotelId,
  readOnly = false
}) => {
  // State for availability
  const [availabilityList, setAvailabilityList] = useState<StaffAvailabilityType[]>([]);
  const [selectedAvailability, setSelectedAvailability] = useState<StaffAvailabilityType | null>(null);

  // State for dialog
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);

  // State for form data
  const [formData, setFormData] = useState<{
    dayOfWeek: number;
    startTime: string;
    endTime: string;
    type: string;
    recurring: string;
    specificDate: Date | null;
    notes: string;
  }>({
    dayOfWeek: 0,
    startTime: '09:00',
    endTime: '17:00',
    type: AVAILABILITY_TYPE_PREFERRED,
    recurring: RECURRING_WEEKLY,
    specificDate: null,
    notes: ''
  });

  // State for loading and errors
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Fetch availability on component mount
  useEffect(() => {
    fetchAvailability();
  }, [staffId]);

  // Fetch availability
  const fetchAvailability = async () => {
    try {
      setLoading(true);
      const fetchedAvailability = await getStaffAvailability(staffId);
      setAvailabilityList(fetchedAvailability);
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching availability:', err);
      setError(err.message || 'Failed to fetch availability');
      setLoading(false);
    }
  };

  // Open dialog for creating a new availability
  const handleOpenNewDialog = () => {
    setFormData({
      dayOfWeek: 0,
      startTime: '09:00',
      endTime: '17:00',
      type: AVAILABILITY_TYPE_PREFERRED,
      recurring: RECURRING_WEEKLY,
      specificDate: null,
      notes: ''
    });
    setSelectedAvailability(null);
    setDialogOpen(true);
  };

  // Open dialog for editing an existing availability
  const handleOpenEditDialog = (availability: StaffAvailabilityType) => {
    setFormData({
      dayOfWeek: availability.dayOfWeek || 0,
      startTime: availability.startTime,
      endTime: availability.endTime,
      type: availability.type,
      recurring: availability.recurring,
      specificDate: availability.specificDate ? availability.specificDate.toDate() : null,
      notes: availability.notes || ''
    });
    setSelectedAvailability(availability);
    setDialogOpen(true);
  };

  // Close dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  // Handle form change
  const handleFormChange = (event: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = event.target;

    if (name) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Type-safe wrapper for Select onChange
  const handleSelectChange = (event: any) => {
    handleFormChange(event as React.ChangeEvent<{ name?: string; value: unknown }>);
  };

  // Handle date change
  const handleDateChange = (date: Date | null) => {
    setFormData(prev => ({
      ...prev,
      specificDate: date
    }));
  };

  // Type-safe wrapper for DatePicker onChange
  const handleDatePickerChange = (value: unknown) => {
    handleDateChange(value as Date | null);
  };

  // Submit form
  const handleSubmit = async () => {
    try {
      // Validate form
      if (formData.recurring === RECURRING_NONE && !formData.specificDate) {
        setError('Please select a specific date for one-time availability');
        return;
      }

      if (formData.startTime === formData.endTime) {
        setError('Start time and end time cannot be the same');
        return;
      }

      setError(null);
      setSuccess(null);

      if (selectedAvailability) {
        // Update existing availability
        await updateStaffAvailability(selectedAvailability.id!, {
          dayOfWeek: formData.recurring === RECURRING_WEEKLY ? formData.dayOfWeek : undefined,
          startTime: formData.startTime,
          endTime: formData.endTime,
          type: formData.type,
          recurring: formData.recurring,
          specificDate: formData.specificDate ? Timestamp.fromDate(formData.specificDate) : undefined,
          notes: formData.notes
        });

        setSuccess('Availability updated successfully');
      } else {
        // Create new availability
        await createStaffAvailability({
          staffId,
          staffName,
          hotelId,
          dayOfWeek: formData.recurring === RECURRING_WEEKLY ? formData.dayOfWeek : undefined,
          startTime: formData.startTime,
          endTime: formData.endTime,
          type: formData.type,
          recurring: formData.recurring,
          specificDate: formData.specificDate ? Timestamp.fromDate(formData.specificDate) : undefined,
          notes: formData.notes
        });

        setSuccess('Availability created successfully');
      }

      // Refresh availability
      await fetchAvailability();
      handleCloseDialog();
    } catch (err: any) {
      console.error('Error saving availability:', err);
      setError(err.message || 'Failed to save availability');
    }
  };

  // Open delete dialog
  const handleOpenDeleteDialog = (availability: StaffAvailabilityType) => {
    setSelectedAvailability(availability);
    setDeleteDialogOpen(true);
  };

  // Close delete dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  // Delete availability
  const handleDelete = async () => {
    try {
      if (selectedAvailability) {
        await deleteStaffAvailability(selectedAvailability.id!);
        setSuccess('Availability deleted successfully');

        // Refresh availability
        await fetchAvailability();
      }

      handleCloseDeleteDialog();
    } catch (err: any) {
      console.error('Error deleting availability:', err);
      setError(err.message || 'Failed to delete availability');
    }
  };

  // Get day name from day of week
  const getDayName = (dayOfWeek: number): string => {
    const day = DAYS_OF_WEEK.find(d => d.value === dayOfWeek);
    return day ? day.label : 'Unknown';
  };

  // Get availability type details
  const getAvailabilityTypeDetails = (type: string) => {
    const availabilityType = AVAILABILITY_TYPES.find(t => t.value === type);
    return availabilityType || AVAILABILITY_TYPES[0];
  };

  // Group availability by day of week
  const getAvailabilityByDay = () => {
    const weeklyAvailability = availabilityList.filter(a => a.recurring === RECURRING_WEEKLY);

    // Group by day of week
    const groupedByDay: { [key: number]: StaffAvailabilityType[] } = {};

    DAYS_OF_WEEK.forEach(day => {
      groupedByDay[day.value] = weeklyAvailability.filter(a => a.dayOfWeek === day.value);
    });

    return groupedByDay;
  };

  // Get one-time availability
  const getOneTimeAvailability = () => {
    return availabilityList.filter(a => a.recurring === RECURRING_NONE);
  };

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">
          {staffName}'s Availability
        </Typography>

        {!readOnly && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleOpenNewDialog}
          >
            Add Availability
          </Button>
        )}
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : availabilityList.length === 0 ? (
        <Paper sx={{ p: 5, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            No availability set
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {readOnly
              ? 'This staff member has not set any availability preferences.'
              : 'Add availability to specify when you are available to work.'}
          </Typography>
          {!readOnly && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleOpenNewDialog}
              sx={{ mt: 3 }}
            >
              Add Availability
            </Button>
          )}
        </Paper>
      ) : (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Weekly Availability
              </Typography>

              <List>
                {DAYS_OF_WEEK.map(day => (
                  <React.Fragment key={day.value}>
                    <ListItem>
                      <ListItemIcon>
                        <RepeatIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={day.label}
                        secondary={
                          getAvailabilityByDay()[day.value]?.length > 0
                            ? `${getAvailabilityByDay()[day.value].length} time slot(s)`
                            : 'No availability set'
                        }
                      />
                    </ListItem>

                    {getAvailabilityByDay()[day.value]?.map(availability => (
                      <ListItem
                        key={availability.id}
                        sx={{
                          pl: 6,
                          borderLeft: '1px solid',
                          borderLeftColor: 'divider',
                          ml: 3
                        }}
                      >
                        <ListItemIcon>
                          {getAvailabilityTypeDetails(availability.type).icon}
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <TimeIcon fontSize="small" sx={{ mr: 1 }} />
                              {availability.startTime} - {availability.endTime}
                              <Chip
                                label={getAvailabilityTypeDetails(availability.type).label}
                                color={getAvailabilityTypeDetails(availability.type).color as any}
                                size="small"
                                sx={{ ml: 1 }}
                              />
                            </Box>
                          }
                          secondary={availability.notes}
                        />
                        {!readOnly && (
                          <ListItemSecondaryAction>
                            <Tooltip title="Edit">
                              <IconButton
                                edge="end"
                                onClick={() => handleOpenEditDialog(availability)}
                              >
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Delete">
                              <IconButton
                                edge="end"
                                onClick={() => handleOpenDeleteDialog(availability)}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          </ListItemSecondaryAction>
                        )}
                      </ListItem>
                    ))}

                    <Divider component="li" />
                  </React.Fragment>
                ))}
              </List>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                One-time Availability
              </Typography>

              {getOneTimeAvailability().length === 0 ? (
                <Typography variant="body2" color="text.secondary" sx={{ p: 2 }}>
                  No one-time availability set
                </Typography>
              ) : (
                <List>
                  {getOneTimeAvailability().map(availability => (
                    <ListItem key={availability.id}>
                      <ListItemIcon>
                        <EventIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {availability.specificDate && format(availability.specificDate.toDate(), 'EEEE, MMMM d, yyyy')}
                            <Chip
                              label={getAvailabilityTypeDetails(availability.type).label}
                              color={getAvailabilityTypeDetails(availability.type).color as any}
                              size="small"
                              sx={{ ml: 1 }}
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <TimeIcon fontSize="small" sx={{ mr: 1 }} />
                              {availability.startTime} - {availability.endTime}
                            </Box>
                            {availability.notes && (
                              <Typography variant="body2" sx={{ mt: 1 }}>
                                {availability.notes}
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                      {!readOnly && (
                        <ListItemSecondaryAction>
                          <Tooltip title="Edit">
                            <IconButton
                              edge="end"
                              onClick={() => handleOpenEditDialog(availability)}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton
                              edge="end"
                              onClick={() => handleOpenDeleteDialog(availability)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </ListItemSecondaryAction>
                      )}
                    </ListItem>
                  ))}
                </List>
              )}
            </Paper>
          </Grid>
        </Grid>
      )}

      {/* Availability Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedAvailability ? 'Edit Availability' : 'Add Availability'}
        </DialogTitle>
        <DialogContent dividers>
          <FormControl fullWidth margin="normal">
            <InputLabel id="recurring-label">Recurring</InputLabel>
            <Select
              labelId="recurring-label"
              name="recurring"
              value={formData.recurring}
              label="Recurring"
              onChange={handleSelectChange}
            >
              {RECURRING_OPTIONS.map(option => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {formData.recurring === RECURRING_WEEKLY ? (
            <FormControl fullWidth margin="normal">
              <InputLabel id="day-of-week-label">Day of Week</InputLabel>
              <Select
                labelId="day-of-week-label"
                name="dayOfWeek"
                value={formData.dayOfWeek}
                label="Day of Week"
                onChange={handleSelectChange}
              >
                {DAYS_OF_WEEK.map(day => (
                  <MenuItem key={day.value} value={day.value}>
                    {day.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          ) : (
            <Box sx={{ mt: 2, mb: 1 }}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Specific Date"
                  value={formData.specificDate}
                  onChange={handleDatePickerChange}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      margin="normal"
                    />
                  )}
                />
              </LocalizationProvider>
            </Box>
          )}

          <FormControl fullWidth margin="normal">
            <InputLabel id="availability-type-label">Availability Type</InputLabel>
            <Select
              labelId="availability-type-label"
              name="type"
              value={formData.type}
              label="Availability Type"
              onChange={handleSelectChange}
            >
              {AVAILABILITY_TYPES.map(type => (
                <MenuItem key={type.value} value={type.value}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {React.cloneElement(type.icon, { fontSize: 'small', sx: { mr: 1 } })}
                    {type.label}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Start Time"
                name="startTime"
                type="time"
                value={formData.startTime}
                onChange={handleFormChange}
                InputLabelProps={{ shrink: true }}
                inputProps={{ step: 300 }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="End Time"
                name="endTime"
                type="time"
                value={formData.endTime}
                onChange={handleFormChange}
                InputLabelProps={{ shrink: true }}
                inputProps={{ step: 300 }}
              />
            </Grid>
          </Grid>

          <TextField
            fullWidth
            label="Notes"
            name="notes"
            value={formData.notes}
            onChange={handleFormChange}
            margin="normal"
            multiline
            rows={3}
            placeholder="Add any notes about this availability"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
          >
            {selectedAvailability ? 'Save Changes' : 'Add Availability'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Delete Availability</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this availability? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StaffAvailability;
