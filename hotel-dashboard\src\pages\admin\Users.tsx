import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Paper,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip,
  InputAdornment,
  Snackbar,
  Alert,
  Tooltip,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  FilterList as FilterListIcon,
  Person as PersonIcon,
  SupervisorAccount as AdminIcon,
  Hotel as HotelIcon,
  CleaningServices as StaffIcon
} from '@mui/icons-material';

// User interface
interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: 'admin' | 'vendor' | 'staff' | 'user';
  status: 'active' | 'inactive' | 'pending';
  createdAt: string;
  hotelId?: string;
  hotelName?: string;
}

// Sample data
const sampleUsers: User[] = [
  {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'admin',
    status: 'active',
    createdAt: '2023-01-15'
  },
  {
    id: '2',
    firstName: 'Rajesh',
    lastName: 'Kumar',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'vendor',
    status: 'active',
    createdAt: '2023-02-10',
    hotelId: 'hotel1',
    hotelName: 'Grand Hotel'
  },
  {
    id: '3',
    firstName: 'Priya',
    lastName: 'Sharma',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'vendor',
    status: 'active',
    createdAt: '2023-02-15',
    hotelId: 'hotel2',
    hotelName: 'Luxury Inn'
  },
  {
    id: '4',
    firstName: 'Amit',
    lastName: 'Patel',
    email: '<EMAIL>',
    phone: '+91 9876543213',
    role: 'staff',
    status: 'active',
    createdAt: '2023-03-05',
    hotelId: 'hotel1',
    hotelName: 'Grand Hotel'
  },
  {
    id: '5',
    firstName: 'Sunita',
    lastName: 'Verma',
    email: '<EMAIL>',
    phone: '+91 9876543214',
    role: 'user',
    status: 'active',
    createdAt: '2023-03-10'
  },
  {
    id: '6',
    firstName: 'Vikram',
    lastName: 'Singh',
    email: '<EMAIL>',
    phone: '+91 9876543215',
    role: 'user',
    status: 'inactive',
    createdAt: '2023-03-15'
  },
  {
    id: '7',
    firstName: 'Neha',
    lastName: 'Gupta',
    email: '<EMAIL>',
    phone: '+91 9876543216',
    role: 'staff',
    status: 'active',
    createdAt: '2023-04-01',
    hotelId: 'hotel2',
    hotelName: 'Luxury Inn'
  },
  {
    id: '8',
    firstName: 'Rahul',
    lastName: 'Sharma',
    email: '<EMAIL>',
    phone: '+91 9876543217',
    role: 'user',
    status: 'pending',
    createdAt: '2023-04-05'
  }
];

const Users: React.FC = () => {
  // State for users data
  const [users, setUsers] = useState<User[]>(sampleUsers);
  const [filteredUsers, setFilteredUsers] = useState<User[]>(sampleUsers);

  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);

  // State for search and filters
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // State for user form dialog
  const [openUserDialog, setOpenUserDialog] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userFormData, setUserFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    role: 'user' as 'admin' | 'vendor' | 'staff' | 'user',
    status: 'active' as 'active' | 'inactive' | 'pending',
    hotelId: '',
    hotelName: ''
  });

  // State for delete confirmation dialog
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);

  // State for snackbar
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning'
  });

  // Sample hotels for dropdown
  const hotels = [
    { id: 'hotel1', name: 'Grand Hotel' },
    { id: 'hotel2', name: 'Luxury Inn' },
    { id: 'hotel3', name: 'Comfort Stay' },
    { id: 'hotel4', name: 'Royal Palace' }
  ];

  // Effect to filter users based on search and filters
  useEffect(() => {
    let result = users;

    // Apply search filter
    if (searchTerm) {
      const lowerCaseSearch = searchTerm.toLowerCase();
      result = result.filter(user =>
        user.firstName.toLowerCase().includes(lowerCaseSearch) ||
        user.lastName.toLowerCase().includes(lowerCaseSearch) ||
        user.email.toLowerCase().includes(lowerCaseSearch) ||
        user.phone.includes(searchTerm) ||
        (user.hotelName && user.hotelName.toLowerCase().includes(lowerCaseSearch))
      );
    }

    // Apply role filter
    if (roleFilter !== 'all') {
      result = result.filter(user => user.role === roleFilter);
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      result = result.filter(user => user.status === statusFilter);
    }

    setFilteredUsers(result);
  }, [users, searchTerm, roleFilter, statusFilter]);

  // Handle page change
  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  // Handle role filter change
  const handleRoleFilterChange = (event: any) => {
    setRoleFilter(event.target.value as string);
    setPage(0);
  };

  // Handle status filter change
  const handleStatusFilterChange = (event: any) => {
    setStatusFilter(event.target.value as string);
    setPage(0);
  };

  // Reset all filters
  const handleResetFilters = () => {
    setSearchTerm('');
    setRoleFilter('all');
    setStatusFilter('all');
  };

  // Open user dialog for adding new user
  const handleAddUser = () => {
    setIsEditMode(false);
    setCurrentUser(null);
    setUserFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      role: 'user' as 'admin' | 'vendor' | 'staff' | 'user',
      status: 'active' as 'active' | 'inactive' | 'pending',
      hotelId: '',
      hotelName: ''
    });
    setOpenUserDialog(true);
  };

  // Open user dialog for editing user
  const handleEditUser = (user: User) => {
    setIsEditMode(true);
    setCurrentUser(user);
    setUserFormData({
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      role: user.role,
      status: user.status,
      hotelId: user.hotelId || '',
      hotelName: user.hotelName || ''
    });
    setOpenUserDialog(true);
  };

  // Handle user form input change
  const handleUserFormChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setUserFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle hotel selection change
  const handleHotelChange = (event: any) => {
    const hotelId = event.target.value as string;
    const selectedHotel = hotels.find(hotel => hotel.id === hotelId);

    setUserFormData(prev => ({
      ...prev,
      hotelId,
      hotelName: selectedHotel ? selectedHotel.name : ''
    }));
  };

  // Handle role change
  const handleRoleChange = (event: any) => {
    const role = event.target.value as 'admin' | 'vendor' | 'staff' | 'user';

    // Reset hotel info if role is not vendor or staff
    if (role !== 'vendor' && role !== 'staff') {
      setUserFormData(prev => ({
        ...prev,
        role,
        hotelId: '',
        hotelName: ''
      }));
    } else {
      setUserFormData(prev => ({
        ...prev,
        role
      }));
    }
  };

  // Handle status change
  const handleStatusChange = (event: any) => {
    setUserFormData(prev => ({
      ...prev,
      status: event.target.value as 'active' | 'inactive' | 'pending'
    }));
  };

  // Save user (create or update)
  const handleSaveUser = () => {
    if (isEditMode && currentUser) {
      // Update existing user
      const updatedUsers = users.map(user => {
        if (user.id === currentUser.id) {
          const updatedUser: User = {
            ...user,
            firstName: userFormData.firstName,
            lastName: userFormData.lastName,
            email: userFormData.email,
            phone: userFormData.phone,
            role: userFormData.role,
            status: userFormData.status,
          };

          // Only include hotel info for vendor or staff
          if (userFormData.role === 'vendor' || userFormData.role === 'staff') {
            updatedUser.hotelId = userFormData.hotelId;
            updatedUser.hotelName = userFormData.hotelName;
          } else {
            updatedUser.hotelId = undefined;
            updatedUser.hotelName = undefined;
          }

          return updatedUser;
        }
        return user;
      });

      setUsers(updatedUsers);
      setSnackbar({
        open: true,
        message: 'User updated successfully',
        severity: 'success'
      });
    } else {
      // Create new user
      const newUser: User = {
        id: Date.now().toString(), // Generate a temporary ID
        firstName: userFormData.firstName,
        lastName: userFormData.lastName,
        email: userFormData.email,
        phone: userFormData.phone,
        role: userFormData.role,
        status: userFormData.status,
        createdAt: new Date().toISOString().split('T')[0]
      };

      // Only include hotel info for vendor or staff
      if (userFormData.role === 'vendor' || userFormData.role === 'staff') {
        newUser.hotelId = userFormData.hotelId;
        newUser.hotelName = userFormData.hotelName;
      }

      setUsers([...users, newUser]);
      setSnackbar({
        open: true,
        message: 'User created successfully',
        severity: 'success'
      });
    }

    setOpenUserDialog(false);
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (user: User) => {
    setUserToDelete(user);
    setOpenDeleteDialog(true);
  };

  // Confirm user deletion
  const handleConfirmDelete = () => {
    if (userToDelete) {
      const updatedUsers = users.filter(user => user.id !== userToDelete.id);
      setUsers(updatedUsers);
      setSnackbar({
        open: true,
        message: 'User deleted successfully',
        severity: 'success'
      });
    }
    setOpenDeleteDialog(false);
  };

  // Toggle user status
  const handleToggleStatus = (user: User) => {
    const newStatus = user.status === 'active' ? 'inactive' as const : 'active' as const;
    const updatedUsers = users.map(u => {
      if (u.id === user.id) {
        return { ...u, status: newStatus };
      }
      return u;
    });
    setUsers(updatedUsers);
    setSnackbar({
      open: true,
      message: `User ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`,
      severity: 'success'
    });
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Render role chip
  const renderRoleChip = (role: string) => {
    switch (role) {
      case 'admin':
        return <Chip icon={<AdminIcon />} label="Admin" color="primary" size="small" />;
      case 'vendor':
        return <Chip icon={<HotelIcon />} label="Vendor" color="secondary" size="small" />;
      case 'staff':
        return <Chip icon={<StaffIcon />} label="Staff" color="info" size="small" />;
      case 'user':
        return <Chip icon={<PersonIcon />} label="User" color="default" size="small" />;
      default:
        return <Chip label={role} size="small" />;
    }
  };

  // Render status chip
  const renderStatusChip = (status: string) => {
    switch (status) {
      case 'active':
        return <Chip label="Active" color="success" size="small" />;
      case 'inactive':
        return <Chip label="Inactive" color="error" size="small" />;
      case 'pending':
        return <Chip label="Pending" color="warning" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Users Management
      </Typography>

      {/* Search and Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search users..."
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Role</InputLabel>
              <Select
                value={roleFilter}
                label="Role"
                onChange={handleRoleFilterChange}
              >
                <MenuItem value="all">All Roles</MenuItem>
                <MenuItem value="admin">Admin</MenuItem>
                <MenuItem value="vendor">Vendor</MenuItem>
                <MenuItem value="staff">Staff</MenuItem>
                <MenuItem value="user">User</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                label="Status"
                onChange={handleStatusFilterChange}
              >
                <MenuItem value="all">All Status</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={6} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleResetFilters}
            >
              Reset
            </Button>
          </Grid>
          <Grid item xs={6} md={2}>
            <Button
              fullWidth
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddUser}
            >
              Add User
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Users Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Phone</TableCell>
                <TableCell>Role</TableCell>
                <TableCell>Hotel</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Created</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredUsers
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>{`${user.firstName} ${user.lastName}`}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>{user.phone}</TableCell>
                    <TableCell>{renderRoleChip(user.role)}</TableCell>
                    <TableCell>{user.hotelName || '-'}</TableCell>
                    <TableCell>{renderStatusChip(user.status)}</TableCell>
                    <TableCell>{user.createdAt}</TableCell>
                    <TableCell align="right">
                      <Tooltip title={user.status === 'active' ? 'Deactivate' : 'Activate'}>
                        <IconButton
                          size="small"
                          onClick={() => handleToggleStatus(user)}
                          color={user.status === 'active' ? 'error' : 'success'}
                        >
                          {user.status === 'active' ? <BlockIcon /> : <CheckCircleIcon />}
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit">
                        <IconButton
                          size="small"
                          onClick={() => handleEditUser(user)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteClick(user)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              {filteredUsers.length === 0 && (
                <TableRow>
                  <TableCell colSpan={8} align="center">
                    No users found matching the criteria
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredUsers.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>

      {/* User Form Dialog */}
      <Dialog open={openUserDialog} onClose={() => setOpenUserDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>{isEditMode ? 'Edit User' : 'Add New User'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="First Name"
                name="firstName"
                value={userFormData.firstName}
                onChange={handleUserFormChange}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Last Name"
                name="lastName"
                value={userFormData.lastName}
                onChange={handleUserFormChange}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={userFormData.email}
                onChange={handleUserFormChange}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Phone"
                name="phone"
                value={userFormData.phone}
                onChange={handleUserFormChange}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  value={userFormData.role}
                  label="Role"
                  onChange={handleRoleChange}
                >
                  <MenuItem value="admin">Admin</MenuItem>
                  <MenuItem value="vendor">Vendor</MenuItem>
                  <MenuItem value="staff">Staff</MenuItem>
                  <MenuItem value="user">User</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={userFormData.status}
                  label="Status"
                  onChange={handleStatusChange}
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Hotel selection for vendor and staff roles */}
            {(userFormData.role === 'vendor' || userFormData.role === 'staff') && (
              <Grid item xs={12}>
                <Divider sx={{ my: 1 }}>
                  <Chip label="Hotel Assignment" />
                </Divider>
              </Grid>
            )}

            {(userFormData.role === 'vendor' || userFormData.role === 'staff') && (
              <Grid item xs={12}>
                <FormControl fullWidth required>
                  <InputLabel>Assign to Hotel</InputLabel>
                  <Select
                    value={userFormData.hotelId}
                    label="Assign to Hotel"
                    onChange={handleHotelChange}
                  >
                    {hotels.map(hotel => (
                      <MenuItem key={hotel.id} value={hotel.id}>
                        {hotel.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenUserDialog(false)}>Cancel</Button>
          <Button
            onClick={handleSaveUser}
            variant="contained"
            disabled={!userFormData.firstName || !userFormData.lastName || !userFormData.email ||
              ((userFormData.role === 'vendor' || userFormData.role === 'staff') && !userFormData.hotelId)}
          >
            {isEditMode ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={openDeleteDialog} onClose={() => setOpenDeleteDialog(false)}>
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the user "{userToDelete?.firstName} {userToDelete?.lastName}"?
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>Cancel</Button>
          <Button onClick={handleConfirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Users;
