/**
 * Utility functions for safely handling string operations
 */

/**
 * Safely converts a value to lowercase
 * @param value - The value to convert to lowercase
 * @returns The lowercase string or an empty string if the value is not a valid string
 */
export const safeToLowerCase = (value: any): string => {
  if (value === null || value === undefined) {
    return '';
  }
  
  try {
    if (typeof value === 'string') {
      return value.toLowerCase();
    }
    return String(value).toLowerCase();
  } catch (error) {
    console.error('Error in safeToLowerCase:', error);
    return '';
  }
};

/**
 * Safely checks if a string includes a substring
 * @param str - The string to check
 * @param searchValue - The substring to search for
 * @returns True if the string includes the substring, false otherwise
 */
export const safeIncludes = (str: any, searchValue: string): boolean => {
  if (str === null || str === undefined) {
    return false;
  }
  
  try {
    if (typeof str === 'string') {
      return str.toLowerCase().includes(searchValue.toLowerCase());
    }
    return String(str).toLowerCase().includes(searchValue.toLowerCase());
  } catch (error) {
    console.error('Error in safeIncludes:', error);
    return false;
  }
};

/**
 * Safely replaces all occurrences of a substring in a string
 * @param str - The string to modify
 * @param searchValue - The substring to replace
 * @param replaceValue - The replacement substring
 * @returns The modified string or the original string if an error occurs
 */
export const safeReplace = (str: any, searchValue: string, replaceValue: string): string => {
  if (str === null || str === undefined) {
    return '';
  }
  
  try {
    if (typeof str === 'string') {
      return str.replace(searchValue, replaceValue);
    }
    return String(str).replace(searchValue, replaceValue);
  } catch (error) {
    console.error('Error in safeReplace:', error);
    return String(str);
  }
};

/**
 * Safely concatenates strings with a separator
 * @param separator - The separator to use between strings
 * @param strings - The strings to concatenate
 * @returns The concatenated string
 */
export const safeJoin = (separator: string, ...strings: any[]): string => {
  try {
    return strings
      .filter(s => s !== null && s !== undefined)
      .map(s => typeof s === 'string' ? s : String(s))
      .join(separator);
  } catch (error) {
    console.error('Error in safeJoin:', error);
    return '';
  }
};
