import React, { useState, useEffect, useMemo, Suspense } from 'react';
import ErrorBoundary from '../../components/common/ErrorBoundary';
import { safeToLowerCase, safeIncludes, safeReplace, safeJoin } from '../../utils/safeString';
import BookingsFallback from './BookingsFallback';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  CircularProgress,
  Alert,
  InputAdornment,
  IconButton,
  Collapse,
  Grid,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  Snackbar,
  OutlinedInput,
  Checkbox,
  ListItemText
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Print as PrintIcon,
  Email as EmailIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { auth } from '../../firebase/config';
import { getHotelsForVendor, HotelData } from '../../firebase/hotelService';
import {
  searchBookings,
  getBookingById,
  updateBooking,
  cancelBooking,
  BookingData,
  BookingSearchParams
} from '../../services/bookingService';
import { getRoomsForHotel } from '../../services/roomService';

// Define TabPanel component for tab content
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`booking-tabpanel-${index}`}
      aria-labelledby={`booking-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const Bookings: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hotels, setHotels] = useState<HotelData[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');

  // Bookings state
  const [bookings, setBookings] = useState<BookingData[]>([]);
  const [filteredBookings, setFilteredBookings] = useState<BookingData[]>([]);
  const [selectedBooking, setSelectedBooking] = useState<BookingData | null>(null);
  const [viewBookingDialog, setViewBookingDialog] = useState(false);
  const [editBookingDialog, setEditBookingDialog] = useState(false);
  const [cancelBookingDialog, setCancelBookingDialog] = useState(false);
  const [cancelReason, setCancelReason] = useState('');

  // Pagination state
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalBookings, setTotalBookings] = useState(0);
  const [lastDoc, setLastDoc] = useState<any>(null);

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<{
    startDate: string;
    endDate: string;
  }>({
    startDate: '',
    endDate: ''
  });
  const [showFilters, setShowFilters] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string[]>([]);

  // Snackbar state
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'success'
  });

  // Load data on component mount
  useEffect(() => {
    fetchHotels();
  }, []);

  // Fetch bookings when hotel or tab changes
  useEffect(() => {
    if (selectedHotel) {
      fetchBookings();
    }
  }, [selectedHotel, tabValue]);

  // Apply filters when search term or filters change
  useEffect(() => {
    applyFilters();
  }, [bookings, searchTerm, dateRange, filterStatus]);

  // Fetch hotels
  const fetchHotels = async () => {
    try {
      setLoading(true);
      if (!auth.currentUser) return;

      const hotelsList = await getHotelsForVendor(auth.currentUser.uid);
      setHotels(hotelsList);

      if (hotelsList.length > 0) {
        setSelectedHotel(hotelsList[0].id || '');
      }

      setLoading(false);
    } catch (err) {
      console.error('Error fetching hotels:', err);
      setError('Failed to load hotels');
      setLoading(false);
    }
  };

  // Fetch bookings
  const fetchBookings = async () => {
    try {
      setLoading(true);
      if (!selectedHotel) return;

      // Prepare search parameters based on current tab
      const searchParams: BookingSearchParams = {
        hotelId: selectedHotel,
        page: page,
        limit: rowsPerPage,
        lastDoc: lastDoc
      };

      // Add status filters based on tab
      switch (tabValue) {
        case 1: // Upcoming
          searchParams.bookingStatus = ['confirmed'];
          searchParams.startDate = new Date(); // From today
          break;
        case 2: // Current
          searchParams.bookingStatus = ['checked_in'];
          break;
        case 3: // Past
          searchParams.bookingStatus = ['checked_out'];
          searchParams.endDate = new Date(); // Until today
          break;
        case 4: // Cancelled
          searchParams.bookingStatus = ['cancelled', 'no_show'];
          break;
        default: // All bookings
          break;
      }

      // Add date range if specified
      if (dateRange.startDate) {
        searchParams.startDate = new Date(dateRange.startDate);
      }
      if (dateRange.endDate) {
        searchParams.endDate = new Date(dateRange.endDate);
      }

      // Add status filters if specified
      if (filterStatus.length > 0) {
        searchParams.bookingStatus = filterStatus;
      }

      // Add search term if specified
      if (searchTerm && typeof searchTerm === 'string') {
        try {
          if (searchTerm.startsWith('BK-')) {
            searchParams.bookingNumber = searchTerm;
          } else if (searchTerm.includes('@')) {
            searchParams.guestEmail = searchTerm;
          } else if (/^\d+$/.test(searchTerm)) {
            searchParams.guestPhone = searchTerm;
          } else {
            searchParams.guestName = searchTerm;
          }
        } catch (error) {
          console.error('Error processing search term:', error);
          // Default to searching by name if there's an error
          searchParams.guestName = String(searchTerm);
        }
      }

      const result = await searchBookings(searchParams);

      setBookings(result.bookings);
      setLastDoc(result.lastDoc);
      setTotalBookings(result.bookings.length); // This is just an approximation

      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching bookings:', err);
      setError(err.message || 'Failed to load bookings');
      setLoading(false);
    }
  };

  // Apply filters to bookings
  const applyFilters = () => {
    try {
      // Make a safe copy of bookings, ensuring it's an array
      let filtered = Array.isArray(bookings) ? [...bookings] : [];

      // Apply search term filter
      if (searchTerm && searchTerm.trim() !== '') {
        try {
          const term = safeToLowerCase(searchTerm);
          filtered = filtered.filter(booking => {
            if (!booking) return false;

            // Check booking number
            const bookingNumberMatch = safeIncludes(booking.bookingNumber, term);

            // Check guest name
            let guestNameMatch = false;
            if (booking.guestInfo) {
              const firstName = booking.guestInfo.firstName || '';
              const lastName = booking.guestInfo.lastName || '';
              if (firstName || lastName) {
                const fullName = safeJoin(' ', firstName, lastName);
                guestNameMatch = safeIncludes(fullName, term);
              }
            }

            // Check email
            const emailMatch = booking.guestInfo && safeIncludes(booking.guestInfo.email, term);

            // Check phone
            const phoneMatch = booking.guestInfo && booking.guestInfo.phone &&
              safeIncludes(booking.guestInfo.phone, term);

            return bookingNumberMatch || guestNameMatch || emailMatch || phoneMatch;
          });
        } catch (error) {
          console.error('Error applying search term filter:', error);
        }
      }

      // Apply date range filter
      if (dateRange.startDate) {
        try {
          const startDate = new Date(dateRange.startDate);
          filtered = filtered.filter(booking => {
            if (!booking || !booking.checkInDate || typeof booking.checkInDate.toDate !== 'function') {
              return false;
            }
            try {
              return booking.checkInDate.toDate() >= startDate;
            } catch (error) {
              console.error('Error comparing check-in date:', error);
              return false;
            }
          });
        } catch (error) {
          console.error('Error applying start date filter:', error);
        }
      }

      if (dateRange.endDate) {
        try {
          const endDate = new Date(dateRange.endDate);
          filtered = filtered.filter(booking => {
            if (!booking || !booking.checkOutDate || typeof booking.checkOutDate.toDate !== 'function') {
              return false;
            }
            try {
              return booking.checkOutDate.toDate() <= endDate;
            } catch (error) {
              console.error('Error comparing check-out date:', error);
              return false;
            }
          });
        } catch (error) {
          console.error('Error applying end date filter:', error);
        }
      }

      // Apply status filter
      if (Array.isArray(filterStatus) && filterStatus.length > 0) {
        try {
          filtered = filtered.filter(booking =>
            booking &&
            booking.bookingStatus &&
            typeof booking.bookingStatus === 'string' &&
            filterStatus.includes(booking.bookingStatus)
          );
        } catch (error) {
          console.error('Error applying status filter:', error);
        }
      }

      setFilteredBookings(filtered);
    } catch (error) {
      console.error('Error in applyFilters:', error);
      // Set to empty array as fallback
      setFilteredBookings([]);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setPage(0); // Reset pagination when changing tabs
    setLastDoc(null);
  };

  // Handle hotel change
  const handleHotelChange = (event: any) => {
    setSelectedHotel(event.target.value);
    setPage(0); // Reset pagination when changing hotel
    setLastDoc(null);
  };

  // Handle search term change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const value = event.target.value || '';
      setSearchTerm(value);
    } catch (error) {
      console.error('Error handling search change:', error);
      setSearchTerm('');
    }
  };

  // Handle date range change
  const handleDateRangeChange = (field: 'startDate' | 'endDate') => (event: React.ChangeEvent<HTMLInputElement>) => {
    setDateRange(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  // Handle filter status change
  const handleFilterStatusChange = (event: any) => {
    setFilterStatus(event.target.value);
  };

  // Toggle filters visibility
  const handleToggleFilters = () => {
    setShowFilters(prev => !prev);
  };

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
    fetchBookings();
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
    fetchBookings();
  };

  // View booking details
  const handleViewBooking = (booking: BookingData) => {
    setSelectedBooking(booking);
    setViewBookingDialog(true);
  };

  // Close booking details dialog
  const handleCloseViewDialog = () => {
    setViewBookingDialog(false);
  };

  // Open edit booking dialog
  const handleEditBooking = (booking: BookingData) => {
    setSelectedBooking(booking);
    setEditBookingDialog(true);
  };

  // Close edit booking dialog
  const handleCloseEditDialog = () => {
    setEditBookingDialog(false);
  };

  // Open cancel booking dialog
  const handleOpenCancelDialog = (booking: BookingData) => {
    setSelectedBooking(booking);
    setCancelBookingDialog(true);
  };

  // Close cancel booking dialog
  const handleCloseCancelDialog = () => {
    setCancelBookingDialog(false);
    setCancelReason('');
  };

  // Handle cancel booking
  const handleCancelBooking = async () => {
    try {
      if (!selectedBooking || !selectedBooking.id) {
        throw new Error('No booking selected');
      }

      if (!cancelReason) {
        throw new Error('Please provide a reason for cancellation');
      }

      await cancelBooking(selectedBooking.id, cancelReason);

      // Show success message
      setSnackbar({
        open: true,
        message: 'Booking cancelled successfully',
        severity: 'success'
      });

      // Close dialog and refresh bookings
      handleCloseCancelDialog();
      fetchBookings();
    } catch (err: any) {
      console.error('Error cancelling booking:', err);
      setSnackbar({
        open: true,
        message: err.message || 'Failed to cancel booking',
        severity: 'error'
      });
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };

  // Format date
  const formatDate = (date: Date | null | undefined) => {
    if (!date) return 'N/A';

    try {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Bookings Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          disabled={!selectedHotel}
        >
          New Booking
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Hotel selector and search */}
      {hotels.length > 0 && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel id="hotel-select-label">Select Hotel</InputLabel>
                <Select
                  labelId="hotel-select-label"
                  value={selectedHotel}
                  label="Select Hotel"
                  onChange={handleHotelChange}
                >
                  {hotels.map((hotel) => (
                    <MenuItem key={hotel.id} value={hotel.id}>
                      {hotel.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search by booking #, guest name, email or phone"
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  )
                }}
              />
            </Grid>

            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FilterIcon />}
                onClick={handleToggleFilters}
              >
                Filters
              </Button>
            </Grid>

            {/* Filters */}
            <Grid item xs={12}>
              <Collapse in={showFilters}>
                <Box sx={{ pt: 2 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="From Date"
                        type="date"
                        value={dateRange.startDate}
                        onChange={handleDateRangeChange('startDate')}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="To Date"
                        type="date"
                        value={dateRange.endDate}
                        onChange={handleDateRangeChange('endDate')}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <FormControl fullWidth>
                        <InputLabel id="status-filter-label">Booking Status</InputLabel>
                        <Select
                          labelId="status-filter-label"
                          multiple
                          value={filterStatus}
                          onChange={handleFilterStatusChange}
                          input={<OutlinedInput label="Booking Status" />}
                          renderValue={(selected) => (
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                              {selected.map((value) => (
                                <Chip key={value} label={value} />
                              ))}
                            </Box>
                          )}
                        >
                          {['confirmed', 'pending', 'checked_in', 'checked_out', 'cancelled', 'no_show'].map((status) => (
                            <MenuItem key={status} value={status}>
                              <Checkbox checked={filterStatus.indexOf(status) > -1} />
                              <ListItemText primary={
                                status ?
                                  status.charAt(0).toUpperCase() + safeReplace(status.slice(1), '_', ' ')
                                  : 'Unknown'
                              } />
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </Box>
              </Collapse>
            </Grid>
          </Grid>
        </Paper>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : hotels.length === 0 ? (
        <Alert severity="info">
          You need to create at least one hotel before you can manage bookings.
        </Alert>
      ) : (
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab label="All Bookings" />
            <Tab label="Upcoming" />
            <Tab label="Current" />
            <Tab label="Past" />
            <Tab label="Cancelled" />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Box>
                <Typography variant="h6" gutterBottom>
                  All Bookings
                </Typography>
                <Typography variant="body1">
                  View and manage all bookings for your hotel.
                </Typography>
              </Box>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => fetchBookings()}
              >
                Refresh
              </Button>
            </Box>

            {filteredBookings.length === 0 ? (
              <Alert severity="info">
                No bookings found. Try adjusting your search or filters.
              </Alert>
            ) : (
              <>
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Booking #</TableCell>
                        <TableCell>Guest</TableCell>
                        <TableCell>Dates</TableCell>
                        <TableCell>Room</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Total</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredBookings.map((booking) => (
                        <TableRow key={booking.id}>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {booking.bookingNumber}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {booking.createdAt ? new Date(booking.createdAt.toDate()).toLocaleDateString() : ''}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {booking.guestInfo && booking.guestInfo.firstName ? booking.guestInfo.firstName : 'N/A'} {booking.guestInfo && booking.guestInfo.lastName ? booking.guestInfo.lastName : ''}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {booking.guestInfo && booking.guestInfo.email ? booking.guestInfo.email : 'N/A'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {booking.checkInDate ? formatDate(booking.checkInDate.toDate()) : 'N/A'}
                            </Typography>
                            <Typography variant="body2">
                              to {booking.checkOutDate ? formatDate(booking.checkOutDate.toDate()) : 'N/A'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              Room {booking.roomId || 'N/A'}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {booking.adults || 0} adults, {booking.children || 0} children
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={safeReplace(booking.bookingStatus, '_', ' ')}
                              color={
                                booking.bookingStatus === 'confirmed' ? 'success' :
                                booking.bookingStatus === 'checked_in' ? 'primary' :
                                booking.bookingStatus === 'checked_out' ? 'default' :
                                booking.bookingStatus === 'cancelled' || booking.bookingStatus === 'no_show' ? 'error' :
                                'warning'
                              }
                              size="small"
                            />
                            <Typography variant="caption" display="block" color="text.secondary" sx={{ mt: 0.5 }}>
                              {safeReplace(booking.paymentStatus, '_', ' ')}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            ${booking.totalAmount || 0}
                          </TableCell>
                          <TableCell align="right">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleViewBooking(booking)}
                            >
                              <VisibilityIcon fontSize="small" />
                            </IconButton>
                            {booking.bookingStatus && (booking.bookingStatus === 'confirmed' || booking.bookingStatus === 'pending') && (
                              <>
                                <IconButton
                                  size="small"
                                  color="primary"
                                  onClick={() => handleEditBooking(booking)}
                                >
                                  <EditIcon fontSize="small" />
                                </IconButton>
                                <IconButton
                                  size="small"
                                  color="error"
                                  onClick={() => handleOpenCancelDialog(booking)}
                                >
                                  <CancelIcon fontSize="small" />
                                </IconButton>
                              </>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                <TablePagination
                  component="div"
                  count={totalBookings}
                  page={page}
                  onPageChange={handleChangePage}
                  rowsPerPage={rowsPerPage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  rowsPerPageOptions={[5, 10, 25, 50]}
                />
              </>
            )}
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Typography variant="h6" gutterBottom>
              Upcoming Bookings
            </Typography>
            <Typography variant="body1">
              View and manage upcoming bookings.
            </Typography>
            {/* Upcoming bookings content will be added later */}
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Typography variant="h6" gutterBottom>
              Current Bookings
            </Typography>
            <Typography variant="body1">
              View and manage guests currently staying at your hotel.
            </Typography>
            {/* Current bookings content will be added later */}
          </TabPanel>

          <TabPanel value={tabValue} index={3}>
            <Typography variant="h6" gutterBottom>
              Past Bookings
            </Typography>
            <Typography variant="body1">
              View history of past bookings.
            </Typography>
            {/* Past bookings content will be added later */}
          </TabPanel>

          <TabPanel value={tabValue} index={4}>
            <Typography variant="h6" gutterBottom>
              Cancelled Bookings
            </Typography>
            <Typography variant="body1">
              View cancelled bookings.
            </Typography>
            {/* Cancelled bookings content will be added later */}
          </TabPanel>
        </Paper>
      )}

      {/* Booking Details Dialog */}
      <Dialog
        open={viewBookingDialog}
        onClose={handleCloseViewDialog}
        maxWidth="md"
        fullWidth
      >
        {selectedBooking && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">
                  Booking Details: {selectedBooking.bookingNumber}
                </Typography>
                <Chip
                  label={safeReplace(selectedBooking.bookingStatus, '_', ' ')}
                  color={
                    selectedBooking.bookingStatus === 'confirmed' ? 'success' :
                    selectedBooking.bookingStatus === 'checked_in' ? 'primary' :
                    selectedBooking.bookingStatus === 'checked_out' ? 'default' :
                    selectedBooking.bookingStatus === 'cancelled' || selectedBooking.bookingStatus === 'no_show' ? 'error' :
                    'warning'
                  }
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={3}>
                {/* Guest Information */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                    Guest Information
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="body1">
                      {selectedBooking.guestInfo && selectedBooking.guestInfo.firstName ? selectedBooking.guestInfo.firstName : 'N/A'} {selectedBooking.guestInfo && selectedBooking.guestInfo.lastName ? selectedBooking.guestInfo.lastName : ''}
                    </Typography>
                    <Typography variant="body2">
                      Email: {selectedBooking.guestInfo && selectedBooking.guestInfo.email ? selectedBooking.guestInfo.email : 'N/A'}
                    </Typography>
                    {selectedBooking.guestInfo && selectedBooking.guestInfo.phone && (
                      <Typography variant="body2">
                        Phone: {selectedBooking.guestInfo.phone}
                      </Typography>
                    )}
                    {selectedBooking.guestInfo && selectedBooking.guestInfo.address && (
                      <Typography variant="body2">
                        Address: {selectedBooking.guestInfo.address}
                        {selectedBooking.guestInfo.city ? `, ${selectedBooking.guestInfo.city}` : ''}
                        {selectedBooking.guestInfo.country ? `, ${selectedBooking.guestInfo.country}` : ''}
                        {selectedBooking.guestInfo.zipCode ? ` ${selectedBooking.guestInfo.zipCode}` : ''}
                      </Typography>
                    )}
                  </Paper>
                </Grid>

                {/* Booking Details */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                    Booking Details
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="body2">
                      <strong>Check-in:</strong> {selectedBooking.checkInDate ? formatDate(selectedBooking.checkInDate.toDate()) : 'N/A'}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Check-out:</strong> {selectedBooking.checkOutDate ? formatDate(selectedBooking.checkOutDate.toDate()) : 'N/A'}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Guests:</strong> {selectedBooking.adults} adults, {selectedBooking.children} children
                    </Typography>
                    <Typography variant="body2">
                      <strong>Room:</strong> {selectedBooking.roomId}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Source:</strong> {safeReplace(selectedBooking.source, '_', ' ')} {selectedBooking.sourceDetails ? `(${selectedBooking.sourceDetails})` : ''}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Created:</strong> {selectedBooking.createdAt && selectedBooking.createdAt.toDate ? formatDate(selectedBooking.createdAt.toDate()) : 'N/A'}
                    </Typography>
                  </Paper>
                </Grid>

                {/* Payment Information */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                    Payment Information
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="body1" fontWeight="bold" color="primary">
                      Total: ${selectedBooking.totalAmount || 0}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Payment Status:</strong> {safeReplace(selectedBooking.paymentStatus, '_', ' ')}
                    </Typography>
                    {selectedBooking.paymentMethod && (
                      <Typography variant="body2">
                        <strong>Payment Method:</strong> {selectedBooking.paymentMethod}
                      </Typography>
                    )}
                    {selectedBooking.taxAmount && (
                      <Typography variant="body2">
                        <strong>Tax:</strong> ${selectedBooking.taxAmount} ({selectedBooking.taxRate}%)
                      </Typography>
                    )}
                    {selectedBooking.discountAmount && (
                      <Typography variant="body2">
                        <strong>Discount:</strong> ${selectedBooking.discountAmount} {selectedBooking.discountCode ? `(${selectedBooking.discountCode})` : ''}
                      </Typography>
                    )}
                  </Paper>
                </Grid>

                {/* Additional Information */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                    Additional Information
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                    {selectedBooking.notes ? (
                      <Typography variant="body2">
                        <strong>Notes:</strong> {selectedBooking.notes}
                      </Typography>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        No additional notes.
                      </Typography>
                    )}

                    {selectedBooking.guestInfo && selectedBooking.guestInfo.specialRequests && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="body2">
                          <strong>Special Requests:</strong> {selectedBooking.guestInfo.specialRequests}
                        </Typography>
                      </Box>
                    )}

                    {selectedBooking.cancellationReason && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="body2" color="error">
                          <strong>Cancellation Reason:</strong> {selectedBooking.cancellationReason}
                        </Typography>
                        {selectedBooking.cancellationDate && selectedBooking.cancellationDate.toDate && (
                          <Typography variant="body2" color="error">
                            <strong>Cancelled on:</strong> {formatDate(selectedBooking.cancellationDate.toDate())}
                          </Typography>
                        )}
                      </Box>
                    )}
                  </Paper>
                </Grid>

                {/* Addons */}
                {selectedBooking.addons && selectedBooking.addons.length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                      Add-ons & Services
                    </Typography>
                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Item</TableCell>
                            <TableCell>Description</TableCell>
                            <TableCell>Quantity</TableCell>
                            <TableCell align="right">Price</TableCell>
                            <TableCell align="right">Total</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {selectedBooking.addons.map((addon, index) => (
                            <TableRow key={index}>
                              <TableCell>{addon.name}</TableCell>
                              <TableCell>{addon.description || '-'}</TableCell>
                              <TableCell>{addon.quantity}</TableCell>
                              <TableCell align="right">${addon.price}</TableCell>
                              <TableCell align="right">${addon.price * addon.quantity}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Grid>
                )}
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseViewDialog}>Close</Button>
              {selectedBooking.bookingStatus && (selectedBooking.bookingStatus === 'confirmed' || selectedBooking.bookingStatus === 'pending') && (
                <>
                  <Button
                    color="primary"
                    onClick={() => {
                      handleCloseViewDialog();
                      handleEditBooking(selectedBooking);
                    }}
                  >
                    Edit
                  </Button>
                  <Button
                    color="error"
                    onClick={() => {
                      handleCloseViewDialog();
                      handleOpenCancelDialog(selectedBooking);
                    }}
                  >
                    Cancel Booking
                  </Button>
                </>
              )}
              <Button
                variant="contained"
                color="primary"
                startIcon={<PrintIcon />}
              >
                Print
              </Button>
              <Button
                variant="contained"
                color="primary"
                startIcon={<EmailIcon />}
              >
                Email
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Cancel Booking Dialog */}
      <Dialog
        open={cancelBookingDialog}
        onClose={handleCloseCancelDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Cancel Booking
        </DialogTitle>
        <DialogContent>
          {selectedBooking && (
            <>
              <Typography variant="body1" gutterBottom>
                Are you sure you want to cancel booking {selectedBooking.bookingNumber || 'N/A'}?
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                This action cannot be undone.
              </Typography>
              <TextField
                fullWidth
                label="Cancellation Reason"
                multiline
                rows={3}
                value={cancelReason}
                onChange={(e) => setCancelReason(e.target.value)}
                required
                sx={{ mt: 2 }}
              />
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseCancelDialog}>Cancel</Button>
          <Button
            variant="contained"
            color="error"
            onClick={handleCancelBooking}
            disabled={!cancelReason}
          >
            Confirm Cancellation
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

// Wrap the Bookings component with an ErrorBoundary and Suspense
const BookingsWithErrorBoundary = () => (
  <ErrorBoundary fallback={<BookingsFallback />}>
    <Suspense fallback={<div>Loading bookings...</div>}>
      <Bookings />
    </Suspense>
  </ErrorBoundary>
);

export default BookingsWithErrorBoundary;
