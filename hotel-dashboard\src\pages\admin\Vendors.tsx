import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Snackbar,
  Alert,
  CircularProgress,
  InputAdornment,
  Grid,
  Chip,
  Tooltip,
  TablePagination,
  Divider,
  Badge
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
  Hotel as HotelIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon
} from '@mui/icons-material';
import { getVendors, createUser, updateUser, deleteUser, UserData } from '../../firebase/userService';
import { ROLE_VENDOR } from '../../firebase/auth';
import { auth } from '../../firebase/config';
import { getHotelsByVendor, HotelData } from '../../firebase/hotelService';

const Vendors: React.FC = () => {
  // Data state
  const [vendors, setVendors] = useState<UserData[]>([]);
  const [vendorHotels, setVendorHotels] = useState<{ [vendorId: string]: HotelData[] }>({});
  const [loading, setLoading] = useState(true);

  // UI state
  const [openDialog, setOpenDialog] = useState(false);
  const [openHotelsDialog, setOpenHotelsDialog] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState<UserData | null>(null);
  const [editingVendor, setEditingVendor] = useState<UserData | null>(null);

  // Search and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Form state
  const [formData, setFormData] = useState({
    email: '',
    displayName: '',
    phone: '',
    address: '',
    password: ''
  });

  // Notifications
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info'
  });

  // Load vendors on component mount
  useEffect(() => {
    fetchVendors();
  }, []);

  const fetchVendors = async () => {
    try {
      setLoading(true);
      const vendorsList = await getVendors();
      setVendors(vendorsList);
    } catch (error) {
      console.error('Error fetching vendors:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load vendors',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (vendor?: UserData) => {
    if (vendor) {
      // Edit mode
      setEditingVendor(vendor);
      setFormData({
        email: vendor.email,
        displayName: vendor.displayName,
        phone: vendor.phone || '',
        address: vendor.address || '',
        password: '' // Don't set password when editing
      });
    } else {
      // Create mode
      setEditingVendor(null);
      setFormData({
        email: '',
        displayName: '',
        phone: '',
        address: '',
        password: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async () => {
    try {
      if (!auth.currentUser) {
        throw new Error('You must be logged in to perform this action');
      }

      if (editingVendor) {
        // Update existing vendor
        await updateUser(editingVendor.id!, {
          displayName: formData.displayName,
          phone: formData.phone,
          address: formData.address
        });

        setSnackbar({
          open: true,
          message: 'Vendor updated successfully',
          severity: 'success'
        });
      } else {
        // Create new vendor
        if (!formData.email || !formData.displayName) {
          throw new Error('Email and name are required');
        }

        const userData: UserData = {
          email: formData.email,
          displayName: formData.displayName,
          role: ROLE_VENDOR,
          phone: formData.phone,
          address: formData.address
        };

        await createUser(userData, formData.password, auth.currentUser);

        setSnackbar({
          open: true,
          message: 'Vendor created successfully',
          severity: 'success'
        });
      }

      handleCloseDialog();
      fetchVendors(); // Refresh the list
    } catch (error: any) {
      console.error('Error saving vendor:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to save vendor',
        severity: 'error'
      });
    }
  };

  const handleDeleteVendor = async (vendorId: string) => {
    if (window.confirm('Are you sure you want to delete this vendor?')) {
      try {
        await deleteUser(vendorId);
        setSnackbar({
          open: true,
          message: 'Vendor deleted successfully',
          severity: 'success'
        });
        fetchVendors(); // Refresh the list
      } catch (error) {
        console.error('Error deleting vendor:', error);
        setSnackbar({
          open: true,
          message: 'Failed to delete vendor',
          severity: 'error'
        });
      }
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Fetch hotels for a specific vendor
  const fetchHotelsForVendor = async (vendorId: string) => {
    try {
      const { hotels } = await getHotelsByVendor(vendorId);
      setVendorHotels(prev => ({
        ...prev,
        [vendorId]: hotels
      }));
      return hotels;
    } catch (error) {
      console.error(`Error fetching hotels for vendor ${vendorId}:`, error);
      return [];
    }
  };

  // Handle opening the hotels dialog
  const handleOpenHotelsDialog = async (vendor: UserData) => {
    setSelectedVendor(vendor);
    setOpenHotelsDialog(true);

    if (vendor.id && !vendorHotels[vendor.id]) {
      await fetchHotelsForVendor(vendor.id);
    }
  };

  // Handle closing the hotels dialog
  const handleCloseHotelsDialog = () => {
    setOpenHotelsDialog(false);
    setSelectedVendor(null);
  };

  // Handle search input change
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setPage(0); // Reset to first page when searching
  };

  // Handle pagination
  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Filter vendors based on search term
  const filteredVendors = vendors.filter(vendor =>
    vendor.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (vendor.phone && vendor.phone.includes(searchTerm)) ||
    (vendor.address && vendor.address.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Get hotel count for a vendor
  const getHotelCount = (vendorId: string) => {
    return vendorHotels[vendorId]?.length || 0;
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Vendors Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Add Vendor
        </Button>
      </Box>

      {/* Search Bar */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={8}>
            <TextField
              fullWidth
              placeholder="Search vendors by name, email, phone or address..."
              value={searchTerm}
              onChange={handleSearch}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchVendors}
            >
              Refresh
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <Paper>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Email</TableCell>
                    <TableCell>Phone</TableCell>
                    <TableCell>Address</TableCell>
                    <TableCell>Hotels</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredVendors.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        No vendors found
                      </TableCell>
                    </TableRow>
                  ) : (
                    // Apply pagination to filtered vendors
                    filteredVendors
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((vendor) => {
                        const hotelCount = vendor.id ? getHotelCount(vendor.id) : 0;

                        return (
                          <TableRow key={vendor.id}>
                            <TableCell>
                              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                                <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                                  {vendor.displayName}
                                </Typography>
                                {vendor.id && (
                                  <Typography variant="caption" color="text.secondary">
                                    ID: {vendor.id.substring(0, 8)}...
                                  </Typography>
                                )}
                              </Box>
                            </TableCell>
                            <TableCell>
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <EmailIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                                {vendor.email}
                              </Box>
                            </TableCell>
                            <TableCell>
                              {vendor.phone ? (
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <PhoneIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                                  {vendor.phone}
                                </Box>
                              ) : (
                                '-'
                              )}
                            </TableCell>
                            <TableCell>
                              {vendor.address ? (
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                                  {vendor.address}
                                </Box>
                              ) : (
                                '-'
                              )}
                            </TableCell>
                            <TableCell>
                              <Chip
                                icon={<HotelIcon />}
                                label={`${hotelCount} ${hotelCount === 1 ? 'Hotel' : 'Hotels'}`}
                                color={hotelCount > 0 ? 'primary' : 'default'}
                                variant={hotelCount > 0 ? 'filled' : 'outlined'}
                                size="small"
                                onClick={() => vendor.id && handleOpenHotelsDialog(vendor)}
                                sx={{ cursor: 'pointer' }}
                              />
                            </TableCell>
                            <TableCell align="right">
                              <Tooltip title="View Details">
                                <IconButton
                                  color="info"
                                  size="small"
                                  onClick={() => vendor.id && handleOpenHotelsDialog(vendor)}
                                >
                                  <ViewIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Edit Vendor">
                                <IconButton
                                  color="primary"
                                  size="small"
                                  onClick={() => handleOpenDialog(vendor)}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete Vendor">
                                <IconButton
                                  color="error"
                                  size="small"
                                  onClick={() => vendor.id && handleDeleteVendor(vendor.id)}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </TableCell>
                          </TableRow>
                        );
                      })
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            {filteredVendors.length > 0 && (
              <TablePagination
                rowsPerPageOptions={[5, 10, 25]}
                component="div"
                count={filteredVendors.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            )}
          </>
        )}
      </Paper>

      {/* Add/Edit Vendor Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingVendor ? 'Edit Vendor' : 'Add New Vendor'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              label="Email Address"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              disabled={!!editingVendor} // Disable email editing for existing vendors
            />
            <TextField
              margin="normal"
              required
              fullWidth
              label="Name"
              name="displayName"
              value={formData.displayName}
              onChange={handleInputChange}
            />
            <TextField
              margin="normal"
              fullWidth
              label="Phone"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
            />
            <TextField
              margin="normal"
              fullWidth
              label="Address"
              name="address"
              value={formData.address}
              onChange={handleInputChange}
            />
            {!editingVendor && (
              <TextField
                margin="normal"
                fullWidth
                label="Password (leave empty to send reset email)"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                helperText="If left empty, a password reset email will be sent to the user"
              />
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingVendor ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Hotels Dialog */}
      <Dialog open={openHotelsDialog} onClose={handleCloseHotelsDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedVendor ? `Hotels Managed by ${selectedVendor.displayName}` : 'Vendor Hotels'}
        </DialogTitle>
        <DialogContent>
          {selectedVendor && selectedVendor.id ? (
            <>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Vendor Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="body1">{selectedVendor.displayName}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <EmailIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="body1">{selectedVendor.email}</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    {selectedVendor.phone && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <PhoneIcon sx={{ mr: 1, color: 'primary.main' }} />
                        <Typography variant="body1">{selectedVendor.phone}</Typography>
                      </Box>
                    )}
                    {selectedVendor.address && (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <LocationIcon sx={{ mr: 1, color: 'primary.main' }} />
                        <Typography variant="body1">{selectedVendor.address}</Typography>
                      </Box>
                    )}
                  </Grid>
                </Grid>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle1" gutterBottom>
                Hotels ({vendorHotels[selectedVendor.id]?.length || 0})
              </Typography>

              {vendorHotels[selectedVendor.id]?.length > 0 ? (
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Hotel Name</TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell>Rating</TableCell>
                        <TableCell>Price</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {vendorHotels[selectedVendor.id].map((hotel) => (
                        <TableRow key={hotel.id}>
                          <TableCell>{hotel.name}</TableCell>
                          <TableCell>{`${hotel.city}, ${hotel.country}`}</TableCell>
                          <TableCell>{hotel.rating ? `${hotel.rating}★` : 'N/A'}</TableCell>
                          <TableCell>{hotel.price ? `₹${hotel.price}/night` : 'N/A'}</TableCell>
                          <TableCell align="right">
                            <Tooltip title="View Hotel">
                              <IconButton
                                size="small"
                                color="primary"
                                onClick={() => {
                                  // This would navigate to the hotel details page
                                  handleCloseHotelsDialog();
                                  setSnackbar({
                                    open: true,
                                    message: `Viewing hotel ${hotel.name} (demo)`,
                                    severity: 'info'
                                  });
                                }}
                              >
                                <ViewIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                  <HotelIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 1 }} />
                  <Typography variant="body1" color="text.secondary">
                    This vendor doesn't have any hotels yet.
                  </Typography>
                  <Button
                    variant="outlined"
                    startIcon={<AddIcon />}
                    sx={{ mt: 2 }}
                    onClick={() => {
                      handleCloseHotelsDialog();
                      setSnackbar({
                        open: true,
                        message: 'Adding a hotel for this vendor (demo)',
                        severity: 'info'
                      });
                    }}
                  >
                    Add Hotel
                  </Button>
                </Paper>
              )}
            </>
          ) : (
            <Typography variant="body1" align="center">
              No vendor selected
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseHotelsDialog}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Vendors;
