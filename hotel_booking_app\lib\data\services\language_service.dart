import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  List<Map<String, dynamic>> _languages = [];
  String _selectedLanguage = 'en'; // Default language is English
  bool _isLoading = false;
  String? _error;

  LanguageService() {
    _loadSelectedLanguage();
    fetchLanguages();
  }

  // Getters
  List<Map<String, dynamic>> get languages => _languages;
  String get selectedLanguage => _selectedLanguage;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Load selected language from SharedPreferences
  Future<void> _loadSelectedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguage = prefs.getString('selectedLanguage');
      if (savedLanguage != null) {
        _selectedLanguage = savedLanguage;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading selected language: $e');
    }
  }

  // Save selected language to SharedPreferences
  Future<void> _saveSelectedLanguage(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selectedLanguage', languageCode);
    } catch (e) {
      debugPrint('Error saving selected language: $e');
    }
  }

  // Fetch languages from Firestore
  Future<void> fetchLanguages() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final snapshot = await _firestore.collection('languages').get();

      if (snapshot.docs.isEmpty) {
        // If no languages exist in Firestore, create default ones
        await _createDefaultLanguages();
        final newSnapshot = await _firestore.collection('languages').get();
        _languages = newSnapshot.docs
            .map((doc) => {
                  'id': doc.id,
                  ...doc.data(),
                })
            .toList();
      } else {
        _languages = snapshot.docs
            .map((doc) => {
                  'id': doc.id,
                  ...doc.data(),
                })
            .toList();
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Create default languages in Firestore if none exist
  Future<void> _createDefaultLanguages() async {
    final batch = _firestore.batch();

    final defaultLanguages = [
      {
        'code': 'en',
        'name': 'English',
        'nativeName': 'English',
        'flag': '🇺🇸',
        'isActive': true,
      },
      {
        'code': 'hi',
        'name': 'Hindi',
        'nativeName': 'हिन्दी',
        'flag': '🇮🇳',
        'isActive': true,
      },
      {
        'code': 'es',
        'name': 'Spanish',
        'nativeName': 'Español',
        'flag': '🇪🇸',
        'isActive': true,
      },
      {
        'code': 'fr',
        'name': 'French',
        'nativeName': 'Français',
        'flag': '🇫🇷',
        'isActive': true,
      },
      {
        'code': 'de',
        'name': 'German',
        'nativeName': 'Deutsch',
        'flag': '🇩🇪',
        'isActive': true,
      },
    ];

    for (final language in defaultLanguages) {
      final docRef =
          _firestore.collection('languages').doc(language['code'] as String);
      batch.set(docRef, language);
    }

    await batch.commit();
  }

  // Set selected language
  Future<void> setLanguage(String languageCode) async {
    _selectedLanguage = languageCode;
    await _saveSelectedLanguage(languageCode);
    notifyListeners();
  }

  // Add a new language (admin function)
  Future<void> addLanguage(Map<String, dynamic> language) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _firestore
          .collection('languages')
          .doc(language['code'] as String)
          .set(language);
      await fetchLanguages();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Update a language (admin function)
  Future<void> updateLanguage(String code, Map<String, dynamic> data) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _firestore.collection('languages').doc(code).update(data);
      await fetchLanguages();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Toggle language active status (admin function)
  Future<void> toggleLanguageStatus(String code, bool isActive) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _firestore.collection('languages').doc(code).update({
        'isActive': isActive,
      });
      await fetchLanguages();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }
}
