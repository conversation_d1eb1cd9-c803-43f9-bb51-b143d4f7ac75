const admin = require('firebase-admin');
const serviceAccount = require('../serviceAccountKey.json');
const { startOfWeek, endOfWeek, addDays, format, addHours, setHours, setMinutes } = require('date-fns');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

// Constants
const STAFF_COLLECTION = 'staff';
const STAFF_SHIFTS_COLLECTION = 'staffShifts';
const STAFF_SCHEDULES_COLLECTION = 'staffSchedules';

// Shift types
const SHIFT_TYPE_MORNING = 'morning';
const SHIFT_TYPE_AFTERNOON = 'afternoon';
const SHIFT_TYPE_NIGHT = 'night';
const SHIFT_TYPE_CUSTOM = 'custom';

// Sample hotel and vendor IDs (replace with actual IDs)
const HOTEL_ID = 'hotel123';
const VENDOR_ID = 'vendor123';

// Shift times
const shiftTimes = {
  [SHIFT_TYPE_MORNING]: { start: '06:00', end: '14:00' },
  [SHIFT_TYPE_AFTERNOON]: { start: '14:00', end: '22:00' },
  [SHIFT_TYPE_NIGHT]: { start: '22:00', end: '06:00' }
};

// Helper function to parse time string to Date
const parseTimeToDate = (dateObj, timeStr) => {
  const [hours, minutes] = timeStr.split(':').map(Number);
  const result = new Date(dateObj);
  result.setHours(hours, minutes, 0, 0);
  return result;
};

// Seed staff schedules
async function seedStaffSchedules() {
  try {
    console.log('Seeding staff schedules...');
    
    // Clear existing staff shifts
    const existingShifts = await db.collection(STAFF_SHIFTS_COLLECTION)
      .where('hotelId', '==', HOTEL_ID)
      .get();
    
    let batch = db.batch();
    let batchCount = 0;
    
    existingShifts.forEach(doc => {
      batch.delete(doc.ref);
      batchCount++;
      
      // Firestore batches are limited to 500 operations
      if (batchCount >= 450) {
        batch.commit();
        batch = db.batch();
        batchCount = 0;
      }
    });
    
    if (batchCount > 0) {
      await batch.commit();
    }
    
    console.log(`Deleted ${existingShifts.size} existing staff shifts.`);
    
    // Clear existing staff schedules
    const existingSchedules = await db.collection(STAFF_SCHEDULES_COLLECTION)
      .where('hotelId', '==', HOTEL_ID)
      .get();
    
    batch = db.batch();
    batchCount = 0;
    
    existingSchedules.forEach(doc => {
      batch.delete(doc.ref);
      batchCount++;
      
      if (batchCount >= 450) {
        batch.commit();
        batch = db.batch();
        batchCount = 0;
      }
    });
    
    if (batchCount > 0) {
      await batch.commit();
    }
    
    console.log(`Deleted ${existingSchedules.size} existing staff schedules.`);
    
    // Get staff members
    const staffSnapshot = await db.collection(STAFF_COLLECTION)
      .where('hotelId', '==', HOTEL_ID)
      .where('status', '==', 'active')
      .get();
    
    if (staffSnapshot.empty) {
      console.log('No active staff members found. Please run seed-staff-members.js first.');
      return;
    }
    
    const staffMembers = [];
    staffSnapshot.forEach(doc => {
      staffMembers.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    console.log(`Found ${staffMembers.length} active staff members.`);
    
    // Generate schedules for the current week
    const today = new Date();
    const weekStart = startOfWeek(today, { weekStartsOn: 1 }); // Week starts on Monday
    const weekEnd = endOfWeek(today, { weekStartsOn: 1 }); // Week ends on Sunday
    
    console.log(`Generating schedules for week: ${format(weekStart, 'MMM d')} - ${format(weekEnd, 'MMM d, yyyy')}`);
    
    // Create shifts for each staff member
    const shifts = [];
    const schedules = [];
    
    for (const staff of staffMembers) {
      const staffShifts = [];
      
      // Generate shifts based on role
      switch (staff.role) {
        case 'housekeeping':
          // Housekeeping staff work morning shifts on weekdays
          for (let i = 0; i < 5; i++) {
            const shiftDate = addDays(weekStart, i);
            const shiftType = SHIFT_TYPE_MORNING;
            
            const startTime = parseTimeToDate(shiftDate, shiftTimes[shiftType].start);
            const endTime = parseTimeToDate(shiftDate, shiftTimes[shiftType].end);
            
            staffShifts.push({
              staffId: staff.id,
              staffName: staff.name,
              hotelId: HOTEL_ID,
              vendorId: VENDOR_ID,
              date: admin.firestore.Timestamp.fromDate(shiftDate),
              shiftType,
              startTime: admin.firestore.Timestamp.fromDate(startTime),
              endTime: admin.firestore.Timestamp.fromDate(endTime),
              role: staff.role,
              notes: '',
              createdAt: admin.firestore.Timestamp.now(),
              updatedAt: admin.firestore.Timestamp.now()
            });
          }
          break;
          
        case 'food_service':
          // Food service staff work afternoon shifts
          for (let i = 0; i < 7; i++) {
            // Skip some days randomly
            if (Math.random() < 0.3) continue;
            
            const shiftDate = addDays(weekStart, i);
            const shiftType = SHIFT_TYPE_AFTERNOON;
            
            const startTime = parseTimeToDate(shiftDate, shiftTimes[shiftType].start);
            const endTime = parseTimeToDate(shiftDate, shiftTimes[shiftType].end);
            
            staffShifts.push({
              staffId: staff.id,
              staffName: staff.name,
              hotelId: HOTEL_ID,
              vendorId: VENDOR_ID,
              date: admin.firestore.Timestamp.fromDate(shiftDate),
              shiftType,
              startTime: admin.firestore.Timestamp.fromDate(startTime),
              endTime: admin.firestore.Timestamp.fromDate(endTime),
              role: staff.role,
              notes: '',
              createdAt: admin.firestore.Timestamp.now(),
              updatedAt: admin.firestore.Timestamp.now()
            });
          }
          break;
          
        case 'maintenance':
          // Maintenance staff work custom shifts
          for (let i = 0; i < 5; i++) {
            // Skip some days randomly
            if (Math.random() < 0.2) continue;
            
            const shiftDate = addDays(weekStart, i);
            const shiftType = SHIFT_TYPE_CUSTOM;
            
            // Custom shift times (9 AM to 5 PM)
            const startTime = setHours(setMinutes(shiftDate, 0), 9);
            const endTime = setHours(setMinutes(shiftDate, 0), 17);
            
            staffShifts.push({
              staffId: staff.id,
              staffName: staff.name,
              hotelId: HOTEL_ID,
              vendorId: VENDOR_ID,
              date: admin.firestore.Timestamp.fromDate(shiftDate),
              shiftType,
              startTime: admin.firestore.Timestamp.fromDate(startTime),
              endTime: admin.firestore.Timestamp.fromDate(endTime),
              role: staff.role,
              notes: 'Regular maintenance shift',
              createdAt: admin.firestore.Timestamp.now(),
              updatedAt: admin.firestore.Timestamp.now()
            });
          }
          break;
          
        case 'front_desk':
          // Front desk staff work all shifts in rotation
          const shiftTypes = [SHIFT_TYPE_MORNING, SHIFT_TYPE_AFTERNOON, SHIFT_TYPE_NIGHT];
          
          for (let i = 0; i < 7; i++) {
            const shiftDate = addDays(weekStart, i);
            const shiftType = shiftTypes[i % 3];
            
            const startTime = parseTimeToDate(shiftDate, shiftTimes[shiftType].start);
            let endTime = parseTimeToDate(shiftDate, shiftTimes[shiftType].end);
            
            // Handle overnight shifts
            if (shiftType === SHIFT_TYPE_NIGHT) {
              endTime = addDays(endTime, 1);
            }
            
            staffShifts.push({
              staffId: staff.id,
              staffName: staff.name,
              hotelId: HOTEL_ID,
              vendorId: VENDOR_ID,
              date: admin.firestore.Timestamp.fromDate(shiftDate),
              shiftType,
              startTime: admin.firestore.Timestamp.fromDate(startTime),
              endTime: admin.firestore.Timestamp.fromDate(endTime),
              role: staff.role,
              notes: '',
              createdAt: admin.firestore.Timestamp.now(),
              updatedAt: admin.firestore.Timestamp.now()
            });
          }
          break;
          
        case 'manager':
          // Managers work morning shifts on weekdays
          for (let i = 0; i < 5; i++) {
            const shiftDate = addDays(weekStart, i);
            const shiftType = SHIFT_TYPE_MORNING;
            
            // Custom shift times (8 AM to 4 PM)
            const startTime = setHours(setMinutes(shiftDate, 0), 8);
            const endTime = setHours(setMinutes(shiftDate, 0), 16);
            
            staffShifts.push({
              staffId: staff.id,
              staffName: staff.name,
              hotelId: HOTEL_ID,
              vendorId: VENDOR_ID,
              date: admin.firestore.Timestamp.fromDate(shiftDate),
              shiftType,
              startTime: admin.firestore.Timestamp.fromDate(startTime),
              endTime: admin.firestore.Timestamp.fromDate(endTime),
              role: staff.role,
              notes: '',
              createdAt: admin.firestore.Timestamp.now(),
              updatedAt: admin.firestore.Timestamp.now()
            });
          }
          break;
      }
      
      // Add shifts to the array
      shifts.push(...staffShifts);
      
      // Create schedule for the staff member
      schedules.push({
        staffId: staff.id,
        staffName: staff.name,
        hotelId: HOTEL_ID,
        vendorId: VENDOR_ID,
        weekStartDate: admin.firestore.Timestamp.fromDate(weekStart),
        weekEndDate: admin.firestore.Timestamp.fromDate(weekEnd),
        shifts: staffShifts,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });
    }
    
    // Add shifts to Firestore
    console.log(`Adding ${shifts.length} shifts to Firestore...`);
    
    batch = db.batch();
    batchCount = 0;
    
    for (const shift of shifts) {
      const shiftRef = db.collection(STAFF_SHIFTS_COLLECTION).doc();
      batch.set(shiftRef, shift);
      batchCount++;
      
      if (batchCount >= 450) {
        await batch.commit();
        batch = db.batch();
        batchCount = 0;
      }
    }
    
    if (batchCount > 0) {
      await batch.commit();
    }
    
    // Add schedules to Firestore
    console.log(`Adding ${schedules.length} schedules to Firestore...`);
    
    batch = db.batch();
    batchCount = 0;
    
    for (const schedule of schedules) {
      const scheduleRef = db.collection(STAFF_SCHEDULES_COLLECTION).doc();
      batch.set(scheduleRef, schedule);
      batchCount++;
      
      if (batchCount >= 450) {
        await batch.commit();
        batch = db.batch();
        batchCount = 0;
      }
    }
    
    if (batchCount > 0) {
      await batch.commit();
    }
    
    console.log('Staff schedules seeded successfully!');
  } catch (error) {
    console.error('Error seeding staff schedules:', error);
  }
}

// Run the seed function
seedStaffSchedules()
  .then(() => {
    console.log('Script completed successfully!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
