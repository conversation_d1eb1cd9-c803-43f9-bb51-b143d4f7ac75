import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit as firestoreLimit,
  Timestamp,
  serverTimestamp,
  DocumentSnapshot,
  writeBatch,
  setDoc
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { startOfWeek, endOfWeek, addDays, format } from 'date-fns';

// Collection names
const STAFF_SCHEDULES_COLLECTION = 'staffSchedules';
const STAFF_SHIFTS_COLLECTION = 'staffShifts';
const STAFF_TIME_OFF_COLLECTION = 'staffTimeOff';

// Shift types
export const SHIFT_TYPE_MORNING = 'morning';
export const SHIFT_TYPE_AFTERNOON = 'afternoon';
export const SHIFT_TYPE_NIGHT = 'night';
export const SHIFT_TYPE_CUSTOM = 'custom';

// Time off types
export const TIME_OFF_TYPE_VACATION = 'vacation';
export const TIME_OFF_TYPE_SICK = 'sick';
export const TIME_OFF_TYPE_PERSONAL = 'personal';
export const TIME_OFF_TYPE_OTHER = 'other';

// Time off status
export const TIME_OFF_STATUS_PENDING = 'pending';
export const TIME_OFF_STATUS_APPROVED = 'approved';
export const TIME_OFF_STATUS_REJECTED = 'rejected';

// Staff schedule interface
export interface StaffSchedule {
  id?: string;
  staffId: string;
  staffName: string;
  hotelId: string;
  vendorId: string;
  weekStartDate: Timestamp;
  weekEndDate: Timestamp;
  shifts: StaffShift[];
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// Staff shift interface
export interface StaffShift {
  id?: string;
  staffId: string;
  staffName: string;
  hotelId: string;
  vendorId: string;
  date: Timestamp;
  shiftType: string;
  startTime: Timestamp;
  endTime: Timestamp;
  role: string;
  notes?: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// Staff time off interface
export interface StaffTimeOff {
  id?: string;
  staffId: string;
  staffName: string;
  hotelId: string;
  vendorId: string;
  startDate: Timestamp;
  endDate: Timestamp;
  type: string;
  status: string;
  reason?: string;
  notes?: string;
  approvedBy?: string;
  approvedAt?: Timestamp;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

/**
 * Get staff schedule for a specific week
 */
export const getStaffScheduleForWeek = async (
  hotelId: string,
  weekStartDate: Date
): Promise<StaffSchedule[]> => {
  try {
    const startDate = startOfWeek(weekStartDate, { weekStartsOn: 1 }); // Week starts on Monday
    const endDate = endOfWeek(weekStartDate, { weekStartsOn: 1 }); // Week ends on Sunday
    
    const startTimestamp = Timestamp.fromDate(startDate);
    const endTimestamp = Timestamp.fromDate(endDate);
    
    const schedulesQuery = query(
      collection(db, STAFF_SCHEDULES_COLLECTION),
      where('hotelId', '==', hotelId),
      where('weekStartDate', '==', startTimestamp)
    );
    
    const querySnapshot = await getDocs(schedulesQuery);
    const schedules: StaffSchedule[] = [];
    
    querySnapshot.forEach((doc) => {
      schedules.push({
        id: doc.id,
        ...doc.data() as Omit<StaffSchedule, 'id'>
      });
    });
    
    return schedules;
  } catch (error) {
    console.error('Error getting staff schedule for week:', error);
    throw error;
  }
};

/**
 * Get staff schedule for a specific staff member
 */
export const getStaffScheduleForStaff = async (
  staffId: string,
  startDate: Date,
  endDate: Date
): Promise<StaffShift[]> => {
  try {
    const startTimestamp = Timestamp.fromDate(startDate);
    const endTimestamp = Timestamp.fromDate(endDate);
    
    const shiftsQuery = query(
      collection(db, STAFF_SHIFTS_COLLECTION),
      where('staffId', '==', staffId),
      where('date', '>=', startTimestamp),
      where('date', '<=', endTimestamp),
      orderBy('date', 'asc'),
      orderBy('startTime', 'asc')
    );
    
    const querySnapshot = await getDocs(shiftsQuery);
    const shifts: StaffShift[] = [];
    
    querySnapshot.forEach((doc) => {
      shifts.push({
        id: doc.id,
        ...doc.data() as Omit<StaffShift, 'id'>
      });
    });
    
    return shifts;
  } catch (error) {
    console.error('Error getting staff schedule for staff:', error);
    throw error;
  }
};

/**
 * Create or update a staff schedule for a week
 */
export const createOrUpdateStaffSchedule = async (
  schedule: Omit<StaffSchedule, 'id' | 'createdAt' | 'updatedAt'>
): Promise<StaffSchedule> => {
  try {
    // Check if schedule already exists
    const existingScheduleQuery = query(
      collection(db, STAFF_SCHEDULES_COLLECTION),
      where('staffId', '==', schedule.staffId),
      where('weekStartDate', '==', schedule.weekStartDate)
    );
    
    const querySnapshot = await getDocs(existingScheduleQuery);
    
    if (!querySnapshot.empty) {
      // Update existing schedule
      const existingScheduleDoc = querySnapshot.docs[0];
      const existingSchedule = {
        id: existingScheduleDoc.id,
        ...existingScheduleDoc.data() as Omit<StaffSchedule, 'id'>
      };
      
      await updateDoc(doc(db, STAFF_SCHEDULES_COLLECTION, existingSchedule.id!), {
        ...schedule,
        updatedAt: serverTimestamp()
      });
      
      return {
        ...schedule,
        id: existingSchedule.id,
        createdAt: existingSchedule.createdAt,
        updatedAt: Timestamp.now()
      };
    } else {
      // Create new schedule
      const now = Timestamp.now();
      const newSchedule: Omit<StaffSchedule, 'id'> = {
        ...schedule,
        createdAt: now,
        updatedAt: now
      };
      
      const docRef = await addDoc(collection(db, STAFF_SCHEDULES_COLLECTION), newSchedule);
      
      return {
        ...newSchedule,
        id: docRef.id
      };
    }
  } catch (error) {
    console.error('Error creating or updating staff schedule:', error);
    throw error;
  }
};

/**
 * Create a staff shift
 */
export const createStaffShift = async (
  shift: Omit<StaffShift, 'id' | 'createdAt' | 'updatedAt'>
): Promise<StaffShift> => {
  try {
    const now = Timestamp.now();
    const newShift: Omit<StaffShift, 'id'> = {
      ...shift,
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await addDoc(collection(db, STAFF_SHIFTS_COLLECTION), newShift);
    
    return {
      ...newShift,
      id: docRef.id
    };
  } catch (error) {
    console.error('Error creating staff shift:', error);
    throw error;
  }
};

/**
 * Update a staff shift
 */
export const updateStaffShift = async (
  shiftId: string,
  updates: Partial<Omit<StaffShift, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<boolean> => {
  try {
    await updateDoc(doc(db, STAFF_SHIFTS_COLLECTION, shiftId), {
      ...updates,
      updatedAt: serverTimestamp()
    });
    
    return true;
  } catch (error) {
    console.error('Error updating staff shift:', error);
    throw error;
  }
};

/**
 * Delete a staff shift
 */
export const deleteStaffShift = async (shiftId: string): Promise<boolean> => {
  try {
    await deleteDoc(doc(db, STAFF_SHIFTS_COLLECTION, shiftId));
    return true;
  } catch (error) {
    console.error('Error deleting staff shift:', error);
    throw error;
  }
};

/**
 * Get shifts for a specific date
 */
export const getShiftsForDate = async (
  hotelId: string,
  date: Date
): Promise<StaffShift[]> => {
  try {
    // Convert date to start and end of day
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    
    const startTimestamp = Timestamp.fromDate(startOfDay);
    const endTimestamp = Timestamp.fromDate(endOfDay);
    
    const shiftsQuery = query(
      collection(db, STAFF_SHIFTS_COLLECTION),
      where('hotelId', '==', hotelId),
      where('date', '>=', startTimestamp),
      where('date', '<=', endTimestamp),
      orderBy('date', 'asc'),
      orderBy('startTime', 'asc')
    );
    
    const querySnapshot = await getDocs(shiftsQuery);
    const shifts: StaffShift[] = [];
    
    querySnapshot.forEach((doc) => {
      shifts.push({
        id: doc.id,
        ...doc.data() as Omit<StaffShift, 'id'>
      });
    });
    
    return shifts;
  } catch (error) {
    console.error('Error getting shifts for date:', error);
    throw error;
  }
};

/**
 * Request time off for a staff member
 */
export const requestTimeOff = async (
  timeOff: Omit<StaffTimeOff, 'id' | 'createdAt' | 'updatedAt' | 'status' | 'approvedBy' | 'approvedAt'>
): Promise<StaffTimeOff> => {
  try {
    const now = Timestamp.now();
    const newTimeOff: Omit<StaffTimeOff, 'id'> = {
      ...timeOff,
      status: TIME_OFF_STATUS_PENDING,
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await addDoc(collection(db, STAFF_TIME_OFF_COLLECTION), newTimeOff);
    
    return {
      ...newTimeOff,
      id: docRef.id
    };
  } catch (error) {
    console.error('Error requesting time off:', error);
    throw error;
  }
};

/**
 * Approve or reject a time off request
 */
export const updateTimeOffStatus = async (
  timeOffId: string,
  status: string,
  approvedBy: string
): Promise<boolean> => {
  try {
    const now = Timestamp.now();
    
    await updateDoc(doc(db, STAFF_TIME_OFF_COLLECTION, timeOffId), {
      status,
      approvedBy,
      approvedAt: now,
      updatedAt: now
    });
    
    return true;
  } catch (error) {
    console.error('Error updating time off status:', error);
    throw error;
  }
};

/**
 * Get time off requests for a hotel
 */
export const getTimeOffRequestsForHotel = async (
  hotelId: string,
  options?: {
    status?: string;
    startDate?: Date;
    endDate?: Date;
  }
): Promise<StaffTimeOff[]> => {
  try {
    let timeOffQuery = query(
      collection(db, STAFF_TIME_OFF_COLLECTION),
      where('hotelId', '==', hotelId),
      orderBy('startDate', 'asc')
    );
    
    // Apply status filter if provided
    if (options?.status) {
      timeOffQuery = query(
        timeOffQuery,
        where('status', '==', options.status)
      );
    }
    
    // Apply date range filter if provided
    if (options?.startDate && options?.endDate) {
      const startTimestamp = Timestamp.fromDate(options.startDate);
      const endTimestamp = Timestamp.fromDate(options.endDate);
      
      timeOffQuery = query(
        timeOffQuery,
        where('startDate', '>=', startTimestamp),
        where('startDate', '<=', endTimestamp)
      );
    }
    
    const querySnapshot = await getDocs(timeOffQuery);
    const timeOffRequests: StaffTimeOff[] = [];
    
    querySnapshot.forEach((doc) => {
      timeOffRequests.push({
        id: doc.id,
        ...doc.data() as Omit<StaffTimeOff, 'id'>
      });
    });
    
    return timeOffRequests;
  } catch (error) {
    console.error('Error getting time off requests for hotel:', error);
    throw error;
  }
};

/**
 * Get time off requests for a staff member
 */
export const getTimeOffRequestsForStaff = async (
  staffId: string,
  options?: {
    status?: string;
  }
): Promise<StaffTimeOff[]> => {
  try {
    let timeOffQuery = query(
      collection(db, STAFF_TIME_OFF_COLLECTION),
      where('staffId', '==', staffId),
      orderBy('startDate', 'asc')
    );
    
    // Apply status filter if provided
    if (options?.status) {
      timeOffQuery = query(
        timeOffQuery,
        where('status', '==', options.status)
      );
    }
    
    const querySnapshot = await getDocs(timeOffQuery);
    const timeOffRequests: StaffTimeOff[] = [];
    
    querySnapshot.forEach((doc) => {
      timeOffRequests.push({
        id: doc.id,
        ...doc.data() as Omit<StaffTimeOff, 'id'>
      });
    });
    
    return timeOffRequests;
  } catch (error) {
    console.error('Error getting time off requests for staff:', error);
    throw error;
  }
};

export default {
  getStaffScheduleForWeek,
  getStaffScheduleForStaff,
  createOrUpdateStaffSchedule,
  createStaffShift,
  updateStaffShift,
  deleteStaffShift,
  getShiftsForDate,
  requestTimeOff,
  updateTimeOffStatus,
  getTimeOffRequestsForHotel,
  getTimeOffRequestsForStaff
};
