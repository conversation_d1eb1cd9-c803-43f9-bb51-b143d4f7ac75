import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  Timestamp,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { StaffShift } from './staffSchedulingService';
import { format, addDays, startOfWeek } from 'date-fns';

// Collection names
const SCHEDULE_TEMPLATES_COLLECTION = 'scheduleTemplates';
const TEMPLATE_SHIFTS_COLLECTION = 'templateShifts';

// Template types
export const TEMPLATE_TYPE_WEEKLY = 'weekly';
export const TEMPLATE_TYPE_DAILY = 'daily';
export const TEMPLATE_TYPE_CUSTOM = 'custom';

// Schedule template interface
export interface ScheduleTemplate {
  id?: string;
  name: string;
  description?: string;
  type: string;
  hotelId: string;
  vendorId: string;
  isDefault?: boolean;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// Template shift interface
export interface TemplateShift {
  id?: string;
  templateId: string;
  dayOfWeek?: number; // 0-6 (Sunday-Saturday) for weekly templates
  role: string;
  shiftType: string;
  startTime: string; // Format: "HH:MM"
  endTime: string; // Format: "HH:MM"
  notes?: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

/**
 * Get all schedule templates for a hotel
 */
export const getTemplatesForHotel = async (
  hotelId: string
): Promise<ScheduleTemplate[]> => {
  try {
    const templatesQuery = query(
      collection(db, SCHEDULE_TEMPLATES_COLLECTION),
      where('hotelId', '==', hotelId),
      orderBy('name', 'asc')
    );
    
    const querySnapshot = await getDocs(templatesQuery);
    const templates: ScheduleTemplate[] = [];
    
    querySnapshot.forEach((doc) => {
      templates.push({
        id: doc.id,
        ...doc.data() as Omit<ScheduleTemplate, 'id'>
      });
    });
    
    return templates;
  } catch (error) {
    console.error('Error getting templates for hotel:', error);
    throw error;
  }
};

/**
 * Get a schedule template by ID
 */
export const getTemplateById = async (
  templateId: string
): Promise<ScheduleTemplate | null> => {
  try {
    const docRef = doc(db, SCHEDULE_TEMPLATES_COLLECTION, templateId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data() as Omit<ScheduleTemplate, 'id'>
      };
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error getting template by ID:', error);
    throw error;
  }
};

/**
 * Get shifts for a template
 */
export const getShiftsForTemplate = async (
  templateId: string
): Promise<TemplateShift[]> => {
  try {
    const shiftsQuery = query(
      collection(db, TEMPLATE_SHIFTS_COLLECTION),
      where('templateId', '==', templateId),
      orderBy('dayOfWeek', 'asc')
    );
    
    const querySnapshot = await getDocs(shiftsQuery);
    const shifts: TemplateShift[] = [];
    
    querySnapshot.forEach((doc) => {
      shifts.push({
        id: doc.id,
        ...doc.data() as Omit<TemplateShift, 'id'>
      });
    });
    
    return shifts;
  } catch (error) {
    console.error('Error getting shifts for template:', error);
    throw error;
  }
};

/**
 * Create a new schedule template
 */
export const createTemplate = async (
  template: Omit<ScheduleTemplate, 'id' | 'createdAt' | 'updatedAt'>
): Promise<ScheduleTemplate> => {
  try {
    const now = Timestamp.now();
    const newTemplate: Omit<ScheduleTemplate, 'id'> = {
      ...template,
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await addDoc(collection(db, SCHEDULE_TEMPLATES_COLLECTION), newTemplate);
    
    return {
      ...newTemplate,
      id: docRef.id
    };
  } catch (error) {
    console.error('Error creating template:', error);
    throw error;
  }
};

/**
 * Update a schedule template
 */
export const updateTemplate = async (
  templateId: string,
  updates: Partial<Omit<ScheduleTemplate, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<boolean> => {
  try {
    await updateDoc(doc(db, SCHEDULE_TEMPLATES_COLLECTION, templateId), {
      ...updates,
      updatedAt: serverTimestamp()
    });
    
    return true;
  } catch (error) {
    console.error('Error updating template:', error);
    throw error;
  }
};

/**
 * Delete a schedule template and its shifts
 */
export const deleteTemplate = async (templateId: string): Promise<boolean> => {
  try {
    // Get all shifts for the template
    const shifts = await getShiftsForTemplate(templateId);
    
    // Create a batch to delete the template and all its shifts
    const batch = writeBatch(db);
    
    // Delete the template
    batch.delete(doc(db, SCHEDULE_TEMPLATES_COLLECTION, templateId));
    
    // Delete all shifts
    for (const shift of shifts) {
      if (shift.id) {
        batch.delete(doc(db, TEMPLATE_SHIFTS_COLLECTION, shift.id));
      }
    }
    
    // Commit the batch
    await batch.commit();
    
    return true;
  } catch (error) {
    console.error('Error deleting template:', error);
    throw error;
  }
};

/**
 * Add a shift to a template
 */
export const addShiftToTemplate = async (
  shift: Omit<TemplateShift, 'id' | 'createdAt' | 'updatedAt'>
): Promise<TemplateShift> => {
  try {
    const now = Timestamp.now();
    const newShift: Omit<TemplateShift, 'id'> = {
      ...shift,
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await addDoc(collection(db, TEMPLATE_SHIFTS_COLLECTION), newShift);
    
    return {
      ...newShift,
      id: docRef.id
    };
  } catch (error) {
    console.error('Error adding shift to template:', error);
    throw error;
  }
};

/**
 * Update a template shift
 */
export const updateTemplateShift = async (
  shiftId: string,
  updates: Partial<Omit<TemplateShift, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<boolean> => {
  try {
    await updateDoc(doc(db, TEMPLATE_SHIFTS_COLLECTION, shiftId), {
      ...updates,
      updatedAt: serverTimestamp()
    });
    
    return true;
  } catch (error) {
    console.error('Error updating template shift:', error);
    throw error;
  }
};

/**
 * Delete a template shift
 */
export const deleteTemplateShift = async (shiftId: string): Promise<boolean> => {
  try {
    await deleteDoc(doc(db, TEMPLATE_SHIFTS_COLLECTION, shiftId));
    return true;
  } catch (error) {
    console.error('Error deleting template shift:', error);
    throw error;
  }
};

/**
 * Apply a template to create actual shifts
 */
export const applyTemplate = async (
  templateId: string,
  startDate: Date,
  staffAssignments: { [role: string]: string[] }, // Map of role to staff IDs
  hotelId: string,
  vendorId: string
): Promise<StaffShift[]> => {
  try {
    // Get the template
    const template = await getTemplateById(templateId);
    if (!template) {
      throw new Error('Template not found');
    }
    
    // Get the template shifts
    const templateShifts = await getShiftsForTemplate(templateId);
    if (templateShifts.length === 0) {
      throw new Error('Template has no shifts');
    }
    
    // Create actual shifts based on the template
    const shifts: StaffShift[] = [];
    const weekStart = startOfWeek(startDate, { weekStartsOn: 1 }); // Week starts on Monday
    
    for (const templateShift of templateShifts) {
      // Skip if no staff is assigned to this role
      if (!staffAssignments[templateShift.role] || staffAssignments[templateShift.role].length === 0) {
        continue;
      }
      
      // Get the day for this shift
      const dayOfWeek = templateShift.dayOfWeek || 0;
      const shiftDate = addDays(weekStart, dayOfWeek);
      
      // Parse start and end times
      const [startHour, startMinute] = templateShift.startTime.split(':').map(Number);
      const [endHour, endMinute] = templateShift.endTime.split(':').map(Number);
      
      // Create a shift for each staff member assigned to this role
      for (const staffId of staffAssignments[templateShift.role]) {
        // Create the shift date with the correct time
        const startDateTime = new Date(shiftDate);
        startDateTime.setHours(startHour, startMinute, 0, 0);
        
        const endDateTime = new Date(shiftDate);
        endDateTime.setHours(endHour, endMinute, 0, 0);
        
        // Handle overnight shifts
        if (endDateTime <= startDateTime) {
          endDateTime.setDate(endDateTime.getDate() + 1);
        }
        
        // Create the shift
        const shift: Omit<StaffShift, 'id'> = {
          staffId,
          staffName: '', // Will be filled in by the caller
          hotelId,
          vendorId,
          date: Timestamp.fromDate(shiftDate),
          role: templateShift.role,
          shiftType: templateShift.shiftType,
          startTime: Timestamp.fromDate(startDateTime),
          endTime: Timestamp.fromDate(endDateTime),
          notes: templateShift.notes || '',
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now()
        };
        
        shifts.push(shift as StaffShift);
      }
    }
    
    return shifts;
  } catch (error) {
    console.error('Error applying template:', error);
    throw error;
  }
};

export default {
  getTemplatesForHotel,
  getTemplateById,
  getShiftsForTemplate,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  addShiftToTemplate,
  updateTemplateShift,
  deleteTemplateShift,
  applyTemplate
};
