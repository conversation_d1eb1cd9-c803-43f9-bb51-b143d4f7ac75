
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Default deny all
    match /{document=**} {
      allow read, write: if false;
    }

    // Users collection
    match /users/{userId} {
      // Users can read their own data
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if request.auth != null && request.auth.uid == userId;

      // Super admins can read and write all user data
      allow read, write: if request.auth != null &&
                          exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
                          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super_admin';

      // Vendors can read and write their staff data
      allow read, write: if request.auth != null &&
                          exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
                          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'vendor' &&
                          resource.data.role == 'staff' &&
                          resource.data.vendorId == request.auth.uid;
    }

    // Hotels collection
    match /hotels/{hotelId} {
      // Anyone can read hotel info
      allow read: if true;

      // Super admins can write all hotel data
      allow write: if request.auth != null &&
                    exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
                    get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super_admin';

      // Vendors can write their own hotel data
      allow write: if request.auth != null &&
                    exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
                    (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'vendor' ||
                     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }

    // Rooms collection
    match /rooms/{roomId} {
      // Anyone can read room info
      allow read: if true;

      // Super admins can write all room data
      allow write: if request.auth != null &&
                    exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
                    get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super_admin';

      // Vendors can write rooms for their hotels
      allow write: if request.auth != null &&
                    exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
                    (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'vendor' ||
                     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }

    // Room Types collection
    match /roomTypes/{typeId} {
      // Anyone can read room type info
      allow read: if true;

      // Super admins can write all room type data
      allow write: if request.auth != null &&
                    exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
                    get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super_admin';

      // Vendors can write room types for their hotels
      allow write: if request.auth != null &&
                    exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
                    (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'vendor' ||
                     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }

    // Bookings collection
    match /bookings/{bookingId} {
      // Super admins can read and write all booking data
      allow read, write: if request.auth != null &&
                          exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
                          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super_admin';

      // Vendors can read and write bookings for their hotels
      allow read, write: if request.auth != null &&
                          exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
                          (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'vendor' ||
                           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');

      // Users can read their own bookings
      allow read: if request.auth != null && resource.data.userId == request.auth.uid;
      allow write: if request.auth != null;
    }

    // Room Availability collection
    match /roomAvailability/{availabilityId} {
      // Anyone can read availability
      allow read: if true;

      // Only authenticated users with proper roles can write
      allow write: if request.auth != null &&
                    (
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super_admin' ||
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'vendor'
                    );
    }

    // Pricing Rules collection
    match /pricingRules/{ruleId} {
      allow read: if true;
      allow write: if request.auth != null &&
                    (
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super_admin' ||
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'vendor'
                    );
    }

    // Booking Transactions collection
    match /bookingTransactions/{transactionId} {
      allow read: if request.auth != null &&
                   (
                     exists(/databases/$(database)/documents/bookings/$(resource.data.bookingId)) &&
                     get(/databases/$(database)/documents/bookings/$(resource.data.bookingId)).data.userId == request.auth.uid ||
                     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super_admin' ||
                     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
                     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'vendor'
                   );
      allow write: if request.auth != null &&
                    (
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super_admin' ||
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'vendor'
                    );
    }

    // Permissions collection
    match /permissions/{permissionId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
                    (
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super_admin' ||
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin'
                    );
    }

    // Allow users to read and write their own notifications
    match /notifications/{notificationId} {
      allow read: if request.auth != null && resource.data.userId == request.auth.uid;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null &&
                            (resource.data.userId == request.auth.uid ||
                             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super_admin');
    }

    // Allow users to submit vendor applications
    match /vendorRequests/{requestId} {
      allow read: if request.auth != null &&
                   (resource.data.userId == request.auth.uid ||
                    get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null &&
                             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
  }
}