import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  Divider,
  useTheme
} from '@mui/material';
import {
  Add as AddIcon,
  Person as PersonIcon,
  Hotel as HotelIcon,
  MeetingRoom as RoomIcon,
  EventAvailable as EventAvailableIcon,
  Discount as DiscountIcon,
  Report as ReportIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface QuickAction {
  title: string;
  icon: React.ReactNode;
  path: string;
  color: string;
}

const QuickActions: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();

  const actions: QuickAction[] = [
    {
      title: 'New Booking',
      icon: <AddIcon />,
      path: '/vendor/bookings/new',
      color: theme.palette.primary.main
    },
    {
      title: 'Add Guest',
      icon: <PersonIcon />,
      path: '/vendor/guests/new',
      color: theme.palette.success.main
    },
    {
      title: 'Add Room',
      icon: <RoomIcon />,
      path: '/vendor/rooms/new',
      color: theme.palette.warning.main
    },
    {
      title: 'Manage Hotel',
      icon: <HotelIcon />,
      path: '/vendor/hotels',
      color: theme.palette.info.main
    },
    {
      title: 'Availability',
      icon: <EventAvailableIcon />,
      path: '/vendor/availability',
      color: theme.palette.secondary.main
    },
    {
      title: 'Promotions',
      icon: <DiscountIcon />,
      path: '/vendor/promotions',
      color: '#FF6B6B'
    },
    {
      title: 'Reports',
      icon: <ReportIcon />,
      path: '/vendor/reports',
      color: '#6B66FF'
    },
    {
      title: 'Settings',
      icon: <SettingsIcon />,
      path: '/vendor/settings',
      color: '#808080'
    }
  ];

  const handleActionClick = (path: string) => {
    navigate(path);
  };

  return (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Typography variant="h6" gutterBottom>
        Quick Actions
      </Typography>
      <Divider sx={{ mb: 2 }} />
      <Grid container spacing={2}>
        {actions.map((action, index) => (
          <Grid item xs={6} sm={3} key={index}>
            <Button
              variant="outlined"
              fullWidth
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                p: 2,
                height: 100,
                borderColor: action.color,
                color: action.color,
                '&:hover': {
                  backgroundColor: `${action.color}10`,
                  borderColor: action.color
                }
              }}
              onClick={() => handleActionClick(action.path)}
            >
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                width: 40,
                height: 40,
                borderRadius: '50%',
                backgroundColor: `${action.color}20`,
                mb: 1
              }}>
                {action.icon}
              </Box>
              <Typography variant="caption" sx={{ textAlign: 'center' }}>
                {action.title}
              </Typography>
            </Button>
          </Grid>
        ))}
      </Grid>
    </Paper>
  );
};

export default QuickActions;
