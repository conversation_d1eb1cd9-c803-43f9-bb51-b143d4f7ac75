import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'package:hotel_booking_app/main.dart' as app;
import 'package:hotel_booking_app/data/services/aadhaar_verification_service.dart';
import 'package:hotel_booking_app/presentation/screens/profile/aadhaar_verification_screen.dart';

// Define verification status constants
const String verificationStatusVerified = 'verified';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Aadhaar Verification Flow', () {
    testWidgets('Complete verification flow test', (WidgetTester tester) async {
      // Initialize Firebase
      await Firebase.initializeApp();

      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to login screen and login
      // This assumes there's a login screen and the app starts with it
      await _login(tester);

      // Navigate to profile screen
      await _navigateToProfile(tester);

      // Navigate to verification screen
      await _navigateToVerificationScreen(tester);

      // Test the verification process
      await _testVerificationProcess(tester);
    });
  });
}

Future<void> _login(WidgetTester tester) async {
  // Find email and password fields
  final emailField = find.byKey(const Key('email_field'));
  final passwordField = find.byKey(const Key('password_field'));
  final loginButton = find.byKey(const Key('login_button'));

  // Enter credentials
  await tester.enterText(emailField, '<EMAIL>');
  await tester.enterText(passwordField, 'password123');

  // Tap login button
  await tester.tap(loginButton);
  await tester.pumpAndSettle();

  // Verify login was successful (e.g., by checking if we're on the home screen)
  expect(find.byType(BottomNavigationBar), findsOneWidget);
}

Future<void> _navigateToProfile(WidgetTester tester) async {
  // Find and tap the profile tab in the bottom navigation bar
  final profileTab = find.byIcon(Icons.person).last;
  await tester.tap(profileTab);
  await tester.pumpAndSettle();

  // Verify we're on the profile screen
  expect(find.text('Account Settings'), findsOneWidget);
}

Future<void> _navigateToVerificationScreen(WidgetTester tester) async {
  // Find and tap the Aadhaar verification option
  final verificationOption = find.text('Aadhaar Verification');
  await tester.tap(verificationOption);
  await tester.pumpAndSettle();

  // Verify we're on the verification screen
  expect(find.byType(AadhaarVerificationScreen), findsOneWidget);
}

Future<void> _testVerificationProcess(WidgetTester tester) async {
  // Find the Aadhaar number input field
  final aadhaarField = find.byKey(const Key('aadhaar_field'));
  expect(aadhaarField, findsOneWidget);

  // Enter a valid Aadhaar number
  await tester.enterText(aadhaarField, '************');
  await tester.pumpAndSettle();

  // Find and tap the "Send OTP" button
  final sendOtpButton = find.text('Send OTP');
  await tester.tap(sendOtpButton);
  await tester.pumpAndSettle();

  // Wait for OTP to be sent (this might take some time in a real scenario)
  await tester.pump(const Duration(seconds: 2));

  // Verify OTP input field is visible
  final otpField = find.byKey(const Key('otp_field'));
  expect(otpField, findsOneWidget);

  // Enter OTP
  await tester.enterText(otpField, '123456');
  await tester.pumpAndSettle();

  // Find and tap the "Verify OTP" button
  final verifyOtpButton = find.text('Verify OTP');
  await tester.tap(verifyOtpButton);
  await tester.pumpAndSettle();

  // Wait for verification to complete
  await tester.pump(const Duration(seconds: 2));

  // Check if verification was successful
  // This could be checking for a success dialog or a verified status
  expect(find.text('Verification Successful'), findsOneWidget);

  // Close the success dialog if it exists
  final okButton = find.text('OK');
  if (okButton.evaluate().isNotEmpty) {
    await tester.tap(okButton);
    await tester.pumpAndSettle();
  }

  // Verify that the verification status is updated
  // Get the verification service from the context
  final context = tester.element(find.byType(AadhaarVerificationScreen));
  final verificationService =
      Provider.of<AadhaarVerificationService>(context, listen: false);

  // Wait for the verification status to be updated
  await tester.pump(const Duration(seconds: 1));

  expect(verificationService.currentVerification?.status,
      equals(verificationStatusVerified));
}

// "access_token": "eyJhbGciOiJIUzUxMiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UlNTwzCL4OJDbuYgQAZfY0NbN4FilDnDGjyujdJdLO0Nh_Rci1V_eJ-MCZHXrggrgQSRqmusg1mbdG-FWMRPkg"
