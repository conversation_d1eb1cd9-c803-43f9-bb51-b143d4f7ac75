import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Button,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  VerifiedUser as VerifiedUserIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
  FilterList as FilterIcon,
  BarChart as BarChartIcon
} from '@mui/icons-material';
import { getVerifications, getVerificationStats, getVerificationByUserId } from '../../services/verificationService';
import { AadhaarVerificationData } from '../../components/AadhaarVerificationCard';
import AadhaarVerificationCard from '../../components/AadhaarVerificationCard';

const Verifications: React.FC = () => {
  const navigate = useNavigate();
  const [verifications, setVerifications] = useState<AadhaarVerificationData[]>([]);
  const [stats, setStats] = useState({
    total: 0,
    verified: 0,
    pending: 0,
    failed: 0,
    notVerified: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedVerification, setSelectedVerification] = useState<AadhaarVerificationData | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);

  // Load data on component mount
  useEffect(() => {
    fetchData();
  }, [statusFilter]);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch verification stats
      const statsData = await getVerificationStats();
      setStats(statsData);

      // Fetch verifications with filter
      const verificationsData = await getVerifications(
        statusFilter !== 'all' ? statusFilter : undefined
      );
      setVerifications(verificationsData);
    } catch (err: any) {
      setError(err.message || 'Failed to load verifications');
      console.error('Error fetching verifications:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusFilterChange = (event: SelectChangeEvent) => {
    setStatusFilter(event.target.value);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handleViewDetails = (verification: AadhaarVerificationData) => {
    // Option 1: Open dialog
    // setSelectedVerification(verification);
    // setDetailsOpen(true);

    // Option 2: Navigate to details page
    navigate(`/vendor/verifications/${verification.id}`);
  };

  const handleCloseDetails = () => {
    setDetailsOpen(false);
  };

  // Format date
  const formatDate = (date?: Date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Filter verifications by search query
  const filteredVerifications = verifications.filter(verification => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      verification.fullName.toLowerCase().includes(query) ||
      verification.maskedAadhaarNumber.toLowerCase().includes(query) ||
      verification.userId.toLowerCase().includes(query)
    );
  });

  // Get status color and icon
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'verified':
        return {
          color: 'success',
          icon: <VerifiedUserIcon fontSize="small" />,
          text: 'Verified'
        };
      case 'pending':
        return {
          color: 'warning',
          icon: null,
          text: 'Pending'
        };
      case 'failed':
        return {
          color: 'error',
          icon: null,
          text: 'Failed'
        };
      case 'notVerified':
        return {
          color: 'default',
          icon: null,
          text: 'Not Verified'
        };
      default:
        return {
          color: 'default',
          icon: null,
          text: 'Unknown'
        };
    }
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h4">
          Aadhaar Verifications
        </Typography>
        <Button
          variant="contained"
          startIcon={<BarChartIcon />}
          onClick={() => navigate('/vendor/verification-reports')}
        >
          View Reports
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary">
                Total
              </Typography>
              <Typography variant="h4">
                {stats.total}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent sx={{ bgcolor: 'success.light' }}>
              <Typography variant="subtitle2" color="text.secondary">
                Verified
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.verified}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent sx={{ bgcolor: 'warning.light' }}>
              <Typography variant="subtitle2" color="text.secondary">
                Pending
              </Typography>
              <Typography variant="h4" color="warning.main">
                {stats.pending}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent sx={{ bgcolor: 'error.light' }}>
              <Typography variant="subtitle2" color="text.secondary">
                Failed
              </Typography>
              <Typography variant="h4" color="error.main">
                {stats.failed}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary">
                Not Verified
              </Typography>
              <Typography variant="h4" color="text.secondary">
                {stats.notVerified}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              label="Search"
              variant="outlined"
              value={searchQuery}
              onChange={handleSearchChange}
              placeholder="Search by name or Aadhaar number"
              InputProps={{
                startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth>
              <InputLabel id="status-filter-label">Status</InputLabel>
              <Select
                labelId="status-filter-label"
                value={statusFilter}
                label="Status"
                onChange={handleStatusFilterChange}
                startAdornment={<FilterIcon color="action" sx={{ mr: 1 }} />}
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value="verified">Verified</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="failed">Failed</MenuItem>
                <MenuItem value="notVerified">Not Verified</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={12} md={4}>
            <Button
              variant="contained"
              startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
              onClick={fetchData}
              disabled={loading}
              fullWidth
            >
              Refresh
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Verifications Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Aadhaar Number</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Verified On</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : filteredVerifications.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                    No verifications found
                  </TableCell>
                </TableRow>
              ) : (
                filteredVerifications.map((verification) => {
                  const statusInfo = getStatusInfo(verification.status);
                  return (
                    <TableRow key={verification.id}>
                      <TableCell>{verification.fullName}</TableCell>
                      <TableCell>{verification.maskedAadhaarNumber}</TableCell>
                      <TableCell>
                        <Chip
                          label={statusInfo.text}
                          color={statusInfo.color as any}
                          size="small"
                          icon={statusInfo.icon ? statusInfo.icon : undefined}
                        />
                      </TableCell>
                      <TableCell>
                        {verification.verifiedAt ? formatDate(verification.verifiedAt) : 'N/A'}
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => handleViewDetails(verification)}
                          color="primary"
                        >
                          <ViewIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Verification Details Dialog */}
      <Dialog open={detailsOpen} onClose={handleCloseDetails} maxWidth="md" fullWidth>
        <DialogTitle>
          Aadhaar Verification Details
        </DialogTitle>
        <DialogContent dividers>
          {selectedVerification && (
            <AadhaarVerificationCard verification={selectedVerification} />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDetails}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Verifications;
