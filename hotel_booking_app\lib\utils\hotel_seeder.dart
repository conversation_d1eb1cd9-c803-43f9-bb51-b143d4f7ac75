import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';

/// Utility class for seeding hotel data into Firestore
class HotelSeeder {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Check if hotels collection exists and has data
  static Future<Map<String, dynamic>> checkHotelsCollection() async {
    try {
      final snapshot =
          await _firestore.collection(AppConstants.hotelsCollection).get();

      return {
        'exists': true,
        'count': snapshot.docs.length,
        'isEmpty': snapshot.docs.isEmpty,
      };
    } catch (e) {
      debugPrint('Error checking hotels collection: $e');
      return {
        'exists': false,
        'count': 0,
        'isEmpty': true,
        'error': e.toString(),
      };
    }
  }

  /// Seed hotels into Firestore
  static Future<bool> seedHotels() async {
    try {
      debugPrint('Starting to seed hotels into Firestore...');

      // First, clear any existing hotels to avoid duplicates
      await clearHotels();

      // Real hotel data with Indian locations and prices in INR
      final hotels = [
        {
          'name': 'Taj Palace',
          'description':
              'A luxurious 5-star hotel in the heart of New Delhi with stunning views and world-class amenities.',
          'address': '2 Sardar Patel Marg, Diplomatic Enclave',
          'city': 'New Delhi',
          'country': 'India',
          'zipCode': '110021',
          'phone': '+91-11-26110202',
          'email': '<EMAIL>',
          'website': 'www.tajhotels.com',
          'rating': 4.8,
          'price': 15000.0, // INR
          'amenities': [
            'Swimming Pool',
            'Spa',
            'Gym',
            'Restaurant',
            'Bar',
            'Room Service',
            'Free WiFi',
            'Parking'
          ],
          'images': [
            'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
            'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
          ],
          'vendorId': 'vendor-taj-palace',
          'createdAt': FieldValue.serverTimestamp(),
          'reviewCount': 120,
          'type': 'Luxury',
        },
        {
          'name': 'The Leela Palace',
          'description':
              'Experience the grandeur of royal India at this magnificent palace hotel with breathtaking architecture.',
          'address': 'Diplomatic Enclave, Chanakyapuri',
          'city': 'New Delhi',
          'country': 'India',
          'zipCode': '110023',
          'phone': '+91-11-39331234',
          'email': '<EMAIL>',
          'website': 'www.theleela.com',
          'rating': 4.9,
          'price': 18000.0, // INR
          'amenities': [
            'Swimming Pool',
            'Spa',
            'Gym',
            'Multiple Restaurants',
            'Bar',
            'Room Service',
            'Free WiFi',
            'Valet Parking'
          ],
          'images': [
            'https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
            'https://images.unsplash.com/photo-1564501049412-61c2a3083791?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
          ],
          'vendorId': 'vendor-leela-palace',
          'createdAt': FieldValue.serverTimestamp(),
          'reviewCount': 150,
          'type': 'Luxury',
        },
        {
          'name': 'Taj Mahal Palace',
          'description':
              'Iconic 5-star hotel overlooking the Arabian Sea, with a rich history and unparalleled luxury.',
          'address': 'Apollo Bunder',
          'city': 'Mumbai',
          'country': 'India',
          'zipCode': '400001',
          'phone': '+91-22-66653366',
          'email': '<EMAIL>',
          'website': 'www.tajhotels.com',
          'rating': 4.9,
          'price': 20000.0, // INR
          'amenities': [
            'Sea View Rooms',
            'Swimming Pool',
            'Spa',
            'Gym',
            'Multiple Restaurants',
            'Bar',
            'Room Service',
            'Free WiFi',
            'Valet Parking'
          ],
          'images': [
            'https://images.unsplash.com/photo-*************-326ccb383e9f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
            'https://images.unsplash.com/photo-*************-70ee3fc28f8e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1121&q=80',
          ],
          'vendorId': 'vendor-taj-mahal-palace',
          'createdAt': FieldValue.serverTimestamp(),
          'reviewCount': 200,
          'type': 'Heritage',
        },
        {
          'name': 'The Oberoi Udaivilas',
          'description':
              'Luxury resort on the banks of Lake Pichola, offering spectacular views of the lake and palaces.',
          'address': 'Haridasji Ki Magri',
          'city': 'Udaipur',
          'country': 'India',
          'zipCode': '313001',
          'phone': '+91-294-2433300',
          'email': '<EMAIL>',
          'website': 'www.oberoihotels.com',
          'rating': 4.9,
          'price': 35000.0, // INR
          'amenities': [
            'Lake View Rooms',
            'Private Pools',
            'Spa',
            'Gym',
            'Fine Dining',
            'Bar',
            'Room Service',
            'Free WiFi',
            'Boat Rides'
          ],
          'images': [
            'https://images.unsplash.com/photo-*************-1674de7a421a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1074&q=80',
            'https://images.unsplash.com/photo-*************-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1080&q=80',
          ],
          'vendorId': 'vendor-oberoi-udaivilas',
          'createdAt': FieldValue.serverTimestamp(),
          'reviewCount': 180,
          'type': 'Resort',
        },
        {
          'name': 'ITC Grand Chola',
          'description':
              'Magnificent luxury hotel inspired by the architecture of the Chola dynasty, offering world-class hospitality.',
          'address': '63 Mount Road, Guindy',
          'city': 'Chennai',
          'country': 'India',
          'zipCode': '600032',
          'phone': '+91-44-22200000',
          'email': '<EMAIL>',
          'website': 'www.itchotels.com',
          'rating': 4.7,
          'price': 12000.0, // INR
          'amenities': [
            'Swimming Pool',
            'Spa',
            'Gym',
            'Multiple Restaurants',
            'Bar',
            'Room Service',
            'Free WiFi',
            'Valet Parking'
          ],
          'images': [
            'https://images.unsplash.com/photo-1455587734955-081b22074882?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
            'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
          ],
          'vendorId': 'vendor-itc-grand-chola',
          'createdAt': FieldValue.serverTimestamp(),
          'reviewCount': 160,
          'type': 'Luxury',
        },
      ];

      // Add hotels to Firestore
      int successCount = 0;
      for (final hotel in hotels) {
        try {
          await _firestore.collection(AppConstants.hotelsCollection).add(hotel);
          successCount++;
        } catch (e) {
          debugPrint('Error adding hotel ${hotel['name']}: $e');
        }
      }

      debugPrint(
          'Successfully added $successCount out of ${hotels.length} hotels to Firestore');
      return successCount > 0;
    } catch (e) {
      debugPrint('Error seeding hotels: $e');
      return false;
    }
  }

  /// Delete all hotels in the collection (use with caution)
  static Future<bool> clearHotels() async {
    try {
      final snapshot =
          await _firestore.collection(AppConstants.hotelsCollection).get();

      int deletedCount = 0;
      for (final doc in snapshot.docs) {
        try {
          await _firestore
              .collection(AppConstants.hotelsCollection)
              .doc(doc.id)
              .delete();
          deletedCount++;
        } catch (e) {
          debugPrint('Error deleting hotel ${doc.id}: $e');
        }
      }

      debugPrint('Successfully deleted $deletedCount hotels from Firestore');
      return true;
    } catch (e) {
      debugPrint('Error clearing hotels: $e');
      return false;
    }
  }
}
