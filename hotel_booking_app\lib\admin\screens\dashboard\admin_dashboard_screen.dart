import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:hotel_booking_app/admin/screens/verification/aadhaar_verification_list_screen.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/core/theme/app_theme.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  bool _isLoading = true;
  Map<String, dynamic> _stats = {};
  
  @override
  void initState() {
    super.initState();
    _fetchDashboardStats();
  }
  
  Future<void> _fetchDashboardStats() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Fetch hotels count
      final hotelsSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.hotelsCollection)
          .count()
          .get();
      
      // Fetch bookings count
      final bookingsSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.bookingsCollection)
          .count()
          .get();
      
      // Fetch users count
      final usersSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.usersCollection)
          .count()
          .get();
      
      // Fetch verifications count
      final verificationsSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.aadhaarVerificationsCollection)
          .count()
          .get();
      
      // Fetch verified users count
      final verifiedUsersSnapshot = await FirebaseFirestore.instance
          .collection(AppConstants.aadhaarVerificationsCollection)
          .where('status', isEqualTo: 'verified')
          .count()
          .get();
      
      setState(() {
        _stats = {
          'hotels': hotelsSnapshot.count,
          'bookings': bookingsSnapshot.count,
          'users': usersSnapshot.count,
          'verifications': verificationsSnapshot.count,
          'verifiedUsers': verifiedUsersSnapshot.count,
        };
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error fetching dashboard stats: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchDashboardStats,
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await FirebaseAuth.instance.signOut();
              if (mounted) {
                Navigator.of(context).pushReplacementNamed('/login');
              }
            },
          ),
        ],
      ),
      drawer: _buildAdminDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Welcome message
                  const Text(
                    'Welcome, Admin',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Here\'s what\'s happening with your hotels today',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // Stats cards
                  Row(
                    children: [
                      _buildStatCard(
                        'Hotels',
                        _stats['hotels']?.toString() ?? '0',
                        Colors.blue,
                        Icons.hotel,
                      ),
                      const SizedBox(width: 16),
                      _buildStatCard(
                        'Bookings',
                        _stats['bookings']?.toString() ?? '0',
                        Colors.orange,
                        Icons.book_online,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      _buildStatCard(
                        'Users',
                        _stats['users']?.toString() ?? '0',
                        Colors.purple,
                        Icons.people,
                      ),
                      const SizedBox(width: 16),
                      _buildStatCard(
                        'Verified Users',
                        _stats['verifiedUsers']?.toString() ?? '0',
                        Colors.green,
                        Icons.verified_user,
                      ),
                    ],
                  ),
                  const SizedBox(height: 32),
                  
                  // Quick actions
                  const Text(
                    'Quick Actions',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildQuickActionCard(
                    title: 'Aadhaar Verifications',
                    description: 'View and manage user verifications',
                    icon: Icons.verified_user,
                    color: Colors.green,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AadhaarVerificationListScreen(),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildQuickActionCard(
                    title: 'Manage Hotels',
                    description: 'Add, edit, or remove hotels',
                    icon: Icons.hotel,
                    color: Colors.blue,
                    onTap: () {
                      // Navigate to hotel management screen
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildQuickActionCard(
                    title: 'Manage Bookings',
                    description: 'View and manage all bookings',
                    icon: Icons.book_online,
                    color: Colors.orange,
                    onTap: () {
                      // Navigate to bookings management screen
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildQuickActionCard(
                    title: 'User Management',
                    description: 'Manage users and permissions',
                    icon: Icons.people,
                    color: Colors.purple,
                    onTap: () {
                      // Navigate to user management screen
                    },
                  ),
                  const SizedBox(height: 32),
                  
                  // Recent activity
                  const Text(
                    'Recent Activity',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildRecentActivityList(),
                ],
              ),
            ),
    );
  }
  
  Widget _buildAdminDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: const BoxDecoration(
              color: AppTheme.primaryColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white,
                  child: Icon(
                    Icons.person,
                    size: 30,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 10),
                const Text(
                  'Admin Panel',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  FirebaseAuth.instance.currentUser?.email ?? '<EMAIL>',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.dashboard),
            title: const Text('Dashboard'),
            selected: true,
            onTap: () {
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.verified_user),
            title: const Text('Aadhaar Verifications'),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AadhaarVerificationListScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.hotel),
            title: const Text('Hotels'),
            onTap: () {
              Navigator.pop(context);
              // Navigate to hotels screen
            },
          ),
          ListTile(
            leading: const Icon(Icons.book_online),
            title: const Text('Bookings'),
            onTap: () {
              Navigator.pop(context);
              // Navigate to bookings screen
            },
          ),
          ListTile(
            leading: const Icon(Icons.people),
            title: const Text('Users'),
            onTap: () {
              Navigator.pop(context);
              // Navigate to users screen
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('Settings'),
            onTap: () {
              Navigator.pop(context);
              // Navigate to settings screen
            },
          ),
          ListTile(
            leading: const Icon(Icons.logout),
            title: const Text('Logout'),
            onTap: () async {
              await FirebaseAuth.instance.signOut();
              if (mounted) {
                Navigator.of(context).pushReplacementNamed('/login');
              }
            },
          ),
        ],
      ),
    );
  }
  
  Widget _buildStatCard(String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                value,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildQuickActionCard({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildRecentActivityList() {
    // In a real implementation, you would fetch recent activity from Firestore
    // For this demo, we'll use mock data
    final activities = [
      {
        'type': 'verification',
        'title': 'New Aadhaar Verification',
        'description': 'User John Doe verified their Aadhaar',
        'time': '10 minutes ago',
        'icon': Icons.verified_user,
        'color': Colors.green,
      },
      {
        'type': 'booking',
        'title': 'New Booking',
        'description': 'Booking #1234 was created',
        'time': '1 hour ago',
        'icon': Icons.book_online,
        'color': Colors.orange,
      },
      {
        'type': 'user',
        'title': 'New User Registration',
        'description': 'Jane Smith created an account',
        'time': '3 hours ago',
        'icon': Icons.person_add,
        'color': Colors.blue,
      },
    ];
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: activities.length,
        separatorBuilder: (context, index) => const Divider(),
        itemBuilder: (context, index) {
          final activity = activities[index];
          return ListTile(
            leading: CircleAvatar(
              backgroundColor: activity['color'] as Color,
              child: Icon(
                activity['icon'] as IconData,
                color: Colors.white,
                size: 20,
              ),
            ),
            title: Text(activity['title'] as String),
            subtitle: Text(activity['description'] as String),
            trailing: Text(
              activity['time'] as String,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            onTap: () {
              // Navigate to details based on activity type
              if (activity['type'] == 'verification') {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AadhaarVerificationListScreen(),
                  ),
                );
              }
            },
          );
        },
      ),
    );
  }
}
