import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/data/models/hotel_model.dart';

/// Utility class for debugging Firebase issues
class FirebaseDebug {
  /// Check if the hotels collection exists and has documents
  static Future<Map<String, dynamic>> checkHotelsCollection() async {
    final result = <String, dynamic>{};

    try {
      debugPrint('Checking hotels collection...');

      // Check if Firestore is accessible
      final firestore = FirebaseFirestore.instance;
      result['firestoreAvailable'] = firestore != null;

      // Try to get the hotels collection
      final snapshot =
          await firestore.collection(AppConstants.hotelsCollection).get();
      result['collectionExists'] = true;
      result['documentsCount'] = snapshot.docs.length;

      // Log the first document if available
      if (snapshot.docs.isNotEmpty) {
        final firstDoc = snapshot.docs.first;
        result['firstDocumentId'] = firstDoc.id;
        result['firstDocumentData'] = firstDoc.data();

        // Try to parse the document as a Hotel
        try {
          final hotel = Hotel.fromMap({...firstDoc.data(), 'id': firstDoc.id});
          result['hotelParsed'] = true;
          result['hotelName'] = hotel.name;
        } catch (e) {
          result['hotelParsed'] = false;
          result['parseError'] = e.toString();
        }
      } else {
        result['documentsExist'] = false;
      }

      debugPrint('Hotels collection check result: $result');
      return result;
    } catch (e) {
      debugPrint('Error checking hotels collection: $e');
      result['error'] = e.toString();
      return result;
    }
  }

  /// Create a sample hotel in Firestore for testing
  static Future<String?> createSampleHotel() async {
    try {
      debugPrint('Creating sample hotel...');

      final firestore = FirebaseFirestore.instance;

      // Sample hotel data
      final sampleHotel = {
        'name': 'Debug Test Hotel',
        'description': 'A test hotel created for debugging purposes',
        'address': '123 Debug Street',
        'city': 'Test City',
        'country': 'Test Country',
        'zipCode': '12345',
        'phone': '******-123-4567',
        'email': '<EMAIL>',
        'website': 'www.example.com',
        'rating': 4.5,
        'price': 100.0,
        'amenities': ['WiFi', 'Pool', 'Breakfast'],
        'images': [
          'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
        ],
        'vendorId': 'debug-vendor',
        'createdAt': FieldValue.serverTimestamp(),
        'reviewCount': 10,
        'type': 'Hotel',
      };

      // Add the hotel to Firestore
      final docRef = await firestore
          .collection(AppConstants.hotelsCollection)
          .add(sampleHotel);

      debugPrint('Sample hotel created with ID: ${docRef.id}');
      return docRef.id;
    } catch (e) {
      debugPrint('Error creating sample hotel: $e');
      return null;
    }
  }

  /// Create multiple sample hotels in Firestore for testing
  static Future<List<String>> createMultipleSampleHotels() async {
    try {
      debugPrint('Creating multiple sample hotels...');

      final firestore = FirebaseFirestore.instance;
      final List<String> createdIds = [];

      // Sample hotel data
      final sampleHotels = [
        {
          'name': 'Grand Hotel Luxury',
          'description':
              'A luxurious 5-star hotel in the heart of the city with stunning views and world-class amenities.',
          'address': '123 Main Street',
          'city': 'Mumbai',
          'country': 'India',
          'zipCode': '400001',
          'phone': '+91-22-12345678',
          'email': '<EMAIL>',
          'website': 'www.grandhotelluxury.com',
          'rating': 4.8,
          'price': 12500.0,
          'amenities': [
            'Swimming Pool',
            'Spa',
            'Gym',
            'Restaurant',
            'Bar',
            'Room Service',
            'Free WiFi',
            'Parking'
          ],
          'images': [
            'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
            'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
          ],
          'vendorId': 'sample-vendor-1',
          'createdAt': FieldValue.serverTimestamp(),
          'reviewCount': 120,
          'type': 'Luxury',
        },
        {
          'name': 'Seaside Resort',
          'description':
              'A beautiful beachfront resort with private access to the beach and stunning ocean views.',
          'address': '456 Beach Road',
          'city': 'Goa',
          'country': 'India',
          'zipCode': '403001',
          'phone': '+91-832-12345678',
          'email': '<EMAIL>',
          'website': 'www.seasideresort.com',
          'rating': 4.5,
          'price': 8500.0,
          'amenities': [
            'Beach Access',
            'Swimming Pool',
            'Spa',
            'Restaurant',
            'Bar',
            'Water Sports',
            'Free WiFi'
          ],
          'images': [
            'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
            'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
          ],
          'vendorId': 'sample-vendor-2',
          'createdAt': FieldValue.serverTimestamp(),
          'reviewCount': 85,
          'type': 'Resort',
        },
        {
          'name': 'Mountain View Lodge',
          'description':
              'A cozy mountain lodge with breathtaking views of the Himalayas and outdoor activities.',
          'address': '789 Mountain Road',
          'city': 'Shimla',
          'country': 'India',
          'zipCode': '171001',
          'phone': '+91-177-12345678',
          'email': '<EMAIL>',
          'website': 'www.mountainviewlodge.com',
          'rating': 4.3,
          'price': 6500.0,
          'amenities': [
            'Mountain Views',
            'Fireplace',
            'Restaurant',
            'Hiking Trails',
            'Free WiFi',
            'Parking'
          ],
          'images': [
            'https://images.unsplash.com/photo-1517320964276-a002fa203177?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
            'https://images.unsplash.com/photo-1506059612708-99d6c258160e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1169&q=80',
          ],
          'vendorId': 'sample-vendor-3',
          'createdAt': FieldValue.serverTimestamp(),
          'reviewCount': 62,
          'type': 'Lodge',
        },
      ];

      // Add sample hotels to Firestore
      for (final hotel in sampleHotels) {
        final docRef = await firestore
            .collection(AppConstants.hotelsCollection)
            .add(hotel);
        createdIds.add(docRef.id);
      }

      debugPrint(
          'Successfully created ${sampleHotels.length} sample hotels with IDs: $createdIds');
      return createdIds;
    } catch (e) {
      debugPrint('Error creating multiple sample hotels: $e');
      return [];
    }
  }
}
