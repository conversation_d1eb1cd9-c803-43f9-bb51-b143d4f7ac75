import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  IconButton,
  CircularProgress,
  useTheme,
  Button,
  Alert
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  ArrowUpward as ArrowUpIcon,
  ArrowDownward as ArrowDownIcon
} from '@mui/icons-material';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db } from '../../firebase/config';
import { useAuth } from '../../contexts/AuthContext';

interface DashboardStats {
  totalUsers: number;
  totalBookings: number;
  totalEarnings: number;
  totalBalance: number;
}

interface RecentActivity {
  id: string;
  type: string;
  title: string;
  description: string;
  timestamp: any;
}

const NewDashboard: React.FC = () => {
  const theme = useTheme();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalBookings: 0,
    totalEarnings: 0,
    totalBalance: 0
  });
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get vendor's hotel ID (assuming vendor manages one hotel)
      const hotelsQuery = query(
        collection(db, 'hotels'),
        where('vendorId', '==', currentUser?.uid || '')
      );
      const hotelsSnapshot = await getDocs(hotelsQuery);

      if (hotelsSnapshot.empty) {
        setStats({
          totalUsers: 0,
          totalBookings: 0,
          totalEarnings: 0,
          totalBalance: 0
        });
        setRecentActivities([]);
        return;
      }

      const hotelId = hotelsSnapshot.docs[0].id;

      // Fetch bookings for this hotel
      const bookingsQuery = query(
        collection(db, 'bookings'),
        where('hotelId', '==', hotelId)
      );
      const bookingsSnapshot = await getDocs(bookingsQuery);

      // Calculate stats
      let totalEarnings = 0;
      const uniqueUsers = new Set();

      bookingsSnapshot.forEach((doc) => {
        const booking = doc.data();
        if (booking.totalAmount) {
          totalEarnings += booking.totalAmount;
        }
        if (booking.userId) {
          uniqueUsers.add(booking.userId);
        }
      });

      // Fetch recent activities (recent bookings)
      const recentBookingsQuery = query(
        collection(db, 'bookings'),
        where('hotelId', '==', hotelId),
        orderBy('createdAt', 'desc'),
        limit(3)
      );
      const recentBookingsSnapshot = await getDocs(recentBookingsQuery);

      const activities: RecentActivity[] = [];
      recentBookingsSnapshot.forEach((doc) => {
        const booking = doc.data();
        activities.push({
          id: doc.id,
          type: 'booking',
          title: 'New Booking',
          description: `${booking.guestName || 'Guest'} booked ${booking.roomType || 'a room'}`,
          timestamp: booking.createdAt
        });
      });

      setStats({
        totalUsers: uniqueUsers.size,
        totalBookings: bookingsSnapshot.size,
        totalEarnings: totalEarnings,
        totalBalance: totalEarnings * 0.8 // Assuming 80% of earnings as balance
      });

      setRecentActivities(activities);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentUser) {
      fetchDashboardData();
    }
  }, [currentUser]);

  // Handle refresh button click
  const handleRefresh = () => {
    fetchDashboardData();
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format time ago
  const formatTimeAgo = (timestamp: any) => {
    if (!timestamp) return 'Unknown time';

    const now = new Date();
    const time = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} hours ago`;
    return `${Math.floor(diffInMinutes / 1440)} days ago`;
  };

  return (
    <Box>
      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Header with distinctive red background */}
      <Paper
        elevation={3}
        sx={{
          p: 3,
          mb: 3,
          background: 'linear-gradient(45deg, #1A73E8 30%, #21CBF3 90%)',
          color: 'white'
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
            VENDOR DASHBOARD
          </Typography>
          <IconButton onClick={handleRefresh} disabled={loading} sx={{ color: 'white' }}>
            {loading ? <CircularProgress size={24} color="inherit" /> : <RefreshIcon />}
          </IconButton>
        </Box>
        <Typography variant="h6" sx={{ mt: 1 }}>
          Welcome to your hotel management dashboard
        </Typography>
      </Paper>

      {/* Stat Cards with distinctive styling */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Users Card */}
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            elevation={3}
            sx={{
              p: 3,
              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
              color: 'white',
              borderRadius: '16px'
            }}
          >
            <Typography variant="h6" gutterBottom>USERS</Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h3">{loading ? '...' : stats.totalUsers}</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ArrowUpIcon sx={{ color: 'white' }} />
                <Typography variant="body1">Total</Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Bookings Card */}
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            elevation={3}
            sx={{
              p: 3,
              background: 'linear-gradient(45deg, #FF9800 30%, #FFEB3B 90%)',
              color: 'white',
              borderRadius: '16px'
            }}
          >
            <Typography variant="h6" gutterBottom>BOOKINGS</Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h3">{loading ? '...' : stats.totalBookings}</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ArrowUpIcon sx={{ color: 'white' }} />
                <Typography variant="body1">Total</Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Earnings Card */}
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            elevation={3}
            sx={{
              p: 3,
              background: 'linear-gradient(45deg, #4CAF50 30%, #8BC34A 90%)',
              color: 'white',
              borderRadius: '16px'
            }}
          >
            <Typography variant="h6" gutterBottom>EARNINGS</Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h3">
                {loading ? '...' : formatCurrency(stats.totalEarnings)}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ArrowUpIcon sx={{ color: 'white' }} />
                <Typography variant="body1">Total</Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Balance Card */}
        <Grid item xs={12} sm={6} md={3}>
          <Paper
            elevation={3}
            sx={{
              p: 3,
              background: 'linear-gradient(45deg, #9C27B0 30%, #E040FB 90%)',
              color: 'white',
              borderRadius: '16px'
            }}
          >
            <Typography variant="h6" gutterBottom>BALANCE</Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h3">
                {loading ? '...' : formatCurrency(stats.totalBalance)}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ArrowUpIcon sx={{ color: 'white' }} />
                <Typography variant="body1">Available</Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Recent Activity Section */}
      <Paper elevation={3} sx={{ p: 3, mb: 3, borderRadius: '16px' }}>
        <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
          Recent Activity
        </Typography>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : recentActivities.length > 0 ? (
          recentActivities.map((activity, index) => (
            <Box
              key={activity.id}
              sx={{
                p: 2,
                bgcolor: '#f5f5f5',
                borderRadius: '8px',
                mb: index < recentActivities.length - 1 ? 2 : 0
              }}
            >
              <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                {activity.title}
              </Typography>
              <Typography variant="body2">
                {activity.description}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {formatTimeAgo(activity.timestamp)}
              </Typography>
            </Box>
          ))
        ) : (
          <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: '8px', textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              No recent activity found. Start by adding hotels and managing bookings.
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Quick Actions Section */}
      <Paper elevation={3} sx={{ p: 3, borderRadius: '16px' }}>
        <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
          Quick Actions
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="contained"
              sx={{
                p: 2,
                bgcolor: '#2196F3',
                '&:hover': {
                  bgcolor: '#1976D2'
                }
              }}
            >
              New Booking
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="contained"
              sx={{
                p: 2,
                bgcolor: '#FF9800',
                '&:hover': {
                  bgcolor: '#F57C00'
                }
              }}
            >
              Check-in Guest
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="contained"
              sx={{
                p: 2,
                bgcolor: '#4CAF50',
                '&:hover': {
                  bgcolor: '#388E3C'
                }
              }}
            >
              Check-out Guest
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="contained"
              sx={{
                p: 2,
                bgcolor: '#9C27B0',
                '&:hover': {
                  bgcolor: '#7B1FA2'
                }
              }}
            >
              View Reports
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default NewDashboard;
