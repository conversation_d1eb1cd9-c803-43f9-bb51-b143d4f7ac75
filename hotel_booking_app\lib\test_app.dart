import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('Firebase initialized successfully');
  } catch (e) {
    print('Error initializing Firebase: $e');
  }
  
  runApp(const TestApp());
}

class TestApp extends StatelessWidget {
  const TestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Firebase Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const FirebaseTestScreen(),
    );
  }
}

class FirebaseTestScreen extends StatefulWidget {
  const FirebaseTestScreen({super.key});

  @override
  State<FirebaseTestScreen> createState() => _FirebaseTestScreenState();
}

class _FirebaseTestScreenState extends State<FirebaseTestScreen> {
  bool _isLoading = false;
  String _result = '';
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  @override
  void initState() {
    super.initState();
    _testFirebaseConnection();
  }
  
  Future<void> _testFirebaseConnection() async {
    setState(() {
      _isLoading = true;
      _result = 'Testing Firebase connection...';
    });
    
    try {
      // Check if Firebase is initialized
      final apps = FirebaseFirestore.instance.app.options;
      print('Firebase app name: ${FirebaseFirestore.instance.app.name}');
      print('Firebase project ID: ${apps.projectId}');
      print('Firebase API key: ${apps.apiKey}');
      
      setState(() {
        _result += '\nFirebase app name: ${FirebaseFirestore.instance.app.name}';
        _result += '\nFirebase project ID: ${apps.projectId}';
        _result += '\nFirebase API key: ${apps.apiKey}';
      });
      
      // Test Firestore connection by reading hotels collection
      final hotelsSnapshot = await _firestore.collection(AppConstants.hotelsCollection).get();
      
      setState(() {
        _result += '\nSuccessfully connected to Firestore';
        _result += '\nHotels collection exists: ${hotelsSnapshot.docs.isNotEmpty}';
        _result += '\nNumber of hotels: ${hotelsSnapshot.docs.length}';
      });
      
      if (hotelsSnapshot.docs.isNotEmpty) {
        final firstHotel = hotelsSnapshot.docs.first;
        setState(() {
          _result += '\nFirst hotel ID: ${firstHotel.id}';
          _result += '\nFirst hotel name: ${firstHotel.data()['name']}';
        });
      }
    } catch (e) {
      setState(() {
        _result += '\nError testing Firebase connection: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  Future<void> _createHotels() async {
    setState(() {
      _isLoading = true;
      _result = 'Creating hotels...';
    });
    
    try {
      // Sample hotel data
      final hotels = [
        {
          'name': 'Taj Palace',
          'description': 'A luxurious 5-star hotel in the heart of New Delhi with stunning views and world-class amenities.',
          'address': '2 Sardar Patel Marg, Diplomatic Enclave',
          'city': 'New Delhi',
          'country': 'India',
          'zipCode': '110021',
          'phone': '+91-11-26110202',
          'email': '<EMAIL>',
          'website': 'www.tajhotels.com',
          'rating': 4.8,
          'price': 15000.0, // INR
          'amenities': [
            'Swimming Pool',
            'Spa',
            'Gym',
            'Restaurant',
            'Bar',
            'Room Service',
            'Free WiFi',
            'Parking'
          ],
          'images': [
            'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
            'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
          ],
          'vendorId': 'vendor-taj-palace',
          'createdAt': FieldValue.serverTimestamp(),
          'reviewCount': 120,
          'type': 'Luxury',
        },
        {
          'name': 'The Leela Palace',
          'description': 'Experience the grandeur of royal India at this magnificent palace hotel with breathtaking architecture.',
          'address': 'Diplomatic Enclave, Chanakyapuri',
          'city': 'New Delhi',
          'country': 'India',
          'zipCode': '110023',
          'phone': '+91-11-39331234',
          'email': '<EMAIL>',
          'website': 'www.theleela.com',
          'rating': 4.9,
          'price': 18000.0, // INR
          'amenities': [
            'Swimming Pool',
            'Spa',
            'Gym',
            'Multiple Restaurants',
            'Bar',
            'Room Service',
            'Free WiFi',
            'Valet Parking'
          ],
          'images': [
            'https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
            'https://images.unsplash.com/photo-1564501049412-61c2a3083791?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
          ],
          'vendorId': 'vendor-leela-palace',
          'createdAt': FieldValue.serverTimestamp(),
          'reviewCount': 150,
          'type': 'Luxury',
        },
      ];
      
      // Add hotels to Firestore
      int successCount = 0;
      for (final hotel in hotels) {
        try {
          await _firestore.collection(AppConstants.hotelsCollection).add(hotel);
          successCount++;
        } catch (e) {
          setState(() {
            _result += '\nError adding hotel ${hotel['name']}: $e';
          });
        }
      }
      
      setState(() {
        _result += '\nSuccessfully added $successCount out of ${hotels.length} hotels to Firestore';
      });
      
      // Verify hotels were added
      final hotelsSnapshot = await _firestore.collection(AppConstants.hotelsCollection).get();
      setState(() {
        _result += '\nTotal hotels in collection after adding: ${hotelsSnapshot.docs.length}';
      });
    } catch (e) {
      setState(() {
        _result += '\nError creating hotels: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  Future<void> _clearHotels() async {
    setState(() {
      _isLoading = true;
      _result = 'Clearing hotels...';
    });
    
    try {
      final hotelsSnapshot = await _firestore.collection(AppConstants.hotelsCollection).get();
      
      int deletedCount = 0;
      for (final doc in hotelsSnapshot.docs) {
        try {
          await _firestore.collection(AppConstants.hotelsCollection).doc(doc.id).delete();
          deletedCount++;
        } catch (e) {
          setState(() {
            _result += '\nError deleting hotel ${doc.id}: $e';
          });
        }
      }
      
      setState(() {
        _result += '\nSuccessfully deleted $deletedCount hotels from Firestore';
      });
    } catch (e) {
      setState(() {
        _result += '\nError clearing hotels: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase Test'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Firebase Connection Test',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testFirebaseConnection,
                    child: const Text('Test Connection'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _createHotels,
                    child: const Text('Create Hotels'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _clearHotels,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade100,
                      foregroundColor: Colors.red.shade900,
                    ),
                    child: const Text('Clear Hotels'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            const Text(
              'Result:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : SingleChildScrollView(
                        child: Text(_result),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
