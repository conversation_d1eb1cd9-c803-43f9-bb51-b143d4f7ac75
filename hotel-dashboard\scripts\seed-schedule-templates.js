const admin = require('firebase-admin');
const serviceAccount = require('../serviceAccountKey.json');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

// Constants
const SCHEDULE_TEMPLATES_COLLECTION = 'scheduleTemplates';
const TEMPLATE_SHIFTS_COLLECTION = 'templateShifts';

// Template types
const TEMPLATE_TYPE_WEEKLY = 'weekly';
const TEMPLATE_TYPE_DAILY = 'daily';
const TEMPLATE_TYPE_CUSTOM = 'custom';

// Shift types
const SHIFT_TYPE_MORNING = 'morning';
const SHIFT_TYPE_AFTERNOON = 'afternoon';
const SHIFT_TYPE_NIGHT = 'night';
const SHIFT_TYPE_CUSTOM = 'custom';

// Sample hotel and vendor IDs (replace with actual IDs)
const HOTEL_ID = 'hotel123';
const VENDOR_ID = 'vendor123';

// Sample templates
const templates = [
  {
    name: 'Standard Weekly Schedule',
    description: 'Standard weekly schedule with morning, afternoon, and night shifts for all days.',
    type: TEMPLATE_TYPE_WEEKLY,
    hotelId: HOTEL_ID,
    vendorId: VENDOR_ID,
    isDefault: true
  },
  {
    name: 'Weekend Coverage',
    description: 'Schedule template for weekend coverage with increased staffing.',
    type: TEMPLATE_TYPE_WEEKLY,
    hotelId: HOTEL_ID,
    vendorId: VENDOR_ID,
    isDefault: false
  },
  {
    name: 'Holiday Schedule',
    description: 'Reduced staffing schedule for holidays.',
    type: TEMPLATE_TYPE_DAILY,
    hotelId: HOTEL_ID,
    vendorId: VENDOR_ID,
    isDefault: false
  }
];

// Sample template shifts for Standard Weekly Schedule
const standardWeeklyShifts = [
  // Monday
  {
    dayOfWeek: 0,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Regular housekeeping shift'
  },
  {
    dayOfWeek: 0,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Regular housekeeping shift'
  },
  {
    dayOfWeek: 0,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Front desk morning shift'
  },
  {
    dayOfWeek: 0,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Front desk afternoon shift'
  },
  {
    dayOfWeek: 0,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_NIGHT,
    startTime: '22:00',
    endTime: '06:00',
    notes: 'Front desk night shift'
  },
  {
    dayOfWeek: 0,
    role: 'maintenance',
    shiftType: SHIFT_TYPE_CUSTOM,
    startTime: '09:00',
    endTime: '17:00',
    notes: 'Regular maintenance shift'
  },
  
  // Tuesday
  {
    dayOfWeek: 1,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Regular housekeeping shift'
  },
  {
    dayOfWeek: 1,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Regular housekeeping shift'
  },
  {
    dayOfWeek: 1,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Front desk morning shift'
  },
  {
    dayOfWeek: 1,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Front desk afternoon shift'
  },
  {
    dayOfWeek: 1,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_NIGHT,
    startTime: '22:00',
    endTime: '06:00',
    notes: 'Front desk night shift'
  },
  {
    dayOfWeek: 1,
    role: 'maintenance',
    shiftType: SHIFT_TYPE_CUSTOM,
    startTime: '09:00',
    endTime: '17:00',
    notes: 'Regular maintenance shift'
  },
  
  // Wednesday
  {
    dayOfWeek: 2,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Regular housekeeping shift'
  },
  {
    dayOfWeek: 2,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Regular housekeeping shift'
  },
  {
    dayOfWeek: 2,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Front desk morning shift'
  },
  {
    dayOfWeek: 2,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Front desk afternoon shift'
  },
  {
    dayOfWeek: 2,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_NIGHT,
    startTime: '22:00',
    endTime: '06:00',
    notes: 'Front desk night shift'
  },
  {
    dayOfWeek: 2,
    role: 'maintenance',
    shiftType: SHIFT_TYPE_CUSTOM,
    startTime: '09:00',
    endTime: '17:00',
    notes: 'Regular maintenance shift'
  },
  
  // Thursday
  {
    dayOfWeek: 3,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Regular housekeeping shift'
  },
  {
    dayOfWeek: 3,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Regular housekeeping shift'
  },
  {
    dayOfWeek: 3,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Front desk morning shift'
  },
  {
    dayOfWeek: 3,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Front desk afternoon shift'
  },
  {
    dayOfWeek: 3,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_NIGHT,
    startTime: '22:00',
    endTime: '06:00',
    notes: 'Front desk night shift'
  },
  {
    dayOfWeek: 3,
    role: 'maintenance',
    shiftType: SHIFT_TYPE_CUSTOM,
    startTime: '09:00',
    endTime: '17:00',
    notes: 'Regular maintenance shift'
  },
  
  // Friday
  {
    dayOfWeek: 4,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Regular housekeeping shift'
  },
  {
    dayOfWeek: 4,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Regular housekeeping shift'
  },
  {
    dayOfWeek: 4,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Front desk morning shift'
  },
  {
    dayOfWeek: 4,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Front desk afternoon shift'
  },
  {
    dayOfWeek: 4,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_NIGHT,
    startTime: '22:00',
    endTime: '06:00',
    notes: 'Front desk night shift'
  },
  {
    dayOfWeek: 4,
    role: 'maintenance',
    shiftType: SHIFT_TYPE_CUSTOM,
    startTime: '09:00',
    endTime: '17:00',
    notes: 'Regular maintenance shift'
  },
  
  // Saturday
  {
    dayOfWeek: 5,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Weekend housekeeping shift'
  },
  {
    dayOfWeek: 5,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Weekend front desk morning shift'
  },
  {
    dayOfWeek: 5,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Weekend front desk afternoon shift'
  },
  {
    dayOfWeek: 5,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_NIGHT,
    startTime: '22:00',
    endTime: '06:00',
    notes: 'Weekend front desk night shift'
  },
  
  // Sunday
  {
    dayOfWeek: 6,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Weekend housekeeping shift'
  },
  {
    dayOfWeek: 6,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Weekend front desk morning shift'
  },
  {
    dayOfWeek: 6,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Weekend front desk afternoon shift'
  },
  {
    dayOfWeek: 6,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_NIGHT,
    startTime: '22:00',
    endTime: '06:00',
    notes: 'Weekend front desk night shift'
  }
];

// Sample template shifts for Weekend Coverage
const weekendCoverageShifts = [
  // Friday
  {
    dayOfWeek: 4,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Weekend prep housekeeping shift'
  },
  {
    dayOfWeek: 4,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Weekend prep housekeeping shift'
  },
  {
    dayOfWeek: 4,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Weekend prep front desk shift'
  },
  {
    dayOfWeek: 4,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Weekend prep front desk shift'
  },
  {
    dayOfWeek: 4,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_NIGHT,
    startTime: '22:00',
    endTime: '06:00',
    notes: 'Weekend prep front desk shift'
  },
  
  // Saturday
  {
    dayOfWeek: 5,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Weekend housekeeping shift'
  },
  {
    dayOfWeek: 5,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Weekend housekeeping shift'
  },
  {
    dayOfWeek: 5,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Weekend front desk shift'
  },
  {
    dayOfWeek: 5,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Weekend front desk shift'
  },
  {
    dayOfWeek: 5,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_NIGHT,
    startTime: '22:00',
    endTime: '06:00',
    notes: 'Weekend front desk shift'
  },
  {
    dayOfWeek: 5,
    role: 'maintenance',
    shiftType: SHIFT_TYPE_CUSTOM,
    startTime: '10:00',
    endTime: '18:00',
    notes: 'Weekend maintenance shift'
  },
  
  // Sunday
  {
    dayOfWeek: 6,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Weekend housekeeping shift'
  },
  {
    dayOfWeek: 6,
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Weekend housekeeping shift'
  },
  {
    dayOfWeek: 6,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Weekend front desk shift'
  },
  {
    dayOfWeek: 6,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Weekend front desk shift'
  },
  {
    dayOfWeek: 6,
    role: 'front_desk',
    shiftType: SHIFT_TYPE_NIGHT,
    startTime: '22:00',
    endTime: '06:00',
    notes: 'Weekend front desk shift'
  },
  {
    dayOfWeek: 6,
    role: 'maintenance',
    shiftType: SHIFT_TYPE_CUSTOM,
    startTime: '10:00',
    endTime: '18:00',
    notes: 'Weekend maintenance shift'
  }
];

// Sample template shifts for Holiday Schedule
const holidayScheduleShifts = [
  // Single day schedule
  {
    dayOfWeek: 0, // Not used for daily templates
    role: 'housekeeping',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '08:00',
    endTime: '16:00',
    notes: 'Holiday housekeeping shift'
  },
  {
    dayOfWeek: 0, // Not used for daily templates
    role: 'front_desk',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '06:00',
    endTime: '14:00',
    notes: 'Holiday front desk morning shift'
  },
  {
    dayOfWeek: 0, // Not used for daily templates
    role: 'front_desk',
    shiftType: SHIFT_TYPE_AFTERNOON,
    startTime: '14:00',
    endTime: '22:00',
    notes: 'Holiday front desk afternoon shift'
  },
  {
    dayOfWeek: 0, // Not used for daily templates
    role: 'front_desk',
    shiftType: SHIFT_TYPE_NIGHT,
    startTime: '22:00',
    endTime: '06:00',
    notes: 'Holiday front desk night shift'
  }
];

// Map templates to their shifts
const templateShiftsMap = {
  'Standard Weekly Schedule': standardWeeklyShifts,
  'Weekend Coverage': weekendCoverageShifts,
  'Holiday Schedule': holidayScheduleShifts
};

// Seed schedule templates
async function seedScheduleTemplates() {
  try {
    console.log('Seeding schedule templates...');
    
    // Clear existing templates and shifts
    const existingTemplates = await db.collection(SCHEDULE_TEMPLATES_COLLECTION)
      .where('hotelId', '==', HOTEL_ID)
      .get();
    
    const existingShifts = await db.collection(TEMPLATE_SHIFTS_COLLECTION)
      .get();
    
    const batch = db.batch();
    
    // Delete existing templates
    existingTemplates.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    // Delete existing shifts
    existingShifts.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
    
    console.log(`Deleted ${existingTemplates.size} existing templates and ${existingShifts.size} existing shifts.`);
    
    // Create new templates and shifts
    const templateIds = {};
    
    for (const template of templates) {
      const templateRef = await db.collection(SCHEDULE_TEMPLATES_COLLECTION).add({
        ...template,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });
      
      templateIds[template.name] = templateRef.id;
      
      console.log(`Created template: ${template.name} (${templateRef.id})`);
    }
    
    // Create shifts for each template
    for (const [templateName, templateId] of Object.entries(templateIds)) {
      const shifts = templateShiftsMap[templateName];
      
      if (shifts) {
        for (const shift of shifts) {
          await db.collection(TEMPLATE_SHIFTS_COLLECTION).add({
            ...shift,
            templateId,
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
          });
        }
        
        console.log(`Added ${shifts.length} shifts to template: ${templateName}`);
      }
    }
    
    console.log('Schedule templates seeded successfully!');
  } catch (error) {
    console.error('Error seeding schedule templates:', error);
  }
}

// Run the seed function
seedScheduleTemplates()
  .then(() => {
    console.log('Script completed successfully!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
