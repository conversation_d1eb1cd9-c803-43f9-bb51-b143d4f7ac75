import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Grid,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Divider,
  Alert,
  Snackbar,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Save as SaveIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Language as LanguageIcon,
  Payments as PaymentsIcon,
  Email as EmailIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';

// TabPanel component for tab content
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const Settings: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  // General settings state
  const [generalSettings, setGeneralSettings] = useState({
    siteName: 'Link In Blink Hotel',
    siteDescription: 'Comprehensive multi-vendor hotel booking solution',
    contactEmail: '<EMAIL>',
    contactPhone: '+91 1234567890',
    defaultCurrency: 'INR',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h'
  });

  // Notification settings state
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    bookingConfirmation: true,
    bookingCancellation: true,
    paymentConfirmation: true,
    systemUpdates: false
  });

  // Security settings state
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    passwordExpiry: 90,
    sessionTimeout: 30,
    loginAttempts: 5
  });

  // Payment settings state
  const [paymentSettings, setPaymentSettings] = useState({
    currency: 'INR',
    currencySymbol: '₹',
    paymentGateways: [
      { id: 1, name: 'Razorpay', enabled: true },
      { id: 2, name: 'PayTM', enabled: true },
      { id: 3, name: 'Stripe', enabled: false }
    ],
    taxRate: 18
  });

  // Email templates state
  const [emailTemplates, setEmailTemplates] = useState([
    { id: 1, name: 'Booking Confirmation', subject: 'Your booking is confirmed' },
    { id: 2, name: 'Booking Cancellation', subject: 'Your booking has been cancelled' },
    { id: 3, name: 'Payment Receipt', subject: 'Payment Receipt for your booking' }
  ]);

  // Handle tab change
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle general settings change
  const handleGeneralSettingsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setGeneralSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle notification settings toggle
  const handleNotificationToggle = (name: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setNotificationSettings(prev => ({
      ...prev,
      [name]: e.target.checked
    }));
  };

  // Handle security settings change
  const handleSecuritySettingsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSecuritySettings(prev => ({
      ...prev,
      [name]: name === 'twoFactorAuth' ? e.target.checked : Number(value)
    }));
  };

  // Handle payment gateway toggle
  const handlePaymentGatewayToggle = (id: number) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setPaymentSettings(prev => ({
      ...prev,
      paymentGateways: prev.paymentGateways.map(gateway =>
        gateway.id === id ? { ...gateway, enabled: e.target.checked } : gateway
      )
    }));
  };

  // Handle save settings
  const handleSaveSettings = () => {
    // In a real app, this would save to Firebase or another backend
    setSnackbar({
      open: true,
      message: 'Settings saved successfully',
      severity: 'success'
    });
  };

  // Handle close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Settings
        </Typography>
        <Button
          variant="contained"
          startIcon={<SaveIcon />}
          onClick={handleSaveSettings}
        >
          Save Changes
        </Button>
      </Box>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab icon={<SettingsIcon />} label="General" />
          <Tab icon={<NotificationsIcon />} label="Notifications" />
          <Tab icon={<SecurityIcon />} label="Security" />
          <Tab icon={<PaymentsIcon />} label="Payment" />
          <Tab icon={<EmailIcon />} label="Email Templates" />
          <Tab icon={<LanguageIcon />} label="Localization" />
        </Tabs>

        {/* General Settings */}
        <TabPanel value={tabValue} index={0}>
          <Typography variant="h6" gutterBottom>
            General Settings
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure basic settings for your hotel booking system.
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Site Name"
                name="siteName"
                value={generalSettings.siteName}
                onChange={handleGeneralSettingsChange}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Contact Email"
                name="contactEmail"
                value={generalSettings.contactEmail}
                onChange={handleGeneralSettingsChange}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Contact Phone"
                name="contactPhone"
                value={generalSettings.contactPhone}
                onChange={handleGeneralSettingsChange}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Default Currency</InputLabel>
                <Select
                  value={generalSettings.defaultCurrency}
                  label="Default Currency"
                  onChange={(e) => setGeneralSettings(prev => ({ ...prev, defaultCurrency: e.target.value }))}
                >
                  <MenuItem value="INR">Indian Rupee (₹)</MenuItem>
                  <MenuItem value="USD">US Dollar ($)</MenuItem>
                  <MenuItem value="EUR">Euro (€)</MenuItem>
                  <MenuItem value="GBP">British Pound (£)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Site Description"
                name="siteDescription"
                value={generalSettings.siteDescription}
                onChange={handleGeneralSettingsChange}
                margin="normal"
                multiline
                rows={3}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Notification Settings */}
        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6" gutterBottom>
            Notification Settings
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure how and when notifications are sent.
          </Typography>

          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Notification Channels
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationSettings.emailNotifications}
                        onChange={handleNotificationToggle('emailNotifications')}
                        color="primary"
                      />
                    }
                    label="Email Notifications"
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationSettings.smsNotifications}
                        onChange={handleNotificationToggle('smsNotifications')}
                        color="primary"
                      />
                    }
                    label="SMS Notifications"
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationSettings.pushNotifications}
                        onChange={handleNotificationToggle('pushNotifications')}
                        color="primary"
                      />
                    }
                    label="Push Notifications"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Notification Events
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationSettings.bookingConfirmation}
                        onChange={handleNotificationToggle('bookingConfirmation')}
                        color="primary"
                      />
                    }
                    label="Booking Confirmation"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationSettings.bookingCancellation}
                        onChange={handleNotificationToggle('bookingCancellation')}
                        color="primary"
                      />
                    }
                    label="Booking Cancellation"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationSettings.paymentConfirmation}
                        onChange={handleNotificationToggle('paymentConfirmation')}
                        color="primary"
                      />
                    }
                    label="Payment Confirmation"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationSettings.systemUpdates}
                        onChange={handleNotificationToggle('systemUpdates')}
                        color="primary"
                      />
                    }
                    label="System Updates"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </TabPanel>

        {/* Security Settings */}
        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            Security Settings
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure security settings for your hotel booking system.
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={securitySettings.twoFactorAuth}
                    onChange={handleSecuritySettingsChange}
                    name="twoFactorAuth"
                    color="primary"
                  />
                }
                label="Two-Factor Authentication"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Password Expiry (days)"
                name="passwordExpiry"
                type="number"
                value={securitySettings.passwordExpiry}
                onChange={handleSecuritySettingsChange}
                margin="normal"
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Session Timeout (minutes)"
                name="sessionTimeout"
                type="number"
                value={securitySettings.sessionTimeout}
                onChange={handleSecuritySettingsChange}
                margin="normal"
                InputProps={{ inputProps: { min: 1 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Max Login Attempts"
                name="loginAttempts"
                type="number"
                value={securitySettings.loginAttempts}
                onChange={handleSecuritySettingsChange}
                margin="normal"
                InputProps={{ inputProps: { min: 1 } }}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Payment Settings */}
        <TabPanel value={tabValue} index={3}>
          <Typography variant="h6" gutterBottom>
            Payment Settings
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure payment methods and settings.
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Currency</InputLabel>
                <Select
                  value={paymentSettings.currency}
                  label="Currency"
                  onChange={(e) => setPaymentSettings(prev => ({ ...prev, currency: e.target.value }))}
                >
                  <MenuItem value="INR">Indian Rupee (₹)</MenuItem>
                  <MenuItem value="USD">US Dollar ($)</MenuItem>
                  <MenuItem value="EUR">Euro (€)</MenuItem>
                  <MenuItem value="GBP">British Pound (£)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Tax Rate (%)"
                type="number"
                value={paymentSettings.taxRate}
                onChange={(e) => setPaymentSettings(prev => ({ ...prev, taxRate: Number(e.target.value) }))}
                margin="normal"
                InputProps={{ inputProps: { min: 0, max: 100 } }}
              />
            </Grid>
          </Grid>

          <Typography variant="subtitle1" sx={{ mt: 3, mb: 2 }}>
            Payment Gateways
          </Typography>
          <List>
            {paymentSettings.paymentGateways.map((gateway) => (
              <ListItem key={gateway.id} divider>
                <ListItemText primary={gateway.name} />
                <ListItemSecondaryAction>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={gateway.enabled}
                        onChange={handlePaymentGatewayToggle(gateway.id)}
                        color="primary"
                      />
                    }
                    label={gateway.enabled ? 'Enabled' : 'Disabled'}
                  />
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </TabPanel>

        {/* Email Templates */}
        <TabPanel value={tabValue} index={4}>
          <Typography variant="h6" gutterBottom>
            Email Templates
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Manage email templates for various notifications.
          </Typography>

          <List>
            {emailTemplates.map((template) => (
              <ListItem key={template.id} divider>
                <ListItemText
                  primary={template.name}
                  secondary={`Subject: ${template.subject}`}
                />
                <ListItemSecondaryAction>
                  <IconButton edge="end" aria-label="edit">
                    <SettingsIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>

          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            sx={{ mt: 2 }}
          >
            Add New Template
          </Button>
        </TabPanel>

        {/* Localization Settings */}
        <TabPanel value={tabValue} index={5}>
          <Typography variant="h6" gutterBottom>
            Localization Settings
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure language and regional settings.
          </Typography>

          <Alert severity="info" sx={{ mb: 3 }}>
            Localization settings are currently under development. More options will be available soon.
          </Alert>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Default Language</InputLabel>
                <Select
                  value="en"
                  label="Default Language"
                >
                  <MenuItem value="en">English</MenuItem>
                  <MenuItem value="hi">Hindi</MenuItem>
                  <MenuItem value="ta">Tamil</MenuItem>
                  <MenuItem value="te">Telugu</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Date Format</InputLabel>
                <Select
                  value={generalSettings.dateFormat}
                  label="Date Format"
                  onChange={(e) => setGeneralSettings(prev => ({ ...prev, dateFormat: e.target.value }))}
                >
                  <MenuItem value="DD/MM/YYYY">DD/MM/YYYY</MenuItem>
                  <MenuItem value="MM/DD/YYYY">MM/DD/YYYY</MenuItem>
                  <MenuItem value="YYYY-MM-DD">YYYY-MM-DD</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Time Format</InputLabel>
                <Select
                  value={generalSettings.timeFormat}
                  label="Time Format"
                  onChange={(e) => setGeneralSettings(prev => ({ ...prev, timeFormat: e.target.value }))}
                >
                  <MenuItem value="12h">12-hour (AM/PM)</MenuItem>
                  <MenuItem value="24h">24-hour</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Settings;
