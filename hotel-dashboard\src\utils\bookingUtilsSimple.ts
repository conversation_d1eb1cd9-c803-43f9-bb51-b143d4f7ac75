/**
 * Format a date as DD MMM YYYY (e.g., 01 Jan 2023)
 */
export const formatDateDDMMMYYYY = (date: Date): string => {
  try {
    const options: Intl.DateTimeFormatOptions = { 
      day: '2-digit', 
      month: 'short', 
      year: 'numeric' 
    };
    return date.toLocaleDateString('en-US', options);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
};

/**
 * Format currency as $X,XXX.XX
 */
export const formatCurrency = (amount: number): string => {
  try {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  } catch (error) {
    console.error('Error formatting currency:', error);
    return '$0.00';
  }
};

/**
 * Get booking status display text and color
 */
export const getBookingStatusInfo = (status: string | undefined): { text: string; color: string } => {
  if (!status) {
    return { text: 'Unknown', color: 'default' };
  }
  
  try {
    switch (status) {
      case 'confirmed':
        return { text: 'Confirmed', color: 'success' };
      case 'pending':
        return { text: 'Pending', color: 'warning' };
      case 'checked_in':
        return { text: 'Checked In', color: 'primary' };
      case 'checked_out':
        return { text: 'Checked Out', color: 'default' };
      case 'cancelled':
        return { text: 'Cancelled', color: 'error' };
      case 'no_show':
        return { text: 'No Show', color: 'error' };
      default:
        return { text: status, color: 'default' };
    }
  } catch (error) {
    console.error('Error getting booking status info:', error);
    return { text: 'Unknown', color: 'default' };
  }
};

/**
 * Get payment status display text and color
 */
export const getPaymentStatusInfo = (status: string | undefined): { text: string; color: string } => {
  if (!status) {
    return { text: 'Unknown', color: 'default' };
  }
  
  try {
    switch (status) {
      case 'paid':
        return { text: 'Paid', color: 'success' };
      case 'pending':
        return { text: 'Pending', color: 'warning' };
      case 'partially_paid':
        return { text: 'Partially Paid', color: 'info' };
      case 'refunded':
        return { text: 'Refunded', color: 'error' };
      case 'cancelled':
        return { text: 'Cancelled', color: 'error' };
      default:
        return { text: status, color: 'default' };
    }
  } catch (error) {
    console.error('Error getting payment status info:', error);
    return { text: 'Unknown', color: 'default' };
  }
};
