import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  CardActions,
  Button,
  Divider,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  CleaningServices as CleaningIcon,
  Restaurant as FoodIcon,
  Build as MaintenanceIcon,
  Notifications as NotificationsIcon,
  ArrowForward as ArrowForwardIcon,
  AccessTime as TimeIcon,
  Room as RoomIcon,
  Person as PersonIcon,
  Flag as PriorityIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { auth } from '../../../firebase/config';
import { getHotelsForVendor } from '../../../firebase/hotelService';
import { getServiceRequestsForHotel, ServiceRequest } from '../../../services/serviceRequestService';

const ServicesDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [hotelId, setHotelId] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [serviceRequests, setServiceRequests] = useState<{
    cleaning: ServiceRequest[];
    food: ServiceRequest[];
    maintenance: ServiceRequest[];
  }>({
    cleaning: [],
    food: [],
    maintenance: []
  });
  const [stats, setStats] = useState<{
    cleaning: { pending: number; inProgress: number; completed: number; cancelled: number; total: number };
    food: { pending: number; inProgress: number; completed: number; cancelled: number; total: number };
    maintenance: { pending: number; inProgress: number; completed: number; cancelled: number; total: number };
  }>({
    cleaning: { pending: 0, inProgress: 0, completed: 0, cancelled: 0, total: 0 },
    food: { pending: 0, inProgress: 0, completed: 0, cancelled: 0, total: 0 },
    maintenance: { pending: 0, inProgress: 0, completed: 0, cancelled: 0, total: 0 }
  });

  useEffect(() => {
    const fetchHotelId = async () => {
      try {
        if (!auth.currentUser) {
          setError('You must be logged in to view service requests');
          setLoading(false);
          return;
        }

        const hotels = await getHotelsForVendor(auth.currentUser.uid);

        if (hotels.length === 0) {
          setError('No hotels found for this vendor');
          setLoading(false);
          return;
        }

        // Use the first hotel ID
        const id = hotels[0].id || '';
        setHotelId(id);

        // Fetch service requests
        await fetchServiceRequests(id);
      } catch (err: any) {
        console.error('Error fetching hotel ID:', err);
        setError(err.message || 'Failed to load hotel information');
        setLoading(false);
      }
    };

    fetchHotelId();
  }, []);

  const fetchServiceRequests = async (hotelId: string) => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all types of service requests
      const [cleaningRequests, foodRequests, maintenanceRequests] = await Promise.all([
        getServiceRequestsForHotel(hotelId, { type: 'cleaning' }),
        getServiceRequestsForHotel(hotelId, { type: 'food' }),
        getServiceRequestsForHotel(hotelId, { type: 'maintenance' })
      ]);

      setServiceRequests({
        cleaning: cleaningRequests,
        food: foodRequests,
        maintenance: maintenanceRequests
      });

      // Calculate stats
      setStats({
        cleaning: calculateStats(cleaningRequests),
        food: calculateStats(foodRequests),
        maintenance: calculateStats(maintenanceRequests)
      });

      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching service requests:', err);
      setError(err.message || 'Failed to load service requests');
      setLoading(false);
    }
  };

  const calculateStats = (requests: ServiceRequest[]) => {
    const stats = {
      pending: 0,
      inProgress: 0,
      completed: 0,
      cancelled: 0,
      total: requests.length
    };

    requests.forEach(request => {
      switch (request.status) {
        case 'pending':
          stats.pending++;
          break;
        case 'in_progress':
          stats.inProgress++;
          break;
        case 'completed':
          stats.completed++;
          break;
        case 'cancelled':
          stats.cancelled++;
          break;
      }
    });

    return stats;
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';

    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return format(date, 'MMM d, yyyy h:mm a');
  };

  const getPriorityChip = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Chip label="High" color="error" size="small" />;
      case 'medium':
        return <Chip label="Medium" color="warning" size="small" />;
      case 'low':
        return <Chip label="Low" color="info" size="small" />;
      default:
        return <Chip label={priority} size="small" />;
    }
  };

  const renderRecentRequests = (requests: ServiceRequest[], type: string) => {
    // Get only pending and in-progress requests
    const activeRequests = requests.filter(
      request => request.status === 'pending' || request.status === 'in_progress'
    );

    // Sort by request time (newest first) and take only the first 5
    const recentRequests = [...activeRequests]
      .sort((a, b) => {
        // Handle Firebase Timestamp objects safely
        const dateA = a.requestTime && typeof a.requestTime.toDate === 'function'
          ? a.requestTime.toDate()
          : new Date(a.requestTime as any);

        const dateB = b.requestTime && typeof b.requestTime.toDate === 'function'
          ? b.requestTime.toDate()
          : new Date(b.requestTime as any);

        return dateB.getTime() - dateA.getTime();
      })
      .slice(0, 5);

    if (recentRequests.length === 0) {
      return (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            No active {type} requests
          </Typography>
        </Box>
      );
    }

    return (
      <List dense>
        {recentRequests.map((request) => (
          <ListItem
            key={request.id}
            secondaryAction={
              <Tooltip title="View Details">
                <IconButton
                  edge="end"
                  size="small"
                  onClick={() => navigate(`/vendor/services/${type}?id=${request.id}`)}
                >
                  <ArrowForwardIcon />
                </IconButton>
              </Tooltip>
            }
          >
            <ListItemIcon>
              {type === 'cleaning' ? (
                <CleaningIcon color="primary" />
              ) : type === 'food' ? (
                <FoodIcon color="primary" />
              ) : (
                <MaintenanceIcon color="primary" />
              )}
            </ListItemIcon>
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <RoomIcon sx={{ fontSize: 16, mr: 0.5 }} />
                  <Typography variant="body2" component="span">
                    Room {request.roomNumber}
                  </Typography>
                  <Box sx={{ ml: 1 }}>
                    {getPriorityChip(request.priority)}
                  </Box>
                </Box>
              }
              secondary={
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                  <TimeIcon sx={{ fontSize: 14, mr: 0.5 }} />
                  <Typography variant="caption" component="span">
                    {formatDate(request.requestTime)}
                  </Typography>
                </Box>
              }
            />
          </ListItem>
        ))}
      </List>
    );
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Services Dashboard
      </Typography>

      <Grid container spacing={3}>
        {/* Service Stats Cards */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CleaningIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                <Typography variant="h6">
                  Cleaning Requests
                </Typography>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Pending
                  </Typography>
                  <Typography variant="h4">
                    {stats.cleaning.pending}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    In Progress
                  </Typography>
                  <Typography variant="h4">
                    {stats.cleaning.inProgress}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Completed
                  </Typography>
                  <Typography variant="h4">
                    {stats.cleaning.completed}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Total
                  </Typography>
                  <Typography variant="h4">
                    {stats.cleaning.total}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
            <Divider />
            <CardActions>
              <Button
                fullWidth
                onClick={() => navigate('/vendor/services/cleaning')}
                endIcon={<ArrowForwardIcon />}
              >
                View All Cleaning Requests
              </Button>
            </CardActions>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <FoodIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                <Typography variant="h6">
                  Food Orders
                </Typography>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Pending
                  </Typography>
                  <Typography variant="h4">
                    {stats.food.pending}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    In Progress
                  </Typography>
                  <Typography variant="h4">
                    {stats.food.inProgress}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Completed
                  </Typography>
                  <Typography variant="h4">
                    {stats.food.completed}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Total
                  </Typography>
                  <Typography variant="h4">
                    {stats.food.total}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
            <Divider />
            <CardActions>
              <Button
                fullWidth
                onClick={() => navigate('/vendor/services/food')}
                endIcon={<ArrowForwardIcon />}
              >
                View All Food Orders
              </Button>
            </CardActions>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <MaintenanceIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                <Typography variant="h6">
                  Maintenance Requests
                </Typography>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Pending
                  </Typography>
                  <Typography variant="h4">
                    {stats.maintenance.pending}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    In Progress
                  </Typography>
                  <Typography variant="h4">
                    {stats.maintenance.inProgress}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Completed
                  </Typography>
                  <Typography variant="h4">
                    {stats.maintenance.completed}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Total
                  </Typography>
                  <Typography variant="h4">
                    {stats.maintenance.total}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
            <Divider />
            <CardActions>
              <Button
                fullWidth
                onClick={() => navigate('/vendor/services/maintenance')}
                endIcon={<ArrowForwardIcon />}
              >
                View All Maintenance Requests
              </Button>
            </CardActions>
          </Card>
        </Grid>

        {/* Recent Requests */}
        <Grid item xs={12}>
          <Typography variant="h5" gutterBottom>
            Recent Active Requests
          </Typography>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ height: '100%' }}>
            <Box sx={{ p: 2, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
              <Typography variant="h6">
                <CleaningIcon sx={{ verticalAlign: 'middle', mr: 1 }} />
                Cleaning Requests
              </Typography>
            </Box>
            <Divider />
            {renderRecentRequests(serviceRequests.cleaning, 'cleaning')}
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ height: '100%' }}>
            <Box sx={{ p: 2, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
              <Typography variant="h6">
                <FoodIcon sx={{ verticalAlign: 'middle', mr: 1 }} />
                Food Orders
              </Typography>
            </Box>
            <Divider />
            {renderRecentRequests(serviceRequests.food, 'food')}
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ height: '100%' }}>
            <Box sx={{ p: 2, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
              <Typography variant="h6">
                <MaintenanceIcon sx={{ verticalAlign: 'middle', mr: 1 }} />
                Maintenance Requests
              </Typography>
            </Box>
            <Divider />
            {renderRecentRequests(serviceRequests.maintenance, 'maintenance')}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ServicesDashboard;
