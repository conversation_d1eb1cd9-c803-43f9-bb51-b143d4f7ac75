import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit as firestoreLimit,
  Timestamp,
  serverTimestamp,
  DocumentSnapshot,
  writeBatch,
  setDoc
} from 'firebase/firestore';
import { db } from '../firebase/config';

// Collection names
const STAFF_COLLECTION = 'staff';
const STAFF_ASSIGNMENTS_COLLECTION = 'staffAssignments';
const STAFF_AVAILABILITY_COLLECTION = 'staffAvailability';

// Staff roles
export const STAFF_ROLE_HOUSEKEEPING = 'housekeeping';
export const STAFF_ROLE_FOOD_SERVICE = 'food_service';
export const STAFF_ROLE_MAINTENANCE = 'maintenance';
export const STAFF_ROLE_FRONT_DESK = 'front_desk';
export const STAFF_ROLE_MANAGER = 'manager';

// Staff status
export const STAFF_STATUS_ACTIVE = 'active';
export const STAFF_STATUS_INACTIVE = 'inactive';
export const STAFF_STATUS_ON_LEAVE = 'on_leave';

// Assignment status
export const ASSIGNMENT_STATUS_ASSIGNED = 'assigned';
export const ASSIGNMENT_STATUS_IN_PROGRESS = 'in_progress';
export const ASSIGNMENT_STATUS_COMPLETED = 'completed';
export const ASSIGNMENT_STATUS_CANCELLED = 'cancelled';

// Staff member interface
export interface StaffMember {
  id?: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  status: string;
  hotelId: string;
  vendorId: string;
  skills?: string[];
  profileImage?: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// Staff assignment interface
export interface StaffAssignment {
  id?: string;
  staffId: string;
  staffName: string;
  serviceRequestId: string;
  serviceRequestType: string;
  roomNumber: string;
  status: string;
  priority: string;
  assignedAt: Timestamp;
  startedAt?: Timestamp;
  completedAt?: Timestamp;
  notes?: string;
  hotelId: string;
  vendorId: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// Staff availability interface
export interface StaffAvailability {
  id?: string;
  staffId: string;
  staffName: string;
  date: Timestamp;
  shifts: {
    start: Timestamp;
    end: Timestamp;
    isAvailable: boolean;
    reason?: string;
  }[];
  hotelId: string;
  vendorId: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

/**
 * Get staff members for a hotel
 */
export const getStaffForHotel = async (
  hotelId: string,
  options?: {
    role?: string;
    status?: string;
    limit?: number;
  }
): Promise<StaffMember[]> => {
  try {
    let staffQuery = query(
      collection(db, STAFF_COLLECTION),
      where('hotelId', '==', hotelId),
      orderBy('name', 'asc')
    );
    
    // Apply role filter if provided
    if (options?.role) {
      staffQuery = query(
        staffQuery,
        where('role', '==', options.role)
      );
    }
    
    // Apply status filter if provided
    if (options?.status) {
      staffQuery = query(
        staffQuery,
        where('status', '==', options.status)
      );
    }
    
    // Apply limit if provided
    if (options?.limit) {
      staffQuery = query(staffQuery, firestoreLimit(options.limit));
    }
    
    const querySnapshot = await getDocs(staffQuery);
    const staff: StaffMember[] = [];
    
    querySnapshot.forEach((doc) => {
      staff.push({
        id: doc.id,
        ...doc.data() as Omit<StaffMember, 'id'>
      });
    });
    
    return staff;
  } catch (error) {
    console.error('Error getting staff for hotel:', error);
    throw error;
  }
};

/**
 * Get staff members by role
 */
export const getStaffByRole = async (
  hotelId: string,
  role: string
): Promise<StaffMember[]> => {
  try {
    return await getStaffForHotel(hotelId, { role, status: STAFF_STATUS_ACTIVE });
  } catch (error) {
    console.error('Error getting staff by role:', error);
    throw error;
  }
};

/**
 * Get available staff for a service type
 */
export const getAvailableStaffForService = async (
  hotelId: string,
  serviceType: string
): Promise<StaffMember[]> => {
  try {
    // Map service type to staff role
    let role = '';
    switch (serviceType) {
      case 'cleaning':
        role = STAFF_ROLE_HOUSEKEEPING;
        break;
      case 'food':
        role = STAFF_ROLE_FOOD_SERVICE;
        break;
      case 'maintenance':
        role = STAFF_ROLE_MAINTENANCE;
        break;
      default:
        throw new Error(`Invalid service type: ${serviceType}`);
    }
    
    // Get active staff with the matching role
    return await getStaffByRole(hotelId, role);
  } catch (error) {
    console.error('Error getting available staff for service:', error);
    throw error;
  }
};

/**
 * Create a staff member
 */
export const createStaffMember = async (
  staffMember: Omit<StaffMember, 'id' | 'createdAt' | 'updatedAt'>
): Promise<StaffMember> => {
  try {
    const now = Timestamp.now();
    const newStaffMember: Omit<StaffMember, 'id'> = {
      ...staffMember,
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await addDoc(collection(db, STAFF_COLLECTION), newStaffMember);
    
    return {
      id: docRef.id,
      ...newStaffMember
    };
  } catch (error) {
    console.error('Error creating staff member:', error);
    throw error;
  }
};

/**
 * Update a staff member
 */
export const updateStaffMember = async (
  staffId: string,
  updates: Partial<Omit<StaffMember, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<boolean> => {
  try {
    const staffRef = doc(db, STAFF_COLLECTION, staffId);
    
    await updateDoc(staffRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
    
    return true;
  } catch (error) {
    console.error('Error updating staff member:', error);
    throw error;
  }
};

/**
 * Delete a staff member
 */
export const deleteStaffMember = async (staffId: string): Promise<boolean> => {
  try {
    await deleteDoc(doc(db, STAFF_COLLECTION, staffId));
    return true;
  } catch (error) {
    console.error('Error deleting staff member:', error);
    throw error;
  }
};

/**
 * Create a staff assignment
 */
export const createStaffAssignment = async (
  assignment: Omit<StaffAssignment, 'id' | 'createdAt' | 'updatedAt' | 'assignedAt' | 'status'>
): Promise<StaffAssignment> => {
  try {
    const now = Timestamp.now();
    const newAssignment: Omit<StaffAssignment, 'id'> = {
      ...assignment,
      assignedAt: now,
      status: ASSIGNMENT_STATUS_ASSIGNED,
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await addDoc(collection(db, STAFF_ASSIGNMENTS_COLLECTION), newAssignment);
    
    return {
      id: docRef.id,
      ...newAssignment
    };
  } catch (error) {
    console.error('Error creating staff assignment:', error);
    throw error;
  }
};

/**
 * Update a staff assignment
 */
export const updateStaffAssignment = async (
  assignmentId: string,
  updates: Partial<Omit<StaffAssignment, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<boolean> => {
  try {
    const assignmentRef = doc(db, STAFF_ASSIGNMENTS_COLLECTION, assignmentId);
    
    await updateDoc(assignmentRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
    
    return true;
  } catch (error) {
    console.error('Error updating staff assignment:', error);
    throw error;
  }
};

/**
 * Get staff assignments for a hotel
 */
export const getStaffAssignmentsForHotel = async (
  hotelId: string,
  options?: {
    staffId?: string;
    serviceRequestType?: string;
    status?: string;
    limit?: number;
  }
): Promise<StaffAssignment[]> => {
  try {
    let assignmentsQuery = query(
      collection(db, STAFF_ASSIGNMENTS_COLLECTION),
      where('hotelId', '==', hotelId),
      orderBy('assignedAt', 'desc')
    );
    
    // Apply staffId filter if provided
    if (options?.staffId) {
      assignmentsQuery = query(
        assignmentsQuery,
        where('staffId', '==', options.staffId)
      );
    }
    
    // Apply serviceRequestType filter if provided
    if (options?.serviceRequestType) {
      assignmentsQuery = query(
        assignmentsQuery,
        where('serviceRequestType', '==', options.serviceRequestType)
      );
    }
    
    // Apply status filter if provided
    if (options?.status) {
      assignmentsQuery = query(
        assignmentsQuery,
        where('status', '==', options.status)
      );
    }
    
    // Apply limit if provided
    if (options?.limit) {
      assignmentsQuery = query(assignmentsQuery, firestoreLimit(options.limit));
    }
    
    const querySnapshot = await getDocs(assignmentsQuery);
    const assignments: StaffAssignment[] = [];
    
    querySnapshot.forEach((doc) => {
      assignments.push({
        id: doc.id,
        ...doc.data() as Omit<StaffAssignment, 'id'>
      });
    });
    
    return assignments;
  } catch (error) {
    console.error('Error getting staff assignments for hotel:', error);
    throw error;
  }
};

/**
 * Get staff assignments for a staff member
 */
export const getStaffAssignmentsForStaff = async (
  staffId: string,
  options?: {
    status?: string;
    limit?: number;
  }
): Promise<StaffAssignment[]> => {
  try {
    let assignmentsQuery = query(
      collection(db, STAFF_ASSIGNMENTS_COLLECTION),
      where('staffId', '==', staffId),
      orderBy('assignedAt', 'desc')
    );
    
    // Apply status filter if provided
    if (options?.status) {
      assignmentsQuery = query(
        assignmentsQuery,
        where('status', '==', options.status)
      );
    }
    
    // Apply limit if provided
    if (options?.limit) {
      assignmentsQuery = query(assignmentsQuery, firestoreLimit(options.limit));
    }
    
    const querySnapshot = await getDocs(assignmentsQuery);
    const assignments: StaffAssignment[] = [];
    
    querySnapshot.forEach((doc) => {
      assignments.push({
        id: doc.id,
        ...doc.data() as Omit<StaffAssignment, 'id'>
      });
    });
    
    return assignments;
  } catch (error) {
    console.error('Error getting staff assignments for staff:', error);
    throw error;
  }
};

export default {
  getStaffForHotel,
  getStaffByRole,
  getAvailableStaffForService,
  createStaffMember,
  updateStaffMember,
  deleteStaffMember,
  createStaffAssignment,
  updateStaffAssignment,
  getStaffAssignmentsForHotel,
  getStaffAssignmentsForStaff
};
