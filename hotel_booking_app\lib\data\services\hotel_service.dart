import 'package:flutter/foundation.dart';
import 'package:hotel_booking_app/data/models/hotel_model.dart';
import 'package:hotel_booking_app/data/models/review_model.dart';
import 'package:hotel_booking_app/data/models/room_model.dart';
import 'package:hotel_booking_app/data/repositories/hotel_repository.dart';
import 'package:hotel_booking_app/data/repositories/user_repository.dart';

class HotelService extends ChangeNotifier {
  final HotelRepository _hotelRepository = HotelRepository();
  final UserRepository _userRepository = UserRepository();

  List<Hotel> _hotels = [];
  List<Hotel> _popularHotels = [];
  List<Hotel> _favoriteHotels = [];
  Hotel? _selectedHotel;
  List<Room> _rooms = [];
  List<Review> _reviews = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Hotel> get hotels => _hotels;
  List<Hotel> get popularHotels => _popularHotels;
  List<Hotel> get favoriteHotels => _favoriteHotels;
  Hotel? get selectedHotel => _selectedHotel;
  List<Room> get rooms => _rooms;
  List<Review> get reviews => _reviews;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Clear hotels lists
  void clearHotels() {
    _hotels = [];
    _popularHotels = [];
    _error = null;
    debugPrint('HotelService: Cleared hotels lists');
    notifyListeners();
  }

  // Fetch all hotels
  Future<void> fetchHotels() async {
    // Only notify if not already loading
    if (!_isLoading) {
      _isLoading = true;
      _error = null;
      notifyListeners();
    }

    debugPrint('HotelService: Starting fetchHotels()');

    try {
      final fetchedHotels = await _hotelRepository.fetchHotels();
      debugPrint(
          'HotelService: Received ${fetchedHotels.length} hotels from repository');

      // Log the first hotel if available
      if (fetchedHotels.isNotEmpty) {
        final firstHotel = fetchedHotels.first;
        debugPrint(
            'HotelService: First hotel - ID: ${firstHotel.id}, Name: ${firstHotel.name}');
      } else {
        debugPrint('HotelService: No hotels received from repository');
      }

      _hotels = fetchedHotels;
      _isLoading = false;
      _error = null;

      debugPrint('HotelService: Updating UI with ${_hotels.length} hotels');
      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
        debugPrint('HotelService: UI updated with hotels');
      });
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      debugPrint('HotelService: Error fetching hotels: $e');

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
        debugPrint('HotelService: UI updated with error');
      });
    }
  }

  // Fetch popular hotels
  Future<void> fetchPopularHotels() async {
    // Only notify if not already loading
    if (!_isLoading) {
      _isLoading = true;
      _error = null;
      notifyListeners();
    }

    try {
      _popularHotels = await _hotelRepository.fetchPopularHotels();
      _isLoading = false;
      _error = null;

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });
    } catch (e) {
      _isLoading = false;
      _error = e.toString();

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });
    }
  }

  // Fetch favorite hotels
  Future<void> fetchFavoriteHotels(String userId) async {
    // Only notify if not already loading
    if (!_isLoading) {
      _isLoading = true;
      _error = null;
      notifyListeners();
    }

    try {
      final favoriteIds = await _userRepository.fetchFavoriteHotelIds(userId);
      final List<Hotel> tempFavorites = [];

      for (final id in favoriteIds) {
        final hotel = await _hotelRepository.fetchHotelById(id);
        if (hotel != null) {
          tempFavorites.add(hotel);
        }
      }

      // Update state only after all data is fetched
      _favoriteHotels = tempFavorites;
      _isLoading = false;
      _error = null;

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });
    } catch (e) {
      _isLoading = false;
      _error = e.toString();

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });
    }
  }

  // Fetch hotel by ID
  Future<void> fetchHotelById(String hotelId) async {
    // Only notify if not already loading
    if (!_isLoading) {
      _isLoading = true;
      _error = null;
      notifyListeners();
    }

    try {
      _selectedHotel = await _hotelRepository.fetchHotelById(hotelId);
      _isLoading = false;
      _error = null;

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });
    } catch (e) {
      _isLoading = false;
      _error = e.toString();

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });
    }
  }

  // Fetch rooms for a hotel
  Future<void> fetchRoomsForHotel(String hotelId) async {
    // Only notify if not already loading
    if (!_isLoading) {
      _isLoading = true;
      _error = null;
      notifyListeners();
    }

    try {
      _rooms = await _hotelRepository.fetchRoomsByHotelId(hotelId);
      _isLoading = false;
      _error = null;

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });
    } catch (e) {
      _isLoading = false;
      _error = e.toString();

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });
    }
  }

  // Fetch reviews for a hotel
  Future<void> fetchReviewsForHotel(String hotelId) async {
    // Only notify if not already loading
    if (!_isLoading) {
      _isLoading = true;
      _error = null;
      notifyListeners();
    }

    try {
      _reviews = await _hotelRepository.fetchReviewsByHotelId(hotelId);
      _isLoading = false;
      _error = null;

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });
    } catch (e) {
      _isLoading = false;
      _error = e.toString();

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });
    }
  }

  // Add a review
  Future<void> addReview(Review review) async {
    // Only notify if not already loading
    if (!_isLoading) {
      _isLoading = true;
      _error = null;
      notifyListeners();
    }

    try {
      await _hotelRepository.addReview(review);
      _isLoading = false;
      _error = null;

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });
    } catch (e) {
      _isLoading = false;
      _error = e.toString();

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });
    }
  }

  // Search hotels
  Future<List<Hotel>> searchHotels(String query) async {
    // Only notify if not already loading
    if (!_isLoading) {
      _isLoading = true;
      _error = null;
      notifyListeners();
    }

    try {
      final results = await _hotelRepository.searchHotels(query);
      _isLoading = false;
      _error = null;

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });

      return results;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });

      return [];
    }
  }

  // Filter hotels
  List<Hotel> filterHotels({
    double? minPrice,
    double? maxPrice,
    double? minRating,
    List<String>? amenities,
    String? propertyType,
  }) {
    debugPrint(
        'Filtering hotels with: minPrice=$minPrice, maxPrice=$maxPrice, minRating=$minRating, amenities=$amenities, propertyType=$propertyType');

    // Start with all hotels
    List<Hotel> filteredHotels = List.from(_hotels);

    // Apply price filter
    if (minPrice != null) {
      filteredHotels =
          filteredHotels.where((hotel) => hotel.price >= minPrice).toList();
      debugPrint('After price min filter: ${filteredHotels.length} hotels');
    }

    if (maxPrice != null) {
      filteredHotels =
          filteredHotels.where((hotel) => hotel.price <= maxPrice).toList();
      debugPrint('After price max filter: ${filteredHotels.length} hotels');
    }

    // Apply rating filter
    if (minRating != null && minRating > 0) {
      filteredHotels =
          filteredHotels.where((hotel) => hotel.rating >= minRating).toList();
      debugPrint('After rating filter: ${filteredHotels.length} hotels');
    }

    // Apply amenities filter
    if (amenities != null && amenities.isNotEmpty) {
      filteredHotels = filteredHotels.where((hotel) {
        // Check if hotel has all selected amenities
        for (final amenity in amenities) {
          if (!hotel.amenities.contains(amenity)) {
            return false;
          }
        }
        return true;
      }).toList();
      debugPrint('After amenities filter: ${filteredHotels.length} hotels');
    }

    // Apply property type filter
    if (propertyType != null && propertyType.isNotEmpty) {
      filteredHotels = filteredHotels
          .where((hotel) =>
              hotel.type?.toLowerCase() == propertyType.toLowerCase())
          .toList();
      debugPrint('After property type filter: ${filteredHotels.length} hotels');
    }

    debugPrint('Final filtered hotels count: ${filteredHotels.length}');
    return filteredHotels;
  }

  // Toggle favorite
  Future<void> toggleFavorite(String userId, String hotelId) async {
    try {
      final isFavorite = await _userRepository.isHotelFavorite(userId, hotelId);

      if (isFavorite) {
        await _userRepository.removeFromFavorites(userId, hotelId);
      } else {
        await _userRepository.addToFavorites(userId, hotelId);
      }

      // Refresh favorite hotels
      await fetchFavoriteHotels(userId);
    } catch (e) {
      _error = e.toString();

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });
    }
  }

  // Check if a hotel is favorite
  Future<bool> isHotelFavorite(String userId, String hotelId) async {
    try {
      return await _userRepository.isHotelFavorite(userId, hotelId);
    } catch (e) {
      _error = e.toString();

      // Use Future.microtask to avoid calling notifyListeners during build
      Future.microtask(() {
        notifyListeners();
      });

      return false;
    }
  }
}
