import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/core/theme/app_theme.dart';
import 'package:hotel_booking_app/data/models/booking_model.dart';
import 'package:hotel_booking_app/data/services/booking_service.dart';
import 'package:hotel_booking_app/data/services/aadhaar_verification_service.dart';
import 'package:hotel_booking_app/data/models/aadhaar_verification_model.dart';
import 'package:hotel_booking_app/presentation/screens/booking/check_in_verification_screen.dart';
import 'package:hotel_booking_app/presentation/screens/profile/aadhaar_verification_screen.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_button.dart';

class BookingDetailsScreen extends StatefulWidget {
  final String bookingId;

  const BookingDetailsScreen({
    super.key,
    required this.bookingId,
  });

  @override
  State<BookingDetailsScreen> createState() => _BookingDetailsScreenState();
}

class _BookingDetailsScreenState extends State<BookingDetailsScreen> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchBookingDetails();
  }

  Future<void> _fetchBookingDetails() async {
    setState(() {
      _isLoading = true;
    });

    final bookingService = Provider.of<BookingService>(context, listen: false);
    await bookingService.fetchBookingById(widget.bookingId);

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final bookingService = Provider.of<BookingService>(context);
    final aadhaarService = Provider.of<AadhaarVerificationService>(context);
    final booking = bookingService.currentBooking;
    final verification = aadhaarService.currentVerification;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking Details'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : booking == null
              ? const Center(child: Text('Booking not found'))
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Booking status card
                      _buildStatusCard(booking),

                      const SizedBox(height: 24),

                      // Hotel and room details
                      _buildHotelCard(booking),

                      const SizedBox(height: 24),

                      // Booking details
                      _buildBookingDetailsCard(booking),

                      const SizedBox(height: 24),

                      // Payment details
                      _buildPaymentCard(booking),

                      const SizedBox(height: 24),

                      // Guest details
                      if (booking.guestDetails != null)
                        _buildGuestDetailsCard(booking),

                      const SizedBox(height: 24),

                      // Verification status
                      _buildVerificationStatusCard(verification),

                      const SizedBox(height: 24),

                      // Action buttons
                      _buildActionButtons(context, booking, verification),

                      const SizedBox(height: 24),
                    ],
                  ),
                ),
    );
  }

  Widget _buildStatusCard(Booking booking) {
    Color statusColor;
    IconData statusIcon;
    String statusText = booking.status;

    // Convert status to display format
    switch (booking.status) {
      case AppConstants.statusPending:
        statusColor = Colors.orange;
        statusIcon = Icons.pending;
        statusText = 'Pending';
        break;
      case AppConstants.statusConfirmed:
        statusColor = Colors.blue;
        statusIcon = Icons.check_circle;
        statusText = 'Confirmed';
        break;
      case AppConstants.statusCheckedIn:
        statusColor = Colors.green;
        statusIcon = Icons.login;
        statusText = 'Checked In';
        break;
      case AppConstants.statusCheckedOut:
        statusColor = Colors.purple;
        statusIcon = Icons.logout;
        statusText = 'Checked Out';
        break;
      case AppConstants.statusCancelled:
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        statusText = 'Cancelled';
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.info;
        statusText = booking.status;
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                statusIcon,
                color: statusColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Booking Status',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    statusText,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: statusColor,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                _getBookingId(booking.id),
                style: TextStyle(
                  color: statusColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHotelCard(Booking booking) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Hotel image (placeholder)
          Container(
            height: 150,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(12),
              ),
              image: const DecorationImage(
                image: NetworkImage(
                    'https://source.unsplash.com/random/800x600?hotel'),
                fit: BoxFit.cover,
              ),
            ),
          ),

          // Hotel details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  booking.hotelName,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(
                      Icons.bed,
                      size: 16,
                      color: AppTheme.primaryColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      booking.roomName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookingDetailsCard(Booking booking) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Booking Details',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailRow(
              'Check-in',
              DateFormat('EEE, MMM d, yyyy').format(booking.checkIn),
              icon: Icons.login,
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              'Check-out',
              DateFormat('EEE, MMM d, yyyy').format(booking.checkOut),
              icon: Icons.logout,
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              'Guests',
              '${booking.guests} ${booking.guests > 1 ? 'persons' : 'person'}',
              icon: Icons.people,
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              'Booking Date',
              booking.createdAt != null
                  ? DateFormat('MMM d, yyyy').format(booking.createdAt!)
                  : 'N/A',
              icon: Icons.calendar_today,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentCard(Booking booking) {
    Color paymentStatusColor;
    String paymentStatusText = booking.paymentStatus;

    // Convert payment status to display format
    switch (booking.paymentStatus) {
      case AppConstants.paymentPending:
        paymentStatusColor = Colors.orange;
        paymentStatusText = 'Pending';
        break;
      case AppConstants.paymentCompleted:
        paymentStatusColor = Colors.green;
        paymentStatusText = 'Completed';
        break;
      case AppConstants.paymentFailed:
        paymentStatusColor = Colors.red;
        paymentStatusText = 'Failed';
        break;
      case AppConstants.paymentRefunded:
        paymentStatusColor = Colors.blue;
        paymentStatusText = 'Refunded';
        break;
      default:
        paymentStatusColor = Colors.grey;
        paymentStatusText = booking.paymentStatus;
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Payment Details',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailRow(
              'Total Amount',
              '\$${booking.totalPrice.toStringAsFixed(0)}',
              icon: Icons.attach_money,
              valueColor: AppTheme.primaryColor,
              valueFontWeight: FontWeight.bold,
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              'Payment Status',
              paymentStatusText,
              icon: Icons.payment,
              valueColor: paymentStatusColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGuestDetailsCard(Booking booking) {
    final guestDetails = booking.guestDetails!;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Guest Details',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (guestDetails['name'] != null)
              _buildDetailRow(
                'Name',
                guestDetails['name'],
                icon: Icons.person,
              ),
            if (guestDetails['email'] != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow(
                'Email',
                guestDetails['email'],
                icon: Icons.email,
              ),
            ],
            if (guestDetails['phone'] != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow(
                'Phone',
                guestDetails['phone'],
                icon: Icons.phone,
              ),
            ],
            if (guestDetails['specialRequests'] != null &&
                guestDetails['specialRequests'].isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildDetailRow(
                'Special Requests',
                guestDetails['specialRequests'],
                icon: Icons.note,
                alignTop: true,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    Booking booking,
    AadhaarVerification? verification,
  ) {
    // Check if check-in is available
    final bool canCheckIn = booking.status == AppConstants.statusConfirmed &&
        booking.checkIn.isBefore(DateTime.now().add(const Duration(days: 1)));

    // Check if cancellation is available
    final bool canCancel = booking.status == AppConstants.statusPending ||
        booking.status == AppConstants.statusConfirmed;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (canCheckIn)
          CustomButton(
            text: verification?.status == AadhaarVerificationStatus.verified
                ? 'Check-in Now'
                : 'Check-in with Aadhaar Verification',
            prefixIcon: const Icon(Icons.login, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => CheckInVerificationScreen(
                    booking: booking,
                  ),
                ),
              );
            },
          ),
        if (canCheckIn && canCancel) const SizedBox(height: 12),
        if (canCancel)
          CustomButton(
            text: 'Cancel Booking',
            isOutlined: true,
            backgroundColor: Colors.red,
            textColor: Colors.red,
            prefixIcon: const Icon(Icons.cancel, color: Colors.red),
            onPressed: () {
              _showCancellationDialog(context, booking);
            },
          ),
      ],
    );
  }

  Widget _buildVerificationStatusCard(AadhaarVerification? verification) {
    final bool isVerified =
        verification?.status == AadhaarVerificationStatus.verified;
    final bool isPending =
        verification?.status == AadhaarVerificationStatus.pending;

    Color statusColor;
    IconData statusIcon;
    String statusTitle;
    String statusDescription;

    if (isVerified) {
      statusColor = Colors.green;
      statusIcon = Icons.verified_user;
      statusTitle = 'Aadhaar Verified';
      statusDescription =
          'Your identity has been verified with Aadhaar. You can check-in seamlessly at the hotel.';
    } else if (isPending) {
      statusColor = Colors.orange;
      statusIcon = Icons.pending;
      statusTitle = 'Verification Pending';
      statusDescription =
          'Your Aadhaar verification is in progress. Please wait for it to complete.';
    } else {
      statusColor = Colors.red;
      statusIcon = Icons.shield;
      statusTitle = 'Verification Required';
      statusDescription =
          'Aadhaar verification is required for check-in. Please complete the verification process.';
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      color: statusColor.withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    statusIcon,
                    color: statusColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        statusTitle,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: statusColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        statusDescription,
                        style: const TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (!isVerified) ...[
              const SizedBox(height: 16),
              CustomButton(
                text: 'Complete Verification',
                backgroundColor: statusColor,
                prefixIcon:
                    const Icon(Icons.verified_user, color: Colors.white),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AadhaarVerificationScreen(),
                    ),
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value, {
    IconData? icon,
    Color? valueColor,
    FontWeight? valueFontWeight,
    bool alignTop = false,
  }) {
    return Row(
      crossAxisAlignment:
          alignTop ? CrossAxisAlignment.start : CrossAxisAlignment.center,
      children: [
        if (icon != null) ...[
          Icon(
            icon,
            size: 18,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 8),
        ],
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade700,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: valueFontWeight ?? FontWeight.w500,
              color: valueColor,
            ),
          ),
        ),
      ],
    );
  }

  void _showCancellationDialog(BuildContext context, Booking booking) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Booking'),
        content: const Text(
          'Are you sure you want to cancel this booking? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('No, Keep Booking'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);

              final bookingService = Provider.of<BookingService>(
                context,
                listen: false,
              );

              await bookingService.cancelBooking(booking.id);

              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Booking cancelled successfully'),
                    backgroundColor: Colors.green,
                  ),
                );

                // Refresh booking details
                _fetchBookingDetails();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Yes, Cancel Booking'),
          ),
        ],
      ),
    );
  }

  String _getBookingId(String id) {
    // Format booking ID for display (e.g., "BK-1234")
    if (id.length > 8) {
      return 'BK-${id.substring(0, 8).toUpperCase()}';
    }
    return 'BK-$id';
  }
}
