import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  serverTimestamp,
  DocumentSnapshot
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { BookingData } from './bookingService';
import { HotelData } from '../firebase/hotelService';
import { RoomData } from './roomService';

/**
 * Interface for mobile app user profile
 */
export interface MobileUserProfile {
  id?: string;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  zipCode?: string;
  preferences?: {
    roomPreferences?: string[];
    dietaryRestrictions?: string[];
    specialRequests?: string[];
    notificationPreferences?: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
  };
  loyaltyPoints?: number;
  membershipTier?: string;
  favoriteHotels?: string[];
  recentSearches?: {
    location: string;
    checkIn: Timestamp;
    checkOut: Timestamp;
    guests: number;
    timestamp: Timestamp;
  }[];
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

/**
 * Interface for mobile app notification
 */
export interface MobileNotification {
  id?: string;
  userId: string;
  title: string;
  message: string;
  type: 'booking' | 'promotion' | 'system' | 'loyalty';
  read: boolean;
  actionUrl?: string;
  bookingId?: string;
  hotelId?: string;
  createdAt?: Timestamp;
}

/**
 * Interface for mobile app review
 */
export interface MobileReview {
  id?: string;
  userId: string;
  hotelId: string;
  bookingId?: string;
  rating: number;
  title?: string;
  comment?: string;
  photos?: string[];
  categories?: {
    cleanliness?: number;
    service?: number;
    comfort?: number;
    location?: number;
    value?: number;
  };
  status: 'pending' | 'approved' | 'rejected';
  vendorResponse?: {
    comment: string;
    timestamp: Timestamp;
    respondedBy: string;
  };
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

/**
 * Interface for mobile app search parameters
 */
export interface MobileSearchParams {
  location?: string;
  checkIn: Date;
  checkOut: Date;
  adults: number;
  children?: number;
  rooms?: number;
  priceMin?: number;
  priceMax?: number;
  amenities?: string[];
  rating?: number;
  distance?: number;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

/**
 * Interface for mobile app search result
 */
export interface MobileSearchResult {
  hotels: (HotelData & {
    distance?: number;
    lowestPrice?: number;
    availableRooms?: number;
    thumbnailUrl?: string;
  })[];
  totalResults: number;
  hasMore: boolean;
  lastDoc?: DocumentSnapshot;
}

/**
 * Create or update mobile user profile
 */
export const createOrUpdateUserProfile = async (profile: MobileUserProfile): Promise<string> => {
  try {
    // Check if profile exists
    const profilesQuery = query(
      collection(db, 'mobileUserProfiles'),
      where('userId', '==', profile.userId)
    );

    const profilesSnapshot = await getDocs(profilesQuery);

    if (!profilesSnapshot.empty) {
      // Update existing profile
      const profileDoc = profilesSnapshot.docs[0];
      await updateDoc(doc(db, 'mobileUserProfiles', profileDoc.id), {
        ...profile,
        updatedAt: serverTimestamp()
      });

      return profileDoc.id;
    } else {
      // Create new profile
      const profileRef = await addDoc(collection(db, 'mobileUserProfiles'), {
        ...profile,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      return profileRef.id;
    }
  } catch (error) {
    console.error('Error creating/updating user profile:', error);
    throw error;
  }
};

/**
 * Get mobile user profile by user ID
 */
export const getUserProfile = async (userId: string): Promise<MobileUserProfile | null> => {
  try {
    const profilesQuery = query(
      collection(db, 'mobileUserProfiles'),
      where('userId', '==', userId)
    );

    const profilesSnapshot = await getDocs(profilesQuery);

    if (!profilesSnapshot.empty) {
      const profileDoc = profilesSnapshot.docs[0];
      return { id: profileDoc.id, ...(profileDoc.data() as object) } as MobileUserProfile;
    }

    return null;
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
};

/**
 * Add hotel to user favorites
 */
export const addHotelToFavorites = async (userId: string, hotelId: string): Promise<boolean> => {
  try {
    const profile = await getUserProfile(userId);

    if (!profile) {
      throw new Error('User profile not found');
    }

    const favoriteHotels = profile.favoriteHotels || [];

    if (!favoriteHotels.includes(hotelId)) {
      favoriteHotels.push(hotelId);

      await updateDoc(doc(db, 'mobileUserProfiles', profile.id!), {
        favoriteHotels,
        updatedAt: serverTimestamp()
      });
    }

    return true;
  } catch (error) {
    console.error('Error adding hotel to favorites:', error);
    throw error;
  }
};

/**
 * Remove hotel from user favorites
 */
export const removeHotelFromFavorites = async (userId: string, hotelId: string): Promise<boolean> => {
  try {
    const profile = await getUserProfile(userId);

    if (!profile) {
      throw new Error('User profile not found');
    }

    const favoriteHotels = profile.favoriteHotels || [];
    const updatedFavorites = favoriteHotels.filter(id => id !== hotelId);

    await updateDoc(doc(db, 'mobileUserProfiles', profile.id!), {
      favoriteHotels: updatedFavorites,
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('Error removing hotel from favorites:', error);
    throw error;
  }
};

/**
 * Add recent search to user profile
 */
export const addRecentSearch = async (
  userId: string,
  search: {
    location: string;
    checkIn: Date;
    checkOut: Date;
    guests: number;
  }
): Promise<boolean> => {
  try {
    const profile = await getUserProfile(userId);

    if (!profile) {
      throw new Error('User profile not found');
    }

    const recentSearches = profile.recentSearches || [];

    // Add new search at the beginning
    recentSearches.unshift({
      location: search.location,
      checkIn: Timestamp.fromDate(search.checkIn),
      checkOut: Timestamp.fromDate(search.checkOut),
      guests: search.guests,
      timestamp: Timestamp.now()
    });

    // Limit to 10 recent searches
    const limitedSearches = recentSearches.slice(0, 10);

    await updateDoc(doc(db, 'mobileUserProfiles', profile.id!), {
      recentSearches: limitedSearches,
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('Error adding recent search:', error);
    throw error;
  }
};

/**
 * Create a notification for mobile user
 */
export const createNotification = async (notification: MobileNotification): Promise<string> => {
  try {
    const notificationRef = await addDoc(collection(db, 'mobileNotifications'), {
      ...notification,
      read: false,
      createdAt: serverTimestamp()
    });

    return notificationRef.id;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
};

/**
 * Get notifications for a user
 */
export const getUserNotifications = async (userId: string, unreadOnly: boolean = false): Promise<MobileNotification[]> => {
  try {
    let notificationsQuery: any = query(
      collection(db, 'mobileNotifications'),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );

    if (unreadOnly) {
      notificationsQuery = query(notificationsQuery, where('read', '==', false));
    }

    const notificationsSnapshot = await getDocs(notificationsQuery);
    const notifications: MobileNotification[] = [];

    notificationsSnapshot.forEach((doc) => {
      const data = doc.data();
      notifications.push({ id: doc.id, ...(data as object) } as MobileNotification);
    });

    return notifications;
  } catch (error) {
    console.error('Error getting user notifications:', error);
    throw error;
  }
};

/**
 * Mark notification as read
 */
export const markNotificationAsRead = async (notificationId: string): Promise<boolean> => {
  try {
    await updateDoc(doc(db, 'mobileNotifications', notificationId), {
      read: true
    });

    return true;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
};

/**
 * Submit a hotel review
 */
export const submitHotelReview = async (review: MobileReview): Promise<string> => {
  try {
    const reviewRef = await addDoc(collection(db, 'mobileReviews'), {
      ...review,
      status: 'pending',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return reviewRef.id;
  } catch (error) {
    console.error('Error submitting hotel review:', error);
    throw error;
  }
};

/**
 * Get hotel reviews
 */
export const getHotelReviews = async (hotelId: string, approvedOnly: boolean = true): Promise<MobileReview[]> => {
  try {
    let reviewsQuery: any = query(
      collection(db, 'mobileReviews'),
      where('hotelId', '==', hotelId)
    );

    if (approvedOnly) {
      reviewsQuery = query(reviewsQuery, where('status', '==', 'approved'));
    }

    reviewsQuery = query(reviewsQuery, orderBy('createdAt', 'desc'));

    const reviewsSnapshot = await getDocs(reviewsQuery);
    const reviews: MobileReview[] = [];

    reviewsSnapshot.forEach((doc) => {
      const data = doc.data();
      reviews.push({ id: doc.id, ...(data as object) } as MobileReview);
    });

    return reviews;
  } catch (error) {
    console.error('Error getting hotel reviews:', error);
    throw error;
  }
};
