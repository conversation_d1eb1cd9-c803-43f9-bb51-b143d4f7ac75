import 'package:flutter/material.dart';
import 'package:hotel_booking_app/core/theme/app_theme.dart';

class LoadingWidget extends StatelessWidget {
  final String? message;
  final bool showBackground;

  const LoadingWidget({
    super.key,
    this.message,
    this.showBackground = false,
  });

  @override
  Widget build(BuildContext context) {
    final loadingWidget = Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ],
      ),
    );

    if (showBackground) {
      return Container(
        color: Colors.black.withOpacity(0.1),
        child: loadingWidget,
      );
    }

    return loadingWidget;
  }
}
