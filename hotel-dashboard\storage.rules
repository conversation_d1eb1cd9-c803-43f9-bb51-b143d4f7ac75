rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Allow public read access to hotel images
    match /hotels/{hotelId}/{allImages=**} {
      allow read: if true;
      allow write: if request.auth != null && 
                    (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super_admin' || 
                     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'vendor');
    }
    
    // Allow public read access to room images
    match /rooms/{roomId}/{allImages=**} {
      allow read: if true;
      allow write: if request.auth != null && 
                    (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super_admin' || 
                     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'vendor');
    }
    
    // Allow users to upload and access their own profile images
    match /users/{userId}/{allImages=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Default deny
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
