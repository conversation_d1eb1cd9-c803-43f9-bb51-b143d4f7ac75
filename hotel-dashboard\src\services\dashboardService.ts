import {
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  Timestamp,
  getDoc,
  doc
} from 'firebase/firestore';
import { db } from '../firebase/config';

// Interface for dashboard statistics
export interface DashboardStats {
  userCount: number;
  bookingCount: number;
  totalEarnings: number;
  accountBalance: number;
  salesPercentage: number;
  totalSalesToday: number;
}

// Interface for revenue data point
export interface RevenueDataPoint {
  name: string;
  value: number;
}

// Interface for transaction
export interface Transaction {
  id: string;
  trackingId: string;
  productName: string;
  customerName: string;
  date: string;
  amount: number;
  paymentMethod: string;
  status: string;
}

/**
 * Get user count for a vendor
 */
export const getUserCount = async (vendorId: string): Promise<number> => {
  try {
    // Get hotels for this vendor
    const hotelsQuery = query(
      collection(db, 'hotels'),
      where('vendorId', '==', vendorId)
    );
    const hotelsSnapshot = await getDocs(hotelsQuery);

    // Extract hotel IDs
    const hotelIds: string[] = [];
    hotelsSnapshot.forEach(doc => {
      hotelIds.push(doc.id);
    });

    if (hotelIds.length === 0) return 0;

    // Count unique users who have made bookings at these hotels
    const bookingsQuery = query(
      collection(db, 'bookings'),
      where('hotelId', 'in', hotelIds)
    );
    const bookingsSnapshot = await getDocs(bookingsQuery);

    // Extract unique user IDs
    const userIds = new Set<string>();
    bookingsSnapshot.forEach(doc => {
      const data = doc.data();
      if (data.userId) {
        userIds.add(data.userId);
      }
    });

    return userIds.size;
  } catch (error) {
    console.error('Error getting user count:', error);
    throw error;
  }
};

/**
 * Get booking count for a vendor
 */
export const getBookingCount = async (vendorId: string): Promise<number> => {
  try {
    // Get hotels for this vendor
    const hotelsQuery = query(
      collection(db, 'hotels'),
      where('vendorId', '==', vendorId)
    );
    const hotelsSnapshot = await getDocs(hotelsQuery);

    // Extract hotel IDs
    const hotelIds: string[] = [];
    hotelsSnapshot.forEach(doc => {
      hotelIds.push(doc.id);
    });

    if (hotelIds.length === 0) return 0;

    // Count bookings for these hotels
    const bookingsQuery = query(
      collection(db, 'bookings'),
      where('hotelId', 'in', hotelIds)
    );
    const bookingsSnapshot = await getDocs(bookingsQuery);

    return bookingsSnapshot.size;
  } catch (error) {
    console.error('Error getting booking count:', error);
    throw error;
  }
};

/**
 * Get total earnings for a vendor
 */
export const getTotalEarnings = async (vendorId: string): Promise<number> => {
  try {
    // Get hotels for this vendor
    const hotelsQuery = query(
      collection(db, 'hotels'),
      where('vendorId', '==', vendorId)
    );
    const hotelsSnapshot = await getDocs(hotelsQuery);

    // Extract hotel IDs
    const hotelIds: string[] = [];
    hotelsSnapshot.forEach(doc => {
      hotelIds.push(doc.id);
    });

    if (hotelIds.length === 0) return 0;

    // Get all bookings for these hotels
    const bookingsQuery = query(
      collection(db, 'bookings'),
      where('hotelId', 'in', hotelIds),
      where('bookingStatus', 'in', ['confirmed', 'checked_in', 'checked_out'])
    );
    const bookingsSnapshot = await getDocs(bookingsQuery);

    // Calculate total earnings
    let totalEarnings = 0;
    bookingsSnapshot.forEach(doc => {
      const data = doc.data();
      if (data.totalAmount) {
        totalEarnings += data.totalAmount;
      }
    });

    return totalEarnings;
  } catch (error) {
    console.error('Error getting total earnings:', error);
    throw error;
  }
};

/**
 * Get account balance for a vendor
 */
export const getAccountBalance = async (vendorId: string): Promise<number> => {
  try {
    // In a real implementation, you would have a separate collection for financial records
    // For now, we'll just return the total earnings
    return await getTotalEarnings(vendorId);
  } catch (error) {
    console.error('Error getting account balance:', error);
    throw error;
  }
};

/**
 * Get revenue data for the last 6 months
 */
export const getRevenueData = async (vendorId: string): Promise<RevenueDataPoint[]> => {
  try {
    // Get hotels for this vendor
    const hotelsQuery = query(
      collection(db, 'hotels'),
      where('vendorId', '==', vendorId)
    );
    const hotelsSnapshot = await getDocs(hotelsQuery);

    // Extract hotel IDs
    const hotelIds: string[] = [];
    hotelsSnapshot.forEach(doc => {
      hotelIds.push(doc.id);
    });

    if (hotelIds.length === 0) return [];

    // Get current date and calculate date 6 months ago
    const now = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    // Get all bookings for these hotels in the last 6 months
    const bookingsQuery = query(
      collection(db, 'bookings'),
      where('hotelId', 'in', hotelIds),
      where('createdAt', '>=', Timestamp.fromDate(sixMonthsAgo)),
      where('bookingStatus', 'in', ['confirmed', 'checked_in', 'checked_out'])
    );
    const bookingsSnapshot = await getDocs(bookingsQuery);

    // Group bookings by month
    const monthlyRevenue: { [key: string]: number } = {};
    const months = ['January', 'February', 'March', 'April', 'May', 'June',
                   'July', 'August', 'September', 'October', 'November', 'December'];

    // Initialize all months with 0
    for (let i = 0; i < 6; i++) {
      const monthIndex = (now.getMonth() - i + 12) % 12;
      monthlyRevenue[months[monthIndex]] = 0;
    }

    // Add booking amounts to respective months
    bookingsSnapshot.forEach(doc => {
      const data = doc.data();
      if (data.createdAt && data.totalAmount) {
        const date = data.createdAt.toDate();
        const monthName = months[date.getMonth()];
        if (monthlyRevenue[monthName] !== undefined) {
          monthlyRevenue[monthName] += data.totalAmount;
        }
      }
    });

    // Convert to array of data points
    const revenueData: RevenueDataPoint[] = [];
    for (let i = 5; i >= 0; i--) {
      const monthIndex = (now.getMonth() - i + 12) % 12;
      const monthName = months[monthIndex];
      revenueData.push({
        name: monthName,
        value: monthlyRevenue[monthName]
      });
    }

    return revenueData;
  } catch (error) {
    console.error('Error getting revenue data:', error);
    throw error;
  }
};

/**
 * Get sales data for today
 */
export const getSalesData = async (vendorId: string): Promise<{ percentage: number, total: number }> => {
  try {
    // Get hotels for this vendor
    const hotelsQuery = query(
      collection(db, 'hotels'),
      where('vendorId', '==', vendorId)
    );
    const hotelsSnapshot = await getDocs(hotelsQuery);

    // Extract hotel IDs
    const hotelIds: string[] = [];
    hotelsSnapshot.forEach(doc => {
      hotelIds.push(doc.id);
    });

    if (hotelIds.length === 0) return { percentage: 0, total: 0 };

    // Get start and end of today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Get bookings made today
    const todayBookingsQuery = query(
      collection(db, 'bookings'),
      where('hotelId', 'in', hotelIds),
      where('createdAt', '>=', Timestamp.fromDate(today)),
      where('createdAt', '<', Timestamp.fromDate(tomorrow))
    );
    const todayBookingsSnapshot = await getDocs(todayBookingsQuery);

    // Calculate total sales today
    let totalSalesToday = 0;
    todayBookingsSnapshot.forEach(doc => {
      const data = doc.data();
      if (data.totalAmount) {
        totalSalesToday += data.totalAmount;
      }
    });

    // Get bookings made yesterday for comparison
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const yesterdayBookingsQuery = query(
      collection(db, 'bookings'),
      where('hotelId', 'in', hotelIds),
      where('createdAt', '>=', Timestamp.fromDate(yesterday)),
      where('createdAt', '<', Timestamp.fromDate(today))
    );
    const yesterdayBookingsSnapshot = await getDocs(yesterdayBookingsQuery);

    // Calculate total sales yesterday
    let totalSalesYesterday = 0;
    yesterdayBookingsSnapshot.forEach(doc => {
      const data = doc.data();
      if (data.totalAmount) {
        totalSalesYesterday += data.totalAmount;
      }
    });

    // Calculate percentage of target (assuming target is yesterday's sales + 10%)
    const target = totalSalesYesterday > 0 ? totalSalesYesterday * 1.1 : 1000;
    const percentage = Math.min(Math.round((totalSalesToday / target) * 100), 100);

    return { percentage, total: totalSalesToday };
  } catch (error) {
    console.error('Error getting sales data:', error);
    throw error;
  }
};

/**
 * Get recent transactions
 */
export const getRecentTransactions = async (vendorId: string, limitCount: number = 10): Promise<Transaction[]> => {
  try {
    // Get hotels for this vendor
    const hotelsQuery = query(
      collection(db, 'hotels'),
      where('vendorId', '==', vendorId)
    );
    const hotelsSnapshot = await getDocs(hotelsQuery);

    // Extract hotel IDs
    const hotelIds: string[] = [];
    hotelsSnapshot.forEach(doc => {
      hotelIds.push(doc.id);
    });

    if (hotelIds.length === 0) return [];

    // Get recent bookings
    const bookingsQuery = query(
      collection(db, 'bookings'),
      where('hotelId', 'in', hotelIds),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );
    const bookingsSnapshot = await getDocs(bookingsQuery);

    // Convert to transactions
    const transactions: Transaction[] = [];

    for (const doc of bookingsSnapshot.docs) {
      const data = doc.data();

      // Format date
      let dateStr = 'N/A';
      if (data.createdAt && typeof data.createdAt.toDate === 'function') {
        const date = data.createdAt.toDate();
        dateStr = `${date.getDate()} ${date.toLocaleString('default', { month: 'long' })}`;
      }

      // Get room details
      let roomName = 'Room';
      if (data.roomId) {
        try {
          // Use a simple string for room name to avoid type issues
          roomName = 'Room ' + data.roomId.substring(0, 4);
        } catch (e) {
          console.error('Error formatting room name:', e);
        }
      }

      // Format guest name
      let guestName = 'Guest';
      if (data.guestInfo) {
        const firstName = data.guestInfo.firstName || '';
        const lastName = data.guestInfo.lastName || '';
        guestName = `${firstName} ${lastName}`.trim() || 'Guest';
      }

      transactions.push({
        id: doc.id,
        trackingId: data.bookingNumber || doc.id.substring(0, 8),
        productName: roomName,
        customerName: guestName,
        date: dateStr,
        amount: data.totalAmount || 0,
        paymentMethod: data.paymentMethod || 'Online Payment',
        status: data.bookingStatus || 'pending'
      });
    }

    return transactions;
  } catch (error) {
    console.error('Error getting recent transactions:', error);
    throw error;
  }
};

/**
 * Get all dashboard data in one call
 */
export const getDashboardData = async (vendorId: string): Promise<{
  stats: DashboardStats;
  revenueData: RevenueDataPoint[];
  transactions: Transaction[];
}> => {
  try {
    // Get user count
    const userCount = await getUserCount(vendorId);

    // Get booking count
    const bookingCount = await getBookingCount(vendorId);

    // Get total earnings
    const totalEarnings = await getTotalEarnings(vendorId);

    // Get account balance
    const accountBalance = await getAccountBalance(vendorId);

    // Get revenue data
    const revenueData = await getRevenueData(vendorId);

    // Get sales data
    const salesData = await getSalesData(vendorId);

    // Get recent transactions
    const transactions = await getRecentTransactions(vendorId);

    return {
      stats: {
        userCount,
        bookingCount,
        totalEarnings,
        accountBalance,
        salesPercentage: salesData.percentage,
        totalSalesToday: salesData.total
      },
      revenueData,
      transactions
    };
  } catch (error) {
    console.error('Error getting dashboard data:', error);
    throw error;
  }
};
