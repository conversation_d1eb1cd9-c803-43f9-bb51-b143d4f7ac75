import 'package:cloud_firestore/cloud_firestore.dart';

enum LoyaltyTier {
  bronze,
  silver,
  gold,
  platinum,
  diamond
}

class LoyaltyTransaction {
  final String id;
  final String userId;
  final int points;
  final String type;
  final String description;
  final String? referenceId; // ID of booking, review, etc.
  final DateTime createdAt;

  LoyaltyTransaction({
    required this.id,
    required this.userId,
    required this.points,
    required this.type,
    required this.description,
    this.referenceId,
    required this.createdAt,
  });

  factory LoyaltyTransaction.fromMap(String id, Map<String, dynamic> map) {
    return LoyaltyTransaction(
      id: id,
      userId: map['userId'] ?? '',
      points: map['points'] ?? 0,
      type: map['type'] ?? '',
      description: map['description'] ?? '',
      referenceId: map['referenceId'],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'points': points,
      'type': type,
      'description': description,
      'referenceId': referenceId,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }
}

class LoyaltyProgram {
  final String id;
  final String name;
  final String description;
  final Map<String, int> activityPoints;
  final List<LoyaltyTierInfo> tiers;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  LoyaltyProgram({
    required this.id,
    required this.name,
    required this.description,
    required this.activityPoints,
    required this.tiers,
    required this.isActive,
    required this.createdAt,
    this.updatedAt,
  });

  factory LoyaltyProgram.fromMap(String id, Map<String, dynamic> map) {
    return LoyaltyProgram(
      id: id,
      name: map['name'] ?? 'Link In Blink Rewards',
      description: map['description'] ?? 'Earn points for stays and activities',
      activityPoints: Map<String, int>.from(map['activityPoints'] ?? {}),
      tiers: (map['tiers'] as List<dynamic>?)
              ?.map((tier) => LoyaltyTierInfo.fromMap(tier))
              .toList() ??
          [],
      isActive: map['isActive'] ?? true,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'activityPoints': activityPoints,
      'tiers': tiers.map((tier) => tier.toMap()).toList(),
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }
}

class LoyaltyTierInfo {
  final String name;
  final LoyaltyTier tier;
  final int pointsRequired;
  final List<String> benefits;
  final String? iconUrl;
  final String? color;

  LoyaltyTierInfo({
    required this.name,
    required this.tier,
    required this.pointsRequired,
    required this.benefits,
    this.iconUrl,
    this.color,
  });

  factory LoyaltyTierInfo.fromMap(Map<String, dynamic> map) {
    return LoyaltyTierInfo(
      name: map['name'] ?? 'Unknown',
      tier: _tierFromString(map['tier'] ?? 'bronze'),
      pointsRequired: map['pointsRequired'] ?? 0,
      benefits: List<String>.from(map['benefits'] ?? []),
      iconUrl: map['iconUrl'],
      color: map['color'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'tier': _tierToString(tier),
      'pointsRequired': pointsRequired,
      'benefits': benefits,
      'iconUrl': iconUrl,
      'color': color,
    };
  }

  static LoyaltyTier _tierFromString(String tier) {
    switch (tier.toLowerCase()) {
      case 'silver':
        return LoyaltyTier.silver;
      case 'gold':
        return LoyaltyTier.gold;
      case 'platinum':
        return LoyaltyTier.platinum;
      case 'diamond':
        return LoyaltyTier.diamond;
      default:
        return LoyaltyTier.bronze;
    }
  }

  static String _tierToString(LoyaltyTier tier) {
    switch (tier) {
      case LoyaltyTier.silver:
        return 'silver';
      case LoyaltyTier.gold:
        return 'gold';
      case LoyaltyTier.platinum:
        return 'platinum';
      case LoyaltyTier.diamond:
        return 'diamond';
      default:
        return 'bronze';
    }
  }
}

class UserLoyalty {
  final String userId;
  final int totalPoints;
  final int availablePoints;
  final int lifetimePoints;
  final LoyaltyTier currentTier;
  final int bookingsCount;
  final int reviewsCount;
  final DateTime? lastActivityDate;
  final DateTime createdAt;
  final DateTime? updatedAt;

  UserLoyalty({
    required this.userId,
    required this.totalPoints,
    required this.availablePoints,
    required this.lifetimePoints,
    required this.currentTier,
    required this.bookingsCount,
    required this.reviewsCount,
    this.lastActivityDate,
    required this.createdAt,
    this.updatedAt,
  });

  factory UserLoyalty.fromMap(Map<String, dynamic> map) {
    return UserLoyalty(
      userId: map['userId'] ?? '',
      totalPoints: map['totalPoints'] ?? 0,
      availablePoints: map['availablePoints'] ?? 0,
      lifetimePoints: map['lifetimePoints'] ?? 0,
      currentTier: LoyaltyTierInfo._tierFromString(map['currentTier'] ?? 'bronze'),
      bookingsCount: map['bookingsCount'] ?? 0,
      reviewsCount: map['reviewsCount'] ?? 0,
      lastActivityDate: (map['lastActivityDate'] as Timestamp?)?.toDate(),
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'totalPoints': totalPoints,
      'availablePoints': availablePoints,
      'lifetimePoints': lifetimePoints,
      'currentTier': LoyaltyTierInfo._tierToString(currentTier),
      'bookingsCount': bookingsCount,
      'reviewsCount': reviewsCount,
      'lastActivityDate': lastActivityDate != null ? Timestamp.fromDate(lastActivityDate!) : null,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  UserLoyalty copyWith({
    String? userId,
    int? totalPoints,
    int? availablePoints,
    int? lifetimePoints,
    LoyaltyTier? currentTier,
    int? bookingsCount,
    int? reviewsCount,
    DateTime? lastActivityDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserLoyalty(
      userId: userId ?? this.userId,
      totalPoints: totalPoints ?? this.totalPoints,
      availablePoints: availablePoints ?? this.availablePoints,
      lifetimePoints: lifetimePoints ?? this.lifetimePoints,
      currentTier: currentTier ?? this.currentTier,
      bookingsCount: bookingsCount ?? this.bookingsCount,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      lastActivityDate: lastActivityDate ?? this.lastActivityDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
