import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/data/models/review_model.dart';

class ReviewService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  final List<Review> _reviews = [];
  List<Review> _hotelReviews = [];
  bool _isLoading = false;
  String? _error;
  
  // Getters
  List<Review> get reviews => _reviews;
  List<Review> get hotelReviews => _hotelReviews;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Create a new review
  Future<bool> createReview({
    required String userId,
    required String hotelId,
    String? roomId,
    required String userName,
    String? userImage,
    required double rating,
    required String comment,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Create a new document reference
      final docRef = _firestore.collection(AppConstants.reviewsCollection).doc();
      
      // Create the review object
      final review = Review(
        id: docRef.id,
        userId: userId,
        hotelId: hotelId,
        roomId: roomId,
        userName: userName,
        userImage: userImage,
        rating: rating,
        comment: comment,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      // Save the review to Firestore
      await docRef.set(review.toMap());
      
      // Update the hotel's average rating
      await _updateHotelRating(hotelId);
      
      // Add the review to the local list
      _reviews.add(review);
      
      // If this is for the current hotel being viewed, add to hotel reviews
      if (_hotelReviews.isNotEmpty && _hotelReviews.first.hotelId == hotelId) {
        _hotelReviews.add(review);
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error creating review: $e');
      _error = 'Failed to create review: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }
  
  // Update an existing review
  Future<bool> updateReview({
    required String reviewId,
    required double rating,
    required String comment,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Get the review document
      final docRef = _firestore.collection(AppConstants.reviewsCollection).doc(reviewId);
      
      // Update the review
      await docRef.update({
        'rating': rating,
        'comment': comment,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      // Find the review in the local lists and update it
      final index = _reviews.indexWhere((r) => r.id == reviewId);
      if (index != -1) {
        final review = _reviews[index];
        final updatedReview = review.copyWith(
          rating: rating,
          comment: comment,
          updatedAt: DateTime.now(),
        );
        _reviews[index] = updatedReview;
        
        // Update in hotel reviews if present
        final hotelIndex = _hotelReviews.indexWhere((r) => r.id == reviewId);
        if (hotelIndex != -1) {
          _hotelReviews[hotelIndex] = updatedReview;
        }
        
        // Update the hotel's average rating
        await _updateHotelRating(review.hotelId);
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error updating review: $e');
      _error = 'Failed to update review: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }
  
  // Delete a review
  Future<bool> deleteReview(String reviewId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Find the review to get the hotelId before deletion
      final reviewIndex = _reviews.indexWhere((r) => r.id == reviewId);
      String? hotelId;
      
      if (reviewIndex != -1) {
        hotelId = _reviews[reviewIndex].hotelId;
      }
      
      // Delete the review from Firestore
      await _firestore.collection(AppConstants.reviewsCollection).doc(reviewId).delete();
      
      // Remove from local lists
      _reviews.removeWhere((r) => r.id == reviewId);
      _hotelReviews.removeWhere((r) => r.id == reviewId);
      
      // Update the hotel's average rating if we have the hotelId
      if (hotelId != null) {
        await _updateHotelRating(hotelId);
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error deleting review: $e');
      _error = 'Failed to delete review: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }
  
  // Get all reviews for a specific hotel
  Future<List<Review>> getHotelReviews(String hotelId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final querySnapshot = await _firestore
          .collection(AppConstants.reviewsCollection)
          .where('hotelId', isEqualTo: hotelId)
          .orderBy('createdAt', descending: true)
          .get();
      
      _hotelReviews = querySnapshot.docs
          .map((doc) => Review.fromMap(doc.data()))
          .toList();
      
      _isLoading = false;
      notifyListeners();
      return _hotelReviews;
    } catch (e) {
      debugPrint('Error getting hotel reviews: $e');
      _error = 'Failed to load reviews: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return [];
    }
  }
  
  // Get all reviews by a specific user
  Future<List<Review>> getUserReviews(String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final querySnapshot = await _firestore
          .collection(AppConstants.reviewsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();
      
      final userReviews = querySnapshot.docs
          .map((doc) => Review.fromMap(doc.data()))
          .toList();
      
      _isLoading = false;
      notifyListeners();
      return userReviews;
    } catch (e) {
      debugPrint('Error getting user reviews: $e');
      _error = 'Failed to load your reviews: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return [];
    }
  }
  
  // Check if a user has already reviewed a hotel
  Future<Review?> getUserHotelReview(String userId, String hotelId) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppConstants.reviewsCollection)
          .where('userId', isEqualTo: userId)
          .where('hotelId', isEqualTo: hotelId)
          .limit(1)
          .get();
      
      if (querySnapshot.docs.isNotEmpty) {
        return Review.fromMap(querySnapshot.docs.first.data());
      }
      
      return null;
    } catch (e) {
      debugPrint('Error checking user review: $e');
      return null;
    }
  }
  
  // Update the hotel's average rating
  Future<void> _updateHotelRating(String hotelId) async {
    try {
      // Get all reviews for the hotel
      final querySnapshot = await _firestore
          .collection(AppConstants.reviewsCollection)
          .where('hotelId', isEqualTo: hotelId)
          .get();
      
      final reviews = querySnapshot.docs
          .map((doc) => Review.fromMap(doc.data()))
          .toList();
      
      // Calculate the average rating
      double totalRating = 0;
      for (final review in reviews) {
        totalRating += review.rating;
      }
      
      final averageRating = reviews.isEmpty ? 0.0 : totalRating / reviews.length;
      
      // Update the hotel document with the new rating
      await _firestore.collection(AppConstants.hotelsCollection).doc(hotelId).update({
        'rating': averageRating,
        'reviewCount': reviews.length,
      });
    } catch (e) {
      debugPrint('Error updating hotel rating: $e');
    }
  }
  
  // Clear any error
  void clearError() {
    _error = null;
    notifyListeners();
  }
}
