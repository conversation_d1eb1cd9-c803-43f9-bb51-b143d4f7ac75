import React, { useState } from 'react';
import { TextField } from '@mui/material';
import {
  Box,
  Typography,
  Paper,
  Button,
  FormControlLabel,
  Checkbox,
  Slider,
  Grid,
  CircularProgress,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  AutoAwesome as AutoAwesomeIcon,
  CalendarMonth as CalendarIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Check as CheckIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon,
  Work as WorkIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format, startOfWeek, addDays, isSameDay } from 'date-fns';
import {
  generateWeeklySchedule,
  applyAutoSchedule,
  SchedulingResult
} from '../../services/autoScheduleService';
import ConflictAlert from './ConflictAlert';

interface AutoSchedulerProps {
  hotelId: string;
  vendorId: string;
  onSuccess?: () => void;
}

const AutoScheduler: React.FC<AutoSchedulerProps> = ({
  hotelId,
  vendorId,
  onSuccess
}) => {
  // State for date
  const [startDate, setStartDate] = useState<Date>(startOfWeek(new Date(), { weekStartsOn: 1 }));

  // State for options
  const [respectAvailability, setRespectAvailability] = useState<boolean>(true);
  const [maxShiftsPerWeek, setMaxShiftsPerWeek] = useState<number>(5);
  const [balanceShifts, setBalanceShifts] = useState<boolean>(true);

  // State for scheduling result
  const [schedulingResult, setSchedulingResult] = useState<SchedulingResult | null>(null);

  // State for dialogs
  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);
  const [conflictDialogOpen, setConflictDialogOpen] = useState<boolean>(false);

  // State for loading and errors
  const [generating, setGenerating] = useState<boolean>(false);
  const [applying, setApplying] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Handle date change
  const handleDateChange = (value: unknown, keyboardInputValue?: string) => {
    if (value instanceof Date) {
      setStartDate(startOfWeek(value, { weekStartsOn: 1 }));
    }
  };

  // Handle option changes
  const handleRespectAvailabilityChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRespectAvailability(event.target.checked);
  };

  const handleMaxShiftsChange = (event: Event, newValue: number | number[]) => {
    setMaxShiftsPerWeek(newValue as number);
  };

  const handleBalanceShiftsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setBalanceShifts(event.target.checked);
  };

  // Generate schedule
  const handleGenerateSchedule = async () => {
    try {
      setGenerating(true);
      setError(null);
      setSuccess(null);
      setSchedulingResult(null);

      const result = await generateWeeklySchedule(
        hotelId,
        vendorId,
        startDate,
        {
          respectAvailability,
          maxShiftsPerWeek,
          balanceShifts
        }
      );

      setSchedulingResult(result);

      if (result.conflicts.length > 0) {
        setConflictDialogOpen(true);
      } else if (result.unfilledPositions.length > 0) {
        setError(`Schedule generated with ${result.unfilledPositions.length} unfilled positions.`);
      } else {
        setSuccess('Schedule generated successfully!');
      }

      setGenerating(false);
    } catch (err: any) {
      console.error('Error generating schedule:', err);
      setError(err.message || 'Failed to generate schedule');
      setGenerating(false);
    }
  };

  // Open confirm dialog
  const handleOpenConfirmDialog = () => {
    setConfirmDialogOpen(true);
  };

  // Close confirm dialog
  const handleCloseConfirmDialog = () => {
    setConfirmDialogOpen(false);
  };

  // Close conflict dialog
  const handleCloseConflictDialog = () => {
    setConflictDialogOpen(false);
  };

  // Apply schedule
  const handleApplySchedule = async () => {
    try {
      if (!schedulingResult) return;

      setApplying(true);
      setError(null);
      setSuccess(null);

      await applyAutoSchedule(schedulingResult);

      setSuccess('Schedule applied successfully!');
      setApplying(false);
      setConfirmDialogOpen(false);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error('Error applying schedule:', err);
      setError(err.message || 'Failed to apply schedule');
      setApplying(false);
    }
  };

  // Get shift type name
  const getShiftTypeName = (shiftType: string): string => {
    switch (shiftType) {
      case 'morning':
        return 'Morning (6:00 - 14:00)';
      case 'afternoon':
        return 'Afternoon (14:00 - 22:00)';
      case 'night':
        return 'Night (22:00 - 6:00)';
      default:
        return shiftType;
    }
  };

  // Get role name
  const getRoleName = (role: string): string => {
    switch (role) {
      case 'housekeeping':
        return 'Housekeeping';
      case 'front_desk':
        return 'Front Desk';
      case 'maintenance':
        return 'Maintenance';
      case 'food_service':
        return 'Food Service';
      case 'manager':
        return 'Manager';
      default:
        return role;
    }
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Automated Scheduling
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Schedule Settings
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Week Starting
              </Typography>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Week Starting"
                  value={startDate}
                  onChange={handleDateChange}
                  disabled={generating || applying}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      helperText="Schedule will start from Monday of this week"
                    />
                  )}
                />
              </LocalizationProvider>
            </Box>

            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Maximum Shifts Per Staff Member
              </Typography>
              <Box sx={{ px: 2 }}>
                <Slider
                  value={maxShiftsPerWeek}
                  onChange={handleMaxShiftsChange}
                  min={1}
                  max={7}
                  step={1}
                  marks
                  valueLabelDisplay="auto"
                  disabled={generating || applying}
                />
              </Box>
              <Typography variant="body2" color="text.secondary">
                Limit the number of shifts assigned to each staff member per week
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 3 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={respectAvailability}
                    onChange={handleRespectAvailabilityChange}
                    disabled={generating || applying}
                  />
                }
                label="Respect Staff Availability"
              />
              <Typography variant="body2" color="text.secondary">
                Only schedule staff during their preferred or available times
              </Typography>
            </Box>

            <Box sx={{ mb: 3 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={balanceShifts}
                    onChange={handleBalanceShiftsChange}
                    disabled={generating || applying}
                  />
                }
                label="Balance Shifts"
              />
              <Typography variant="body2" color="text.secondary">
                Distribute shifts evenly among staff members
              </Typography>
            </Box>
          </Grid>
        </Grid>

        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={generating ? <CircularProgress size={20} color="inherit" /> : <AutoAwesomeIcon />}
            onClick={handleGenerateSchedule}
            disabled={generating || applying}
            size="large"
          >
            {generating ? 'Generating...' : 'Generate Schedule'}
          </Button>
        </Box>
      </Paper>

      {schedulingResult && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Generated Schedule
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={applying ? <CircularProgress size={20} color="inherit" /> : <CheckIcon />}
              onClick={handleOpenConfirmDialog}
              disabled={generating || applying}
            >
              Apply Schedule
            </Button>
          </Box>

          <Box sx={{ mb: 3 }}>
            <Typography variant="body1">
              Week of {format(startDate, 'MMMM d, yyyy')}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {schedulingResult.shifts.length} shifts generated
              {schedulingResult.unfilledPositions.length > 0 && ` • ${schedulingResult.unfilledPositions.length} unfilled positions`}
              {schedulingResult.conflicts.length > 0 && ` • ${schedulingResult.conflicts.length} conflicts`}
            </Typography>
          </Box>

          {schedulingResult.conflicts.length > 0 && (
            <Box sx={{ mb: 3 }}>
              <ConflictAlert conflicts={schedulingResult.conflicts} />
            </Box>
          )}

          {schedulingResult.unfilledPositions.length > 0 && (
            <Accordion sx={{ mb: 3 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography color="error" sx={{ display: 'flex', alignItems: 'center' }}>
                  <WarningIcon sx={{ mr: 1 }} />
                  {schedulingResult.unfilledPositions.length} Unfilled Positions
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <List dense>
                  {schedulingResult.unfilledPositions.map((position, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <ErrorIcon color="error" />
                      </ListItemIcon>
                      <ListItemText
                        primary={`${getRoleName(position.role)} - ${getShiftTypeName(position.shiftType)}`}
                        secondary={`${format(position.day, 'EEEE, MMMM d')} • ${position.reason}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </AccordionDetails>
            </Accordion>
          )}

          <Accordion defaultExpanded>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography sx={{ display: 'flex', alignItems: 'center' }}>
                <CalendarIcon sx={{ mr: 1 }} />
                Schedule Preview
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              {/* Group shifts by day */}
              {Array.from(Array(7).keys()).map(dayOffset => {
                const day = addDays(startDate, dayOffset);
                const dayShifts = schedulingResult.shifts.filter(shift =>
                  isSameDay(shift.date.toDate(), day)
                );

                return (
                  <Box key={dayOffset} sx={{ mb: 3 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      {format(day, 'EEEE, MMMM d')}
                    </Typography>

                    {dayShifts.length === 0 ? (
                      <Typography variant="body2" color="text.secondary">
                        No shifts scheduled
                      </Typography>
                    ) : (
                      <List dense>
                        {dayShifts.map((shift, index) => (
                          <ListItem key={index} sx={{
                            bgcolor: 'background.paper',
                            borderRadius: 1,
                            mb: 1,
                            border: '1px solid',
                            borderColor: 'divider'
                          }}>
                            <ListItemIcon>
                              <TimeIcon />
                            </ListItemIcon>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  {getShiftTypeName(shift.shiftType)}
                                  <Chip
                                    label={getRoleName(shift.role)}
                                    size="small"
                                    color="primary"
                                    sx={{ ml: 1 }}
                                  />
                                </Box>
                              }
                              secondary={
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <PersonIcon sx={{ mr: 0.5, fontSize: 'small' }} />
                                  {shift.staffName}
                                  <Box component="span" sx={{ mx: 1 }}>•</Box>
                                  <TimeIcon sx={{ mr: 0.5, fontSize: 'small' }} />
                                  {format(shift.startTime.toDate(), 'h:mm a')} - {format(shift.endTime.toDate(), 'h:mm a')}
                                </Box>
                              }
                            />
                          </ListItem>
                        ))}
                      </List>
                    )}

                    <Divider sx={{ mt: 2 }} />
                  </Box>
                );
              })}
            </AccordionDetails>
          </Accordion>
        </Paper>
      )}

      {/* Confirm Dialog */}
      <Dialog
        open={confirmDialogOpen}
        onClose={handleCloseConfirmDialog}
      >
        <DialogTitle>Apply Generated Schedule</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to apply this schedule? This will create {schedulingResult?.shifts.length} shifts in the system.
            {schedulingResult?.unfilledPositions.length ? ` There are ${schedulingResult.unfilledPositions.length} unfilled positions.` : ''}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseConfirmDialog}>Cancel</Button>
          <Button
            onClick={handleApplySchedule}
            variant="contained"
            color="primary"
            disabled={applying}
            startIcon={applying ? <CircularProgress size={20} color="inherit" /> : <CheckIcon />}
          >
            {applying ? 'Applying...' : 'Apply Schedule'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Conflict Dialog */}
      <Dialog
        open={conflictDialogOpen}
        onClose={handleCloseConflictDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Schedule Conflicts Detected</DialogTitle>
        <DialogContent>
          <DialogContentText paragraph>
            The following conflicts were detected in the generated schedule:
          </DialogContentText>

          {schedulingResult && (
            <ConflictAlert conflicts={schedulingResult.conflicts} />
          )}

          <DialogContentText paragraph sx={{ mt: 2 }}>
            These conflicts have been automatically avoided in the generated schedule.
            Some positions may be unfilled as a result.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleCloseConflictDialog}
            variant="contained"
          >
            OK
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AutoScheduler;
