import React, { useState, useEffect } from 'react';
import { SelectChangeEvent } from '@mui/material';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Button,
  Grid,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Divider,
  Chip,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import StaffCalendarWrapper from '../../components/staff/StaffCalendarWrapper';
import ScheduleTemplates from '../../components/staff/ScheduleTemplates';
import ApplyTemplate from '../../components/staff/ApplyTemplate';
import StaffAvailability from '../../components/staff/StaffAvailability';
import AvailabilityCalendar from '../../components/staff/AvailabilityCalendar';
import AutoScheduler from '../../components/staff/AutoScheduler';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  NavigateBefore as PrevIcon,
  NavigateNext as NextIcon,
  Today as TodayIcon,
  Event as EventIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import {
  startOfWeek,
  endOfWeek,
  addDays,
  format,
  addWeeks,
  subWeeks,
  parseISO,
  setHours,
  setMinutes
} from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import { auth } from '../../firebase/config';
import { getHotelsForVendor } from '../../firebase/hotelService';
import { getStaffForHotel, StaffMember } from '../../services/staffManagementService';
import {
  getStaffScheduleForWeek,
  getShiftsForDate,
  createStaffShift,
  updateStaffShift,
  deleteStaffShift,
  getTimeOffRequestsForHotel,
  requestTimeOff,
  updateTimeOffStatus,
  StaffSchedule,
  StaffShift,
  StaffTimeOff,
  SHIFT_TYPE_MORNING,
  SHIFT_TYPE_AFTERNOON,
  SHIFT_TYPE_NIGHT,
  SHIFT_TYPE_CUSTOM,
  TIME_OFF_TYPE_VACATION,
  TIME_OFF_TYPE_SICK,
  TIME_OFF_TYPE_PERSONAL,
  TIME_OFF_TYPE_OTHER,
  TIME_OFF_STATUS_PENDING,
  TIME_OFF_STATUS_APPROVED,
  TIME_OFF_STATUS_REJECTED
} from '../../services/staffSchedulingService';
import { checkAllConflicts } from '../../services/scheduleConflictService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`staff-tabpanel-${index}`}
      aria-labelledby={`staff-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
};

// Define shift times
const shiftTimes: { [key: string]: { start: string; end: string } } = {
  [SHIFT_TYPE_MORNING]: { start: '06:00', end: '14:00' },
  [SHIFT_TYPE_AFTERNOON]: { start: '14:00', end: '22:00' },
  [SHIFT_TYPE_NIGHT]: { start: '22:00', end: '06:00' }
};

const StaffScheduling: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [availabilityTabValue, setAvailabilityTabValue] = useState<number>(0);
  const [selectedStaffForAvailability, setSelectedStaffForAvailability] = useState<string>('');
  const [hotelId, setHotelId] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Staff list
  const [staffList, setStaffList] = useState<StaffMember[]>([]);
  const [loadingStaff, setLoadingStaff] = useState<boolean>(false);

  // Schedule state
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(startOfWeek(new Date(), { weekStartsOn: 1 }));
  const [weekDays, setWeekDays] = useState<Date[]>([]);
  const [schedules, setSchedules] = useState<StaffSchedule[]>([]);
  const [shifts, setShifts] = useState<{ [key: string]: StaffShift[] }>({});
  const [loadingSchedule, setLoadingSchedule] = useState<boolean>(false);

  // Shift dialog state
  const [shiftDialogOpen, setShiftDialogOpen] = useState<boolean>(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedShift, setSelectedShift] = useState<StaffShift | null>(null);
  const [shiftFormData, setShiftFormData] = useState<{
    staffId: string;
    shiftType: string;
    startTime: string;
    endTime: string;
    notes: string;
  }>({
    staffId: '',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '',
    endTime: '',
    notes: ''
  });

  // Time off state
  const [timeOffRequests, setTimeOffRequests] = useState<StaffTimeOff[]>([]);
  const [loadingTimeOff, setLoadingTimeOff] = useState<boolean>(false);
  const [timeOffStatusFilter, setTimeOffStatusFilter] = useState<string>('all');
  const [timeOffDialogOpen, setTimeOffDialogOpen] = useState<boolean>(false);
  const [selectedTimeOff, setSelectedTimeOff] = useState<StaffTimeOff | null>(null);
  const [timeOffFormData, setTimeOffFormData] = useState<{
    staffId: string;
    startDate: string;
    endDate: string;
    type: string;
    reason: string;
  }>({
    staffId: '',
    startDate: format(new Date(), 'yyyy-MM-dd'),
    endDate: format(new Date(), 'yyyy-MM-dd'),
    type: TIME_OFF_TYPE_VACATION,
    reason: ''
  });

  useEffect(() => {
    const fetchHotelId = async () => {
      try {
        if (!auth.currentUser) {
          setError('You must be logged in to view staff scheduling');
          setLoading(false);
          return;
        }

        const hotels = await getHotelsForVendor(auth.currentUser.uid);

        if (hotels.length === 0) {
          setError('No hotels found for this vendor');
          setLoading(false);
          return;
        }

        // Use the first hotel ID
        const id = hotels[0].id || '';
        setHotelId(id);

        // Fetch staff
        await fetchStaff(id);

        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching hotel ID:', err);
        setError(err.message || 'Failed to load hotel information');
        setLoading(false);
      }
    };

    fetchHotelId();
  }, []);

  useEffect(() => {
    // Generate week days when current week start changes
    const days = [];
    for (let i = 0; i < 7; i++) {
      days.push(addDays(currentWeekStart, i));
    }
    setWeekDays(days);

    // Fetch schedule for the week
    if (hotelId) {
      fetchScheduleForWeek(hotelId, currentWeekStart);
    }
  }, [currentWeekStart, hotelId]);

  const fetchStaff = async (hotelId: string) => {
    try {
      setLoadingStaff(true);

      const staff = await getStaffForHotel(hotelId, { status: 'active' });
      setStaffList(staff);

      setLoadingStaff(false);
    } catch (err: any) {
      console.error('Error fetching staff:', err);
      setError(err.message || 'Failed to load staff');
      setLoadingStaff(false);
    }
  };

  const fetchScheduleForWeek = async (hotelId: string, weekStart: Date) => {
    try {
      setLoadingSchedule(true);

      // Fetch schedules for the week
      const weekSchedules = await getStaffScheduleForWeek(hotelId, weekStart);
      setSchedules(weekSchedules);

      // Fetch shifts for each day of the week
      const shiftsData: { [key: string]: StaffShift[] } = {};

      for (const day of weekDays) {
        const dayShifts = await getShiftsForDate(hotelId, day);
        const dateKey = format(day, 'yyyy-MM-dd');
        shiftsData[dateKey] = dayShifts;
      }

      setShifts(shiftsData);
      setLoadingSchedule(false);
    } catch (err: any) {
      console.error('Error fetching schedule:', err);
      setError(err.message || 'Failed to load schedule');
      setLoadingSchedule(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);

    // Fetch time off requests when switching to the calendar or time off tab
    if ((newValue === 1 || newValue === 2) && hotelId) {
      fetchTimeOffRequests();
    }
  };

  // Handle availability tab change
  const handleAvailabilityTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setAvailabilityTabValue(newValue);
  };

  // Handle staff for availability change
  const handleStaffForAvailabilityChange = (event: SelectChangeEvent<string>) => {
    setSelectedStaffForAvailability(event.target.value);
  };

  const fetchTimeOffRequests = async () => {
    try {
      setLoadingTimeOff(true);

      const options: any = {};
      if (timeOffStatusFilter !== 'all') {
        options.status = timeOffStatusFilter;
      }

      const requests = await getTimeOffRequestsForHotel(hotelId, options);
      setTimeOffRequests(requests);

      setLoadingTimeOff(false);
    } catch (err: any) {
      console.error('Error fetching time off requests:', err);
      setError(err.message || 'Failed to load time off requests');
      setLoadingTimeOff(false);
    }
  };

  const handlePrevWeek = () => {
    setCurrentWeekStart(subWeeks(currentWeekStart, 1));
  };

  const handleNextWeek = () => {
    setCurrentWeekStart(addWeeks(currentWeekStart, 1));
  };

  const handleCurrentWeek = () => {
    setCurrentWeekStart(startOfWeek(new Date(), { weekStartsOn: 1 }));
  };

  const handleOpenShiftDialog = (date: Date, shift?: StaffShift) => {
    setSelectedDate(date);

    if (shift) {
      // Edit existing shift
      setSelectedShift(shift);
      setShiftFormData({
        staffId: shift.staffId,
        shiftType: shift.shiftType,
        startTime: format(shift.startTime.toDate(), 'HH:mm'),
        endTime: format(shift.endTime.toDate(), 'HH:mm'),
        notes: shift.notes || ''
      });
    } else {
      // Add new shift
      setSelectedShift(null);
      setShiftFormData({
        staffId: staffList.length > 0 ? staffList[0].id || '' : '',
        shiftType: SHIFT_TYPE_MORNING,
        startTime: shiftTimes[SHIFT_TYPE_MORNING].start,
        endTime: shiftTimes[SHIFT_TYPE_MORNING].end,
        notes: ''
      });
    }

    setShiftDialogOpen(true);
  };

  const handleCloseShiftDialog = () => {
    setShiftDialogOpen(false);
    setSelectedDate(null);
    setSelectedShift(null);
  };

  const handleShiftFormChange = (event: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = event.target;

    if (name) {
      setShiftFormData(prev => ({
        ...prev,
        [name]: value
      }));

      // Update shift times when shift type changes
      if (name === 'shiftType' && value !== SHIFT_TYPE_CUSTOM) {
        const shiftType = value as string;
        setShiftFormData(prev => ({
          ...prev,
          startTime: shiftTimes[shiftType].start,
          endTime: shiftTimes[shiftType].end
        }));
      }
    }
  };

  // Type-safe wrapper for Select onChange
  const handleShiftSelectChange = (event: SelectChangeEvent<string>) => {
    handleShiftFormChange(event as unknown as React.ChangeEvent<{ name?: string; value: unknown }>);
  };

  const handleOpenTimeOffDialog = (timeOff?: StaffTimeOff) => {
    if (timeOff) {
      // Edit existing time off request
      setSelectedTimeOff(timeOff);
      setTimeOffFormData({
        staffId: timeOff.staffId,
        startDate: format(timeOff.startDate.toDate(), 'yyyy-MM-dd'),
        endDate: format(timeOff.endDate.toDate(), 'yyyy-MM-dd'),
        type: timeOff.type,
        reason: timeOff.reason || ''
      });
    } else {
      // Add new time off request
      setSelectedTimeOff(null);
      setTimeOffFormData({
        staffId: staffList.length > 0 ? staffList[0].id || '' : '',
        startDate: format(new Date(), 'yyyy-MM-dd'),
        endDate: format(new Date(), 'yyyy-MM-dd'),
        type: TIME_OFF_TYPE_VACATION,
        reason: ''
      });
    }

    setTimeOffDialogOpen(true);
  };

  const handleCloseTimeOffDialog = () => {
    setTimeOffDialogOpen(false);
    setSelectedTimeOff(null);
  };

  const handleTimeOffFormChange = (event: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = event.target;

    if (name) {
      setTimeOffFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Type-safe wrapper for Select onChange
  const handleTimeOffSelectChange = (event: SelectChangeEvent<string>) => {
    handleTimeOffFormChange(event as unknown as React.ChangeEvent<{ name?: string; value: unknown }>);
  };

  const handleSubmitTimeOff = async () => {
    try {
      const staffMember = staffList.find(staff => staff.id === timeOffFormData.staffId);
      if (!staffMember) return;

      // Create date objects for start and end dates
      const startDate = parseISO(timeOffFormData.startDate);
      const endDate = parseISO(timeOffFormData.endDate);

      // Ensure end date is not before start date
      if (endDate < startDate) {
        setError('End date cannot be before start date');
        return;
      }

      await requestTimeOff({
        staffId: timeOffFormData.staffId,
        staffName: staffMember.name,
        hotelId,
        vendorId: auth.currentUser?.uid || '',
        startDate: Timestamp.fromDate(startDate),
        endDate: Timestamp.fromDate(endDate),
        type: timeOffFormData.type,
        reason: timeOffFormData.reason
      });

      // Refresh time off requests
      await fetchTimeOffRequests();
      handleCloseTimeOffDialog();
    } catch (err: any) {
      console.error('Error submitting time off request:', err);
      setError(err.message || 'Failed to submit time off request');
    }
  };

  const handleUpdateTimeOffStatus = async (timeOffId: string, status: string) => {
    try {
      if (!auth.currentUser) return;

      await updateTimeOffStatus(timeOffId, status, auth.currentUser.displayName || auth.currentUser.email || 'Unknown');

      // Refresh time off requests
      await fetchTimeOffRequests();
    } catch (err: any) {
      console.error('Error updating time off status:', err);
      setError(err.message || 'Failed to update time off status');
    }
  };

  const handleTimeOffStatusFilterChange = (event: SelectChangeEvent<string>) => {
    setTimeOffStatusFilter(event.target.value);

    // Refresh time off requests with new filter
    if (hotelId) {
      fetchTimeOffRequests();
    }
  };

  const getTimeOffTypeText = (type: string): string => {
    switch (type) {
      case TIME_OFF_TYPE_VACATION:
        return 'Vacation';
      case TIME_OFF_TYPE_SICK:
        return 'Sick Leave';
      case TIME_OFF_TYPE_PERSONAL:
        return 'Personal Leave';
      case TIME_OFF_TYPE_OTHER:
        return 'Other';
      default:
        return type;
    }
  };

  const getTimeOffStatusChip = (status: string) => {
    switch (status) {
      case TIME_OFF_STATUS_PENDING:
        return <Chip label="Pending" color="warning" size="small" />;
      case TIME_OFF_STATUS_APPROVED:
        return <Chip label="Approved" color="success" size="small" />;
      case TIME_OFF_STATUS_REJECTED:
        return <Chip label="Rejected" color="error" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const handleSubmitShift = async () => {
    if (!selectedDate || !shiftFormData.staffId) return;

    try {
      const staffMember = staffList.find(staff => staff.id === shiftFormData.staffId);
      if (!staffMember) return;

      // Create date objects for start and end times
      const dateStr = format(selectedDate, 'yyyy-MM-dd');
      const startTimeDate = parseISO(`${dateStr}T${shiftFormData.startTime}`);
      const endTimeDate = parseISO(`${dateStr}T${shiftFormData.endTime}`);

      // Handle overnight shifts
      let adjustedEndTimeDate = endTimeDate;
      if (endTimeDate <= startTimeDate) {
        adjustedEndTimeDate = addDays(endTimeDate, 1);
      }

      if (selectedShift && selectedShift.id) {
        // Update existing shift
        const updates = {
          staffId: shiftFormData.staffId,
          staffName: staffMember.name,
          shiftType: shiftFormData.shiftType,
          startTime: Timestamp.fromDate(startTimeDate),
          endTime: Timestamp.fromDate(adjustedEndTimeDate),
          notes: shiftFormData.notes
        };

        // Check for conflicts before updating
        const shiftWithId = {
          ...updates,
          id: selectedShift.id,
          hotelId,
          vendorId: auth.currentUser?.uid || '',
          date: Timestamp.fromDate(selectedDate),
          role: staffMember.role
        };

        const conflicts = await checkAllConflicts(shiftWithId, hotelId);

        if (conflicts.length > 0) {
          // Show conflicts to user
          setError(`Schedule conflicts detected: ${conflicts.length} issues found. Please check the Calendar view for details.`);
        }

        await updateStaffShift(selectedShift.id, updates);
      } else {
        // Create new shift
        const newShift = {
          staffId: shiftFormData.staffId,
          staffName: staffMember.name,
          hotelId,
          vendorId: auth.currentUser?.uid || '',
          date: Timestamp.fromDate(selectedDate),
          role: staffMember.role,
          shiftType: shiftFormData.shiftType,
          startTime: Timestamp.fromDate(startTimeDate),
          endTime: Timestamp.fromDate(adjustedEndTimeDate),
          notes: shiftFormData.notes
        };

        // Check for conflicts before creating
        const conflicts = await checkAllConflicts(newShift, hotelId);

        if (conflicts.length > 0) {
          // Show conflicts to user
          setError(`Schedule conflicts detected: ${conflicts.length} issues found. Please check the Calendar view for details.`);
        }

        await createStaffShift(newShift);
      }

      // Refresh schedule
      await fetchScheduleForWeek(hotelId, currentWeekStart);
      handleCloseShiftDialog();
    } catch (err: any) {
      console.error('Error saving shift:', err);
      setError(err.message || 'Failed to save shift');
    }
  };

  const handleDeleteShift = async (shiftId: string) => {
    try {
      await deleteStaffShift(shiftId);

      // Refresh schedule
      await fetchScheduleForWeek(hotelId, currentWeekStart);
    } catch (err: any) {
      console.error('Error deleting shift:', err);
      setError(err.message || 'Failed to delete shift');
    }
  };

  const getStaffNameById = (staffId: string): string => {
    const staff = staffList.find(s => s.id === staffId);
    return staff ? staff.name : 'Unknown';
  };

  const getShiftColor = (shiftType: string): string => {
    switch (shiftType) {
      case SHIFT_TYPE_MORNING:
        return '#e3f2fd'; // Light blue
      case SHIFT_TYPE_AFTERNOON:
        return '#fff8e1'; // Light amber
      case SHIFT_TYPE_NIGHT:
        return '#e8eaf6'; // Light indigo
      case SHIFT_TYPE_CUSTOM:
        return '#f1f8e9'; // Light green
      default:
        return '#f5f5f5'; // Light grey
    }
  };

  const formatShiftTime = (shift: StaffShift): string => {
    const startTime = format(shift.startTime.toDate(), 'HH:mm');
    const endTime = format(shift.endTime.toDate(), 'HH:mm');
    return `${startTime} - ${endTime}`;
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Staff Scheduling
      </Typography>

      <Tabs value={tabValue} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tab label="Weekly Schedule" />
        <Tab label="Calendar View" />
        <Tab label="Time Off Requests" />
        <Tab label="Schedule Templates" />
        <Tab label="Staff Availability" />
        <Tab label="Auto Scheduler" />
      </Tabs>

      <TabPanel value={tabValue} index={0}>
        {/* Weekly Schedule Tab */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton onClick={handlePrevWeek}>
              <PrevIcon />
            </IconButton>
            <Typography variant="h6" sx={{ mx: 2 }}>
              {format(currentWeekStart, 'MMM d')} - {format(addDays(currentWeekStart, 6), 'MMM d, yyyy')}
            </Typography>
            <IconButton onClick={handleNextWeek}>
              <NextIcon />
            </IconButton>
            <Button
              variant="outlined"
              startIcon={<TodayIcon />}
              onClick={handleCurrentWeek}
              sx={{ ml: 2 }}
            >
              Current Week
            </Button>
          </Box>

          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => fetchScheduleForWeek(hotelId, currentWeekStart)}
            disabled={loadingSchedule}
          >
            Refresh
          </Button>
        </Box>

        {loadingSchedule ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Paper sx={{ overflowX: 'auto' }}>
            <Box sx={{ minWidth: 800, p: 2 }}>
              {/* Week header */}
              <Grid container spacing={1}>
                <Grid item xs={2}>
                  <Typography variant="subtitle1" fontWeight="bold">
                    Staff
                  </Typography>
                </Grid>
                {weekDays.map((day, index) => (
                  <Grid item xs={10/7} key={index}>
                    <Box sx={{
                      textAlign: 'center',
                      p: 1,
                      bgcolor: 'primary.light',
                      color: 'primary.contrastText',
                      borderRadius: 1
                    }}>
                      <Typography variant="subtitle2">
                        {format(day, 'EEE')}
                      </Typography>
                      <Typography variant="body2">
                        {format(day, 'MMM d')}
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>

              {/* Staff rows */}
              {staffList.length === 0 ? (
                <Box sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="body1">
                    No staff members found. Please add staff members first.
                  </Typography>
                </Box>
              ) : (
                staffList.map((staff) => (
                  <Grid container spacing={1} key={staff.id} sx={{ mt: 1 }}>
                    <Grid item xs={2}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <PersonIcon sx={{ mr: 1 }} />
                        <Typography variant="body1">
                          {staff.name}
                        </Typography>
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        {staff.role.replace(/_/g, ' ')}
                      </Typography>
                    </Grid>

                    {weekDays.map((day, index) => {
                      const dateKey = format(day, 'yyyy-MM-dd');
                      const dayShifts = shifts[dateKey] || [];
                      const staffShifts = dayShifts.filter(shift => shift.staffId === staff.id);

                      return (
                        <Grid item xs={10/7} key={index}>
                          <Paper
                            variant="outlined"
                            sx={{
                              p: 1,
                              minHeight: 80,
                              position: 'relative',
                              '&:hover': {
                                bgcolor: 'action.hover'
                              }
                            }}
                          >
                            {staffShifts.length > 0 ? (
                              <Box>
                                {staffShifts.map((shift, shiftIndex) => (
                                  <Box
                                    key={shiftIndex}
                                    sx={{
                                      p: 1,
                                      mb: 1,
                                      borderRadius: 1,
                                      bgcolor: getShiftColor(shift.shiftType),
                                      position: 'relative'
                                    }}
                                  >
                                    <Typography variant="caption" fontWeight="bold">
                                      {shift.shiftType.replace(/_/g, ' ')}
                                    </Typography>
                                    <Typography variant="caption" display="block">
                                      {formatShiftTime(shift)}
                                    </Typography>
                                    {shift.notes && (
                                      <Tooltip title={shift.notes}>
                                        <Typography
                                          variant="caption"
                                          sx={{
                                            display: 'block',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            whiteSpace: 'nowrap'
                                          }}
                                        >
                                          {shift.notes}
                                        </Typography>
                                      </Tooltip>
                                    )}
                                    <Box
                                      sx={{
                                        position: 'absolute',
                                        top: 2,
                                        right: 2,
                                        opacity: 0,
                                        transition: 'opacity 0.2s',
                                        '.MuiPaper-root:hover &': {
                                          opacity: 1
                                        }
                                      }}
                                    >
                                      <IconButton
                                        size="small"
                                        onClick={() => handleOpenShiftDialog(day, shift)}
                                      >
                                        <EditIcon fontSize="small" />
                                      </IconButton>
                                      <IconButton
                                        size="small"
                                        color="error"
                                        onClick={() => shift.id && handleDeleteShift(shift.id)}
                                      >
                                        <DeleteIcon fontSize="small" />
                                      </IconButton>
                                    </Box>
                                  </Box>
                                ))}
                              </Box>
                            ) : (
                              <Box
                                sx={{
                                  height: '100%',
                                  display: 'flex',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  opacity: 0.5
                                }}
                              >
                                <Typography variant="caption" color="text.secondary">
                                  No shifts
                                </Typography>
                              </Box>
                            )}

                            <IconButton
                              size="small"
                              sx={{
                                position: 'absolute',
                                bottom: 2,
                                right: 2,
                                opacity: 0,
                                transition: 'opacity 0.2s',
                                '.MuiPaper-root:hover &': {
                                  opacity: 1
                                }
                              }}
                              onClick={() => handleOpenShiftDialog(day)}
                            >
                              <AddIcon fontSize="small" />
                            </IconButton>
                          </Paper>
                        </Grid>
                      );
                    })}
                  </Grid>
                ))
              )}
            </Box>
          </Paper>
        )}

        {/* Shift Dialog */}
        <Dialog open={shiftDialogOpen} onClose={handleCloseShiftDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            {selectedShift ? 'Edit Shift' : 'Add Shift'} - {selectedDate && format(selectedDate, 'EEEE, MMMM d, yyyy')}
          </DialogTitle>
          <DialogContent dividers>
            <FormControl fullWidth margin="normal">
              <InputLabel id="staff-label">Staff Member</InputLabel>
              <Select
                labelId="staff-label"
                name="staffId"
                value={shiftFormData.staffId}
                label="Staff Member"
                onChange={handleShiftSelectChange}
              >
                {staffList.map((staff) => (
                  <MenuItem key={staff.id} value={staff.id || ''}>
                    {staff.name} ({staff.role.replace(/_/g, ' ')})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth margin="normal">
              <InputLabel id="shift-type-label">Shift Type</InputLabel>
              <Select
                labelId="shift-type-label"
                name="shiftType"
                value={shiftFormData.shiftType}
                label="Shift Type"
                onChange={handleShiftSelectChange}
              >
                <MenuItem value={SHIFT_TYPE_MORNING}>Morning (6:00 - 14:00)</MenuItem>
                <MenuItem value={SHIFT_TYPE_AFTERNOON}>Afternoon (14:00 - 22:00)</MenuItem>
                <MenuItem value={SHIFT_TYPE_NIGHT}>Night (22:00 - 6:00)</MenuItem>
                <MenuItem value={SHIFT_TYPE_CUSTOM}>Custom</MenuItem>
              </Select>
            </FormControl>

            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Start Time"
                  name="startTime"
                  type="time"
                  value={shiftFormData.startTime}
                  onChange={handleShiftFormChange}
                  InputLabelProps={{ shrink: true }}
                  inputProps={{ step: 300 }}
                  disabled={shiftFormData.shiftType !== SHIFT_TYPE_CUSTOM}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="End Time"
                  name="endTime"
                  type="time"
                  value={shiftFormData.endTime}
                  onChange={handleShiftFormChange}
                  InputLabelProps={{ shrink: true }}
                  inputProps={{ step: 300 }}
                  disabled={shiftFormData.shiftType !== SHIFT_TYPE_CUSTOM}
                />
              </Grid>
            </Grid>

            <TextField
              fullWidth
              label="Notes"
              name="notes"
              value={shiftFormData.notes}
              onChange={handleShiftFormChange}
              margin="normal"
              multiline
              rows={3}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseShiftDialog}>Cancel</Button>
            <Button
              onClick={handleSubmitShift}
              variant="contained"
              disabled={!shiftFormData.staffId}
            >
              {selectedShift ? 'Save Changes' : 'Add Shift'}
            </Button>
          </DialogActions>
        </Dialog>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {/* Calendar View Tab */}
        <StaffCalendarWrapper
          hotelId={hotelId}
          vendorId={auth.currentUser?.uid || ''}
          staffList={staffList}
          shifts={Object.values(shifts).flat()}
          timeOffRequests={timeOffRequests}
          loading={loadingSchedule}
          error={error}
          onRefresh={() => fetchScheduleForWeek(hotelId, currentWeekStart)}
          onShiftCreate={async (shiftData) => {
            try {
              await createStaffShift(shiftData);
              await fetchScheduleForWeek(hotelId, currentWeekStart);
            } catch (err: any) {
              console.error('Error creating shift:', err);
              setError(err.message || 'Failed to create shift');
            }
          }}
          onShiftUpdate={async (shiftId, updates) => {
            try {
              await updateStaffShift(shiftId, updates);
              await fetchScheduleForWeek(hotelId, currentWeekStart);
            } catch (err: any) {
              console.error('Error updating shift:', err);
              setError(err.message || 'Failed to update shift');
            }
          }}
          onShiftDelete={async (shiftId) => {
            try {
              await deleteStaffShift(shiftId);
              await fetchScheduleForWeek(hotelId, currentWeekStart);
            } catch (err: any) {
              console.error('Error deleting shift:', err);
              setError(err.message || 'Failed to delete shift');
            }
          }}
        />
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        {/* Time Off Requests Tab */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            Time Off Requests
          </Typography>

          <Box>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenTimeOffDialog()}
              sx={{ mr: 2 }}
            >
              New Request
            </Button>

            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchTimeOffRequests}
              disabled={loadingTimeOff}
            >
              Refresh
            </Button>
          </Box>
        </Box>

        <Paper sx={{ mb: 3, p: 2 }}>
          <FormControl fullWidth>
            <InputLabel id="time-off-status-filter-label">Status</InputLabel>
            <Select
              labelId="time-off-status-filter-label"
              value={timeOffStatusFilter}
              label="Status"
              onChange={handleTimeOffStatusFilterChange}
              startAdornment={
                <InputAdornment position="start">
                  <FilterIcon />
                </InputAdornment>
              }
            >
              <MenuItem value="all">All Statuses</MenuItem>
              <MenuItem value={TIME_OFF_STATUS_PENDING}>Pending</MenuItem>
              <MenuItem value={TIME_OFF_STATUS_APPROVED}>Approved</MenuItem>
              <MenuItem value={TIME_OFF_STATUS_REJECTED}>Rejected</MenuItem>
            </Select>
          </FormControl>
        </Paper>

        {loadingTimeOff ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
            <CircularProgress />
          </Box>
        ) : timeOffRequests.length === 0 ? (
          <Paper sx={{ p: 5, textAlign: 'center' }}>
            <Typography variant="h6" gutterBottom>
              No time off requests found
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {timeOffStatusFilter === 'all'
                ? 'There are no time off requests in the system.'
                : `There are no ${timeOffStatusFilter} time off requests.`}
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenTimeOffDialog()}
              sx={{ mt: 3 }}
            >
              Create New Request
            </Button>
          </Paper>
        ) : (
          <Paper>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Staff Member</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Dates</TableCell>
                    <TableCell>Reason</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {timeOffRequests.map((request) => (
                    <TableRow key={request.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <PersonIcon sx={{ mr: 1 }} />
                          <Typography variant="body2">
                            {request.staffName}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        {getTimeOffTypeText(request.type)}
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">
                            From: {format(request.startDate.toDate(), 'MMM d, yyyy')}
                          </Typography>
                          <Typography variant="body2">
                            To: {format(request.endDate.toDate(), 'MMM d, yyyy')}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        {request.reason || 'No reason provided'}
                      </TableCell>
                      <TableCell>
                        {getTimeOffStatusChip(request.status)}
                        {request.approvedBy && request.status !== TIME_OFF_STATUS_PENDING && (
                          <Typography variant="caption" display="block" color="text.secondary" sx={{ mt: 0.5 }}>
                            By: {request.approvedBy}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        {request.status === TIME_OFF_STATUS_PENDING ? (
                          <Box>
                            <Tooltip title="Approve">
                              <IconButton
                                color="success"
                                onClick={() => request.id && handleUpdateTimeOffStatus(request.id, TIME_OFF_STATUS_APPROVED)}
                              >
                                <CheckCircleIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Reject">
                              <IconButton
                                color="error"
                                onClick={() => request.id && handleUpdateTimeOffStatus(request.id, TIME_OFF_STATUS_REJECTED)}
                              >
                                <CancelIcon />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        ) : (
                          <Typography variant="caption" color="text.secondary">
                            {request.status === TIME_OFF_STATUS_APPROVED ? 'Approved' : 'Rejected'} on {request.approvedAt && format(request.approvedAt.toDate(), 'MMM d, yyyy')}
                          </Typography>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        )}

        {/* Time Off Dialog */}
        <Dialog open={timeOffDialogOpen} onClose={handleCloseTimeOffDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            Request Time Off
          </DialogTitle>
          <DialogContent dividers>
            <FormControl fullWidth margin="normal">
              <InputLabel id="staff-label">Staff Member</InputLabel>
              <Select
                labelId="staff-label"
                name="staffId"
                value={timeOffFormData.staffId}
                label="Staff Member"
                onChange={handleTimeOffSelectChange}
              >
                {staffList.map((staff) => (
                  <MenuItem key={staff.id} value={staff.id || ''}>
                    {staff.name} ({staff.role.replace(/_/g, ' ')})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth margin="normal">
              <InputLabel id="time-off-type-label">Type</InputLabel>
              <Select
                labelId="time-off-type-label"
                name="type"
                value={timeOffFormData.type}
                label="Type"
                onChange={handleTimeOffSelectChange}
              >
                <MenuItem value={TIME_OFF_TYPE_VACATION}>Vacation</MenuItem>
                <MenuItem value={TIME_OFF_TYPE_SICK}>Sick Leave</MenuItem>
                <MenuItem value={TIME_OFF_TYPE_PERSONAL}>Personal Leave</MenuItem>
                <MenuItem value={TIME_OFF_TYPE_OTHER}>Other</MenuItem>
              </Select>
            </FormControl>

            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Start Date"
                  name="startDate"
                  type="date"
                  value={timeOffFormData.startDate}
                  onChange={handleTimeOffFormChange}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="End Date"
                  name="endDate"
                  type="date"
                  value={timeOffFormData.endDate}
                  onChange={handleTimeOffFormChange}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </Grid>

            <TextField
              fullWidth
              label="Reason"
              name="reason"
              value={timeOffFormData.reason}
              onChange={handleTimeOffFormChange}
              margin="normal"
              multiline
              rows={3}
              placeholder="Please provide a reason for your time off request"
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseTimeOffDialog}>Cancel</Button>
            <Button
              onClick={handleSubmitTimeOff}
              variant="contained"
              disabled={!timeOffFormData.staffId}
            >
              Submit Request
            </Button>
          </DialogActions>
        </Dialog>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        {/* Schedule Templates Tab */}
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <ScheduleTemplates
                hotelId={hotelId}
                vendorId={auth.currentUser?.uid || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <ApplyTemplate
                hotelId={hotelId}
                vendorId={auth.currentUser?.uid || ''}
                onSuccess={() => fetchScheduleForWeek(hotelId, currentWeekStart)}
              />
            </Grid>
          </Grid>
        </Box>
      </TabPanel>

      <TabPanel value={tabValue} index={4}>
        {/* Staff Availability Tab */}
        <Box sx={{ mb: 3 }}>
          <Tabs value={availabilityTabValue} onChange={handleAvailabilityTabChange} sx={{ mb: 3 }}>
            <Tab label="Availability Calendar" />
            <Tab label="Manage Staff Availability" />
          </Tabs>

          <TabPanel value={availabilityTabValue} index={0}>
            {/* Availability Calendar View */}
            <AvailabilityCalendar
              hotelId={hotelId}
              staffList={staffList}
            />
          </TabPanel>

          <TabPanel value={availabilityTabValue} index={1}>
            {/* Manage Staff Availability */}
            <Box sx={{ mb: 3 }}>
              <FormControl fullWidth>
                <InputLabel id="staff-availability-label">Select Staff Member</InputLabel>
                <Select
                  labelId="staff-availability-label"
                  value={selectedStaffForAvailability}
                  label="Select Staff Member"
                  onChange={handleStaffForAvailabilityChange}
                >
                  {staffList.map((staff) => (
                    <MenuItem key={staff.id} value={staff.id || ''}>
                      {staff.name} ({staff.role?.replace(/_/g, ' ') || 'Staff'})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>

            {selectedStaffForAvailability ? (
              <StaffAvailability
                staffId={selectedStaffForAvailability}
                staffName={staffList.find(s => s.id === selectedStaffForAvailability)?.name || 'Staff Member'}
                hotelId={hotelId}
              />
            ) : (
              <Paper sx={{ p: 5, textAlign: 'center' }}>
                <Typography variant="h6" gutterBottom>
                  No staff member selected
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Please select a staff member to manage their availability.
                </Typography>
              </Paper>
            )}
          </TabPanel>
        </Box>
      </TabPanel>

      <TabPanel value={tabValue} index={5}>
        {/* Auto Scheduler Tab */}
        <AutoScheduler
          hotelId={hotelId}
          vendorId={auth.currentUser?.uid || ''}
          onSuccess={() => fetchScheduleForWeek(hotelId, currentWeekStart)}
        />
      </TabPanel>
    </Box>
  );
};

export default StaffScheduling;
