import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/core/theme/app_theme.dart';
import 'package:hotel_booking_app/presentation/screens/auth/login_screen.dart';
import 'package:hotel_booking_app/presentation/screens/main_navigation_screen.dart';
import 'package:provider/provider.dart';
import 'package:hotel_booking_app/data/services/auth_service.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: AppConstants.longAnimationDuration,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );

    _animationController.forward();

    // Initialize app with error handling
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Simulate initialization process with a shorter delay
      await Future.delayed(const Duration(seconds: 1));

      // Check if Firebase is initialized
      if (Firebase.apps.isEmpty) {
        debugPrint("Firebase not initialized in splash screen");
        // Don't throw, just continue with a warning
        setState(() {
          _errorMessage =
              "Warning: Firebase not initialized. Some features may not work.";
        });
        await Future.delayed(const Duration(seconds: 1));
      } else {
        debugPrint(
            "Firebase initialized with ${Firebase.apps.length} apps in splash screen");
      }

      // Navigate after initialization attempt
      _navigateToNextScreen();
    } catch (e) {
      // Handle initialization error
      debugPrint("Error in splash screen initialization: $e");
      setState(() {
        _isLoading = false;
        _errorMessage = "Could not initialize app: ${e.toString()}";
      });

      // Still navigate after a delay, even if there's an error
      Future.delayed(const Duration(seconds: 2), () {
        _navigateToNextScreen();
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _navigateToNextScreen() {
    // Only navigate if the widget is still mounted
    if (!mounted) return;

    try {
      final authService = Provider.of<AuthService>(context, listen: false);

      // Check if auth service is properly initialized
      if (!authService.isInitialized) {
        debugPrint("Auth service not initialized, navigating to login screen");
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const LoginScreen()),
        );
        return;
      }

      if (authService.isAuthenticated) {
        debugPrint("User is authenticated, navigating to main screen");
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const MainNavigationScreen()),
        );
      } else {
        debugPrint("User is not authenticated, navigating to login screen");
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const LoginScreen()),
        );
      }
    } catch (e) {
      // Log the error
      debugPrint("Error during navigation: $e");

      // Fallback to login screen if there's an error
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (_) => const LoginScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryColor,
              AppTheme.primaryColor.withAlpha(204),
              AppTheme.accentColor,
            ],
          ),
        ),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SafeArea(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Link In Blink logo with shadow
                  Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(40),
                          blurRadius: 20,
                          spreadRadius: 2,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Image.asset(
                      'assets/images/link_in_blink_white.png',
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        // Fallback if image fails to load
                        return Icon(
                          Icons.hotel,
                          size: 100,
                          color: Colors.white,
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 40),

                  // App name with enhanced typography
                  Text(
                    AppConstants.appName.toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 2.0,
                      shadows: [
                        Shadow(
                          color: Colors.black26,
                          blurRadius: 5,
                          offset: Offset(0, 3),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Tagline with enhanced styling
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(38),
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: const Text(
                      'Your Premium Hotel Booking Experience',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),

                  const SizedBox(height: 40),

                  // Show error message if there is one
                  if (_errorMessage != null) ...[
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 24),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.withAlpha(51), // 0.2 opacity = 51/255
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _errorMessage!,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],

                  // Custom loading indicator
                  if (_isLoading)
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(51),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 3,
                      ),
                    ),

                  const SizedBox(height: 24),

                  // Loading text
                  Text(
                    _isLoading
                        ? 'Loading amazing deals for you...'
                        : 'Continuing to app...',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
