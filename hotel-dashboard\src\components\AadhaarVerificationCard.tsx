import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  <PERSON>,
  Divider,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  VerifiedUser as VerifiedIcon,
  ErrorOutline as ErrorIcon,
  HourglassEmpty as PendingIcon,
  Help as UnknownIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';

// Interface for Aadhaar verification data
export interface AadhaarVerificationData {
  id: string;
  userId: string;
  maskedAadhaarNumber: string;
  fullName: string;
  dateOfBirth?: Date;
  dob?: Date;
  gender?: string;
  address?: string;
  status: 'verified' | 'pending' | 'failed' | 'notVerified';
  verifiedAt?: Date;
  verificationMethod?: string;
  referenceId?: string;
  verificationDetails?: any;
  createdAt?: Date;
  updatedAt?: Date;
  photoUrl?: string;
  phoneNumber?: string;
  email?: string;
  adminComment?: string;
}

interface AadhaarVerificationCardProps {
  verification: AadhaarVerificationData | null;
  loading?: boolean;
  error?: string;
  onRefresh?: () => void;
}

const AadhaarVerificationCard: React.FC<AadhaarVerificationCardProps> = ({
  verification,
  loading = false,
  error,
  onRefresh
}) => {
  const [detailsOpen, setDetailsOpen] = useState(false);

  const handleOpenDetails = () => {
    setDetailsOpen(true);
  };

  const handleCloseDetails = () => {
    setDetailsOpen(false);
  };

  // Get status color and icon
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'verified':
        return {
          color: 'success',
          icon: <VerifiedIcon />,
          text: 'Verified'
        };
      case 'pending':
        return {
          color: 'warning',
          icon: <PendingIcon />,
          text: 'Pending'
        };
      case 'failed':
        return {
          color: 'error',
          icon: <ErrorIcon />,
          text: 'Failed'
        };
      case 'notVerified':
        return {
          color: 'default',
          icon: <UnknownIcon />,
          text: 'Not Verified'
        };
      default:
        return {
          color: 'default',
          icon: <UnknownIcon />,
          text: 'Unknown'
        };
    }
  };

  // Format date
  const formatDate = (date?: Date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Card sx={{ mb: 2, height: '100%' }}>
        <CardContent sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
          <CircularProgress size={40} />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
          {onRefresh && (
            <Button variant="outlined" onClick={onRefresh} size="small">
              Retry
            </Button>
          )}
        </CardContent>
      </Card>
    );
  }

  if (!verification) {
    return (
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Box display="flex" alignItems="center" mb={2}>
            <UnknownIcon color="disabled" sx={{ mr: 1 }} />
            <Typography variant="h6">Aadhaar Verification</Typography>
          </Box>
          <Alert severity="info">
            No verification data available for this user.
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const statusInfo = getStatusInfo(verification.status);

  return (
    <>
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
            <Box display="flex" alignItems="center">
              {statusInfo.icon}
              <Typography variant="h6" sx={{ ml: 1 }}>
                Aadhaar Verification
              </Typography>
            </Box>
            <Chip
              label={statusInfo.text}
              color={statusInfo.color as any}
              size="small"
            />
          </Box>

          <Divider sx={{ my: 2 }} />

          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Name
              </Typography>
              <Typography variant="body1" gutterBottom>
                {verification.fullName}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Aadhaar Number
              </Typography>
              <Typography variant="body1" gutterBottom>
                {verification.maskedAadhaarNumber}
              </Typography>
            </Grid>
            {verification.verifiedAt && (
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Verified On
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {formatDate(verification.verifiedAt)}
                </Typography>
              </Grid>
            )}
          </Grid>

          <Box display="flex" justifyContent="flex-end" mt={2}>
            <Button
              variant="outlined"
              size="small"
              startIcon={<ViewIcon />}
              onClick={handleOpenDetails}
            >
              View Details
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Details Dialog */}
      <Dialog open={detailsOpen} onClose={handleCloseDetails} maxWidth="md" fullWidth>
        <DialogTitle>
          Aadhaar Verification Details
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>
                Personal Information
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Full Name
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {verification.fullName}
                </Typography>
              </Box>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Aadhaar Number
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {verification.maskedAadhaarNumber}
                </Typography>
              </Box>
              {verification.gender && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Gender
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {verification.gender}
                  </Typography>
                </Box>
              )}
              {verification.dateOfBirth && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Date of Birth
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formatDate(verification.dateOfBirth)}
                  </Typography>
                </Box>
              )}
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>
                Verification Information
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Status
                </Typography>
                <Chip
                  label={statusInfo.text}
                  color={statusInfo.color as any}
                  size="small"
                  icon={statusInfo.icon}
                />
              </Box>
              {verification.verifiedAt && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Verified On
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formatDate(verification.verifiedAt)}
                  </Typography>
                </Box>
              )}
              {verification.verificationMethod && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Verification Method
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {verification.verificationMethod}
                  </Typography>
                </Box>
              )}
              {verification.referenceId && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Reference ID
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {verification.referenceId}
                  </Typography>
                </Box>
              )}
            </Grid>
            {verification.address && (
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  Address
                </Typography>
                <Typography variant="body1" paragraph>
                  {verification.address}
                </Typography>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDetails}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AadhaarVerificationCard;
