import React from 'react';
import { Box, Typography, Paper, Button } from '@mui/material';
import { useNavigate } from 'react-router-dom';

const BookingsUnavailable: React.FC = () => {
  const navigate = useNavigate();
  
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Bookings Management
      </Typography>
      
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h5" gutterBottom>
          Bookings Module Temporarily Unavailable
        </Typography>
        <Typography variant="body1" paragraph>
          We're sorry, but the bookings management module is temporarily unavailable.
        </Typography>
        <Typography variant="body2" paragraph color="text.secondary">
          Our team is working to restore this functionality as soon as possible.
        </Typography>
        <Box sx={{ mt: 3 }}>
          <Button 
            variant="contained" 
            color="primary" 
            onClick={() => navigate('/vendor')}
            sx={{ mx: 1 }}
          >
            Return to Dashboard
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default BookingsUnavailable;
