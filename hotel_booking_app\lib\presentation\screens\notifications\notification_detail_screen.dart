import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:hotel_booking_app/data/models/notification_model.dart';
import 'package:hotel_booking_app/data/services/notification_service.dart';
import 'package:hotel_booking_app/data/services/booking_service.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_app_bar.dart';
import 'package:hotel_booking_app/presentation/screens/booking/booking_details_screen.dart';
import 'package:hotel_booking_app/presentation/screens/hotel/hotel_details_screen.dart';

class NotificationDetailScreen extends StatefulWidget {
  final NotificationModel notification;

  const NotificationDetailScreen({
    super.key,
    required this.notification,
  });

  @override
  State<NotificationDetailScreen> createState() =>
      _NotificationDetailScreenState();
}

class _NotificationDetailScreenState extends State<NotificationDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Notification Details',
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and title
            Row(
              children: [
                _buildNotificationIcon(widget.notification.type),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.notification.title,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        DateFormat('EEEE, MMMM d, yyyy • h:mm a')
                            .format(widget.notification.createdAt),
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Message content
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Text(
                widget.notification.message,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade800,
                  height: 1.5,
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Related information
            if (widget.notification.bookingId != null ||
                widget.notification.hotelId != null)
              _buildRelatedInfoSection(context),

            const SizedBox(height: 24),

            // Action buttons
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationIcon(NotificationType type) {
    IconData iconData;
    Color iconColor;

    switch (type) {
      case NotificationType.booking:
        iconData = Icons.hotel;
        iconColor = Colors.blue;
        break;
      case NotificationType.promotion:
        iconData = Icons.local_offer;
        iconColor = Colors.purple;
        break;
      case NotificationType.system:
        iconData = Icons.info;
        iconColor = Colors.teal;
        break;
      case NotificationType.loyalty:
        iconData = Icons.card_membership;
        iconColor = Colors.amber;
        break;
      case NotificationType.payment:
        iconData = Icons.payment;
        iconColor = Colors.green;
        break;
      case NotificationType.reminder:
        iconData = Icons.alarm;
        iconColor = Colors.orange;
        break;
      case NotificationType.alert:
        iconData = Icons.warning;
        iconColor = Colors.red;
        break;
      case NotificationType.serviceRequest:
        iconData = Icons.room_service;
        iconColor = Colors.deepPurple;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: iconColor.withAlpha(25),
        shape: BoxShape.circle,
      ),
      child: Icon(iconData, color: iconColor, size: 28),
    );
  }

  Widget _buildRelatedInfoSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Related Information',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),

        // Booking information
        if (widget.notification.bookingId != null)
          Card(
            elevation: 1,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ListTile(
              leading:
                  const Icon(Icons.confirmation_number, color: Colors.blue),
              title: const Text('Booking Details'),
              subtitle: Text('Booking ID: ${widget.notification.bookingId}'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                _navigateToBookingDetails(
                    context, widget.notification.bookingId!);
              },
            ),
          ),

        // Hotel information
        if (widget.notification.hotelId != null)
          Card(
            elevation: 1,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ListTile(
              leading: const Icon(Icons.hotel, color: Colors.green),
              title: const Text('Hotel Details'),
              subtitle: Text('Hotel ID: ${widget.notification.hotelId}'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                _navigateToHotelDetails(context, widget.notification.hotelId!);
              },
            ),
          ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: const Icon(Icons.arrow_back),
            label: const Text('Back'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () {
              _deleteNotification(context);
            },
            icon: const Icon(Icons.delete),
            label: const Text('Delete'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _navigateToBookingDetails(BuildContext context, String bookingId) {
    final bookingService = Provider.of<BookingService>(context, listen: false);
    bookingService.fetchBookingById(bookingId).then((_) {
      if (!mounted) return;

      final booking = bookingService.selectedBooking;
      if (booking != null) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => BookingDetailsScreen(bookingId: booking.id),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Booking not found')),
        );
      }
    }).catchError((error) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading booking: ${error.toString()}')),
      );
    });
  }

  void _navigateToHotelDetails(BuildContext context, String hotelId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HotelDetailsScreen(hotelId: hotelId),
      ),
    );
  }

  void _deleteNotification(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Notification'),
        content:
            const Text('Are you sure you want to delete this notification?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              final notificationService =
                  Provider.of<NotificationService>(context, listen: false);
              notificationService
                  .deleteNotification(widget.notification.id)
                  .then((_) {
                if (!mounted) return;
                Navigator.pop(context); // Go back to notifications list
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Notification deleted')),
                );
              }).catchError((error) {
                if (!mounted) return;
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                      content: Text(
                          'Error deleting notification: ${error.toString()}')),
                );
              });
            },
            child: const Text('DELETE'),
          ),
        ],
      ),
    );
  }
}
