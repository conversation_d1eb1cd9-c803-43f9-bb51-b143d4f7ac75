import { 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword, 
  signOut as firebaseSignOut,
  sendPasswordResetEmail,
  updateProfile,
  User
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db } from './config';

// User roles
export const ROLE_SUPER_ADMIN = 'super_admin';
export const ROLE_VENDOR = 'vendor';
export const ROLE_STAFF = 'staff';

// Sign in with email and password
export const signIn = async (email: string, password: string) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    // Get user data from Firestore
    const userDoc = await getDoc(doc(db, 'users', user.uid));
    
    if (!userDoc.exists()) {
      throw new Error('User data not found');
    }
    
    const userData = userDoc.data();
    
    // Check if user has admin, vendor, or staff role
    if (
      userData.role !== ROLE_SUPER_ADMIN && 
      userData.role !== ROLE_VENDOR && 
      userData.role !== ROLE_STAFF
    ) {
      await firebaseSignOut(auth);
      throw new Error('You do not have permission to access this dashboard');
    }
    
    return {
      user,
      userData
    };
  } catch (error: any) {
    throw new Error(error.message);
  }
};

// Sign out
export const signOut = async () => {
  try {
    await firebaseSignOut(auth);
  } catch (error: any) {
    throw new Error(error.message);
  }
};

// Register new user (admin only)
export const registerUser = async (
  email: string, 
  password: string, 
  displayName: string, 
  role: string,
  createdBy: string
) => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    // Update profile
    await updateProfile(user, {
      displayName
    });
    
    // Create user document in Firestore
    await setDoc(doc(db, 'users', user.uid), {
      uid: user.uid,
      email,
      displayName,
      role,
      createdBy,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    return user;
  } catch (error: any) {
    throw new Error(error.message);
  }
};

// Reset password
export const resetPassword = async (email: string) => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error: any) {
    throw new Error(error.message);
  }
};

// Update user profile
export const updateUserProfile = async (
  user: User,
  data: {
    displayName?: string;
    photoURL?: string;
    phoneNumber?: string;
  }
) => {
  try {
    // Update Firebase Auth profile
    if (data.displayName || data.photoURL) {
      await updateProfile(user, {
        displayName: data.displayName,
        photoURL: data.photoURL
      });
    }
    
    // Update Firestore document
    const updateData: any = {
      updatedAt: serverTimestamp()
    };
    
    if (data.displayName) {
      updateData.displayName = data.displayName;
    }
    
    if (data.photoURL) {
      updateData.photoURL = data.photoURL;
    }
    
    if (data.phoneNumber) {
      updateData.phoneNumber = data.phoneNumber;
    }
    
    await updateDoc(doc(db, 'users', user.uid), updateData);
  } catch (error: any) {
    throw new Error(error.message);
  }
};
