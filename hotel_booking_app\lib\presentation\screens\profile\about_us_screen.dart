import 'package:flutter/material.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/core/theme/app_theme.dart';
import 'package:hotel_booking_app/data/services/content_service.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_app_bar.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutUsScreen extends StatelessWidget {
  const AboutUsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'About Us',
        showBackButton: true,
      ),
      body: Consumer<ContentService>(
        builder: (context, contentService, _) {
          if (contentService.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (contentService.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 60,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading content',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    contentService.error!,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      contentService.fetchAboutUs();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final aboutUs = contentService.aboutUs;
          if (aboutUs == null) {
            return const Center(
              child: Text('No content available'),
            );
          }

          final sections = aboutUs['sections'] as List;
          final contactInfo = aboutUs['contactInfo'] as Map<String, dynamic>;

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with Logo
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppTheme.primaryColor,
                        AppTheme.accentColor,
                      ],
                    ),
                  ),
                  child: Column(
                    children: [
                      Image.asset(
                        'assets/images/link_in_blink.png',
                        height: 100,
                        fit: BoxFit.contain,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        aboutUs['title'] ?? 'LINK IN BLINK HOTEL',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 1.5,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Version ${AppConstants.appVersion}',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),

                // Content Sections
                ...sections.map((section) {
                  return _buildSection(
                    context,
                    title: section['title'],
                    content: section['content'],
                    icon: _getIconData(section['icon']),
                  );
                }),

                // Contact Information
                _buildSection(
                  context,
                  title: 'Contact Us',
                  content: '',
                  icon: Icons.contact_mail,
                  child: Column(
                    children: [
                      _buildContactItem(
                        icon: Icons.email,
                        title: 'Email',
                        content: contactInfo['email'] ?? '',
                      ),
                      const SizedBox(height: 16),
                      _buildContactItem(
                        icon: Icons.phone,
                        title: 'Phone',
                        content: contactInfo['phone'] ?? '',
                      ),
                      const SizedBox(height: 16),
                      _buildContactItem(
                        icon: Icons.location_on,
                        title: 'Address',
                        content: contactInfo['address'] ?? '',
                      ),
                    ],
                  ),
                ),

                // Social Media
                Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Follow Us',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: contentService.socialLinks.map((link) {
                          final colorHex = link['color'] as String;
                          final color = Color(
                              int.parse(colorHex.substring(1), radix: 16) +
                                  0xFF000000);

                          return _buildSocialButton(
                            icon: _getIconData(link['icon']),
                            color: color,
                            onTap: () => _launchUrl(link['url'], context),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),

                // Copyright
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  color: Colors.grey.shade100,
                  child: Column(
                    children: [
                      const Text(
                        '© 2023 Link In Blink Hotel. All rights reserved.',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TextButton(
                            onPressed: () {
                              Navigator.pushNamed(context, '/privacy-policy');
                            },
                            child: const Text('Privacy Policy'),
                          ),
                          const Text('|', style: TextStyle(color: Colors.grey)),
                          TextButton(
                            onPressed: () {
                              Navigator.pushNamed(context, '/terms-of-service');
                            },
                            child: const Text('Terms of Service'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'history':
        return Icons.history;
      case 'flag':
        return Icons.flag;
      case 'people':
        return Icons.people;
      case 'contact_mail':
        return Icons.contact_mail;
      case 'facebook':
        return Icons.facebook;
      case 'camera_alt':
        return Icons.camera_alt;
      case 'telegram':
        return Icons.telegram;
      case 'video_library':
        return Icons.video_library;
      case 'link':
        return Icons.link;
      default:
        return Icons.info;
    }
  }

  Future<void> _launchUrl(String urlString, BuildContext context) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url)) {
      if (!context.mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Could not launch $urlString'),
        ),
      );
    }
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required String content,
    required IconData icon,
    Widget? child,
  }) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withAlpha(30),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (content.isNotEmpty)
            Text(
              content,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade700,
                height: 1.5,
              ),
            ),
          if (child != null) child,
        ],
      ),
    );
  }

  Widget _buildContactItem({
    required IconData icon,
    required String title,
    required String content,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: AppTheme.secondaryColor,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                content,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade700,
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSocialButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withAlpha(30),
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: color,
          size: 24,
        ),
      ),
    );
  }
}
