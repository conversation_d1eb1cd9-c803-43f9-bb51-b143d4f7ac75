import { 
  collection, 
  query, 
  where, 
  getDocs, 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  deleteDoc,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../firebase/config';

// Interface for pricing rule
export interface PricingRule {
  id?: string;
  hotelId: string;
  roomTypeId?: string; // If null, applies to all room types
  name: string;
  description?: string;
  startDate: Timestamp;
  endDate: Timestamp;
  dayOfWeek?: number[]; // 0-6, where 0 is Sunday
  adjustmentType: 'percentage' | 'fixed';
  adjustmentValue: number; // Percentage (e.g., 10 for 10%) or fixed amount
  minStayLength?: number;
  maxStayLength?: number;
  priority: number; // Higher priority rules are applied first
  status: 'active' | 'inactive';
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// Interface for seasonal pricing
export interface SeasonalPricing {
  id?: string;
  hotelId: string;
  name: string;
  description?: string;
  startDate: Timestamp;
  endDate: Timestamp;
  roomPrices: Record<string, number>; // roomId -> price
  status: 'active' | 'inactive';
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// Interface for demand-based pricing settings
export interface DemandPricingSettings {
  id?: string;
  hotelId: string;
  enabled: boolean;
  lowDemandThreshold: number; // Percentage of rooms booked
  highDemandThreshold: number; // Percentage of rooms booked
  lowDemandAdjustment: number; // Percentage decrease
  highDemandAdjustment: number; // Percentage increase
  maxPriceIncrease: number; // Maximum percentage increase
  minPriceDecrease: number; // Maximum percentage decrease
  updatedAt?: Timestamp;
}

/**
 * Create a new pricing rule
 */
export const createPricingRule = async (rule: PricingRule): Promise<string> => {
  try {
    // Create a new document reference
    const ruleRef = doc(collection(db, 'pricingRules'));
    
    // Add the rule data with timestamps
    await setDoc(ruleRef, {
      ...rule,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    return ruleRef.id;
  } catch (error) {
    console.error('Error creating pricing rule:', error);
    throw error;
  }
};

/**
 * Get all pricing rules for a hotel
 */
export const getPricingRules = async (hotelId: string): Promise<PricingRule[]> => {
  try {
    const rulesQuery = query(
      collection(db, 'pricingRules'),
      where('hotelId', '==', hotelId)
    );
    
    const rulesSnapshot = await getDocs(rulesQuery);
    const rules: PricingRule[] = [];
    
    rulesSnapshot.forEach((doc) => {
      rules.push({ id: doc.id, ...doc.data() } as PricingRule);
    });
    
    // Sort by priority (higher first)
    return rules.sort((a, b) => b.priority - a.priority);
  } catch (error) {
    console.error('Error getting pricing rules:', error);
    throw error;
  }
};

/**
 * Update a pricing rule
 */
export const updatePricingRule = async (ruleId: string, rule: Partial<PricingRule>): Promise<void> => {
  try {
    const ruleRef = doc(db, 'pricingRules', ruleId);
    
    await updateDoc(ruleRef, {
      ...rule,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error updating pricing rule:', error);
    throw error;
  }
};

/**
 * Delete a pricing rule
 */
export const deletePricingRule = async (ruleId: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, 'pricingRules', ruleId));
  } catch (error) {
    console.error('Error deleting pricing rule:', error);
    throw error;
  }
};

/**
 * Create or update seasonal pricing
 */
export const saveSeasonalPricing = async (seasonalPricing: SeasonalPricing): Promise<string> => {
  try {
    if (seasonalPricing.id) {
      // Update existing seasonal pricing
      const seasonRef = doc(db, 'seasonalPricing', seasonalPricing.id);
      
      await updateDoc(seasonRef, {
        ...seasonalPricing,
        updatedAt: serverTimestamp()
      });
      
      return seasonalPricing.id;
    } else {
      // Create new seasonal pricing
      const seasonRef = doc(collection(db, 'seasonalPricing'));
      
      await setDoc(seasonRef, {
        ...seasonalPricing,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      
      return seasonRef.id;
    }
  } catch (error) {
    console.error('Error saving seasonal pricing:', error);
    throw error;
  }
};

/**
 * Get all seasonal pricing for a hotel
 */
export const getSeasonalPricing = async (hotelId: string): Promise<SeasonalPricing[]> => {
  try {
    const seasonQuery = query(
      collection(db, 'seasonalPricing'),
      where('hotelId', '==', hotelId)
    );
    
    const seasonSnapshot = await getDocs(seasonQuery);
    const seasons: SeasonalPricing[] = [];
    
    seasonSnapshot.forEach((doc) => {
      seasons.push({ id: doc.id, ...doc.data() } as SeasonalPricing);
    });
    
    return seasons;
  } catch (error) {
    console.error('Error getting seasonal pricing:', error);
    throw error;
  }
};

/**
 * Delete seasonal pricing
 */
export const deleteSeasonalPricing = async (seasonId: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, 'seasonalPricing', seasonId));
  } catch (error) {
    console.error('Error deleting seasonal pricing:', error);
    throw error;
  }
};

/**
 * Save demand-based pricing settings
 */
export const saveDemandPricingSettings = async (settings: DemandPricingSettings): Promise<string> => {
  try {
    // Check if settings already exist for this hotel
    const settingsQuery = query(
      collection(db, 'demandPricingSettings'),
      where('hotelId', '==', settings.hotelId)
    );
    
    const settingsSnapshot = await getDocs(settingsQuery);
    
    if (!settingsSnapshot.empty) {
      // Update existing settings
      const existingSettings = settingsSnapshot.docs[0];
      await updateDoc(existingSettings.ref, {
        ...settings,
        updatedAt: serverTimestamp()
      });
      
      return existingSettings.id;
    } else {
      // Create new settings
      const settingsRef = doc(collection(db, 'demandPricingSettings'));
      
      await setDoc(settingsRef, {
        ...settings,
        updatedAt: serverTimestamp()
      });
      
      return settingsRef.id;
    }
  } catch (error) {
    console.error('Error saving demand pricing settings:', error);
    throw error;
  }
};

/**
 * Get demand-based pricing settings for a hotel
 */
export const getDemandPricingSettings = async (hotelId: string): Promise<DemandPricingSettings | null> => {
  try {
    const settingsQuery = query(
      collection(db, 'demandPricingSettings'),
      where('hotelId', '==', hotelId)
    );
    
    const settingsSnapshot = await getDocs(settingsQuery);
    
    if (!settingsSnapshot.empty) {
      const settings = settingsSnapshot.docs[0];
      return { id: settings.id, ...settings.data() } as DemandPricingSettings;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting demand pricing settings:', error);
    throw error;
  }
};

/**
 * Calculate the final price for a room based on all pricing rules
 */
export const calculateRoomPrice = async (
  roomId: string, 
  checkInDate: Date, 
  checkOutDate: Date
): Promise<number> => {
  try {
    // Get room data
    const roomDoc = await getDoc(doc(db, 'rooms', roomId));
    
    if (!roomDoc.exists()) {
      throw new Error('Room not found');
    }
    
    const roomData = roomDoc.data();
    const basePrice = roomData.price || 0;
    const hotelId = roomData.hotelId;
    const roomTypeId = roomData.type;
    
    // Get all pricing rules for the hotel
    const rules = await getPricingRules(hotelId);
    
    // Get seasonal pricing for the hotel
    const seasons = await getSeasonalPricing(hotelId);
    
    // Get demand pricing settings
    const demandSettings = await getDemandPricingSettings(hotelId);
    
    // Calculate the number of nights
    const nights = Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24));
    
    // Apply seasonal pricing
    let seasonalPrice = basePrice;
    for (const season of seasons) {
      if (season.status !== 'active') continue;
      
      const seasonStart = season.startDate.toDate();
      const seasonEnd = season.endDate.toDate();
      
      // Check if check-in date falls within the season
      if (checkInDate >= seasonStart && checkInDate <= seasonEnd) {
        seasonalPrice = season.roomPrices[roomId] || basePrice;
        break;
      }
    }
    
    // Apply pricing rules
    let finalPrice = seasonalPrice;
    for (const rule of rules) {
      if (rule.status !== 'active') continue;
      
      // Check if rule applies to this room type
      if (rule.roomTypeId && rule.roomTypeId !== roomTypeId) continue;
      
      // Check date range
      const ruleStart = rule.startDate.toDate();
      const ruleEnd = rule.endDate.toDate();
      
      if (checkInDate > ruleEnd || checkOutDate < ruleStart) continue;
      
      // Check day of week
      if (rule.dayOfWeek && rule.dayOfWeek.length > 0) {
        const checkInDay = checkInDate.getDay();
        if (!rule.dayOfWeek.includes(checkInDay)) continue;
      }
      
      // Check stay length
      if (rule.minStayLength && nights < rule.minStayLength) continue;
      if (rule.maxStayLength && nights > rule.maxStayLength) continue;
      
      // Apply adjustment
      if (rule.adjustmentType === 'percentage') {
        finalPrice += finalPrice * (rule.adjustmentValue / 100);
      } else {
        finalPrice += rule.adjustmentValue;
      }
    }
    
    // Apply demand-based pricing if enabled
    if (demandSettings && demandSettings.enabled) {
      // Get current occupancy rate
      // This would require additional logic to calculate based on bookings
      // For now, we'll use a placeholder value
      const currentOccupancy = 0.7; // 70% occupancy
      
      if (currentOccupancy <= demandSettings.lowDemandThreshold / 100) {
        // Low demand - decrease price
        const decrease = finalPrice * (demandSettings.lowDemandAdjustment / 100);
        finalPrice -= Math.min(decrease, finalPrice * (demandSettings.minPriceDecrease / 100));
      } else if (currentOccupancy >= demandSettings.highDemandThreshold / 100) {
        // High demand - increase price
        const increase = finalPrice * (demandSettings.highDemandAdjustment / 100);
        finalPrice += Math.min(increase, finalPrice * (demandSettings.maxPriceIncrease / 100));
      }
    }
    
    // Ensure price is not negative
    return Math.max(finalPrice, 0);
  } catch (error) {
    console.error('Error calculating room price:', error);
    throw error;
  }
};
