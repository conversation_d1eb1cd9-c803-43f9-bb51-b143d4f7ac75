import 'package:flutter/material.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_app_bar.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_button.dart';

class PaymentMethodsScreen extends StatefulWidget {
  const PaymentMethodsScreen({super.key});

  @override
  State<PaymentMethodsScreen> createState() => _PaymentMethodsScreenState();
}

class _PaymentMethodsScreenState extends State<PaymentMethodsScreen> {
  // Mock payment methods for demonstration
  final List<Map<String, dynamic>> _paymentMethods = [
    {
      'id': '1',
      'type': 'credit_card',
      'brand': 'Visa',
      'last4': '4242',
      'expMonth': 12,
      'expYear': 2025,
      'isDefault': true,
    },
    {
      'id': '2',
      'type': 'credit_card',
      'brand': 'Mastercard',
      'last4': '5555',
      'expMonth': 8,
      'expYear': 2024,
      'isDefault': false,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Payment Methods',
        showBackButton: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: _paymentMethods.isEmpty
                ? _buildEmptyState()
                : _buildPaymentMethodsList(),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: CustomButton(
              text: 'Add Payment Method',
              prefixIcon: const Icon(Icons.add, color: Colors.white),
              onPressed: () {
                _showAddPaymentMethodBottomSheet(context);
              },
              height: 56,
              borderRadius: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.credit_card_off,
            size: 80,
            color: Colors.grey.shade300,
          ),
          const SizedBox(height: 16),
          Text(
            'No Payment Methods',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add a payment method to make booking easier',
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              _showAddPaymentMethodBottomSheet(context);
            },
            icon: const Icon(Icons.add),
            label: const Text('Add Payment Method'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _paymentMethods.length,
      itemBuilder: (context, index) {
        final method = _paymentMethods[index];
        return _buildPaymentMethodCard(method);
      },
    );
  }

  Widget _buildPaymentMethodCard(Map<String, dynamic> method) {
    IconData cardIcon;
    Color cardColor;

    // Set card icon and color based on brand
    switch (method['brand'].toString().toLowerCase()) {
      case 'visa':
        cardIcon = Icons.credit_card;
        cardColor = Colors.blue;
        break;
      case 'mastercard':
        cardIcon = Icons.credit_card;
        cardColor = Colors.orange;
        break;
      case 'amex':
        cardIcon = Icons.credit_card;
        cardColor = Colors.indigo;
        break;
      default:
        cardIcon = Icons.credit_card;
        cardColor = Colors.grey;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          ListTile(
            contentPadding: const EdgeInsets.all(16),
            leading: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: cardColor.withAlpha(30),
                shape: BoxShape.circle,
              ),
              child: Icon(
                cardIcon,
                color: cardColor,
              ),
            ),
            title: Row(
              children: [
                Text(
                  '${method['brand']} •••• ${method['last4']}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(width: 8),
                if (method['isDefault'])
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.green.withAlpha(30),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Default',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            subtitle: Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'Expires ${method['expMonth']}/${method['expYear']}',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
              ),
            ),
            trailing: PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert),
              onSelected: (value) {
                if (value == 'edit') {
                  // Edit payment method
                } else if (value == 'delete') {
                  _showDeleteConfirmationDialog(method);
                } else if (value == 'default') {
                  _setAsDefault(method['id']);
                }
              },
              itemBuilder: (context) => [
                if (!method['isDefault'])
                  const PopupMenuItem<String>(
                    value: 'default',
                    child: Text('Set as Default'),
                  ),
                const PopupMenuItem<String>(
                  value: 'edit',
                  child: Text('Edit'),
                ),
                const PopupMenuItem<String>(
                  value: 'delete',
                  child: Text('Delete'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showAddPaymentMethodBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 16,
            right: 16,
            top: 16,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Add Payment Method',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'This is a demo screen. In a real app, you would integrate with a payment processor like Stripe or Razorpay to securely collect and store payment information.',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 24),
              CustomButton(
                text: 'Close',
                onPressed: () {
                  Navigator.pop(context);
                },
                height: 56,
                borderRadius: 12,
              ),
              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  void _showDeleteConfirmationDialog(Map<String, dynamic> method) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Payment Method'),
        content: Text(
          'Are you sure you want to delete ${method['brand']} ending in ${method['last4']}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deletePaymentMethod(method['id']);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );
  }

  void _deletePaymentMethod(String id) {
    setState(() {
      _paymentMethods.removeWhere((method) => method['id'] == id);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Payment method deleted')),
    );
  }

  void _setAsDefault(String id) {
    setState(() {
      for (var method in _paymentMethods) {
        method['isDefault'] = method['id'] == id;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Default payment method updated')),
    );
  }
}
