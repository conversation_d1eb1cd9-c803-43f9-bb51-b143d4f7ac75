import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Grid,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Divider,
  Alert,
  Snackbar,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Save as SaveIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Payments as PaymentsIcon,
  Email as EmailIcon,
  Settings as SettingsIcon,
  Hotel as HotelIcon
} from '@mui/icons-material';

// TabPanel component for tab content
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const Settings: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  // Hotel settings state
  const [hotelSettings, setHotelSettings] = useState({
    hotelName: 'Grand Hotel',
    description: 'A luxury hotel in the heart of the city',
    address: '123 Main Street, Mumbai, Maharashtra',
    contactEmail: '<EMAIL>',
    contactPhone: '+91 9876543210',
    checkInTime: '14:00',
    checkOutTime: '11:00',
    defaultCurrency: 'INR',
    taxRate: 18
  });

  // Notification settings state
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    newBookingAlert: true,
    bookingCancellationAlert: true,
    paymentConfirmationAlert: true,
    lowInventoryAlert: false
  });

  // Payment settings state
  const [paymentSettings, setPaymentSettings] = useState({
    acceptedPaymentMethods: [
      { id: 1, name: 'Credit Card', enabled: true },
      { id: 2, name: 'Debit Card', enabled: true },
      { id: 3, name: 'UPI', enabled: true },
      { id: 4, name: 'Net Banking', enabled: true },
      { id: 5, name: 'PayTM', enabled: true },
      { id: 6, name: 'Cash', enabled: true }
    ],
    advancePaymentPercentage: 20,
    cancellationFeePercentage: 10,
    gracePeriodHours: 24
  });

  // Booking rules state
  const [bookingRules, setBookingRules] = useState({
    minAdvanceBookingDays: 1,
    maxAdvanceBookingDays: 90,
    allowSameDayBooking: true,
    minStayLength: 1,
    maxStayLength: 30,
    allowPartialPayment: true,
    allowModification: true,
    allowCancellation: true,
    cancellationDeadlineHours: 24
  });

  // Handle tab change
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle hotel settings change
  const handleHotelSettingsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setHotelSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle notification settings toggle
  const handleNotificationToggle = (name: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setNotificationSettings(prev => ({
      ...prev,
      [name]: e.target.checked
    }));
  };

  // Handle payment method toggle
  const handlePaymentMethodToggle = (id: number) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setPaymentSettings(prev => ({
      ...prev,
      acceptedPaymentMethods: prev.acceptedPaymentMethods.map(method =>
        method.id === id ? { ...method, enabled: e.target.checked } : method
      )
    }));
  };

  // Handle booking rules change
  const handleBookingRulesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setBookingRules(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : Number(value)
    }));
  };

  // Handle save settings
  const handleSaveSettings = () => {
    // In a real app, this would save to Firebase or another backend
    setSnackbar({
      open: true,
      message: 'Settings saved successfully',
      severity: 'success'
    });
  };

  // Handle close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Hotel Settings
        </Typography>
        <Button
          variant="contained"
          startIcon={<SaveIcon />}
          onClick={handleSaveSettings}
        >
          Save Changes
        </Button>
      </Box>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab icon={<HotelIcon />} label="Hotel Info" />
          <Tab icon={<SettingsIcon />} label="Booking Rules" />
          <Tab icon={<PaymentsIcon />} label="Payment" />
          <Tab icon={<NotificationsIcon />} label="Notifications" />
          <Tab icon={<SecurityIcon />} label="Security" />
        </Tabs>

        {/* Hotel Info Settings */}
        <TabPanel value={tabValue} index={0}>
          <Typography variant="h6" gutterBottom>
            Hotel Information
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure your hotel's basic information.
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Hotel Name"
                name="hotelName"
                value={hotelSettings.hotelName}
                onChange={handleHotelSettingsChange}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Contact Email"
                name="contactEmail"
                value={hotelSettings.contactEmail}
                onChange={handleHotelSettingsChange}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Contact Phone"
                name="contactPhone"
                value={hotelSettings.contactPhone}
                onChange={handleHotelSettingsChange}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Address"
                name="address"
                value={hotelSettings.address}
                onChange={handleHotelSettingsChange}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Check-in Time"
                name="checkInTime"
                type="time"
                value={hotelSettings.checkInTime}
                onChange={handleHotelSettingsChange}
                margin="normal"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Check-out Time"
                name="checkOutTime"
                type="time"
                value={hotelSettings.checkOutTime}
                onChange={handleHotelSettingsChange}
                margin="normal"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Tax Rate (%)"
                name="taxRate"
                type="number"
                value={hotelSettings.taxRate}
                onChange={handleHotelSettingsChange}
                margin="normal"
                InputProps={{ inputProps: { min: 0, max: 100 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Default Currency</InputLabel>
                <Select
                  value={hotelSettings.defaultCurrency}
                  label="Default Currency"
                  onChange={(e) => setHotelSettings(prev => ({ ...prev, defaultCurrency: e.target.value }))}
                >
                  <MenuItem value="INR">Indian Rupee (₹)</MenuItem>
                  <MenuItem value="USD">US Dollar ($)</MenuItem>
                  <MenuItem value="EUR">Euro (€)</MenuItem>
                  <MenuItem value="GBP">British Pound (£)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Hotel Description"
                name="description"
                value={hotelSettings.description}
                onChange={handleHotelSettingsChange}
                margin="normal"
                multiline
                rows={4}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Booking Rules Settings */}
        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6" gutterBottom>
            Booking Rules
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure rules for bookings at your hotel.
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Minimum Advance Booking Days"
                name="minAdvanceBookingDays"
                type="number"
                value={bookingRules.minAdvanceBookingDays}
                onChange={handleBookingRulesChange}
                margin="normal"
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Maximum Advance Booking Days"
                name="maxAdvanceBookingDays"
                type="number"
                value={bookingRules.maxAdvanceBookingDays}
                onChange={handleBookingRulesChange}
                margin="normal"
                InputProps={{ inputProps: { min: 1 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Minimum Stay Length (nights)"
                name="minStayLength"
                type="number"
                value={bookingRules.minStayLength}
                onChange={handleBookingRulesChange}
                margin="normal"
                InputProps={{ inputProps: { min: 1 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Maximum Stay Length (nights)"
                name="maxStayLength"
                type="number"
                value={bookingRules.maxStayLength}
                onChange={handleBookingRulesChange}
                margin="normal"
                InputProps={{ inputProps: { min: 1 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Cancellation Deadline (hours before check-in)"
                name="cancellationDeadlineHours"
                type="number"
                value={bookingRules.cancellationDeadlineHours}
                onChange={handleBookingRulesChange}
                margin="normal"
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={bookingRules.allowSameDayBooking}
                    onChange={handleBookingRulesChange}
                    name="allowSameDayBooking"
                    color="primary"
                  />
                }
                label="Allow Same Day Booking"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={bookingRules.allowPartialPayment}
                    onChange={handleBookingRulesChange}
                    name="allowPartialPayment"
                    color="primary"
                  />
                }
                label="Allow Partial Payment"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={bookingRules.allowModification}
                    onChange={handleBookingRulesChange}
                    name="allowModification"
                    color="primary"
                  />
                }
                label="Allow Booking Modification"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={bookingRules.allowCancellation}
                    onChange={handleBookingRulesChange}
                    name="allowCancellation"
                    color="primary"
                  />
                }
                label="Allow Booking Cancellation"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Payment Settings */}
        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            Payment Settings
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure payment methods and policies.
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Advance Payment Percentage"
                name="advancePaymentPercentage"
                type="number"
                value={paymentSettings.advancePaymentPercentage}
                onChange={(e) => setPaymentSettings(prev => ({ ...prev, advancePaymentPercentage: Number(e.target.value) }))}
                margin="normal"
                InputProps={{ inputProps: { min: 0, max: 100 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Cancellation Fee Percentage"
                name="cancellationFeePercentage"
                type="number"
                value={paymentSettings.cancellationFeePercentage}
                onChange={(e) => setPaymentSettings(prev => ({ ...prev, cancellationFeePercentage: Number(e.target.value) }))}
                margin="normal"
                InputProps={{ inputProps: { min: 0, max: 100 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Grace Period for Cancellation (hours)"
                name="gracePeriodHours"
                type="number"
                value={paymentSettings.gracePeriodHours}
                onChange={(e) => setPaymentSettings(prev => ({ ...prev, gracePeriodHours: Number(e.target.value) }))}
                margin="normal"
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>
          </Grid>

          <Typography variant="subtitle1" sx={{ mt: 3, mb: 2 }}>
            Accepted Payment Methods
          </Typography>
          <List>
            {paymentSettings.acceptedPaymentMethods.map((method) => (
              <ListItem key={method.id} divider>
                <ListItemText primary={method.name} />
                <ListItemSecondaryAction>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={method.enabled}
                        onChange={handlePaymentMethodToggle(method.id)}
                        color="primary"
                      />
                    }
                    label={method.enabled ? 'Enabled' : 'Disabled'}
                  />
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </TabPanel>

        {/* Notification Settings */}
        <TabPanel value={tabValue} index={3}>
          <Typography variant="h6" gutterBottom>
            Notification Settings
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure how and when you receive notifications.
          </Typography>

          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Notification Channels
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationSettings.emailNotifications}
                        onChange={handleNotificationToggle('emailNotifications')}
                        color="primary"
                      />
                    }
                    label="Email Notifications"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationSettings.smsNotifications}
                        onChange={handleNotificationToggle('smsNotifications')}
                        color="primary"
                      />
                    }
                    label="SMS Notifications"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Notification Events
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationSettings.newBookingAlert}
                        onChange={handleNotificationToggle('newBookingAlert')}
                        color="primary"
                      />
                    }
                    label="New Booking Alert"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationSettings.bookingCancellationAlert}
                        onChange={handleNotificationToggle('bookingCancellationAlert')}
                        color="primary"
                      />
                    }
                    label="Booking Cancellation Alert"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationSettings.paymentConfirmationAlert}
                        onChange={handleNotificationToggle('paymentConfirmationAlert')}
                        color="primary"
                      />
                    }
                    label="Payment Confirmation Alert"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationSettings.lowInventoryAlert}
                        onChange={handleNotificationToggle('lowInventoryAlert')}
                        color="primary"
                      />
                    }
                    label="Low Inventory Alert"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </TabPanel>

        {/* Security Settings */}
        <TabPanel value={tabValue} index={4}>
          <Typography variant="h6" gutterBottom>
            Security Settings
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure security settings for your hotel account.
          </Typography>

          <Alert severity="info" sx={{ mb: 3 }}>
            For security reasons, some settings can only be changed by contacting the system administrator.
          </Alert>

          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Change Password
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Current Password"
                    type="password"
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="New Password"
                    type="password"
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Confirm New Password"
                    type="password"
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    color="primary"
                    sx={{ mt: 2 }}
                  >
                    Update Password
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </TabPanel>
      </Paper>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Settings;
