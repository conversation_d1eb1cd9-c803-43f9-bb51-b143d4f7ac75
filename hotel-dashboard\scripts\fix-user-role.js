/**
 * <PERSON><PERSON><PERSON> to fix user roles in Firestore
 *
 * This script:
 * 1. Checks for the admin user
 * 2. Updates the role to super_admin if needed
 *
 * Run with: node scripts/fix-user-role.js
 */

const { initializeApp } = require('firebase/app');
const {
  getAuth,
  signInWithEmailAndPassword,
  signOut
} = require('firebase/auth');
const {
  getFirestore,
  doc,
  getDoc,
  updateDoc,
  collection,
  query,
  where,
  getDocs,
  setDoc
} = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI",
  authDomain: "linkinblink-hotel.firebaseapp.com",
  projectId: "linkinblink-hotel",
  storageBucket: "linkinblink-hotel.firebasestorage.app",
  messagingSenderId: "132613661307",
  appId: "1:132613661307:web:a9e5c8f4c2b8e9a9e5c8f4",
  measurementId: "G-MEASUREMENT_ID"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// User roles
const ROLE_SUPER_ADMIN = 'super_admin';

async function fixUserRole() {
  try {
    console.log('Checking for admin user...');

    // Try to sign in with admin credentials
    try {
      await signInWithEmailAndPassword(auth, '<EMAIL>', 'Admin123!');
      console.log('Successfully signed in as admin');

      // Get the current user
      const user = auth.currentUser;

      if (user) {
        // Check if user document exists in Firestore
        const userDoc = await getDoc(doc(db, 'users', user.uid));

        if (userDoc.exists()) {
          const userData = userDoc.data();
          console.log('Current user data:', userData);

          // Check if role is correct
          if (userData.role !== ROLE_SUPER_ADMIN) {
            console.log(`Updating role from ${userData.role} to ${ROLE_SUPER_ADMIN}`);

            // Update the role
            await updateDoc(doc(db, 'users', user.uid), {
              role: ROLE_SUPER_ADMIN
            });

            console.log('Role updated successfully');
          } else {
            console.log('User already has the correct role');
          }
        } else {
          console.log('User document does not exist in Firestore');
          console.log('Creating user document...');

          // Create the user document
          await setDoc(doc(db, 'users', user.uid), {
            email: '<EMAIL>',
            displayName: 'Super Admin',
            role: ROLE_SUPER_ADMIN,
            createdAt: new Date(),
            updatedAt: new Date()
          });

          console.log('User document created successfully');
        }
      }

      // Sign out
      await signOut(auth);
      console.log('Signed out');
    } catch (error) {
      console.error('Error signing in as admin:', error.message);
    }

    // List all users in Firestore
    console.log('\nListing all users in Firestore:');
    const usersQuery = query(collection(db, 'users'));
    const usersSnapshot = await getDocs(usersQuery);

    if (usersSnapshot.empty) {
      console.log('No users found in Firestore');
    } else {
      usersSnapshot.forEach(doc => {
        const userData = doc.data();
        console.log(`ID: ${doc.id}, Email: ${userData.email}, Role: ${userData.role}`);
      });
    }

    console.log('\nScript completed');
    process.exit(0);
  } catch (error) {
    console.error('Error in fixUserRole:', error);
    process.exit(1);
  }
}

// Run the function
fixUserRole();
