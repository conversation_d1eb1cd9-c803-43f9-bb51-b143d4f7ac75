# Aadhaar Verification Implementation

This document provides an overview of the Aadhaar verification implementation in the Link In Blink Hotel mobile application.

## Overview

The Aadhaar verification feature allows users to verify their identity using their Aadhaar number through the Sandbox.co.in API. This verification enhances security and provides a seamless check-in experience at partner hotels.

## Implementation Details

### API Integration

The implementation uses the Sandbox.co.in API for Aadhaar verification. The API provides two main endpoints:

1. **Generate OTP**: Sends an OTP to the user's registered mobile number associated with their Aadhaar.
2. **Verify OTP**: Verifies the OTP and returns the user's Aadhaar details.

### Security Measures

The implementation includes several security measures:

1. **Masked Aadhaar Numbers**: Only the last 4 digits of the Aadhaar number are stored in the database.
2. **Secure API Credentials**: API credentials are stored securely.
3. **Minimal Data Storage**: Only essential verification data is stored.
4. **Timeout Handling**: API requests have timeouts to prevent indefinite waiting.

### User Flow

1. User enters their 12-digit Aadhaar number.
2. System validates the Aadhaar number format.
3. System sends an OTP to the user's registered mobile number.
4. User enters the OTP.
5. System verifies the OTP and retrieves user details.
6. Verification status is updated in the database.

### Components

#### 1. SandboxApiService

This service handles the API requests to the Sandbox.co.in API:

- `generateOtp(String aadhaarNumber)`: Generates an OTP for Aadhaar verification.
- `verifyOtp(String referenceId, String otp)`: Verifies the OTP and retrieves user details.

#### 2. AadhaarVerificationService

This service manages the verification process:

- `initiateVerification(String aadhaarNumber)`: Initiates the verification process by generating an OTP.
- `verifyOtp(String verificationId, String otp)`: Verifies the OTP and updates the verification status.
- `getVerificationByUserId(String userId)`: Retrieves the verification status for a user.

#### 3. UI Screens

- `AadhaarVerificationScreen`: Allows users to enter their Aadhaar number and verify it.
- `CheckInVerificationScreen`: Allows users to verify their Aadhaar during check-in.

## API Endpoints

### Generate OTP

```
POST https://api.sandbox.co.in/kyc/aadhaar/okyc/otp
```

**Request:**
```json
{
  "aadhaar_number": "123456789012"
}
```

**Response:**
```json
{
  "reference_id": "10683915",
  "message": "OTP sent successfully"
}
```

### Verify OTP

```
POST https://api.sandbox.co.in/kyc/aadhaar/okyc/otp/verify
```

**Request:**
```json
{
  "reference_id": "10683915",
  "otp": "123456"
}
```

**Response:**
```json
{
  "reference_id": "10683915",
  "status": "valid",
  "message": "Aadhaar Card Exists",
  "care_of": "S/O: Johnny Doe",
  "dob": "1990",
  "full_address": "Main Cross 3rd, Bengaluru, Karnataka",
  "gender": "M",
  "name": "John Doe",
  "year_of_birth": "1990",
  "photo": "data:image/jpeg;base64,/9j/4A..."
}
```

## Database Schema

The Aadhaar verification data is stored in the `aadhaarVerifications` collection in Firebase Firestore with the following schema:

```
{
  "userId": "user123",
  "maskedAadhaarNumber": "XXXX-XXXX-1234",
  "fullName": "John Doe",
  "status": "verified",
  "verifiedAt": Timestamp,
  "verificationMethod": "OTP",
  "referenceId": "10683915",
  "gender": "M",
  "address": "Main Cross 3rd, Bengaluru, Karnataka",
  "dateOfBirth": Timestamp,
  "verificationDetails": {
    "otpVerified": true,
    "verificationTimestamp": "2023-06-01T12:00:00Z",
    "apiResponse": { ... }
  },
  "createdAt": Timestamp,
  "updatedAt": Timestamp
}
```

## Error Handling

The implementation includes comprehensive error handling for various scenarios:

1. **Network Errors**: Timeouts, connection issues, etc.
2. **Validation Errors**: Invalid Aadhaar number, invalid OTP, etc.
3. **API Errors**: Server errors, authentication errors, etc.

## Future Improvements

1. **Biometric Verification**: Add support for biometric verification.
2. **Offline Verification**: Add support for offline verification using QR codes.
3. **Enhanced Security**: Implement additional security measures like encryption.
4. **Performance Optimization**: Optimize API calls and database operations.

## References

- [Sandbox.co.in API Documentation](https://developer.sandbox.co.in/reference/aadhaar-kyc)
- [Aadhaar Verification Guidelines](https://uidai.gov.in/ecosystem/authentication-devices-documents/developer-section.html)
