import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Alert,
  Divider
} from '@mui/material';
import {
  PhoneAndroid as PhoneIcon,
} from '@mui/icons-material';
import { getBookingStatusInfo, getPaymentStatusInfo, formatDateDDMMMYYYY, formatCurrency } from '../utils/bookingUtilsSimple';

interface MobileAppBookingsProps {
  hotelId: string;
  onViewBooking?: (booking: any) => void;
  onCheckIn?: (booking: any) => void;
  onCancel?: (booking: any) => void;
}

const MobileAppBookingsSimple: React.FC<MobileAppBookingsProps> = ({
  hotelId,
  onViewBooking,
  onCheckIn,
  onCancel
}) => {
  return (
    <Paper sx={{ p: 3, height: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <PhoneIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6">
            Mobile App Bookings
          </Typography>
        </Box>
      </Box>

      <Divider sx={{ mb: 2 }} />

      <Alert severity="info" sx={{ mb: 2 }}>
        Mobile app bookings are currently being updated. Please check back soon.
      </Alert>

      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
        <Button
          variant="outlined"
          size="small"
          onClick={() => onViewBooking && onViewBooking(null)}
        >
          View All Mobile Bookings
        </Button>
      </Box>
    </Paper>
  );
};

export default MobileAppBookingsSimple;
