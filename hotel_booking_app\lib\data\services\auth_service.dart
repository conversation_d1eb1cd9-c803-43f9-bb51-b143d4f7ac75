import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_core/firebase_core.dart';

class AuthService extends ChangeNotifier {
  late final FirebaseAuth _auth;
  late final FirebaseFirestore _firestore;
  User? _user;
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic>? _userData;
  bool _isInitialized = false;

  // Getters
  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _user != null;
  Map<String, dynamic>? get userData => _userData;
  bool get isInitialized => _isInitialized;

  AuthService() {
    _initServices();
  }

  void _initServices() {
    try {
      // Check if Firebase is initialized
      if (Firebase.apps.isNotEmpty) {
        try {
          _auth = FirebaseAuth.instance;
          _firestore = FirebaseFirestore.instance;
          _isInitialized = true;
          _init();
          debugPrint('AuthService initialized successfully');
        } catch (e) {
          _error = 'Error initializing Firebase services: $e';
          debugPrint(_error);
          _isInitialized = false;
        }
      } else {
        _error = 'Firebase not initialized';
        debugPrint(_error);
        _isInitialized = false;
      }
    } catch (e) {
      _error = 'Error in AuthService initialization: $e';
      debugPrint(_error);
      _isInitialized = false;
    }
  }

  void _init() {
    if (!_isInitialized) return;

    try {
      _auth.authStateChanges().listen((User? user) async {
        _user = user;
        if (user != null) {
          await _fetchUserData();
        } else {
          _userData = null;
        }
        notifyListeners();
      });
    } catch (e) {
      _error = 'Error setting up auth state listener: $e';
      debugPrint(_error);
    }
  }

  Future<void> _fetchUserData() async {
    if (_user == null) return;

    try {
      debugPrint('Fetching user data for: ${_user!.uid}');

      final doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(_user!.uid)
          .get();

      if (doc.exists) {
        debugPrint('User document exists in Firestore');
        _userData = doc.data();
      } else {
        debugPrint('User document does not exist, creating new document');
        // Create user document if it doesn't exist
        final userData = {
          'uid': _user!.uid,
          'email': _user!.email,
          'displayName': _user!.displayName,
          'photoURL': _user!.photoURL,
          'phoneNumber': _user!.phoneNumber,
          'role': AppConstants.roleUser,
          'createdAt': FieldValue.serverTimestamp(),
        };

        await _firestore
            .collection(AppConstants.usersCollection)
            .doc(_user!.uid)
            .set(userData);

        _userData = userData;
        debugPrint('New user document created in Firestore');
      }
    } catch (e) {
      debugPrint('Error fetching user data: $e');
      _error = e.toString();
    }
  }

  // Sign in with email and password
  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    if (!_isInitialized) {
      _error = "Authentication service not initialized";
      debugPrint(_error);
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      debugPrint('Attempting to sign in user: $email');

      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      _user = userCredential.user;
      debugPrint('User signed in successfully: ${_user!.uid}');

      await _fetchUserData();
      debugPrint('User data fetched from Firestore');

      // Save auth token to shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.userIdKey, _user!.uid);
      debugPrint('User ID saved to SharedPreferences');

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error during sign in: $e');
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Register with email and password
  Future<bool> registerWithEmailAndPassword(
    String email,
    String password,
    String name,
  ) async {
    if (!_isInitialized) {
      _error = "Authentication service not initialized";
      debugPrint(_error);
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      debugPrint('Attempting to register user: $email');

      // Create user with Firebase Auth
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      _user = userCredential.user;
      debugPrint('User created successfully: ${_user!.uid}');

      // Update display name
      await _user!.updateDisplayName(name);
      debugPrint('Display name updated: $name');

      // Reload user to get updated profile
      await _user!.reload();
      _user = _auth.currentUser;

      // Create user document in Firestore
      final userData = {
        'uid': _user!.uid,
        'email': email,
        'displayName': name,
        'photoURL': null,
        'phoneNumber': null,
        'role': AppConstants.roleUser,
        'createdAt': FieldValue.serverTimestamp(),
      };

      debugPrint('Creating user document in Firestore');
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(_user!.uid)
          .set(userData);

      _userData = userData;
      debugPrint('User data saved to Firestore');

      // Save auth token to shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.userIdKey, _user!.uid);
      debugPrint('User ID saved to SharedPreferences');

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error during registration: $e');
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _auth.signOut();

      // Clear shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.userIdKey);

      _user = null;
      _userData = null;
    } catch (e) {
      _error = e.toString();
    }

    _isLoading = false;
    notifyListeners();
  }

  // Reset password
  Future<bool> resetPassword(String email) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _auth.sendPasswordResetEmail(email: email);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Update user profile
  Future<bool> updateProfile({
    String? displayName,
    String? photoURL,
    String? phoneNumber,
  }) async {
    if (_user == null) return false;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Update Firebase Auth profile
      if (displayName != null) {
        await _user!.updateDisplayName(displayName);
      }

      if (photoURL != null) {
        await _user!.updatePhotoURL(photoURL);
      }

      // Update Firestore document
      final updateData = <String, dynamic>{};

      if (displayName != null) {
        updateData['displayName'] = displayName;
      }

      if (photoURL != null) {
        updateData['photoURL'] = photoURL;
      }

      if (phoneNumber != null) {
        updateData['phoneNumber'] = phoneNumber;
      }

      if (updateData.isNotEmpty) {
        await _firestore
            .collection(AppConstants.usersCollection)
            .doc(_user!.uid)
            .update(updateData);

        // Update local user data
        _userData = {...?_userData, ...updateData};
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Update user profile with extended data
  Future<bool> updateUserProfile(Map<String, dynamic> userData) async {
    if (_user == null) return false;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Update Firebase Auth profile if displayName is provided
      if (userData.containsKey('displayName')) {
        await _user!.updateDisplayName(userData['displayName']);
      }

      // Update Firestore document
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(_user!.uid)
          .update(userData);

      // Update local user data
      _userData = {...?_userData, ...userData};

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
}
