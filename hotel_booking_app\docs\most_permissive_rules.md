# Most Permissive Firebase Rules for Testing

If you're still experiencing permission issues, you can temporarily use these extremely permissive rules for testing. **Note that these rules are NOT suitable for production use as they allow full access to your database without any authentication.**

## Temporary Testing Rules (Most Permissive)

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if true;  // Allow all operations for testing
    }
  }
}
```

## How to Apply These Rules

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to Firestore Database in the left sidebar
4. Click on the "Rules" tab
5. Replace the existing rules with the rules above
6. Click "Publish"

## Important Warning

⚠️ **SECURITY WARNING**: These rules allow anyone to read and write to your entire database. Only use these rules temporarily for testing in a development environment. Do not use these rules in production or with real user data.

## After Testing

Once you've confirmed that your app works with these permissive rules, you should immediately replace them with more secure rules.

## Debugging Steps

If you're still experiencing permission issues after applying these rules, check the following:

1. **Firebase Initialization**: Make sure Firebase is properly initialized in your app.

2. **Authentication State**: Check if the user is properly authenticated before accessing Firestore:
   ```dart
   final user = FirebaseAuth.instance.currentUser;
   if (user == null) {
     print('User is not authenticated');
     return;
   }
   print('User is authenticated with ID: ${user.uid}');
   ```

3. **Collection Name**: Verify that you're using the correct collection name in your code. The collection name in your code is now `aadhaar_verifications` (snake_case).

4. **Rules Propagation**: Sometimes it takes a few minutes for rule changes to propagate. Wait a few minutes after publishing new rules before testing again.

5. **Firebase Console**: Check the Firebase console logs for any additional error information.

6. **Network Connectivity**: Ensure your device has a stable internet connection.

7. **Firebase Project**: Make sure you're using the correct Firebase project in your app.

## Collection Structure

Make sure your Firestore collection structure matches what your code expects:

- Collection name: `aadhaar_verifications` (snake_case, not camelCase)
- Document fields:
  - `userId`: String (the Firebase Auth UID)
  - `maskedAadhaarNumber`: String
  - `status`: String ('pending' or 'verified')
  - `referenceId`: String
  - Other fields as needed
