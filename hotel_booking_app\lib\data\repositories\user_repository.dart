import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';

class UserRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Fetch user data
  Future<Map<String, dynamic>?> fetchUserData(String userId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        return doc.data();
      }
      return null;
    } catch (e) {
      print('Error fetching user data: $e');
      return null;
    }
  }

  // Update user profile
  Future<bool> updateUserProfile(
    String userId,
    Map<String, dynamic> userData,
  ) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        ...userData,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      return true;
    } catch (e) {
      print('Error updating user profile: $e');
      return false;
    }
  }

  // Add to favorites
  Future<bool> addToFavorites(String userId, String hotelId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'favorites': FieldValue.arrayUnion([hotelId]),
      });
      return true;
    } catch (e) {
      print('Error adding to favorites: $e');
      return false;
    }
  }

  // Remove from favorites
  Future<bool> removeFromFavorites(String userId, String hotelId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'favorites': FieldValue.arrayRemove([hotelId]),
      });
      return true;
    } catch (e) {
      print('Error removing from favorites: $e');
      return false;
    }
  }

  // Check if hotel is favorite
  Future<bool> isHotelFavorite(String userId, String hotelId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        final userData = doc.data()!;
        final List<dynamic> favorites = userData['favorites'] ?? [];
        return favorites.contains(hotelId);
      }
      return false;
    } catch (e) {
      print('Error checking if hotel is favorite: $e');
      return false;
    }
  }

  // Fetch favorite hotel IDs
  Future<List<String>> fetchFavoriteHotelIds(String userId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        final userData = doc.data()!;
        final List<dynamic> favorites = userData['favorites'] ?? [];
        return favorites.cast<String>();
      }
      return [];
    } catch (e) {
      print('Error fetching favorite hotel IDs: $e');
      return [];
    }
  }
}
