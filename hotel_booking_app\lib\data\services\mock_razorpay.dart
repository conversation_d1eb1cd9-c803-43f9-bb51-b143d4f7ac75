import 'package:flutter/foundation.dart';

/// Mock implementation of the Razorpay class
class Razorpay {
  // Event constants
  // Using uppercase for backward compatibility with original Razorpay API
  // ignore: constant_identifier_names
  static const String EVENT_PAYMENT_SUCCESS = 'payment.success';
  // ignore: constant_identifier_names
  static const String EVENT_PAYMENT_ERROR = 'payment.error';
  // ignore: constant_identifier_names
  static const String EVENT_EXTERNAL_WALLET = 'payment.external_wallet';

  // Callback functions
  final Map<String, Function> _callbacks = {};

  // Register a callback for an event
  void on(String event, Function callback) {
    _callbacks[event] = callback;
  }

  // Clear all callbacks
  void clear() {
    _callbacks.clear();
  }

  // Open the checkout
  void open(Map<String, dynamic> options) {
    debugPrint('MOCK Razorpay: Opening checkout with options: $options');

    // Simulate a successful payment after a short delay
    Future.delayed(const Duration(seconds: 1), () {
      if (_callbacks.containsKey(EVENT_PAYMENT_SUCCESS)) {
        final successResponse = PaymentSuccessResponse(
          'mock_payment_id_${DateTime.now().millisecondsSinceEpoch}',
          'mock_order_id',
          'mock_signature',
        );
        _callbacks[EVENT_PAYMENT_SUCCESS]!(successResponse);
      }
    });
  }

  // Dispose the Razorpay instance
  void dispose() {
    clear();
  }
}

/// Mock implementation of PaymentSuccessResponse
class PaymentSuccessResponse {
  final String paymentId;
  final String orderId;
  final String signature;

  PaymentSuccessResponse(this.paymentId, this.orderId, this.signature);

  @override
  String toString() {
    return 'PaymentSuccessResponse(paymentId: $paymentId, orderId: $orderId, signature: $signature)';
  }
}

/// Mock implementation of PaymentFailureResponse
class PaymentFailureResponse {
  final int? code;
  final String? message;
  final dynamic error;

  PaymentFailureResponse(this.code, this.message, this.error);

  @override
  String toString() {
    return 'PaymentFailureResponse(code: $code, message: $message, error: $error)';
  }
}

/// Mock implementation of ExternalWalletResponse
class ExternalWalletResponse {
  final String walletName;

  ExternalWalletResponse(this.walletName);

  @override
  String toString() {
    return 'ExternalWalletResponse(walletName: $walletName)';
  }
}
