import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'firebase_options.dart';
import 'package:hotel_booking_app/presentation/screens/splash_screen.dart';
import 'package:hotel_booking_app/core/theme/app_theme.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/data/services/auth_service.dart';
import 'package:hotel_booking_app/data/services/hotel_service.dart';
import 'package:hotel_booking_app/data/services/booking_service.dart';
import 'package:hotel_booking_app/data/services/database_service.dart';
import 'package:hotel_booking_app/data/services/aadhaar_verification_service.dart';
import 'package:hotel_booking_app/data/services/review_service.dart';
import 'package:hotel_booking_app/data/services/notification_service.dart';
import 'package:hotel_booking_app/data/services/service_request_service.dart';
import 'package:hotel_booking_app/data/services/language_service.dart';
import 'package:hotel_booking_app/data/services/currency_service.dart';
import 'package:hotel_booking_app/data/services/faq_service.dart';
import 'package:hotel_booking_app/data/services/support_service.dart';
import 'package:hotel_booking_app/data/services/content_service.dart';
import 'package:hotel_booking_app/data/services/payment_service.dart';
import 'package:hotel_booking_app/data/services/mock_payment_service.dart';
import 'package:hotel_booking_app/data/services/checkin_service.dart';
import 'package:hotel_booking_app/admin/screens/auth/admin_login_screen.dart';
import 'package:hotel_booking_app/presentation/screens/debug/firebase_test_screen.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Flag to track Firebase initialization status
  bool firebaseInitialized = false;
  FirebaseAnalytics? analytics;
  DatabaseService? databaseService;
  AuthService? authService;

  // Initialize Firebase with error handling
  try {
    // Special handling for web platform
    if (kIsWeb) {
      debugPrint('Initializing Firebase for web platform');
      try {
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.web,
        );
      } catch (webError) {
        // Handle web-specific Firebase initialization errors
        debugPrint('Web-specific Firebase initialization error: $webError');
        // Try initializing without options as a fallback
        try {
          await Firebase.initializeApp();
        } catch (e) {
          debugPrint('Failed to initialize Firebase without options: $e');
        }
      }
    } else {
      debugPrint('Initializing Firebase for non-web platform');
      try {
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
      } catch (mobileError) {
        debugPrint(
            'Mobile-specific Firebase initialization error: $mobileError');
        // Try initializing without options as a fallback for mobile too
        try {
          await Firebase.initializeApp();
        } catch (fallbackError) {
          debugPrint('Fallback Firebase initialization error: $fallbackError');
          // Continue without Firebase
        }
      }
    }

    // Check if Firebase was actually initialized
    if (Firebase.apps.isNotEmpty) {
      debugPrint(
          'Firebase initialized successfully with ${Firebase.apps.length} apps');
      firebaseInitialized = true;

      // Print Firebase project details for verification
      try {
        final app = Firebase.app();
        debugPrint('Connected to Firebase project: ${app.options.projectId}');
        debugPrint('Firebase API Key: ${app.options.apiKey}');
        debugPrint('Firebase App ID: ${app.options.appId}');

        // Test Firestore connection
        final firestore = FirebaseFirestore.instance;
        firestore.collection('hotels').get().then((snapshot) {
          debugPrint('Successfully connected to Firestore');
          debugPrint('Number of hotels in collection: ${snapshot.docs.length}');
          if (snapshot.docs.isNotEmpty) {
            debugPrint('First hotel ID: ${snapshot.docs.first.id}');
            debugPrint('First hotel data: ${snapshot.docs.first.data()}');
          } else {
            debugPrint('No hotels found in Firestore collection');
          }
        }).catchError((error) {
          debugPrint('Error connecting to Firestore: $error');
        });
      } catch (e) {
        debugPrint('Error accessing Firebase app details: $e');
      }
    } else {
      debugPrint('Firebase initialization failed - no apps registered');
    }
  } catch (e) {
    debugPrint('Error in Firebase initialization process: $e');
    // Continue with app startup even if Firebase initialization fails
  }

  // Create services after Firebase initialization attempt
  try {
    // Create AuthService first
    authService = AuthService();
    debugPrint('Auth service created successfully');

    // Create database service if Firebase was initialized
    if (firebaseInitialized) {
      try {
        databaseService = DatabaseService();
        debugPrint('Database service created successfully');
      } catch (dbServiceError) {
        debugPrint('Error creating database service: $dbServiceError');
        // Continue without database service
      }

      // Initialize Firebase Analytics if Firebase was initialized
      try {
        analytics = FirebaseAnalytics.instance;
        debugPrint('Firebase Analytics initialized successfully');
      } catch (analyticsError) {
        debugPrint('Error initializing Firebase Analytics: $analyticsError');
        // Continue without analytics
      }

      // Check if database needs initialization
      if (databaseService != null && databaseService.isInitialized) {
        try {
          final isInitialized = await databaseService.isDatabaseInitialized();
          if (!isInitialized) {
            final success = await databaseService.initializeDatabase();
            debugPrint(
                'Database initialization ${success ? 'successful' : 'failed'}');
          }
        } catch (dbError) {
          debugPrint('Database initialization error: $dbError');
          // Continue with app startup even if database initialization fails
        }
      }
    }
  } catch (e) {
    debugPrint('Error creating services: $e');
    // Continue with app startup even if service creation fails
  }

  // Create a default AuthService if it failed to initialize
  authService ??= AuthService();

  runApp(
    MultiProvider(
      providers: [
        // Use the pre-initialized AuthService
        ChangeNotifierProvider.value(value: authService),
        ChangeNotifierProvider(create: (_) => HotelService()),
        ChangeNotifierProvider(create: (_) => BookingService()),
        ChangeNotifierProvider(create: (_) => AadhaarVerificationService()),
        ChangeNotifierProvider(create: (_) => ReviewService()),
        ChangeNotifierProvider(create: (_) => NotificationService()),
        ChangeNotifierProvider(create: (_) => ServiceRequestService()),
        ChangeNotifierProvider(create: (_) => LanguageService()),
        ChangeNotifierProvider(create: (_) => CurrencyService()),
        ChangeNotifierProvider(create: (_) => FaqService()),
        ChangeNotifierProvider(
            create: (context) => SupportService(authService!)),
        ChangeNotifierProvider(create: (_) => ContentService()),
        ChangeNotifierProvider(create: (_) => PaymentService()),
        ChangeNotifierProvider(create: (_) => MockPaymentService()),
        ChangeNotifierProvider(create: (_) => CheckinService()),
        // Only provide database service if it was successfully initialized
        if (databaseService != null)
          ChangeNotifierProvider.value(value: databaseService),
        // Only provide analytics if Firebase was successfully initialized
        if (analytics != null) Provider.value(value: analytics),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en', ''), // English
        Locale('es', ''), // Spanish
        Locale('fr', ''), // French
        // Add more locales as needed
      ],
      home: const SplashScreen(),
      routes: {
        '/admin': (context) => const AdminLoginScreen(),
        '/firebase_test': (context) => const FirebaseTestScreen(),
      },
    );
  }
}
