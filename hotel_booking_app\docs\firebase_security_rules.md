# Firebase Security Rules for Hotel Booking App

This document provides the recommended Firebase security rules for the hotel booking application, particularly focusing on the Aadhaar verification functionality.

## Firestore Security Rules

Add the following rules to your Firebase Firestore security rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Base rules
    match /{document=**} {
      allow read, write: if false; // Default deny all
    }
    
    // User profiles
    match /users/{userId} {
      allow read: if request.auth != null && (request.auth.uid == userId || isAdmin());
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Aadhaar verifications
    match /aadhaar_verifications/{verificationId} {
      // Allow users to read their own verification
      allow read: if request.auth != null && 
                   (resource.data.userId == request.auth.uid || isAdmin() || isHotelStaff());
      
      // Allow users to create/update their own verification
      allow create: if request.auth != null;
      allow update: if request.auth != null && 
                     (resource.data.userId == request.auth.uid || isAdmin());
      
      // Only admins can delete verifications
      allow delete: if isAdmin();
    }
    
    // Hotels
    match /hotels/{hotelId} {
      allow read: if true; // Public read access
      allow write: if isAdmin() || isHotelOwner(hotelId);
    }
    
    // Bookings
    match /bookings/{bookingId} {
      allow read: if request.auth != null && 
                   (resource.data.userId == request.auth.uid || 
                    isAdmin() || 
                    isHotelStaff(resource.data.hotelId));
      allow create: if request.auth != null;
      allow update: if request.auth != null && 
                     (resource.data.userId == request.auth.uid || 
                      isAdmin() || 
                      isHotelStaff(resource.data.hotelId));
      allow delete: if isAdmin();
    }
    
    // Helper functions
    function isAdmin() {
      return request.auth != null && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isHotelOwner(hotelId) {
      return request.auth != null && 
             exists(/databases/$(database)/documents/hotels/$(hotelId)) &&
             get(/databases/$(database)/documents/hotels/$(hotelId)).data.ownerId == request.auth.uid;
    }
    
    function isHotelStaff(hotelId) {
      return request.auth != null && 
             exists(/databases/$(database)/documents/hotels/$(hotelId)) &&
             (get(/databases/$(database)/documents/hotels/$(hotelId)).data.staffIds[request.auth.uid] == true ||
              get(/databases/$(database)/documents/hotels/$(hotelId)).data.ownerId == request.auth.uid);
    }
  }
}
```

## How to Apply These Rules

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to Firestore Database in the left sidebar
4. Click on the "Rules" tab
5. Replace the existing rules with the rules above
6. Click "Publish"

## Important Notes

- These rules assume you have a user document structure with a `role` field that can be set to 'admin'
- Hotel documents should have an `ownerId` field and a `staffIds` map for access control
- The Aadhaar verification collection should be named `aadhaar_verifications` with documents containing a `userId` field
- Always test your security rules thoroughly before deploying to production

## Troubleshooting

If you encounter permission errors like:

```
Error getting verification by user ID: [cloud_firestore/permission-denied] Missing or insufficient permission
```

Check the following:

1. Ensure the user is authenticated (signed in)
2. Verify that the security rules match the collection and field names in your code
3. Check that the user has the appropriate role or relationship to the document they're trying to access
4. Use the Firebase Rules Simulator to test your rules with different scenarios

## Testing Rules with the Rules Simulator

The Firebase Console provides a Rules Simulator that allows you to test your security rules with different authentication states and document paths. This is a valuable tool for ensuring your rules work as expected before publishing them.
