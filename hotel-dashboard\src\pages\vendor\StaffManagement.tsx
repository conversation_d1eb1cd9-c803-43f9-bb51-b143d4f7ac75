import React, { useState, useEffect } from 'react';
import { SelectChangeEvent } from '@mui/material';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Button,
  TextField,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  CircularProgress,
  Alert,
  Divider,
  FormControlLabel,
  Checkbox,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Work as WorkIcon,
  CheckCircle as ActiveIcon,
  Cancel as InactiveIcon,
  HotelOutlined as HotelIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { auth } from '../../firebase/config';
import { getHotelsForVendor } from '../../firebase/hotelService';
import {
  getStaffForHotel,
  createStaffMember,
  updateStaffMember,
  deleteStaffMember,
  getStaffAssignmentsForHotel,
  StaffMember,
  StaffAssignment,
  STAFF_ROLE_HOUSEKEEPING,
  STAFF_ROLE_FOOD_SERVICE,
  STAFF_ROLE_MAINTENANCE,
  STAFF_ROLE_FRONT_DESK,
  STAFF_ROLE_MANAGER,
  STAFF_STATUS_ACTIVE,
  STAFF_STATUS_INACTIVE,
  STAFF_STATUS_ON_LEAVE
} from '../../services/staffManagementService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`staff-tabpanel-${index}`}
      aria-labelledby={`staff-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
};

const StaffManagement: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [hotelId, setHotelId] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Staff list state
  const [staffList, setStaffList] = useState<StaffMember[]>([]);
  const [filteredStaffList, setFilteredStaffList] = useState<StaffMember[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Staff assignments state
  const [staffAssignments, setStaffAssignments] = useState<StaffAssignment[]>([]);
  const [loadingAssignments, setLoadingAssignments] = useState<boolean>(false);

  // Staff dialog state
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<'add' | 'edit'>('add');
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null);
  const [formData, setFormData] = useState<{
    name: string;
    email: string;
    phone: string;
    role: string;
    status: string;
    skills: string[];
    profileImage: string;
  }>({
    name: '',
    email: '',
    phone: '',
    role: STAFF_ROLE_HOUSEKEEPING,
    status: STAFF_STATUS_ACTIVE,
    skills: [],
    profileImage: ''
  });

  // Available skills based on role
  const availableSkills = {
    [STAFF_ROLE_HOUSEKEEPING]: ['regular cleaning', 'deep cleaning', 'turndown service', 'laundry'],
    [STAFF_ROLE_FOOD_SERVICE]: ['room service', 'food preparation', 'bartending', 'catering'],
    [STAFF_ROLE_MAINTENANCE]: ['plumbing', 'electrical', 'hvac', 'furniture repair', 'general repairs'],
    [STAFF_ROLE_FRONT_DESK]: ['check-in', 'check-out', 'customer service', 'reservations'],
    [STAFF_ROLE_MANAGER]: ['staff management', 'customer service', 'operations', 'scheduling']
  };

  useEffect(() => {
    const fetchHotelId = async () => {
      try {
        if (!auth.currentUser) {
          setError('You must be logged in to view staff management');
          setLoading(false);
          return;
        }

        const hotels = await getHotelsForVendor(auth.currentUser.uid);

        if (hotels.length === 0) {
          setError('No hotels found for this vendor');
          setLoading(false);
          return;
        }

        // Use the first hotel ID
        const id = hotels[0].id || '';
        setHotelId(id);

        // Fetch staff
        await fetchStaff(id);
      } catch (err: any) {
        console.error('Error fetching hotel ID:', err);
        setError(err.message || 'Failed to load hotel information');
        setLoading(false);
      }
    };

    fetchHotelId();
  }, []);

  useEffect(() => {
    // Apply filters whenever staff list, search query, or filters change
    applyFilters();
  }, [staffList, searchQuery, roleFilter, statusFilter]);

  const fetchStaff = async (hotelId: string) => {
    try {
      setLoading(true);
      setError(null);

      const staff = await getStaffForHotel(hotelId);
      setStaffList(staff);
      setFilteredStaffList(staff);

      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching staff:', err);
      setError(err.message || 'Failed to load staff');
      setLoading(false);
    }
  };

  const fetchStaffAssignments = async () => {
    try {
      setLoadingAssignments(true);

      const assignments = await getStaffAssignmentsForHotel(hotelId);
      setStaffAssignments(assignments);

      setLoadingAssignments(false);
    } catch (err: any) {
      console.error('Error fetching staff assignments:', err);
      setLoadingAssignments(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);

    // Fetch assignments when switching to the assignments tab
    if (newValue === 1) {
      fetchStaffAssignments();
    }
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handleRoleFilterChange = (event: SelectChangeEvent<string>) => {
    setRoleFilter(event.target.value);
  };

  const handleStatusFilterChange = (event: SelectChangeEvent<string>) => {
    setStatusFilter(event.target.value);
  };

  const applyFilters = () => {
    let filtered = [...staffList];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        staff =>
          staff.name.toLowerCase().includes(query) ||
          staff.email.toLowerCase().includes(query) ||
          (staff.phone && staff.phone.includes(query))
      );
    }

    // Apply role filter
    if (roleFilter !== 'all') {
      filtered = filtered.filter(staff => staff.role === roleFilter);
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(staff => staff.status === statusFilter);
    }

    setFilteredStaffList(filtered);
  };

  const handleOpenDialog = (mode: 'add' | 'edit', staff?: StaffMember) => {
    setDialogMode(mode);

    if (mode === 'edit' && staff) {
      setSelectedStaff(staff);
      setFormData({
        name: staff.name,
        email: staff.email,
        phone: staff.phone || '',
        role: staff.role,
        status: staff.status,
        skills: staff.skills || [],
        profileImage: staff.profileImage || ''
      });
    } else {
      setSelectedStaff(null);
      setFormData({
        name: '',
        email: '',
        phone: '',
        role: STAFF_ROLE_HOUSEKEEPING,
        status: STAFF_STATUS_ACTIVE,
        skills: [],
        profileImage: ''
      });
    }

    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const handleFormChange = (event: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = event.target;

    if (name) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));

      // Reset skills when role changes
      if (name === 'role') {
        setFormData(prev => ({
          ...prev,
          skills: []
        }));
      }
    }
  };

  // Type-safe wrapper for Select onChange
  const handleSelectChange = (event: SelectChangeEvent<string>) => {
    handleFormChange(event as unknown as React.ChangeEvent<{ name?: string; value: unknown }>);
  };

  const handleSkillChange = (skill: string) => {
    setFormData(prev => {
      const skills = [...prev.skills];

      if (skills.includes(skill)) {
        return {
          ...prev,
          skills: skills.filter(s => s !== skill)
        };
      } else {
        return {
          ...prev,
          skills: [...skills, skill]
        };
      }
    });
  };

  const handleSubmit = async () => {
    try {
      if (!auth.currentUser) {
        throw new Error('You must be logged in to manage staff');
      }

      if (dialogMode === 'add') {
        await createStaffMember({
          ...formData,
          hotelId,
          vendorId: auth.currentUser.uid
        });
      } else if (dialogMode === 'edit' && selectedStaff?.id) {
        await updateStaffMember(selectedStaff.id, formData);
      }

      // Refresh staff list
      await fetchStaff(hotelId);
      handleCloseDialog();
    } catch (err: any) {
      console.error('Error saving staff member:', err);
      setError(err.message || 'Failed to save staff member');
    }
  };

  const handleDeleteStaff = async (staffId: string) => {
    try {
      await deleteStaffMember(staffId);

      // Refresh staff list
      await fetchStaff(hotelId);
    } catch (err: any) {
      console.error('Error deleting staff member:', err);
      setError(err.message || 'Failed to delete staff member');
    }
  };

  const formatRole = (role: string) => {
    return role.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case STAFF_STATUS_ACTIVE:
        return <Chip label="Active" color="success" size="small" />;
      case STAFF_STATUS_INACTIVE:
        return <Chip label="Inactive" color="error" size="small" />;
      case STAFF_STATUS_ON_LEAVE:
        return <Chip label="On Leave" color="warning" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  // Paginate staff list
  const paginatedStaff = filteredStaffList.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Staff Management
      </Typography>

      <Tabs value={tabValue} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tab label="Staff List" />
        <Tab label="Assignments" />
      </Tabs>

      <TabPanel value={tabValue} index={0}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog('add')}
          >
            Add Staff Member
          </Button>

          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => fetchStaff(hotelId)}
          >
            Refresh
          </Button>
        </Box>

        <Paper sx={{ mb: 3, p: 2 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search by name, email, or phone"
                value={searchQuery}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel id="role-filter-label">Role</InputLabel>
                <Select
                  labelId="role-filter-label"
                  value={roleFilter}
                  label="Role"
                  onChange={handleRoleFilterChange}
                  startAdornment={
                    <InputAdornment position="start">
                      <WorkIcon />
                    </InputAdornment>
                  }
                >
                  <MenuItem value="all">All Roles</MenuItem>
                  <MenuItem value={STAFF_ROLE_HOUSEKEEPING}>Housekeeping</MenuItem>
                  <MenuItem value={STAFF_ROLE_FOOD_SERVICE}>Food Service</MenuItem>
                  <MenuItem value={STAFF_ROLE_MAINTENANCE}>Maintenance</MenuItem>
                  <MenuItem value={STAFF_ROLE_FRONT_DESK}>Front Desk</MenuItem>
                  <MenuItem value={STAFF_ROLE_MANAGER}>Manager</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel id="status-filter-label">Status</InputLabel>
                <Select
                  labelId="status-filter-label"
                  value={statusFilter}
                  label="Status"
                  onChange={handleStatusFilterChange}
                  startAdornment={
                    <InputAdornment position="start">
                      <FilterIcon />
                    </InputAdornment>
                  }
                >
                  <MenuItem value="all">All Statuses</MenuItem>
                  <MenuItem value={STAFF_STATUS_ACTIVE}>Active</MenuItem>
                  <MenuItem value={STAFF_STATUS_INACTIVE}>Inactive</MenuItem>
                  <MenuItem value={STAFF_STATUS_ON_LEAVE}>On Leave</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Paper>

        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Staff Member</TableCell>
                  <TableCell>Contact</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Skills</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedStaff.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      No staff members found
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedStaff.map((staff) => (
                    <TableRow key={staff.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {staff.profileImage ? (
                            <Avatar src={staff.profileImage} sx={{ mr: 2 }} />
                          ) : (
                            <Avatar sx={{ mr: 2 }}>
                              {staff.name.charAt(0)}
                            </Avatar>
                          )}
                          <Typography variant="body1">
                            {staff.name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                            <EmailIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2">
                              {staff.email}
                            </Typography>
                          </Box>
                          {staff.phone && (
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <PhoneIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                              <Typography variant="body2">
                                {staff.phone}
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatRole(staff.role)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {staff.skills && staff.skills.length > 0 ? (
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {staff.skills.map((skill, index) => (
                              <Chip key={index} label={skill} size="small" />
                            ))}
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            No skills listed
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        {getStatusChip(staff.status)}
                      </TableCell>
                      <TableCell>
                        <IconButton
                          color="primary"
                          onClick={() => handleOpenDialog('edit', staff)}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          color="error"
                          onClick={() => staff.id && handleDeleteStaff(staff.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredStaffList.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {/* Staff Assignments Tab */}
        <Typography variant="h6" gutterBottom>
          Current Staff Assignments
        </Typography>

        {loadingAssignments ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : staffAssignments.length === 0 ? (
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1">
              No staff assignments found
            </Typography>
          </Paper>
        ) : (
          <Paper>
            <List>
              {staffAssignments.map((assignment) => (
                <React.Fragment key={assignment.id}>
                  <ListItem alignItems="flex-start">
                    <ListItemAvatar>
                      <Avatar>
                        <PersonIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="subtitle1">
                            {assignment.staffName}
                          </Typography>
                          <Chip
                            label={assignment.status}
                            size="small"
                            color={
                              assignment.status === 'assigned' ? 'warning' :
                              assignment.status === 'in_progress' ? 'info' :
                              assignment.status === 'completed' ? 'success' :
                              'error'
                            }
                            sx={{ ml: 1 }}
                          />
                        </Box>
                      }
                      secondary={
                        <>
                          <Typography variant="body2" component="span">
                            Room {assignment.roomNumber} - {assignment.serviceRequestType.charAt(0).toUpperCase() + assignment.serviceRequestType.slice(1)} Request
                          </Typography>
                          <Typography variant="caption" display="block" color="text.secondary">
                            Assigned: {new Date(assignment.assignedAt.toDate()).toLocaleString()}
                          </Typography>
                          {assignment.completedAt && (
                            <Typography variant="caption" display="block" color="text.secondary">
                              Completed: {new Date(assignment.completedAt.toDate()).toLocaleString()}
                            </Typography>
                          )}
                        </>
                      }
                    />
                    <ListItemSecondaryAction>
                      <Chip
                        label={assignment.priority}
                        size="small"
                        color={
                          assignment.priority === 'high' ? 'error' :
                          assignment.priority === 'medium' ? 'warning' :
                          'info'
                        }
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  <Divider variant="inset" component="li" />
                </React.Fragment>
              ))}
            </List>
          </Paper>
        )}
      </TabPanel>

      {/* Staff Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {dialogMode === 'add' ? 'Add Staff Member' : 'Edit Staff Member'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Name"
                name="name"
                value={formData.name}
                onChange={handleFormChange}
                margin="normal"
                required
              />
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleFormChange}
                margin="normal"
                required
              />
              <TextField
                fullWidth
                label="Phone"
                name="phone"
                value={formData.phone}
                onChange={handleFormChange}
                margin="normal"
              />
              <TextField
                fullWidth
                label="Profile Image URL"
                name="profileImage"
                value={formData.profileImage}
                onChange={handleFormChange}
                margin="normal"
                helperText="Leave empty for default avatar"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="role-label">Role</InputLabel>
                <Select
                  labelId="role-label"
                  name="role"
                  value={formData.role}
                  label="Role"
                  onChange={handleSelectChange}
                >
                  <MenuItem value={STAFF_ROLE_HOUSEKEEPING}>Housekeeping</MenuItem>
                  <MenuItem value={STAFF_ROLE_FOOD_SERVICE}>Food Service</MenuItem>
                  <MenuItem value={STAFF_ROLE_MAINTENANCE}>Maintenance</MenuItem>
                  <MenuItem value={STAFF_ROLE_FRONT_DESK}>Front Desk</MenuItem>
                  <MenuItem value={STAFF_ROLE_MANAGER}>Manager</MenuItem>
                </Select>
              </FormControl>
              <FormControl fullWidth margin="normal">
                <InputLabel id="status-label">Status</InputLabel>
                <Select
                  labelId="status-label"
                  name="status"
                  value={formData.status}
                  label="Status"
                  onChange={handleSelectChange}
                >
                  <MenuItem value={STAFF_STATUS_ACTIVE}>Active</MenuItem>
                  <MenuItem value={STAFF_STATUS_INACTIVE}>Inactive</MenuItem>
                  <MenuItem value={STAFF_STATUS_ON_LEAVE}>On Leave</MenuItem>
                </Select>
              </FormControl>
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Skills
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {availableSkills[formData.role as keyof typeof availableSkills]?.map((skill) => (
                    <FormControlLabel
                      key={skill}
                      control={
                        <Checkbox
                          checked={formData.skills.includes(skill)}
                          onChange={() => handleSkillChange(skill)}
                          size="small"
                        />
                      }
                      label={skill}
                    />
                  ))}
                </Box>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!formData.name || !formData.email}
          >
            {dialogMode === 'add' ? 'Add Staff Member' : 'Save Changes'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StaffManagement;
