# Schedule Templates

The hotel booking system includes a powerful schedule templates feature that helps managers create and apply common staffing patterns. This document explains how to use schedule templates to streamline staff scheduling.

## What Are Schedule Templates?

Schedule templates are predefined patterns of staff shifts that can be applied to create schedules for future weeks. They help ensure consistent staffing patterns and save time when creating schedules.

## Types of Templates

The system supports three types of templates:

1. **Weekly Templates**: Define a complete week's schedule with shifts for each day of the week.
2. **Daily Templates**: Define a single day's schedule that can be applied to any day.
3. **Custom Templates**: Define custom scheduling patterns for special situations.

## Managing Templates

### Creating Templates

To create a new schedule template:

1. Navigate to the Staff Scheduling page
2. Click on the "Schedule Templates" tab
3. Click the "New Template" button
4. Enter a name and description for the template
5. Select the template type (weekly, daily, or custom)
6. Optionally, set the template as the default
7. Click "Create Template"

### Adding Shifts to Templates

To add shifts to a template:

1. Select the template from the list
2. Click the "Add Shift" button
3. Select the day of the week (for weekly templates)
4. Select the role for the shift
5. Select the shift type (morning, afternoon, night, or custom)
6. If custom, set the start and end times
7. Add any notes for the shift
8. Click "Add Shift"

### Editing Templates

To edit an existing template:

1. Click the edit icon next to the template name
2. Update the template details
3. Click "Save Changes"

### Editing Template Shifts

To edit a shift in a template:

1. Click the edit icon next to the shift
2. Update the shift details
3. Click "Save Changes"

### Deleting Templates

To delete a template:

1. Click the delete icon next to the template name
2. Confirm the deletion

### Deleting Template Shifts

To delete a shift from a template:

1. Click the delete icon next to the shift
2. The shift will be removed immediately

## Applying Templates

### Applying a Template to Create Schedules

To apply a template to create actual staff schedules:

1. Navigate to the Staff Scheduling page
2. Click on the "Schedule Templates" tab
3. In the "Apply Template" section, select the template to apply
4. Select the week starting date
5. Assign staff members to each role in the template
6. Click "Apply Template"

### Conflict Detection

When applying a template, the system checks for potential scheduling conflicts:

1. **Shift Overlaps**: Checks if staff members are scheduled for overlapping shifts
2. **Time Off Conflicts**: Checks if staff members are scheduled during approved time off
3. **Consecutive Shifts**: Checks if staff members have sufficient rest between shifts
4. **Maximum Hours**: Checks if staff members exceed maximum working hours

If conflicts are detected, you'll be shown a dialog with the details and can choose to proceed or cancel.

## Benefits of Using Schedule Templates

Using schedule templates provides several benefits:

1. **Consistency**: Ensures consistent staffing patterns across weeks
2. **Efficiency**: Saves time when creating schedules
3. **Flexibility**: Allows for different templates for different situations
4. **Conflict Prevention**: Helps prevent scheduling conflicts
5. **Staff Satisfaction**: Helps ensure fair and predictable schedules

## Sample Templates

The system comes with several sample templates:

### Standard Weekly Schedule

A complete weekly schedule with morning, afternoon, and night shifts for all days of the week.

### Weekend Coverage

A template focused on weekend coverage with increased staffing for Friday, Saturday, and Sunday.

### Holiday Schedule

A reduced staffing template for holidays.

## Tips for Effective Template Use

1. **Create Role-Specific Templates**: Create templates for specific roles or departments
2. **Seasonal Templates**: Create templates for different seasons or busy periods
3. **Default Template**: Set your most commonly used template as the default
4. **Template Variations**: Create variations of templates for different staffing levels
5. **Review and Update**: Regularly review and update templates to reflect changing needs

## Technical Details

Templates and their shifts are stored in the following Firestore collections:

- `scheduleTemplates`: Stores template metadata
- `templateShifts`: Stores individual shifts within templates

When a template is applied, the system creates actual shifts in the `staffSchedules` collection based on the template shifts and staff assignments.
