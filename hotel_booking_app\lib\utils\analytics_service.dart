import 'package:flutter/foundation.dart';

/// Service for tracking analytics events and logging errors
class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;

  AnalyticsService._internal();

  /// Log an event with the given name and parameters
  Future<void> logEvent({
    required String name,
    Map<String, dynamic>? parameters,
  }) async {
    debugPrint('Analytics event logged: $name');
    if (parameters != null) {
      debugPrint('Parameters: $parameters');
    }
  }

  /// Log an error with the given message and error details
  Future<void> logError({
    required String message,
    dynamic error,
    StackTrace? stackTrace,
  }) async {
    debugPrint('ERROR: $message');
    if (error != null) {
      debugPrint('Error details: $error');
    }
    if (stackTrace != null) {
      debugPrint('Stack trace: $stackTrace');
    }
  }

  /// Log a screen view
  Future<void> logScreenView({
    required String screenName,
    String? screenClass,
  }) async {
    debugPrint('Screen view logged: $screenName');
  }

  /// Log a verification event
  Future<void> logVerificationEvent({
    required String action,
    required bool success,
    String? errorMessage,
    Map<String, dynamic>? additionalParams,
  }) async {
    final params = <String, dynamic>{
      'success': success,
    };

    if (errorMessage != null) {
      params['error_message'] = errorMessage;
    }

    if (additionalParams != null) {
      params.addAll(additionalParams);
    }

    debugPrint('Verification event logged: verification_$action');
    debugPrint('Parameters: $params');
  }
}
