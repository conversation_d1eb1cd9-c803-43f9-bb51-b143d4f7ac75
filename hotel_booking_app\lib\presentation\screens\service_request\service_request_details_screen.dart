import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:hotel_booking_app/data/models/booking_model.dart';
import 'package:hotel_booking_app/data/models/service_request_model.dart';
import 'package:hotel_booking_app/data/services/service_request_service.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_app_bar.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_button.dart';

class ServiceRequestDetailsScreen extends StatefulWidget {
  final BaseServiceRequest request;
  final Booking booking;

  const ServiceRequestDetailsScreen({
    super.key,
    required this.request,
    required this.booking,
  });

  @override
  _ServiceRequestDetailsScreenState createState() =>
      _ServiceRequestDetailsScreenState();
}

class _ServiceRequestDetailsScreenState
    extends State<ServiceRequestDetailsScreen> {
  bool _isLoading = false;
  String? _errorMessage;
  late BaseServiceRequest _request;

  @override
  void initState() {
    super.initState();
    _request = widget.request;
    _refreshRequestDetails();
  }

  Future<void> _refreshRequestDetails() async {
    if (_request.id == null) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final serviceRequestService =
          Provider.of<ServiceRequestService>(context, listen: false);
      final updatedRequest =
          await serviceRequestService.getServiceRequestById(_request.id!);

      if (updatedRequest != null) {
        setState(() {
          _request = updatedRequest;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error refreshing request details: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '${_getServiceTypeTitle(_request.type)} Details',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _refreshRequestDetails,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStatusCard(),
                    const SizedBox(height: 16),
                    _buildRequestDetailsCard(),
                    const SizedBox(height: 16),
                    _buildServiceSpecificDetails(),
                    if (_request.status == 'pending')
                      Padding(
                        padding: const EdgeInsets.only(top: 24),
                        child: CustomButton(
                          text: 'Cancel Request',
                          onPressed: _cancelRequest,
                          color: Colors.red,
                          icon: Icons.cancel,
                        ),
                      ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildStatusCard() {
    final statusColor = _getStatusColor(_request.status);
    final statusText = _getStatusText(_request.status);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              _getServiceTypeIcon(_request.type),
              size: 48,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getServiceTypeTitle(_request.type),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Room ${_request.roomNumber}',
                    style: TextStyle(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 6,
              ),
              decoration: BoxDecoration(
                color: statusColor.withAlpha(25),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: statusColor),
              ),
              child: Text(
                statusText,
                style: TextStyle(
                  color: statusColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequestDetailsCard() {
    final requestTime =
        DateFormat('MMM d, yyyy h:mm a').format(_request.requestTime.toDate());

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Request Details',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailRow('Request Time', requestTime),
            _buildDetailRow('Priority', _formatPriority(_request.priority)),
            if (_request.assignedTo != null)
              _buildDetailRow('Assigned To', _request.assignedTo!),
            if (_request.completedTime != null)
              _buildDetailRow(
                'Completed Time',
                DateFormat('MMM d, yyyy h:mm a')
                    .format(_request.completedTime!.toDate()),
              ),
            if (_request.notes.isNotEmpty) ...[
              const SizedBox(height: 8),
              const Text(
                'Notes:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(_request.notes),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildServiceSpecificDetails() {
    if (_request is CleaningRequest) {
      return _buildCleaningDetails(_request as CleaningRequest);
    } else if (_request is FoodOrder) {
      return _buildFoodOrderDetails(_request as FoodOrder);
    } else if (_request is MaintenanceRequest) {
      return _buildMaintenanceDetails(_request as MaintenanceRequest);
    }

    return const SizedBox.shrink();
  }

  Widget _buildCleaningDetails(CleaningRequest request) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Cleaning Details',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailRow(
                'Cleaning Type', _formatCleaningType(request.cleaningType)),
            if (request.items != null && request.items!.isNotEmpty) ...[
              const SizedBox(height: 8),
              const Text(
                'Items to Clean:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: request.items!.map((item) {
                  return Chip(
                    label: Text(item),
                    backgroundColor:
                        Theme.of(context).primaryColor.withAlpha(25),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFoodOrderDetails(FoodOrder order) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Food Order Details',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (order.deliveryTime != null)
              _buildDetailRow(
                'Delivery Time',
                DateFormat('MMM d, yyyy h:mm a')
                    .format(order.deliveryTime!.toDate()),
              ),
            if (order.paymentMethod != null)
              _buildDetailRow('Payment Method', order.paymentMethod!),
            const SizedBox(height: 8),
            const Text(
              'Items:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: order.items.length,
              itemBuilder: (context, index) {
                final item = order.items[index];
                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  title: Text(item.name),
                  subtitle: item.specialInstructions != null
                      ? Text('Special: ${item.specialInstructions}')
                      : null,
                  trailing: Text(
                    '${item.quantity} x \$${item.price.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                );
              },
            ),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Total:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  '\$${order.totalAmount.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceDetails(MaintenanceRequest request) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Maintenance Details',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailRow('Issue Type', _formatIssueType(request.issueType)),
            if (request.estimatedCompletionTime != null)
              _buildDetailRow(
                'Estimated Completion',
                DateFormat('MMM d, yyyy h:mm a')
                    .format(request.estimatedCompletionTime!.toDate()),
              ),
            const SizedBox(height: 8),
            const Text(
              'Description:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(request.description),
            if (request.partsRequired != null &&
                request.partsRequired!.isNotEmpty) ...[
              const SizedBox(height: 8),
              const Text(
                'Parts Required:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: request.partsRequired!.map((part) {
                  return Chip(
                    label: Text(part),
                    backgroundColor:
                        Theme.of(context).primaryColor.withAlpha(25),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelRequest() async {
    if (_request.id == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Request'),
        content:
            const Text('Are you sure you want to cancel this service request?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Yes'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final serviceRequestService =
          Provider.of<ServiceRequestService>(context, listen: false);
      await serviceRequestService.cancelServiceRequest(_request.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Service request cancelled successfully'),
            backgroundColor: Colors.green,
          ),
        );

        await _refreshRequestDetails();
        if (mounted) {
          Navigator.pop(context, true);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error cancelling request: ${e.toString()}';
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  IconData _getServiceTypeIcon(String serviceType) {
    switch (serviceType) {
      case 'cleaning':
        return Icons.cleaning_services;
      case 'food':
        return Icons.restaurant;
      case 'maintenance':
        return Icons.build;
      default:
        return Icons.miscellaneous_services;
    }
  }

  String _getServiceTypeTitle(String serviceType) {
    switch (serviceType) {
      case 'cleaning':
        return 'Cleaning';
      case 'food':
        return 'Food Service';
      case 'maintenance':
        return 'Maintenance';
      default:
        return 'Service';
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'in_progress':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatPriority(String priority) {
    switch (priority) {
      case 'low':
        return 'Low';
      case 'medium':
        return 'Medium';
      case 'high':
        return 'High';
      default:
        return priority;
    }
  }

  String _formatCleaningType(String? cleaningType) {
    if (cleaningType == null) return 'Regular';

    switch (cleaningType) {
      case 'regular':
        return 'Regular Cleaning';
      case 'deep':
        return 'Deep Cleaning';
      case 'turndown':
        return 'Turndown Service';
      case 'special':
        return 'Special Request';
      default:
        return cleaningType;
    }
  }

  String _formatIssueType(String issueType) {
    switch (issueType) {
      case 'plumbing':
        return 'Plumbing';
      case 'electrical':
        return 'Electrical';
      case 'hvac':
        return 'HVAC/Climate Control';
      case 'furniture':
        return 'Furniture';
      case 'appliance':
        return 'Appliance';
      case 'structural':
        return 'Structural';
      case 'other':
        return 'Other';
      default:
        return issueType;
    }
  }
}
