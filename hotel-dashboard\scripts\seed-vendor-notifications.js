const admin = require('firebase-admin');
const serviceAccount = require('../serviceAccountKey.json');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

// Constants
const VENDOR_NOTIFICATIONS_COLLECTION = 'vendorNotifications';
const NOTIFICATION_TYPE_SERVICE_REQUEST = 'service_request';
const NOTIFICATION_TYPE_BOOKING = 'booking';
const NOTIFICATION_TYPE_VERIFICATION = 'verification';
const NOTIFICATION_TYPE_SYSTEM = 'system';
const NOTIFICATION_STATUS_UNREAD = 'unread';
const NOTIFICATION_STATUS_READ = 'read';

// Sample vendor ID (replace with actual vendor ID)
const VENDOR_ID = 'vendor123';
const HOTEL_ID = 'hotel123';

// Sample notifications
const sampleNotifications = [
  // Service request notifications
  {
    title: 'New Cleaning Request',
    message: 'Room 101 has requested cleaning service.',
    type: NOTIFICATION_TYPE_SERVICE_REQUEST,
    status: NOTIFICATION_STATUS_UNREAD,
    vendorId: VENDOR_ID,
    hotelId: HOTEL_ID,
    relatedId: 'cleaning123',
    relatedType: 'cleaning',
    createdAt: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 30 * 60 * 1000)), // 30 minutes ago
    priority: 'medium',
    actions: [
      {
        label: 'View Request',
        url: '/vendor/services/cleaning?id=cleaning123'
      }
    ]
  },
  {
    title: 'New Food Order',
    message: 'Room 203 has placed a food order.',
    type: NOTIFICATION_TYPE_SERVICE_REQUEST,
    status: NOTIFICATION_STATUS_UNREAD,
    vendorId: VENDOR_ID,
    hotelId: HOTEL_ID,
    relatedId: 'food123',
    relatedType: 'food',
    createdAt: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 45 * 60 * 1000)), // 45 minutes ago
    priority: 'high',
    actions: [
      {
        label: 'View Order',
        url: '/vendor/services/food?id=food123'
      }
    ]
  },
  {
    title: 'Maintenance Request',
    message: 'Room 305 has reported a leaking faucet.',
    type: NOTIFICATION_TYPE_SERVICE_REQUEST,
    status: NOTIFICATION_STATUS_READ,
    vendorId: VENDOR_ID,
    hotelId: HOTEL_ID,
    relatedId: 'maintenance123',
    relatedType: 'maintenance',
    createdAt: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 2 * 60 * 60 * 1000)), // 2 hours ago
    readAt: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 1.5 * 60 * 60 * 1000)), // 1.5 hours ago
    priority: 'medium',
    actions: [
      {
        label: 'View Request',
        url: '/vendor/services/maintenance?id=maintenance123'
      }
    ]
  },
  
  // Booking notifications
  {
    title: 'New Booking',
    message: 'John Doe has made a new booking for Room 101. Booking #BK12345.',
    type: NOTIFICATION_TYPE_BOOKING,
    status: NOTIFICATION_STATUS_UNREAD,
    vendorId: VENDOR_ID,
    hotelId: HOTEL_ID,
    relatedId: 'booking123',
    relatedType: 'booking',
    createdAt: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 3 * 60 * 60 * 1000)), // 3 hours ago
    priority: 'high',
    actions: [
      {
        label: 'View Booking',
        url: '/vendor/bookings?id=booking123'
      }
    ]
  },
  {
    title: 'Booking Cancelled',
    message: 'Jane Smith has cancelled their booking for Room 203. Booking #BK12346.',
    type: NOTIFICATION_TYPE_BOOKING,
    status: NOTIFICATION_STATUS_READ,
    vendorId: VENDOR_ID,
    hotelId: HOTEL_ID,
    relatedId: 'booking124',
    relatedType: 'booking',
    createdAt: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 5 * 60 * 60 * 1000)), // 5 hours ago
    readAt: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 4.5 * 60 * 60 * 1000)), // 4.5 hours ago
    priority: 'medium',
    actions: [
      {
        label: 'View Booking',
        url: '/vendor/bookings?id=booking124'
      }
    ]
  },
  
  // Verification notifications
  {
    title: 'New Aadhaar Verification',
    message: 'Robert Johnson has completed Aadhaar verification.',
    type: NOTIFICATION_TYPE_VERIFICATION,
    status: NOTIFICATION_STATUS_UNREAD,
    vendorId: VENDOR_ID,
    hotelId: HOTEL_ID,
    relatedId: 'verification123',
    relatedType: 'aadhaar',
    createdAt: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 6 * 60 * 60 * 1000)), // 6 hours ago
    priority: 'low',
    actions: [
      {
        label: 'View Verification',
        url: '/vendor/verifications/verification123'
      }
    ]
  },
  
  // System notifications
  {
    title: 'System Maintenance',
    message: 'The system will undergo maintenance tonight from 2 AM to 4 AM.',
    type: NOTIFICATION_TYPE_SYSTEM,
    status: NOTIFICATION_STATUS_READ,
    vendorId: VENDOR_ID,
    hotelId: HOTEL_ID,
    createdAt: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 24 * 60 * 60 * 1000)), // 1 day ago
    readAt: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 23 * 60 * 60 * 1000)), // 23 hours ago
    priority: 'medium'
  },
  {
    title: 'New Feature Available',
    message: 'We have added a new feature to help you manage room inventory more efficiently.',
    type: NOTIFICATION_TYPE_SYSTEM,
    status: NOTIFICATION_STATUS_UNREAD,
    vendorId: VENDOR_ID,
    hotelId: HOTEL_ID,
    createdAt: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)), // 2 days ago
    priority: 'low',
    actions: [
      {
        label: 'Learn More',
        url: '/vendor/settings'
      }
    ]
  }
];

// Seed notifications
async function seedVendorNotifications() {
  try {
    console.log('Seeding vendor notifications...');
    
    // Clear existing notifications
    const existingNotifications = await db.collection(VENDOR_NOTIFICATIONS_COLLECTION)
      .where('vendorId', '==', VENDOR_ID)
      .get();
    
    const batch = db.batch();
    
    existingNotifications.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
    console.log(`Deleted ${existingNotifications.size} existing notifications.`);
    
    // Add new notifications
    const notificationPromises = sampleNotifications.map(notification => 
      db.collection(VENDOR_NOTIFICATIONS_COLLECTION).add(notification)
    );
    
    await Promise.all(notificationPromises);
    
    console.log(`Added ${sampleNotifications.length} new notifications.`);
    console.log('Vendor notifications seeded successfully!');
  } catch (error) {
    console.error('Error seeding vendor notifications:', error);
  }
}

// Run the seed function
seedVendorNotifications()
  .then(() => {
    console.log('Script completed successfully!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
