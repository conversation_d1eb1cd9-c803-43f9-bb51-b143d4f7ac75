import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Button,
  CircularProgress,
  Alert,
  Tooltip,
  Badge,
  Divider
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  PhoneAndroid as PhoneIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { searchBookings, BookingData } from '../services/bookingService';
import { getBookingStatusInfo, getPaymentStatusInfo, formatDateDDMMMYYYY, formatCurrency } from '../utils/bookingUtils';

interface MobileAppBookingsProps {
  hotelId: string;
  onViewBooking?: (booking: BookingData) => void;
  onCheckIn?: (booking: BookingData) => void;
  onCancel?: (booking: BookingData) => void;
  limit?: number;
}

const MobileAppBookings: React.FC<MobileAppBookingsProps> = ({
  hotelId,
  onViewBooking,
  onCheckIn,
  onCancel,
  limit = 5
}) => {
  const [bookings, setBookings] = useState<BookingData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newBookingsCount, setNewBookingsCount] = useState(0);

  // Fetch bookings on component mount and when hotelId changes
  useEffect(() => {
    if (hotelId) {
      fetchMobileAppBookings();
    }
  }, [hotelId]);

  // Fetch mobile app bookings
  const fetchMobileAppBookings = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await searchBookings({
        hotelId,
        limit,
        bookingStatus: ['confirmed', 'pending']
      });

      setBookings(result.bookings);

      // Count new bookings (less than 24 hours old)
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

      const newBookings = result.bookings.filter(booking => {
        if (!booking.createdAt) return false;
        const bookingDate = booking.createdAt.toDate();
        return bookingDate > twentyFourHoursAgo;
      });

      setNewBookingsCount(newBookings.length);
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching mobile app bookings:', err);
      setError(err.message || 'Failed to load mobile app bookings');
      setLoading(false);
    }
  };

  // Handle view booking
  const handleViewBooking = (booking: BookingData) => {
    if (onViewBooking) {
      onViewBooking(booking);
    }
  };

  // Handle check-in
  const handleCheckIn = (booking: BookingData) => {
    if (onCheckIn) {
      onCheckIn(booking);
    }
  };

  // Handle cancel
  const handleCancel = (booking: BookingData) => {
    if (onCancel) {
      onCancel(booking);
    }
  };

  return (
    <Paper sx={{ p: 3, height: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <PhoneIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6">
            Mobile App Bookings
            {newBookingsCount > 0 && (
              <Badge
                badgeContent={newBookingsCount}
                color="error"
                sx={{ ml: 1 }}
              />
            )}
          </Typography>
        </Box>
        <Button
          startIcon={<RefreshIcon />}
          size="small"
          onClick={fetchMobileAppBookings}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      <Divider sx={{ mb: 2 }} />

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : bookings.length === 0 ? (
        <Alert severity="info">
          No mobile app bookings found.
        </Alert>
      ) : (
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Booking #</TableCell>
                <TableCell>Guest</TableCell>
                <TableCell>Dates</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Amount</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {bookings.map((booking) => {
                const bookingStatus = getBookingStatusInfo(booking.bookingStatus);
                const paymentStatus = getPaymentStatusInfo(booking.paymentStatus);

                return (
                  <TableRow key={booking.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {booking.bookingNumber}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {booking.createdAt && formatDateDDMMMYYYY(booking.createdAt.toDate())}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {booking.guestInfo.firstName} {booking.guestInfo.lastName}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {booking.guestInfo.email}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatDateDDMMMYYYY(booking.checkInDate.toDate())}
                      </Typography>
                      <Typography variant="body2">
                        to {formatDateDDMMMYYYY(booking.checkOutDate.toDate())}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={bookingStatus.text}
                        color={bookingStatus.color as any}
                        size="small"
                        sx={{ mb: 0.5 }}
                      />
                      <br />
                      <Chip
                        label={paymentStatus.text}
                        color={paymentStatus.color as any}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      {formatCurrency(booking.totalAmount)}
                    </TableCell>
                    <TableCell align="right">
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleViewBooking(booking)}
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>

                      {booking.bookingStatus === 'confirmed' && (
                        <Tooltip title="Check In">
                          <IconButton
                            size="small"
                            color="success"
                            onClick={() => handleCheckIn(booking)}
                          >
                            <CheckCircleIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}

                      {(booking.bookingStatus === 'confirmed' || booking.bookingStatus === 'pending') && (
                        <Tooltip title="Cancel">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleCancel(booking)}
                          >
                            <CancelIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {bookings.length > 0 && (
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
          <Button
            variant="outlined"
            size="small"
            onClick={() => onViewBooking && onViewBooking(null as any)}
          >
            View All Mobile Bookings
          </Button>
        </Box>
      )}
    </Paper>
  );
};

export default MobileAppBookings;
