import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';

/// Utility class to check Firebase configuration and connectivity
class FirebaseChecker {
  /// Check if Firebase is properly initialized
  static bool isFirebaseInitialized() {
    try {
      return Firebase.apps.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking Firebase initialization: $e');
      return false;
    }
  }

  /// Get Firebase app information
  static Map<String, dynamic> getFirebaseAppInfo() {
    try {
      final app = Firebase.app();
      return {
        'name': app.name,
        'options': {
          'apiKey': app.options.apiKey,
          'appId': app.options.appId,
          'messagingSenderId': app.options.messagingSenderId,
          'projectId': app.options.projectId,
        },
        'isAutomaticDataCollectionEnabled': app.isAutomaticDataCollectionEnabled,
      };
    } catch (e) {
      debugPrint('Error getting Firebase app info: $e');
      return {'error': e.toString()};
    }
  }

  /// Check Firestore connectivity
  static Future<bool> checkFirestoreConnectivity() async {
    try {
      // Try to read a document from Firestore
      await FirebaseFirestore.instance.collection('test_connectivity').doc('test').get();
      return true;
    } catch (e) {
      debugPrint('Error checking Firestore connectivity: $e');
      return false;
    }
  }

  /// Check if a collection exists
  static Future<bool> checkCollectionExists(String collectionPath) async {
    try {
      final querySnapshot = await FirebaseFirestore.instance.collection(collectionPath).limit(1).get();
      debugPrint('Collection $collectionPath exists: ${querySnapshot.size > 0 ? 'Yes (has documents)' : 'Yes (empty)'}');
      return true;
    } catch (e) {
      debugPrint('Error checking if collection exists: $e');
      debugPrint('Collection path: $collectionPath');
      return false;
    }
  }

  /// Create a test document in a collection
  static Future<String?> createTestDocument(String collectionPath) async {
    try {
      final docRef = await FirebaseFirestore.instance.collection(collectionPath).add({
        'test': true,
        'timestamp': FieldValue.serverTimestamp(),
        'createdBy': FirebaseAuth.instance.currentUser?.uid ?? 'anonymous',
      });
      debugPrint('Created test document in $collectionPath with ID: ${docRef.id}');
      return docRef.id;
    } catch (e) {
      debugPrint('Error creating test document: $e');
      return null;
    }
  }

  /// Delete a test document
  static Future<bool> deleteTestDocument(String collectionPath, String documentId) async {
    try {
      await FirebaseFirestore.instance.collection(collectionPath).doc(documentId).delete();
      debugPrint('Deleted test document $documentId from $collectionPath');
      return true;
    } catch (e) {
      debugPrint('Error deleting test document: $e');
      return false;
    }
  }

  /// Run a comprehensive Firebase check
  static Future<Map<String, dynamic>> runComprehensiveCheck() async {
    final results = <String, dynamic>{};
    
    // Check Firebase initialization
    results['isFirebaseInitialized'] = isFirebaseInitialized();
    
    // Get Firebase app info
    results['firebaseAppInfo'] = getFirebaseAppInfo();
    
    // Check Firestore connectivity
    results['firestoreConnectivity'] = await checkFirestoreConnectivity();
    
    // Check if collections exist
    results['collectionsExist'] = {
      'users': await checkCollectionExists(AppConstants.usersCollection),
      'aadhaar_verifications': await checkCollectionExists(AppConstants.aadhaarVerificationsCollection),
      'hotels': await checkCollectionExists(AppConstants.hotelsCollection),
      'bookings': await checkCollectionExists(AppConstants.bookingsCollection),
    };
    
    // Check authentication
    final user = FirebaseAuth.instance.currentUser;
    results['authentication'] = {
      'isAuthenticated': user != null,
      'userId': user?.uid,
      'email': user?.email,
      'displayName': user?.displayName,
    };
    
    // Create and delete a test document if authenticated
    if (user != null) {
      final testDocId = await createTestDocument('test_permissions');
      if (testDocId != null) {
        results['testDocumentCreated'] = true;
        results['testDocumentDeleted'] = await deleteTestDocument('test_permissions', testDocId);
      } else {
        results['testDocumentCreated'] = false;
      }
    }
    
    return results;
  }

  /// Show Firebase check results dialog
  static Future<void> showFirebaseCheckDialog(BuildContext context) async {
    final results = await runComprehensiveCheck();
    
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Firebase Configuration Check'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Firebase Initialized: ${results['isFirebaseInitialized']}'),
                const SizedBox(height: 8),
                Text('Firestore Connectivity: ${results['firestoreConnectivity']}'),
                const SizedBox(height: 8),
                Text('Authentication: ${results['authentication']['isAuthenticated'] ? 'Yes' : 'No'}'),
                if (results['authentication']['isAuthenticated'])
                  Text('User ID: ${results['authentication']['userId']}'),
                const SizedBox(height: 8),
                const Text('Collections Exist:'),
                Text('  - users: ${results['collectionsExist']['users']}'),
                Text('  - aadhaar_verifications: ${results['collectionsExist']['aadhaar_verifications']}'),
                Text('  - hotels: ${results['collectionsExist']['hotels']}'),
                Text('  - bookings: ${results['collectionsExist']['bookings']}'),
                if (results.containsKey('testDocumentCreated'))
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 8),
                      Text('Test Document Created: ${results['testDocumentCreated']}'),
                      Text('Test Document Deleted: ${results['testDocumentDeleted']}'),
                    ],
                  ),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Close'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
}
