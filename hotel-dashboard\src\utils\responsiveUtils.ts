import { useMediaQuery, useTheme } from '@mui/material';

/**
 * Custom hook to check if the current viewport is mobile
 * @returns boolean indicating if the viewport is mobile
 */
export const useIsMobile = (): boolean => {
  const theme = useTheme();
  return useMediaQuery(theme.breakpoints.down('md'));
};

/**
 * Custom hook to check if the current viewport is a tablet
 * @returns boolean indicating if the viewport is a tablet
 */
export const useIsTablet = (): boolean => {
  const theme = useTheme();
  return useMediaQuery(theme.breakpoints.between('sm', 'md'));
};

/**
 * Custom hook to check if the current viewport is a desktop
 * @returns boolean indicating if the viewport is a desktop
 */
export const useIsDesktop = (): boolean => {
  const theme = useTheme();
  return useMediaQuery(theme.breakpoints.up('md'));
};

/**
 * Get responsive padding based on screen size
 * @returns object with responsive padding values
 */
export const getResponsivePadding = () => ({
  xs: 2,
  sm: 3,
  md: 4,
});

/**
 * Get responsive spacing based on screen size
 * @returns object with responsive spacing values
 */
export const getResponsiveSpacing = () => ({
  xs: 1,
  sm: 2,
  md: 3,
});

/**
 * Get responsive font sizes based on screen size
 * @returns object with responsive font size values
 */
export const getResponsiveFontSizes = () => ({
  h1: {
    xs: '1.75rem',
    sm: '2.25rem',
    md: '2.5rem',
  },
  h2: {
    xs: '1.5rem',
    sm: '1.75rem',
    md: '2rem',
  },
  h3: {
    xs: '1.25rem',
    sm: '1.5rem',
    md: '1.75rem',
  },
  h4: {
    xs: '1.125rem',
    sm: '1.25rem',
    md: '1.5rem',
  },
  h5: {
    xs: '1rem',
    sm: '1.125rem',
    md: '1.25rem',
  },
  h6: {
    xs: '0.875rem',
    sm: '1rem',
    md: '1.125rem',
  },
  body1: {
    xs: '0.875rem',
    sm: '1rem',
    md: '1rem',
  },
  body2: {
    xs: '0.75rem',
    sm: '0.875rem',
    md: '0.875rem',
  },
});

export default {
  useIsMobile,
  useIsTablet,
  useIsDesktop,
  getResponsivePadding,
  getResponsiveSpacing,
  getResponsiveFontSizes,
};
