import {
  collection,
  doc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  getDocs,
  getDoc,
  serverTimestamp,
  Timestamp,
  orderBy,
  limit,
  startAfter,
  addDoc,
  runTransaction
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { getRoomById, updateRoomAvailabilityRange } from './roomService';
import { calculateRoomPrice } from './pricingService';

// Interface for booking data
export interface BookingData {
  id?: string;
  bookingNumber?: string;
  hotelId: string;
  roomId: string;
  userId: string;
  guestInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    address?: string;
    city?: string;
    country?: string;
    zipCode?: string;
    specialRequests?: string;
  };
  checkInDate: Timestamp;
  checkOutDate: Timestamp;
  adults: number;
  children: number;
  totalAmount: number;
  paymentStatus: 'pending' | 'paid' | 'partially_paid' | 'refunded' | 'cancelled';
  bookingStatus: 'confirmed' | 'pending' | 'checked_in' | 'checked_out' | 'cancelled' | 'no_show';
  paymentMethod?: string;
  source: string; // 'website', 'mobile_app', 'phone', 'walk_in', 'third_party'
  sourceDetails?: string; // e.g., 'Booking.com', 'Expedia', etc.
  notes?: string;
  cancellationReason?: string;
  cancellationDate?: Timestamp;
  refundAmount?: number;
  addons?: BookingAddon[];
  taxAmount?: number;
  taxRate?: number;
  discountCode?: string;
  discountAmount?: number;
  createdBy?: string;
  createdAt?: any;
  updatedAt?: any;
}

// Interface for booking addon
export interface BookingAddon {
  id: string;
  name: string;
  description?: string;
  price: number;
  quantity: number;
  date?: Timestamp;
}

// Interface for booking transaction
export interface BookingTransaction {
  id?: string;
  bookingId: string;
  amount: number;
  type: 'payment' | 'refund' | 'adjustment';
  method: string;
  status: 'completed' | 'pending' | 'failed';
  reference?: string;
  notes?: string;
  createdBy: string;
  createdAt?: any;
}

// Interface for booking search params
export interface BookingSearchParams {
  hotelId?: string;
  userId?: string;
  bookingNumber?: string;
  guestName?: string;
  guestEmail?: string;
  guestPhone?: string;
  startDate?: Date;
  endDate?: Date;
  bookingStatus?: string[];
  paymentStatus?: string[];
  source?: string[];
  minAmount?: number;
  maxAmount?: number;
  page?: number;
  limit?: number;
  lastDoc?: any;
}

/**
 * Generate a unique booking number
 */
const generateBookingNumber = async (): Promise<string> => {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `BK-${timestamp}${random}`;
};

/**
 * Create a new booking
 */
export const createBooking = async (bookingData: BookingData): Promise<string> => {
  try {
    // Generate booking number
    const bookingNumber = await generateBookingNumber();

    // Calculate final price based on pricing rules
    const checkInDate = bookingData.checkInDate.toDate();
    const checkOutDate = bookingData.checkOutDate.toDate();

    // Get room data to verify availability and calculate price
    const room = await getRoomById(bookingData.roomId);

    if (!room) {
      throw new Error('Room not found');
    }

    // Calculate total nights
    const nights = Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24));

    if (nights <= 0) {
      throw new Error('Check-out date must be after check-in date');
    }

    // Calculate price per night with dynamic pricing
    const pricePerNight = await calculateRoomPrice(bookingData.roomId, checkInDate, checkOutDate);

    // Calculate total amount
    let totalAmount = pricePerNight * nights;

    // Add addon costs
    if (bookingData.addons && bookingData.addons.length > 0) {
      const addonTotal = bookingData.addons.reduce((sum, addon) => sum + (addon.price * addon.quantity), 0);
      totalAmount += addonTotal;
    }

    // Apply tax if provided
    if (bookingData.taxRate) {
      const taxAmount = totalAmount * (bookingData.taxRate / 100);
      totalAmount += taxAmount;
      bookingData.taxAmount = taxAmount;
    }

    // Apply discount if provided
    if (bookingData.discountAmount) {
      totalAmount -= bookingData.discountAmount;
      // Ensure total is not negative
      totalAmount = Math.max(totalAmount, 0);
    }

    // Create booking in a transaction to ensure atomicity
    const bookingId = await runTransaction(db, async (transaction) => {
      // Create a new document reference
      const bookingRef = doc(collection(db, 'bookings'));

      // Update room availability
      await updateRoomAvailabilityRange(
        bookingData.roomId,
        checkInDate,
        checkOutDate,
        false,
        undefined,
        undefined,
        `Booked: ${bookingNumber}`
      );

      // Add the booking data with timestamps
      transaction.set(bookingRef, {
        ...bookingData,
        bookingNumber,
        totalAmount,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      return bookingRef.id;
    });

    return bookingId;
  } catch (error) {
    console.error('Error creating booking:', error);
    throw error;
  }
};

/**
 * Get a booking by ID
 */
export const getBookingById = async (bookingId: string): Promise<BookingData | null> => {
  try {
    const bookingDoc = await getDoc(doc(db, 'bookings', bookingId));

    if (bookingDoc.exists()) {
      return { id: bookingDoc.id, ...bookingDoc.data() } as BookingData;
    }

    return null;
  } catch (error) {
    console.error('Error getting booking:', error);
    throw error;
  }
};

/**
 * Get a booking by booking number
 */
export const getBookingByNumber = async (bookingNumber: string): Promise<BookingData | null> => {
  try {
    const bookingsQuery = query(
      collection(db, 'bookings'),
      where('bookingNumber', '==', bookingNumber)
    );

    const bookingsSnapshot = await getDocs(bookingsQuery);

    if (!bookingsSnapshot.empty) {
      const bookingDoc = bookingsSnapshot.docs[0];
      return { id: bookingDoc.id, ...bookingDoc.data() } as BookingData;
    }

    return null;
  } catch (error) {
    console.error('Error getting booking by number:', error);
    throw error;
  }
};

/**
 * Update a booking
 */
export const updateBooking = async (bookingId: string, bookingData: Partial<BookingData>): Promise<boolean> => {
  try {
    const bookingRef = doc(db, 'bookings', bookingId);

    // Get current booking data
    const bookingDoc = await getDoc(bookingRef);

    if (!bookingDoc.exists()) {
      throw new Error('Booking not found');
    }

    const currentBooking = bookingDoc.data() as BookingData;

    // Check if dates are being updated
    if (
      (bookingData.checkInDate && !bookingData.checkInDate.isEqual(currentBooking.checkInDate)) ||
      (bookingData.checkOutDate && !bookingData.checkOutDate.isEqual(currentBooking.checkOutDate))
    ) {
      // Free up the previously booked dates
      await updateRoomAvailabilityRange(
        currentBooking.roomId,
        currentBooking.checkInDate.toDate(),
        currentBooking.checkOutDate.toDate(),
        true
      );

      // Block the new dates
      if (bookingData.checkInDate && bookingData.checkOutDate) {
        await updateRoomAvailabilityRange(
          currentBooking.roomId,
          bookingData.checkInDate.toDate(),
          bookingData.checkOutDate.toDate(),
          false,
          undefined,
          undefined,
          `Booked: ${currentBooking.bookingNumber}`
        );
      }
    }

    // Update the booking
    await updateDoc(bookingRef, {
      ...bookingData,
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('Error updating booking:', error);
    throw error;
  }
};

/**
 * Check in a guest
 */
export const checkInGuest = async (bookingId: string, notes?: string): Promise<boolean> => {
  try {
    const bookingRef = doc(db, 'bookings', bookingId);

    // Get current booking data
    const bookingDoc = await getDoc(bookingRef);

    if (!bookingDoc.exists()) {
      throw new Error('Booking not found');
    }

    const currentBooking = bookingDoc.data() as BookingData;

    // Verify booking status
    if (currentBooking.bookingStatus === 'checked_in') {
      throw new Error('Guest is already checked in');
    }

    if (currentBooking.bookingStatus === 'cancelled' || currentBooking.bookingStatus === 'no_show') {
      throw new Error('Cannot check in a cancelled or no-show booking');
    }

    // Update the booking status
    await updateDoc(bookingRef, {
      bookingStatus: 'checked_in',
      notes: notes ? `${currentBooking.notes || ''}\n${notes}` : currentBooking.notes,
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('Error checking in guest:', error);
    throw error;
  }
};

/**
 * Check out a guest
 */
export const checkOutGuest = async (bookingId: string, notes?: string): Promise<boolean> => {
  try {
    const bookingRef = doc(db, 'bookings', bookingId);

    // Get current booking data
    const bookingDoc = await getDoc(bookingRef);

    if (!bookingDoc.exists()) {
      throw new Error('Booking not found');
    }

    const currentBooking = bookingDoc.data() as BookingData;

    // Verify booking status
    if (currentBooking.bookingStatus !== 'checked_in') {
      throw new Error('Guest must be checked in before checking out');
    }

    // Update the booking status
    await updateDoc(bookingRef, {
      bookingStatus: 'checked_out',
      notes: notes ? `${currentBooking.notes || ''}\n${notes}` : currentBooking.notes,
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('Error checking out guest:', error);
    throw error;
  }
};

/**
 * Cancel a booking
 */
export const cancelBooking = async (bookingId: string, reason: string, refundAmount?: number): Promise<boolean> => {
  try {
    const bookingRef = doc(db, 'bookings', bookingId);

    // Get current booking data
    const bookingDoc = await getDoc(bookingRef);

    if (!bookingDoc.exists()) {
      throw new Error('Booking not found');
    }

    const currentBooking = bookingDoc.data() as BookingData;

    // Free up the booked dates
    await updateRoomAvailabilityRange(
      currentBooking.roomId,
      currentBooking.checkInDate.toDate(),
      currentBooking.checkOutDate.toDate(),
      true
    );

    // Update the booking status
    await updateDoc(bookingRef, {
      bookingStatus: 'cancelled',
      paymentStatus: refundAmount ? 'refunded' : currentBooking.paymentStatus,
      cancellationReason: reason,
      cancellationDate: Timestamp.now(),
      refundAmount,
      updatedAt: serverTimestamp()
    });

    // Create a refund transaction if applicable
    if (refundAmount && refundAmount > 0) {
      await addDoc(collection(db, 'bookingTransactions'), {
        bookingId,
        amount: refundAmount,
        type: 'refund',
        method: 'system',
        status: 'completed',
        notes: `Refund for cancelled booking: ${reason}`,
        createdBy: 'system',
        createdAt: serverTimestamp()
      });
    }

    return true;
  } catch (error) {
    console.error('Error cancelling booking:', error);
    throw error;
  }
};

/**
 * Search bookings with filters
 */
export const searchBookings = async (params: BookingSearchParams): Promise<{ bookings: BookingData[], lastDoc: any }> => {
  try {
    let bookingsQuery: any = collection(db, 'bookings');

    // Apply filters
    if (params.hotelId) {
      bookingsQuery = query(bookingsQuery, where('hotelId', '==', params.hotelId));
    }

    if (params.userId) {
      bookingsQuery = query(bookingsQuery, where('userId', '==', params.userId));
    }

    if (params.bookingNumber) {
      bookingsQuery = query(bookingsQuery, where('bookingNumber', '==', params.bookingNumber));
    }

    if (params.bookingStatus && params.bookingStatus.length > 0) {
      bookingsQuery = query(bookingsQuery, where('bookingStatus', 'in', params.bookingStatus));
    }

    if (params.paymentStatus && params.paymentStatus.length > 0) {
      bookingsQuery = query(bookingsQuery, where('paymentStatus', 'in', params.paymentStatus));
    }

    if (params.source && params.source.length > 0) {
      bookingsQuery = query(bookingsQuery, where('source', 'in', params.source));
    }

    // Add date range filter if provided
    if (params.startDate) {
      const startTimestamp = Timestamp.fromDate(params.startDate);
      bookingsQuery = query(bookingsQuery, where('checkInDate', '>=', startTimestamp));
    }

    if (params.endDate) {
      const endTimestamp = Timestamp.fromDate(params.endDate);
      bookingsQuery = query(bookingsQuery, where('checkOutDate', '<=', endTimestamp));
    }

    // Add ordering
    bookingsQuery = query(bookingsQuery, orderBy('createdAt', 'desc'));

    // Add pagination
    const pageSize = params.limit || 10;
    bookingsQuery = query(bookingsQuery, limit(pageSize));

    if (params.lastDoc) {
      bookingsQuery = query(bookingsQuery, startAfter(params.lastDoc));
    }

    const bookingsSnapshot = await getDocs(bookingsQuery);
    const bookings: BookingData[] = [];

    bookingsSnapshot.forEach((doc) => {
      const data = doc.data();
      bookings.push({ id: doc.id, ...(data as object) } as BookingData);
    });

    // Get the last document for pagination
    const lastDoc = bookingsSnapshot.docs[bookingsSnapshot.docs.length - 1];

    return { bookings, lastDoc };
  } catch (error) {
    console.error('Error searching bookings:', error);
    throw error;
  }
};

/**
 * Add a payment transaction to a booking
 */
export const addBookingPayment = async (transaction: BookingTransaction): Promise<string> => {
  try {
    // Create transaction
    const transactionRef = await addDoc(collection(db, 'bookingTransactions'), {
      ...transaction,
      createdAt: serverTimestamp()
    });

    // Update booking payment status
    const bookingRef = doc(db, 'bookings', transaction.bookingId);
    const bookingDoc = await getDoc(bookingRef);

    if (bookingDoc.exists()) {
      const booking = bookingDoc.data() as BookingData;

      // Get all transactions for this booking
      const transactionsQuery = query(
        collection(db, 'bookingTransactions'),
        where('bookingId', '==', transaction.bookingId),
        where('type', '==', 'payment'),
        where('status', '==', 'completed')
      );

      const transactionsSnapshot = await getDocs(transactionsQuery);
      let totalPaid = 0;

      transactionsSnapshot.forEach((doc) => {
        const trans = doc.data() as BookingTransaction;
        totalPaid += trans.amount;
      });

      // Add the new transaction amount
      if (transaction.status === 'completed' && transaction.type === 'payment') {
        totalPaid += transaction.amount;
      }

      // Determine payment status
      let paymentStatus: 'pending' | 'paid' | 'partially_paid' = 'pending';

      if (totalPaid >= booking.totalAmount) {
        paymentStatus = 'paid';
      } else if (totalPaid > 0) {
        paymentStatus = 'partially_paid';
      }

      // Update booking
      await updateDoc(bookingRef, {
        paymentStatus,
        updatedAt: serverTimestamp()
      });
    }

    return transactionRef.id;
  } catch (error) {
    console.error('Error adding booking payment:', error);
    throw error;
  }
};
