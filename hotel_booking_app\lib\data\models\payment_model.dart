import 'package:cloud_firestore/cloud_firestore.dart';

enum PaymentMethod {
  creditCard,
  debitCard,
  upi,
  netBanking,
  wallet,
  cash
}

enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  refunded
}

class PaymentDetails {
  final String id;
  final String userId;
  final String bookingId;
  final double amount;
  final String currency;
  final PaymentMethod method;
  final PaymentStatus status;
  final String? transactionId;
  final String? gatewayResponse;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime? updatedAt;

  PaymentDetails({
    required this.id,
    required this.userId,
    required this.bookingId,
    required this.amount,
    required this.currency,
    required this.method,
    required this.status,
    this.transactionId,
    this.gatewayResponse,
    this.metadata,
    required this.createdAt,
    this.updatedAt,
  });

  factory PaymentDetails.fromMap(Map<String, dynamic> map, String id) {
    return PaymentDetails(
      id: id,
      userId: map['userId'] ?? '',
      bookingId: map['bookingId'] ?? '',
      amount: (map['amount'] ?? 0).toDouble(),
      currency: map['currency'] ?? 'INR',
      method: _methodFromString(map['method'] ?? 'creditCard'),
      status: _statusFromString(map['status'] ?? 'pending'),
      transactionId: map['transactionId'],
      gatewayResponse: map['gatewayResponse'],
      metadata: map['metadata'],
      createdAt: map['createdAt'] != null
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: map['updatedAt'] != null
          ? (map['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'bookingId': bookingId,
      'amount': amount,
      'currency': currency,
      'method': _methodToString(method),
      'status': _statusToString(status),
      'transactionId': transactionId,
      'gatewayResponse': gatewayResponse,
      'metadata': metadata,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  static PaymentMethod _methodFromString(String method) {
    switch (method) {
      case 'creditCard':
        return PaymentMethod.creditCard;
      case 'debitCard':
        return PaymentMethod.debitCard;
      case 'upi':
        return PaymentMethod.upi;
      case 'netBanking':
        return PaymentMethod.netBanking;
      case 'wallet':
        return PaymentMethod.wallet;
      case 'cash':
        return PaymentMethod.cash;
      default:
        return PaymentMethod.creditCard;
    }
  }

  static String _methodToString(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.creditCard:
        return 'creditCard';
      case PaymentMethod.debitCard:
        return 'debitCard';
      case PaymentMethod.upi:
        return 'upi';
      case PaymentMethod.netBanking:
        return 'netBanking';
      case PaymentMethod.wallet:
        return 'wallet';
      case PaymentMethod.cash:
        return 'cash';
    }
  }

  static PaymentStatus _statusFromString(String status) {
    switch (status) {
      case 'pending':
        return PaymentStatus.pending;
      case 'processing':
        return PaymentStatus.processing;
      case 'completed':
        return PaymentStatus.completed;
      case 'failed':
        return PaymentStatus.failed;
      case 'refunded':
        return PaymentStatus.refunded;
      default:
        return PaymentStatus.pending;
    }
  }

  static String _statusToString(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return 'pending';
      case PaymentStatus.processing:
        return 'processing';
      case PaymentStatus.completed:
        return 'completed';
      case PaymentStatus.failed:
        return 'failed';
      case PaymentStatus.refunded:
        return 'refunded';
    }
  }
}

class SavedPaymentMethod {
  final String id;
  final String userId;
  final PaymentMethod type;
  final String? cardBrand;
  final String? last4;
  final int? expiryMonth;
  final int? expiryYear;
  final String? upiId;
  final String? bankName;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime? updatedAt;

  SavedPaymentMethod({
    required this.id,
    required this.userId,
    required this.type,
    this.cardBrand,
    this.last4,
    this.expiryMonth,
    this.expiryYear,
    this.upiId,
    this.bankName,
    required this.isDefault,
    required this.createdAt,
    this.updatedAt,
  });

  factory SavedPaymentMethod.fromMap(Map<String, dynamic> map, String id) {
    return SavedPaymentMethod(
      id: id,
      userId: map['userId'] ?? '',
      type: PaymentDetails._methodFromString(map['type'] ?? 'creditCard'),
      cardBrand: map['cardBrand'],
      last4: map['last4'],
      expiryMonth: map['expiryMonth'],
      expiryYear: map['expiryYear'],
      upiId: map['upiId'],
      bankName: map['bankName'],
      isDefault: map['isDefault'] ?? false,
      createdAt: map['createdAt'] != null
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: map['updatedAt'] != null
          ? (map['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'type': PaymentDetails._methodToString(type),
      'cardBrand': cardBrand,
      'last4': last4,
      'expiryMonth': expiryMonth,
      'expiryYear': expiryYear,
      'upiId': upiId,
      'bankName': bankName,
      'isDefault': isDefault,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }
}
