/**
 * Seed script to create initial users in Firebase Authentication and Firestore
 * 
 * This script creates:
 * 1. A super admin user
 * 2. A vendor user
 * 3. A staff user for the vendor
 * 
 * Run this script with: node scripts/seed-users.js
 */

const { 
  createUserWithEmailAndPassword,
  getAuth,
  signOut
} = require('firebase/auth');
const { 
  doc, 
  setDoc, 
  collection, 
  query, 
  where, 
  getDocs, 
  serverTimestamp,
  getFirestore
} = require('firebase/firestore');
const { initializeApp } = require('firebase/app');

// Firebase configuration - same as in config.ts
const firebaseConfig = {
  apiKey: "AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI",
  authDomain: "linkinblink-hotel.firebaseapp.com",
  projectId: "linkinblink-hotel",
  storageBucket: "linkinblink-hotel.firebasestorage.app",
  messagingSenderId: "132613661307",
  appId: "1:132613661307:web:a9e5c8f4c2b8e9a9e5c8f4",
  measurementId: "G-MEASUREMENT_ID"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// User roles
const ROLE_SUPER_ADMIN = 'super_admin';
const ROLE_VENDOR = 'vendor';
const ROLE_STAFF = 'staff';

// Sample users to create
const users = [
  {
    email: '<EMAIL>',
    password: 'Admin123!',
    displayName: 'Super Admin',
    role: ROLE_SUPER_ADMIN,
    phone: '+1234567890',
    address: '123 Admin Street, Admin City'
  },
  {
    email: '<EMAIL>',
    password: 'Vendor123!',
    displayName: 'Hotel Vendor',
    role: ROLE_VENDOR,
    phone: '+1987654321',
    address: '456 Vendor Avenue, Vendor City'
  },
  {
    email: '<EMAIL>',
    password: 'Staff123!',
    displayName: 'Hotel Staff',
    role: ROLE_STAFF,
    phone: '+1122334455',
    address: '789 Staff Road, Staff City'
  }
];

/**
 * Create a user in Firebase Authentication and Firestore
 */
async function createUser(userData, createdBy = null) {
  try {
    console.log(`Creating user: ${userData.email} (${userData.role})...`);
    
    // Check if user already exists
    const userQuery = query(
      collection(db, 'users'),
      where('email', '==', userData.email)
    );
    
    const userSnapshot = await getDocs(userQuery);
    
    if (!userSnapshot.empty) {
      console.log(`User ${userData.email} already exists. Skipping...`);
      return userSnapshot.docs[0].id;
    }
    
    // Create user in Firebase Authentication
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      userData.email,
      userData.password
    );
    
    const uid = userCredential.user.uid;
    
    // Add user data to Firestore
    await setDoc(doc(db, 'users', uid), {
      email: userData.email,
      displayName: userData.displayName,
      role: userData.role,
      phone: userData.phone || '',
      address: userData.address || '',
      createdBy: createdBy,
      vendorId: userData.role === ROLE_STAFF ? userData.vendorId : (userData.role === ROLE_VENDOR ? uid : null),
      hotelId: userData.role === ROLE_STAFF ? userData.hotelId : null,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
    
    console.log(`User ${userData.email} created successfully with ID: ${uid}`);
    
    return uid;
  } catch (error) {
    console.error(`Error creating user ${userData.email}:`, error.message);
    throw error;
  }
}

/**
 * Main function to seed users
 */
async function seedUsers() {
  try {
    console.log('Starting user seeding process...');
    
    // Create super admin
    const adminId = await createUser(users[0]);
    
    // Create vendor
    const vendorId = await createUser(users[1], adminId);
    
    // Create staff with reference to vendor
    const staffData = {
      ...users[2],
      vendorId: vendorId,
      hotelId: 'default-hotel' // This will be updated when hotels are created
    };
    
    await createUser(staffData, vendorId);
    
    console.log('User seeding completed successfully!');
    
    // Sign out after creating users
    await signOut(auth);
    
    console.log('Seed script completed. You can now log in with any of these accounts:');
    users.forEach(user => {
      console.log(`- ${user.email} (${user.role}) - Password: ${user.password}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error seeding users:', error.message);
    process.exit(1);
  }
}

// Run the seed function
seedUsers();
