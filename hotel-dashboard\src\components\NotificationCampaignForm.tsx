import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Radio,
  RadioGroup,
  FormLabel,
  Divider,
  Grid,
  Chip,
  CircularProgress,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Send as SendIcon,
  Schedule as ScheduleIcon,
  Save as SaveIcon,
  Delete as DeleteIcon,
  Preview as PreviewIcon,
  ContentCopy as CopyIcon,
  NotificationsActive as NotificationIcon
} from '@mui/icons-material';
import {
  createNotificationCampaign,
  getNotificationTemplates,
  NotificationCampaign,
  NotificationTemplate
} from '../services/notificationService';
import { Timestamp } from 'firebase/firestore';

interface NotificationCampaignFormProps {
  hotelId: string;
  userId: string;
  onSuccess?: (campaignId: string) => void;
  onCancel?: () => void;
}

const NotificationCampaignForm: React.FC<NotificationCampaignFormProps> = ({
  hotelId,
  userId,
  onSuccess,
  onCancel
}) => {
  // Form state
  const [formData, setFormData] = useState<Partial<NotificationCampaign>>({
    name: '',
    hotelId,
    title: '',
    message: '',
    type: 'promotion',
    targetAudience: {
      allUsers: true,
      userIds: [],
      filters: {
        hasUpcomingBooking: false,
        hasPastBooking: false
      }
    },
    schedule: {
      sendImmediately: true,
      scheduledDate: undefined
    },
    status: 'draft',
    createdBy: userId
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState<NotificationTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [previewOpen, setPreviewOpen] = useState(false);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'success'
  });

  // Load templates on component mount
  useEffect(() => {
    loadTemplates();
  }, [userId]);

  // Load notification templates
  const loadTemplates = async () => {
    try {
      setLoading(true);
      const templatesList = await getNotificationTemplates(userId);
      setTemplates(templatesList);
      setLoading(false);
    } catch (error: any) {
      console.error('Error loading templates:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to load notification templates',
        severity: 'error'
      });
      setLoading(false);
    }
  };

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle select change
  const handleSelectChange = (event: any) => {
    const name = event.target.name;
    const value = event.target.value;

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle checkbox change
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;

    if (name.startsWith('filters.')) {
      const filterName = name.replace('filters.', '');
      setFormData(prev => ({
        ...prev,
        targetAudience: {
          ...prev.targetAudience,
          filters: {
            ...prev.targetAudience?.filters,
            [filterName]: checked
          }
        }
      }));
    } else if (name === 'allUsers') {
      setFormData(prev => ({
        ...prev,
        targetAudience: {
          ...prev.targetAudience,
          allUsers: checked
        }
      }));
    } else if (name === 'sendImmediately') {
      setFormData(prev => ({
        ...prev,
        schedule: {
          ...prev.schedule,
          sendImmediately: checked
        }
      }));
    }
  };

  // Handle date change
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;

    if (value) {
      const scheduledDate = new Date(value);

      setFormData(prev => ({
        ...prev,
        schedule: {
          sendImmediately: prev.schedule?.sendImmediately || false,
          scheduledDate: Timestamp.fromDate(scheduledDate)
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        schedule: {
          sendImmediately: prev.schedule?.sendImmediately || false,
          scheduledDate: undefined
        }
      }));
    }
  };

  // Handle template selection
  const handleTemplateChange = (event: any) => {
    const templateId = event.target.value;
    setSelectedTemplate(templateId);

    if (templateId) {
      const template = templates.find(t => t.id === templateId);

      if (template) {
        setFormData(prev => ({
          ...prev,
          title: template.title,
          message: template.message,
          type: template.type
        }));
      }
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);

      // Validate form
      if (!formData.name || !formData.title || !formData.message) {
        throw new Error('Please fill in all required fields');
      }

      // Create campaign
      const campaignId = await createNotificationCampaign(formData as Omit<NotificationCampaign, 'createdAt' | 'updatedAt'>);

      setSnackbar({
        open: true,
        message: 'Notification campaign created successfully',
        severity: 'success'
      });

      setLoading(false);

      // Call success callback
      if (onSuccess) {
        onSuccess(campaignId);
      }
    } catch (error: any) {
      console.error('Error creating campaign:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to create notification campaign',
        severity: 'error'
      });
      setLoading(false);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };

  // Toggle preview dialog
  const togglePreview = () => {
    setPreviewOpen(prev => !prev);
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <NotificationIcon sx={{ mr: 1, color: 'primary.main' }} />
        <Typography variant="h6">Create Notification Campaign</Typography>
      </Box>

      <Divider sx={{ mb: 3 }} />

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          {/* Campaign Details */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom fontWeight="medium">
              Campaign Details
            </Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              required
              label="Campaign Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              helperText="Internal name for this campaign"
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Notification Type</InputLabel>
              <Select
                name="type"
                value={formData.type}
                label="Notification Type"
                onChange={handleSelectChange}
              >
                <MenuItem value="promotion">Promotion</MenuItem>
                <MenuItem value="booking">Booking</MenuItem>
                <MenuItem value="system">System</MenuItem>
                <MenuItem value="loyalty">Loyalty</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Use Template</InputLabel>
              <Select
                value={selectedTemplate}
                label="Use Template"
                onChange={handleTemplateChange}
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {templates.map((template) => (
                  <MenuItem key={template.id} value={template.id}>
                    {template.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              required
              label="Notification Title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              helperText="Title shown in the notification"
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              required
              multiline
              rows={4}
              label="Notification Message"
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              helperText="Message content of the notification"
            />
          </Grid>

          {/* Target Audience */}
          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Typography variant="subtitle1" gutterBottom fontWeight="medium">
              Target Audience
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.targetAudience?.allUsers}
                  onChange={handleCheckboxChange}
                  name="allUsers"
                />
              }
              label="Send to all users"
            />
          </Grid>

          {!formData.targetAudience?.allUsers && (
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Filters
              </Typography>
              <Box sx={{ ml: 2 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={formData.targetAudience?.filters?.hasUpcomingBooking}
                      onChange={handleCheckboxChange}
                      name="filters.hasUpcomingBooking"
                    />
                  }
                  label="Users with upcoming bookings"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={formData.targetAudience?.filters?.hasPastBooking}
                      onChange={handleCheckboxChange}
                      name="filters.hasPastBooking"
                    />
                  }
                  label="Users with past bookings"
                />
              </Box>
            </Grid>
          )}

          {/* Schedule */}
          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Typography variant="subtitle1" gutterBottom fontWeight="medium">
              Schedule
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <FormControl component="fieldset">
              <RadioGroup
                row
                value={formData.schedule?.sendImmediately ? 'immediately' : 'scheduled'}
                onChange={(e) => {
                  const sendImmediately = e.target.value === 'immediately';
                  setFormData(prev => ({
                    ...prev,
                    schedule: {
                      sendImmediately,
                      scheduledDate: prev.schedule?.scheduledDate
                    }
                  }));
                }}
              >
                <FormControlLabel
                  value="immediately"
                  control={<Radio />}
                  label="Send immediately"
                />
                <FormControlLabel
                  value="scheduled"
                  control={<Radio />}
                  label="Schedule for later"
                />
              </RadioGroup>
            </FormControl>
          </Grid>

          {!formData.schedule?.sendImmediately && (
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="Scheduled Date"
                type="datetime-local"
                InputLabelProps={{ shrink: true }}
                value={formData.schedule?.scheduledDate ?
                  new Date(formData.schedule.scheduledDate.toDate()).toISOString().slice(0, 16) :
                  ''
                }
                onChange={handleDateChange}
                helperText="When to send this notification"
              />
            </Grid>
          )}

          {/* Actions */}
          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
              <Button
                variant="outlined"
                onClick={onCancel}
              >
                Cancel
              </Button>

              <Box>
                <Button
                  variant="outlined"
                  startIcon={<PreviewIcon />}
                  onClick={togglePreview}
                  sx={{ mr: 2 }}
                >
                  Preview
                </Button>

                <Button
                  type="submit"
                  variant="contained"
                  startIcon={formData.schedule?.sendImmediately ? <SendIcon /> : <ScheduleIcon />}
                  disabled={loading}
                >
                  {loading ? (
                    <CircularProgress size={24} />
                  ) : formData.schedule?.sendImmediately ? (
                    'Send Now'
                  ) : (
                    'Schedule'
                  )}
                </Button>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </form>

      {/* Preview Dialog */}
      <Dialog
        open={previewOpen}
        onClose={togglePreview}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Notification Preview</DialogTitle>
        <DialogContent>
          <Box sx={{
            p: 2,
            bgcolor: 'background.paper',
            borderRadius: 1,
            boxShadow: 1
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <NotificationIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="subtitle2" color="primary">
                {formData.type ? formData.type.charAt(0).toUpperCase() + formData.type.slice(1) : 'System'} Notification
              </Typography>
            </Box>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              {formData.title || 'Notification Title'}
            </Typography>
            <Typography variant="body2">
              {formData.message || 'Notification message will appear here.'}
            </Typography>
          </Box>

          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Target Audience:
            </Typography>
            <Typography variant="body2">
              {formData.targetAudience?.allUsers ?
                'All users' :
                'Filtered users'
              }
            </Typography>

            <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
              Schedule:
            </Typography>
            <Typography variant="body2">
              {formData.schedule?.sendImmediately ?
                'Send immediately' :
                `Scheduled for ${formData.schedule?.scheduledDate ?
                  new Date(formData.schedule.scheduledDate.toDate()).toLocaleString() :
                  'later'}`
              }
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={togglePreview}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default NotificationCampaignForm;
