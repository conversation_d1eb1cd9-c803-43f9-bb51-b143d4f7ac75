import { 
  collection, 
  query, 
  where, 
  getDocs, 
  orderBy, 
  limit,
  startAfter,
  Timestamp,
  getDoc,
  doc
} from 'firebase/firestore';
import { db } from '../firebase/config';

// Interface for analytics data
export interface AnalyticsData {
  totalRevenue: number;
  totalBookings: number;
  occupancyRate: number;
  averageDailyRate: number;
  revenuePerAvailableRoom: number;
  bookingsBySource: Record<string, number>;
  revenueByRoomType: Record<string, number>;
  monthlyRevenue: Array<{month: string, revenue: number}>;
  topCustomers: Array<{id: string, name: string, bookings: number, revenue: number}>;
}

// Interface for booking data
export interface BookingData {
  id: string;
  hotelId: string;
  roomId: string;
  userId: string;
  checkInDate: Timestamp;
  checkOutDate: Timestamp;
  guests: number;
  totalAmount: number;
  status: string;
  source: string;
  createdAt: Timestamp;
}

// Interface for room data
export interface RoomData {
  id: string;
  hotelId: string;
  name: string;
  type: string;
  price: number;
  capacity: number;
}

/**
 * Get analytics data for a specific hotel
 */
export const getHotelAnalytics = async (hotelId: string, period: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<AnalyticsData> => {
  try {
    // Calculate date range based on period
    const now = new Date();
    let startDate = new Date();
    
    switch (period) {
      case 'day':
        startDate.setDate(now.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
    }
    
    // Get all bookings for the hotel in the specified period
    const bookingsQuery = query(
      collection(db, 'bookings'),
      where('hotelId', '==', hotelId),
      where('createdAt', '>=', Timestamp.fromDate(startDate)),
      where('createdAt', '<=', Timestamp.fromDate(now)),
      orderBy('createdAt', 'desc')
    );
    
    const bookingsSnapshot = await getDocs(bookingsQuery);
    const bookings: BookingData[] = [];
    
    bookingsSnapshot.forEach((doc) => {
      bookings.push({ id: doc.id, ...doc.data() } as BookingData);
    });
    
    // Get all rooms for the hotel
    const roomsQuery = query(
      collection(db, 'rooms'),
      where('hotelId', '==', hotelId)
    );
    
    const roomsSnapshot = await getDocs(roomsQuery);
    const rooms: RoomData[] = [];
    
    roomsSnapshot.forEach((doc) => {
      rooms.push({ id: doc.id, ...doc.data() } as RoomData);
    });
    
    // Calculate analytics
    const totalRevenue = bookings.reduce((sum, booking) => sum + booking.totalAmount, 0);
    const totalBookings = bookings.length;
    
    // Calculate occupancy rate
    const totalRooms = rooms.length;
    const totalDays = Math.ceil((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const totalRoomDays = totalRooms * totalDays;
    
    // Count booked room days
    let bookedRoomDays = 0;
    bookings.forEach(booking => {
      const checkIn = booking.checkInDate.toDate();
      const checkOut = booking.checkOutDate.toDate();
      const stayDays = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
      bookedRoomDays += stayDays;
    });
    
    const occupancyRate = totalRoomDays > 0 ? (bookedRoomDays / totalRoomDays) * 100 : 0;
    
    // Calculate average daily rate
    const averageDailyRate = bookedRoomDays > 0 ? totalRevenue / bookedRoomDays : 0;
    
    // Calculate RevPAR (Revenue Per Available Room)
    const revenuePerAvailableRoom = totalRoomDays > 0 ? totalRevenue / totalRoomDays : 0;
    
    // Calculate bookings by source
    const bookingsBySource: Record<string, number> = {};
    bookings.forEach(booking => {
      const source = booking.source || 'direct';
      bookingsBySource[source] = (bookingsBySource[source] || 0) + 1;
    });
    
    // Calculate revenue by room type
    const revenueByRoomType: Record<string, number> = {};
    const roomMap: Record<string, RoomData> = {};
    
    rooms.forEach(room => {
      roomMap[room.id] = room;
    });
    
    bookings.forEach(booking => {
      const room = roomMap[booking.roomId];
      if (room) {
        const roomType = room.type || 'standard';
        revenueByRoomType[roomType] = (revenueByRoomType[roomType] || 0) + booking.totalAmount;
      }
    });
    
    // Calculate monthly revenue
    const monthlyRevenue: Array<{month: string, revenue: number}> = [];
    const monthlyRevenueMap: Record<string, number> = {};
    
    bookings.forEach(booking => {
      const date = booking.createdAt.toDate();
      const monthYear = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      monthlyRevenueMap[monthYear] = (monthlyRevenueMap[monthYear] || 0) + booking.totalAmount;
    });
    
    Object.entries(monthlyRevenueMap).forEach(([month, revenue]) => {
      monthlyRevenue.push({ month, revenue });
    });
    
    // Sort monthly revenue by date
    monthlyRevenue.sort((a, b) => a.month.localeCompare(b.month));
    
    // Get top customers
    const customerMap: Record<string, {id: string, name: string, bookings: number, revenue: number}> = {};
    
    for (const booking of bookings) {
      if (!customerMap[booking.userId]) {
        // Get user data
        try {
          const userDoc = await getDoc(doc(db, 'users', booking.userId));
          if (userDoc.exists()) {
            const userData = userDoc.data();
            customerMap[booking.userId] = {
              id: booking.userId,
              name: userData.displayName || 'Unknown',
              bookings: 0,
              revenue: 0
            };
          } else {
            customerMap[booking.userId] = {
              id: booking.userId,
              name: 'Unknown',
              bookings: 0,
              revenue: 0
            };
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
          customerMap[booking.userId] = {
            id: booking.userId,
            name: 'Unknown',
            bookings: 0,
            revenue: 0
          };
        }
      }
      
      customerMap[booking.userId].bookings += 1;
      customerMap[booking.userId].revenue += booking.totalAmount;
    }
    
    const topCustomers = Object.values(customerMap)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);
    
    return {
      totalRevenue,
      totalBookings,
      occupancyRate,
      averageDailyRate,
      revenuePerAvailableRoom,
      bookingsBySource,
      revenueByRoomType,
      monthlyRevenue,
      topCustomers
    };
  } catch (error) {
    console.error('Error getting hotel analytics:', error);
    throw error;
  }
};

/**
 * Get analytics data for a vendor (all hotels)
 */
export const getVendorAnalytics = async (vendorId: string, period: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<AnalyticsData> => {
  try {
    // Get all hotels for the vendor
    const hotelsQuery = query(
      collection(db, 'hotels'),
      where('vendorId', '==', vendorId)
    );
    
    const hotelsSnapshot = await getDocs(hotelsQuery);
    const hotelIds: string[] = [];
    
    hotelsSnapshot.forEach((doc) => {
      hotelIds.push(doc.id);
    });
    
    // If no hotels, return empty analytics
    if (hotelIds.length === 0) {
      return {
        totalRevenue: 0,
        totalBookings: 0,
        occupancyRate: 0,
        averageDailyRate: 0,
        revenuePerAvailableRoom: 0,
        bookingsBySource: {},
        revenueByRoomType: {},
        monthlyRevenue: [],
        topCustomers: []
      };
    }
    
    // Get analytics for each hotel and combine them
    const hotelAnalytics = await Promise.all(
      hotelIds.map(hotelId => getHotelAnalytics(hotelId, period))
    );
    
    // Combine analytics
    const combinedAnalytics: AnalyticsData = {
      totalRevenue: 0,
      totalBookings: 0,
      occupancyRate: 0,
      averageDailyRate: 0,
      revenuePerAvailableRoom: 0,
      bookingsBySource: {},
      revenueByRoomType: {},
      monthlyRevenue: [],
      topCustomers: []
    };
    
    hotelAnalytics.forEach(analytics => {
      combinedAnalytics.totalRevenue += analytics.totalRevenue;
      combinedAnalytics.totalBookings += analytics.totalBookings;
      
      // Combine bookings by source
      Object.entries(analytics.bookingsBySource).forEach(([source, count]) => {
        combinedAnalytics.bookingsBySource[source] = (combinedAnalytics.bookingsBySource[source] || 0) + count;
      });
      
      // Combine revenue by room type
      Object.entries(analytics.revenueByRoomType).forEach(([roomType, revenue]) => {
        combinedAnalytics.revenueByRoomType[roomType] = (combinedAnalytics.revenueByRoomType[roomType] || 0) + revenue;
      });
      
      // Combine monthly revenue
      analytics.monthlyRevenue.forEach(({ month, revenue }) => {
        const existingMonth = combinedAnalytics.monthlyRevenue.find(m => m.month === month);
        if (existingMonth) {
          existingMonth.revenue += revenue;
        } else {
          combinedAnalytics.monthlyRevenue.push({ month, revenue });
        }
      });
      
      // Combine top customers
      analytics.topCustomers.forEach(customer => {
        const existingCustomer = combinedAnalytics.topCustomers.find(c => c.id === customer.id);
        if (existingCustomer) {
          existingCustomer.bookings += customer.bookings;
          existingCustomer.revenue += customer.revenue;
        } else {
          combinedAnalytics.topCustomers.push({ ...customer });
        }
      });
    });
    
    // Calculate average occupancy rate
    combinedAnalytics.occupancyRate = hotelAnalytics.reduce((sum, analytics) => sum + analytics.occupancyRate, 0) / hotelAnalytics.length;
    
    // Calculate average daily rate
    combinedAnalytics.averageDailyRate = hotelAnalytics.reduce((sum, analytics) => sum + analytics.averageDailyRate, 0) / hotelAnalytics.length;
    
    // Calculate average RevPAR
    combinedAnalytics.revenuePerAvailableRoom = hotelAnalytics.reduce((sum, analytics) => sum + analytics.revenuePerAvailableRoom, 0) / hotelAnalytics.length;
    
    // Sort monthly revenue by date
    combinedAnalytics.monthlyRevenue.sort((a, b) => a.month.localeCompare(b.month));
    
    // Sort top customers by revenue
    combinedAnalytics.topCustomers.sort((a, b) => b.revenue - a.revenue);
    combinedAnalytics.topCustomers = combinedAnalytics.topCustomers.slice(0, 5);
    
    return combinedAnalytics;
  } catch (error) {
    console.error('Error getting vendor analytics:', error);
    throw error;
  }
};
