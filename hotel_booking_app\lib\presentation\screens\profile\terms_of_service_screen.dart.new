import 'package:flutter/material.dart';
import 'package:hotel_booking_app/core/theme/app_theme.dart';
import 'package:hotel_booking_app/data/services/content_service.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_app_bar.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Terms of Service',
        showBackButton: true,
      ),
      body: Consumer<ContentService>(
        builder: (context, contentService, _) {
          if (contentService.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (contentService.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 60,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading content',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    contentService.error!,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      contentService.fetchTermsOfService();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final termsOfService = contentService.termsOfService;
          if (termsOfService == null) {
            return const Center(
              child: Text('No content available'),
            );
          }

          final sections = termsOfService['sections'] as List;
          final contactInfo = termsOfService['contactInfo'] as Map<String, dynamic>;
          final lastUpdated = termsOfService['lastUpdated'];
          
          String formattedDate = 'January 1, 2023';
          if (lastUpdated != null && lastUpdated is Map && lastUpdated.containsKey('seconds')) {
            final timestamp = DateTime.fromMillisecondsSinceEpoch(
              (lastUpdated['seconds'] as int) * 1000,
            );
            formattedDate = DateFormat('MMMM d, yyyy').format(timestamp);
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Last Updated
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.update,
                        size: 18,
                        color: Colors.grey.shade700,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Last Updated: $formattedDate',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // Content Sections
                ...sections.map((section) {
                  final title = section['title'] as String;
                  final content = section['content'] as String;
                  final bulletPoints = section['bulletPoints'] as List?;

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSectionTitle(title),
                      _buildParagraph(content),
                      
                      // Bullet points if any
                      if (bulletPoints != null)
                        ...bulletPoints.map((point) => 
                          _buildBulletPoint(point as String)
                        ).toList(),
                    ],
                  );
                }).toList(),

                // Contact Us
                _buildSectionTitle('Contact Us'),
                _buildParagraph(
                  'If you have any questions about these Terms, please contact us at:',
                ),
                _buildParagraph(
                  'Email: ${contactInfo['email'] ?? ''}\nAddress: ${contactInfo['address'] ?? ''}\nPhone: ${contactInfo['phone'] ?? ''}',
                ),

                const SizedBox(height: 32),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16, top: 24),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  Widget _buildParagraph(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 16,
          color: Colors.grey.shade800,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8, left: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: AppTheme.secondaryColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade800,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
