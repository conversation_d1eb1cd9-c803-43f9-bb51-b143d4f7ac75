import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Divider,
  IconButton,
  Button,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Card,
  CardContent,
  CardMedia,
  Tooltip
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Search as SearchIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Visibility as VisibilityIcon,
  VerifiedUser as VerifiedUserIcon
} from '@mui/icons-material';
import { getAllVerifications, approveVerification, rejectVerification, VerificationData } from '../../firebase/verificationService';

// VerificationData interface is imported from verificationService.ts

const Verifications: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [verifications, setVerifications] = useState<VerificationData[]>([]);
  const [filteredVerifications, setFilteredVerifications] = useState<VerificationData[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedVerification, setSelectedVerification] = useState<VerificationData | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [approving, setApproving] = useState(false);
  const [rejecting, setRejecting] = useState(false);
  const [rejectReason, setRejectReason] = useState('');

  // Load data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  // Filter verifications when search term changes
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredVerifications(verifications);
    } else {
      const lowercasedFilter = searchTerm.toLowerCase();
      const filtered = verifications.filter(item => {
        return (
          item.userName.toLowerCase().includes(lowercasedFilter) ||
          item.userEmail.toLowerCase().includes(lowercasedFilter) ||
          item.aadhaarNumber.includes(lowercasedFilter)
        );
      });
      setFilteredVerifications(filtered);
    }
  }, [searchTerm, verifications]);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Fetch verification data from the service
      const verificationsList = await getAllVerifications();

      // Cast the result to VerificationData[] to ensure type safety
      setVerifications(verificationsList as VerificationData[]);
      setFilteredVerifications(verificationsList as VerificationData[]);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching verification data:', error);
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchData();
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleViewVerification = (verification: VerificationData) => {
    setSelectedVerification(verification);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedVerification(null);
    setRejectReason('');
  };

  const handleApproveVerification = async () => {
    if (!selectedVerification) return;

    try {
      setApproving(true);

      // Call the actual approve function
      await approveVerification(selectedVerification.id);

      // Refresh the data to get the updated list
      await fetchData();

      setApproving(false);
      handleCloseDialog();
    } catch (error) {
      console.error('Error approving verification:', error);
      setApproving(false);
    }
  };

  const handleRejectVerification = async () => {
    if (!selectedVerification) return;

    try {
      setRejecting(true);

      // Call the actual reject function
      await rejectVerification(selectedVerification.id, rejectReason);

      // Refresh the data to get the updated list
      await fetchData();

      setRejecting(false);
      handleCloseDialog();
    } catch (error) {
      console.error('Error rejecting verification:', error);
      setRejecting(false);
    }
  };

  // Format timestamp to readable date
  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    const date = new Date(timestamp.seconds * 1000);
    return date.toLocaleString();
  };

  return (
    <Box>
      {/* Header */}
      <Paper
        elevation={3}
        sx={{
          p: 3,
          mb: 3,
          background: 'linear-gradient(45deg, #3f51b5 30%, #2196f3 90%)',
          color: 'white',
          borderRadius: '16px'
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <VerifiedUserIcon sx={{ fontSize: 40, mr: 2 }} />
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                Aadhaar Verifications
              </Typography>
              <Typography variant="subtitle1">
                Manage and review user verification requests
              </Typography>
            </Box>
          </Box>
          <IconButton onClick={handleRefresh} sx={{ color: 'white', bgcolor: 'rgba(255,255,255,0.1)', '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' } }}>
            {loading ? <CircularProgress size={24} color="inherit" /> : <RefreshIcon />}
          </IconButton>
        </Box>
      </Paper>

      {/* Search and Filters */}
      <Paper elevation={3} sx={{ p: 2, mb: 3, borderRadius: '16px' }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Search by name, email, or Aadhaar number"
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={6} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Chip
                label={`All (${verifications.length})`}
                color="primary"
                onClick={() => setFilteredVerifications(verifications)}
                sx={{ fontWeight: 'bold' }}
              />
              <Chip
                label={`Pending (${verifications.filter(v => v.status === 'pending').length})`}
                color="warning"
                onClick={() => setFilteredVerifications(verifications.filter(v => v.status === 'pending'))}
              />
              <Chip
                label={`Approved (${verifications.filter(v => v.status === 'approved').length})`}
                color="success"
                onClick={() => setFilteredVerifications(verifications.filter(v => v.status === 'approved'))}
              />
              <Chip
                label={`Rejected (${verifications.filter(v => v.status === 'rejected').length})`}
                color="error"
                onClick={() => setFilteredVerifications(verifications.filter(v => v.status === 'rejected'))}
              />
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Verifications Table */}
      <Paper elevation={3} sx={{ borderRadius: '16px', overflow: 'hidden' }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead sx={{ bgcolor: '#f5f5f5' }}>
                  <TableRow>
                    <TableCell><Typography variant="subtitle2" fontWeight="bold">User</Typography></TableCell>
                    <TableCell><Typography variant="subtitle2" fontWeight="bold">Aadhaar Number</Typography></TableCell>
                    <TableCell><Typography variant="subtitle2" fontWeight="bold">Submitted</Typography></TableCell>
                    <TableCell><Typography variant="subtitle2" fontWeight="bold">Status</Typography></TableCell>
                    <TableCell><Typography variant="subtitle2" fontWeight="bold">Reviewed</Typography></TableCell>
                    <TableCell align="right"><Typography variant="subtitle2" fontWeight="bold">Actions</Typography></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredVerifications.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        <Typography variant="body1" sx={{ py: 2 }}>
                          No verification requests found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredVerifications
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((verification) => (
                        <TableRow key={verification.id} hover>
                          <TableCell>
                            <Box>
                              <Typography variant="body1">{verification.userName}</Typography>
                              <Typography variant="body2" color="text.secondary">{verification.userEmail}</Typography>
                            </Box>
                          </TableCell>
                          <TableCell>{verification.aadhaarNumber}</TableCell>
                          <TableCell>{formatDate(verification.submittedAt)}</TableCell>
                          <TableCell>
                            {verification.status === 'pending' && (
                              <Chip label="Pending" color="warning" size="small" />
                            )}
                            {verification.status === 'approved' && (
                              <Chip label="Approved" color="success" size="small" icon={<CheckCircleIcon />} />
                            )}
                            {verification.status === 'rejected' && (
                              <Chip label="Rejected" color="error" size="small" icon={<CancelIcon />} />
                            )}
                          </TableCell>
                          <TableCell>
                            {verification.reviewedAt ? formatDate(verification.reviewedAt) : 'Not reviewed'}
                          </TableCell>
                          <TableCell align="right">
                            <Tooltip title="View Details">
                              <IconButton
                                color="primary"
                                onClick={() => handleViewVerification(verification)}
                                size="small"
                              >
                                <VisibilityIcon />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={filteredVerifications.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Paper>

      {/* Verification Details Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        {selectedVerification && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">Verification Details</Typography>
                <Chip
                  label={selectedVerification.status.charAt(0).toUpperCase() + selectedVerification.status.slice(1)}
                  color={
                    selectedVerification.status === 'approved' ? 'success' :
                    selectedVerification.status === 'rejected' ? 'error' : 'warning'
                  }
                />
              </Box>
            </DialogTitle>
            <DialogContent dividers>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card elevation={0} sx={{ height: '100%' }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>User Information</Typography>
                      <Divider sx={{ mb: 2 }} />
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" color="text.secondary">Name</Typography>
                        <Typography variant="body1">{selectedVerification.userName}</Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" color="text.secondary">Email</Typography>
                        <Typography variant="body1">{selectedVerification.userEmail}</Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" color="text.secondary">Aadhaar Number</Typography>
                        <Typography variant="body1">{selectedVerification.aadhaarNumber}</Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" color="text.secondary">Submitted At</Typography>
                        <Typography variant="body1">{formatDate(selectedVerification.submittedAt)}</Typography>
                      </Box>
                      {selectedVerification.reviewedAt && (
                        <>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="subtitle2" color="text.secondary">Reviewed At</Typography>
                            <Typography variant="body1">{formatDate(selectedVerification.reviewedAt)}</Typography>
                          </Box>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="subtitle2" color="text.secondary">Reviewed By</Typography>
                            <Typography variant="body1">{selectedVerification.reviewedBy}</Typography>
                          </Box>
                        </>
                      )}
                      {selectedVerification.notes && (
                        <Box sx={{ mb: 2 }}>
                          <Typography variant="subtitle2" color="text.secondary">Notes</Typography>
                          <Typography variant="body1">{selectedVerification.notes}</Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card elevation={0} sx={{ height: '100%' }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Aadhaar Document</Typography>
                      <Divider sx={{ mb: 2 }} />
                      <CardMedia
                        component="img"
                        image={selectedVerification.aadhaarImage}
                        alt="Aadhaar Card"
                        sx={{
                          width: '100%',
                          borderRadius: 1,
                          border: '1px solid #e0e0e0'
                        }}
                      />
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {selectedVerification.status === 'pending' && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="h6" gutterBottom>Review Decision</Typography>
                  <Divider sx={{ mb: 2 }} />
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    label="Rejection Reason (optional for approval)"
                    variant="outlined"
                    value={rejectReason}
                    onChange={(e) => setRejectReason(e.target.value)}
                    sx={{ mb: 2 }}
                  />
                </Box>
              )}
            </DialogContent>
            <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>
              <Button onClick={handleCloseDialog}>Close</Button>
              {selectedVerification.status === 'pending' && (
                <Box>
                  <Button
                    variant="contained"
                    color="error"
                    onClick={handleRejectVerification}
                    disabled={rejecting}
                    startIcon={rejecting ? <CircularProgress size={20} /> : <CancelIcon />}
                    sx={{ mr: 1 }}
                  >
                    Reject
                  </Button>
                  <Button
                    variant="contained"
                    color="success"
                    onClick={handleApproveVerification}
                    disabled={approving}
                    startIcon={approving ? <CircularProgress size={20} /> : <CheckCircleIcon />}
                  >
                    Approve
                  </Button>
                </Box>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default Verifications;
