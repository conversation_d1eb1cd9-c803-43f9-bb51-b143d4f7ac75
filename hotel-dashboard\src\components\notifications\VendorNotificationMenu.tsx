import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Badge,
  IconButton,
  Menu,
  MenuItem,
  Typography,
  Box,
  Divider,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Tooltip,
  CircularProgress,
  Chip
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  RoomService as RoomServiceIcon,
  BookOnline as BookingIcon,
  VerifiedUser as VerificationIcon,
  Info as SystemIcon,
  CheckCircle as ReadIcon,
  MarkChatRead as MarkReadIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { auth } from '../../firebase/config';
import {
  getVendorNotifications,
  markVendorNotificationAsRead,
  markAllVendorNotificationsAsRead,
  deleteVendorNotification,
  getUnreadVendorNotificationCount,
  VendorNotification,
  NOTIFICATION_TYPE_SERVICE_REQUEST,
  NOTIFICATION_TYPE_BOOKING,
  NOTIFICATION_TYPE_VERIFICATION,
  NOTIFICATION_TYPE_SYSTEM,
  NOTIFICATION_STATUS_UNREAD
} from '../../services/vendorNotificationService';

interface VendorNotificationMenuProps {
  hotelId?: string;
}

const VendorNotificationMenu: React.FC<VendorNotificationMenuProps> = ({ hotelId }) => {
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [notifications, setNotifications] = useState<VendorNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [markingAllAsRead, setMarkingAllAsRead] = useState<boolean>(false);

  const open = Boolean(anchorEl);

  // Fetch notifications and unread count on component mount and when menu opens
  useEffect(() => {
    if (auth.currentUser) {
      fetchUnreadCount();
    }
  }, [hotelId]);

  useEffect(() => {
    if (open && auth.currentUser) {
      fetchNotifications();
    }
  }, [open, hotelId]);

  const fetchUnreadCount = async () => {
    try {
      if (!auth.currentUser) return;
      
      const count = await getUnreadVendorNotificationCount(auth.currentUser.uid, hotelId);
      setUnreadCount(count);
    } catch (error) {
      console.error('Error fetching unread notification count:', error);
    }
  };

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      if (!auth.currentUser) return;
      
      const notificationsList = await getVendorNotifications(auth.currentUser.uid, {
        limit: 10,
        hotelId
      });
      
      setNotifications(notificationsList);
      
      // Update unread count
      const unreadNotifications = notificationsList.filter(
        notification => notification.status === NOTIFICATION_STATUS_UNREAD
      );
      setUnreadCount(unreadNotifications.length);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationClick = async (notification: VendorNotification) => {
    try {
      if (notification.id && notification.status === NOTIFICATION_STATUS_UNREAD) {
        await markVendorNotificationAsRead(notification.id);
        
        // Update local state
        setNotifications(prevNotifications =>
          prevNotifications.map(n =>
            n.id === notification.id ? { ...n, status: 'read', readAt: notification.readAt } : n
          )
        );
        
        // Update unread count
        setUnreadCount(prevCount => Math.max(0, prevCount - 1));
      }
      
      // Navigate to the action URL if available
      if (notification.actions && notification.actions.length > 0) {
        navigate(notification.actions[0].url);
      }
      
      handleClose();
    } catch (error) {
      console.error('Error handling notification click:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      setMarkingAllAsRead(true);
      if (!auth.currentUser) return;
      
      await markAllVendorNotificationsAsRead(auth.currentUser.uid, hotelId);
      
      // Update local state
      setNotifications(prevNotifications =>
        prevNotifications.map(n => ({
          ...n,
          status: 'read',
          readAt: n.readAt || new Date() as any
        }))
      );
      
      // Reset unread count
      setUnreadCount(0);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    } finally {
      setMarkingAllAsRead(false);
    }
  };

  const handleDeleteNotification = async (event: React.MouseEvent, notificationId: string) => {
    event.stopPropagation();
    try {
      await deleteVendorNotification(notificationId);
      
      // Update local state
      setNotifications(prevNotifications =>
        prevNotifications.filter(n => n.id !== notificationId)
      );
      
      // Update unread count if the deleted notification was unread
      const deletedNotification = notifications.find(n => n.id === notificationId);
      if (deletedNotification && deletedNotification.status === NOTIFICATION_STATUS_UNREAD) {
        setUnreadCount(prevCount => Math.max(0, prevCount - 1));
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case NOTIFICATION_TYPE_SERVICE_REQUEST:
        return <RoomServiceIcon color="primary" />;
      case NOTIFICATION_TYPE_BOOKING:
        return <BookingIcon color="info" />;
      case NOTIFICATION_TYPE_VERIFICATION:
        return <VerificationIcon color="success" />;
      case NOTIFICATION_TYPE_SYSTEM:
        return <SystemIcon color="warning" />;
      default:
        return <NotificationsIcon />;
    }
  };

  const getPriorityChip = (priority?: string) => {
    if (!priority) return null;
    
    switch (priority) {
      case 'high':
        return <Chip label="High" color="error" size="small" sx={{ ml: 1 }} />;
      case 'medium':
        return <Chip label="Medium" color="warning" size="small" sx={{ ml: 1 }} />;
      case 'low':
        return <Chip label="Low" color="info" size="small" sx={{ ml: 1 }} />;
      default:
        return null;
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';
    
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleString();
  };

  return (
    <>
      <Tooltip title="Notifications">
        <IconButton
          onClick={handleClick}
          size="large"
          color="inherit"
          aria-label="notifications"
        >
          <Badge badgeContent={unreadCount} color="error">
            <NotificationsIcon />
          </Badge>
        </IconButton>
      </Tooltip>
      
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: 360,
            maxHeight: 500,
            overflow: 'auto'
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Notifications</Typography>
          {unreadCount > 0 && (
            <Tooltip title="Mark all as read">
              <IconButton
                size="small"
                onClick={handleMarkAllAsRead}
                disabled={markingAllAsRead}
              >
                {markingAllAsRead ? <CircularProgress size={20} /> : <MarkReadIcon />}
              </IconButton>
            </Tooltip>
          )}
        </Box>
        
        <Divider />
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : notifications.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              No notifications
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {notifications.map((notification) => (
              <React.Fragment key={notification.id}>
                <ListItem
                  alignItems="flex-start"
                  sx={{
                    cursor: 'pointer',
                    bgcolor: notification.status === NOTIFICATION_STATUS_UNREAD ? 'action.hover' : 'inherit',
                    '&:hover': {
                      bgcolor: 'action.selected'
                    }
                  }}
                  onClick={() => handleNotificationClick(notification)}
                  secondaryAction={
                    <Tooltip title="Delete">
                      <IconButton
                        edge="end"
                        size="small"
                        onClick={(e) => notification.id && handleDeleteNotification(e, notification.id)}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  }
                >
                  <ListItemAvatar>
                    <Avatar>
                      {getNotificationIcon(notification.type)}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="subtitle2" component="span">
                          {notification.title}
                        </Typography>
                        {getPriorityChip(notification.priority)}
                        {notification.status !== NOTIFICATION_STATUS_UNREAD && (
                          <Tooltip title="Read">
                            <ReadIcon fontSize="small" color="disabled" sx={{ ml: 1 }} />
                          </Tooltip>
                        )}
                      </Box>
                    }
                    secondary={
                      <>
                        <Typography variant="body2" component="span" color="text.primary">
                          {notification.message}
                        </Typography>
                        <Typography variant="caption" display="block" color="text.secondary">
                          {formatDate(notification.createdAt)}
                        </Typography>
                      </>
                    }
                  />
                </ListItem>
                <Divider component="li" />
              </React.Fragment>
            ))}
          </List>
        )}
        
        <Box sx={{ p: 1, display: 'flex', justifyContent: 'center' }}>
          <Button
            size="small"
            onClick={() => {
              navigate('/vendor/notifications');
              handleClose();
            }}
          >
            View All Notifications
          </Button>
        </Box>
      </Menu>
    </>
  );
};

export default VendorNotificationMenu;
