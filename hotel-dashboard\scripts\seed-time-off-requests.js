const admin = require('firebase-admin');
const serviceAccount = require('../serviceAccountKey.json');
const { addDays, subDays, format } = require('date-fns');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

// Constants
const STAFF_COLLECTION = 'staff';
const STAFF_TIME_OFF_COLLECTION = 'staffTimeOff';

// Time off types
const TIME_OFF_TYPE_VACATION = 'vacation';
const TIME_OFF_TYPE_SICK = 'sick';
const TIME_OFF_TYPE_PERSONAL = 'personal';
const TIME_OFF_TYPE_OTHER = 'other';

// Time off status
const TIME_OFF_STATUS_PENDING = 'pending';
const TIME_OFF_STATUS_APPROVED = 'approved';
const TIME_OFF_STATUS_REJECTED = 'rejected';

// Sample hotel and vendor IDs (replace with actual IDs)
const HOTEL_ID = 'hotel123';
const VENDOR_ID = 'vendor123';

// Sample approvers
const APPROVERS = [
  'Hotel Manager',
  'Admin User',
  'John Smith',
  'Jane Doe'
];

// Sample reasons for time off
const VACATION_REASONS = [
  'Family vacation',
  'Beach trip',
  'Visiting relatives',
  'Holiday travel',
  'Personal time off'
];

const SICK_REASONS = [
  'Not feeling well',
  'Doctor\'s appointment',
  'Medical procedure',
  'Recovering from illness',
  'Dental appointment'
];

const PERSONAL_REASONS = [
  'Family emergency',
  'Personal matters',
  'Moving to a new home',
  'Attending a wedding',
  'Attending a funeral'
];

const OTHER_REASONS = [
  'Professional development',
  'Training course',
  'Volunteer work',
  'Religious observance',
  'Jury duty'
];

// Helper function to get random item from array
const getRandomItem = (array) => {
  return array[Math.floor(Math.random() * array.length)];
};

// Helper function to get random date in the future
const getRandomFutureDate = (minDays = 1, maxDays = 60) => {
  const daysInFuture = Math.floor(Math.random() * (maxDays - minDays + 1)) + minDays;
  return addDays(new Date(), daysInFuture);
};

// Helper function to get random date in the past
const getRandomPastDate = (minDays = 1, maxDays = 60) => {
  const daysInPast = Math.floor(Math.random() * (maxDays - minDays + 1)) + minDays;
  return subDays(new Date(), daysInPast);
};

// Seed time off requests
async function seedTimeOffRequests() {
  try {
    console.log('Seeding time off requests...');
    
    // Clear existing time off requests
    const existingRequests = await db.collection(STAFF_TIME_OFF_COLLECTION)
      .where('hotelId', '==', HOTEL_ID)
      .get();
    
    const batch = db.batch();
    
    existingRequests.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
    console.log(`Deleted ${existingRequests.size} existing time off requests.`);
    
    // Get staff members
    const staffSnapshot = await db.collection(STAFF_COLLECTION)
      .where('hotelId', '==', HOTEL_ID)
      .where('status', '==', 'active')
      .get();
    
    if (staffSnapshot.empty) {
      console.log('No active staff members found. Please run seed-staff-members.js first.');
      return;
    }
    
    const staffMembers = [];
    staffSnapshot.forEach(doc => {
      staffMembers.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    console.log(`Found ${staffMembers.length} active staff members.`);
    
    // Generate time off requests
    const timeOffRequests = [];
    
    // For each staff member, create some time off requests
    for (const staff of staffMembers) {
      // Create 1-3 time off requests per staff member
      const numRequests = Math.floor(Math.random() * 3) + 1;
      
      for (let i = 0; i < numRequests; i++) {
        // Randomly decide if this is a past, current, or future request
        const requestType = Math.random();
        
        let startDate, endDate, status, approvedBy, approvedAt, type, reason;
        
        if (requestType < 0.3) {
          // Past request (completed)
          startDate = getRandomPastDate(30, 60);
          endDate = addDays(startDate, Math.floor(Math.random() * 5) + 1);
          status = Math.random() > 0.2 ? TIME_OFF_STATUS_APPROVED : TIME_OFF_STATUS_REJECTED;
          approvedBy = getRandomItem(APPROVERS);
          approvedAt = addDays(startDate, -Math.floor(Math.random() * 10) - 1); // Approved 1-10 days before start
        } else if (requestType < 0.6) {
          // Current/upcoming request (approved)
          startDate = getRandomFutureDate(1, 14);
          endDate = addDays(startDate, Math.floor(Math.random() * 5) + 1);
          status = TIME_OFF_STATUS_APPROVED;
          approvedBy = getRandomItem(APPROVERS);
          approvedAt = subDays(new Date(), Math.floor(Math.random() * 5) + 1); // Approved 1-5 days ago
        } else {
          // Pending request
          startDate = getRandomFutureDate(14, 60);
          endDate = addDays(startDate, Math.floor(Math.random() * 5) + 1);
          status = TIME_OFF_STATUS_PENDING;
          approvedBy = null;
          approvedAt = null;
        }
        
        // Randomly select type and reason
        const typeRandom = Math.random();
        if (typeRandom < 0.4) {
          type = TIME_OFF_TYPE_VACATION;
          reason = getRandomItem(VACATION_REASONS);
        } else if (typeRandom < 0.7) {
          type = TIME_OFF_TYPE_SICK;
          reason = getRandomItem(SICK_REASONS);
        } else if (typeRandom < 0.9) {
          type = TIME_OFF_TYPE_PERSONAL;
          reason = getRandomItem(PERSONAL_REASONS);
        } else {
          type = TIME_OFF_TYPE_OTHER;
          reason = getRandomItem(OTHER_REASONS);
        }
        
        // Create time off request
        timeOffRequests.push({
          staffId: staff.id,
          staffName: staff.name,
          hotelId: HOTEL_ID,
          vendorId: VENDOR_ID,
          startDate: admin.firestore.Timestamp.fromDate(startDate),
          endDate: admin.firestore.Timestamp.fromDate(endDate),
          type,
          status,
          reason,
          approvedBy,
          approvedAt: approvedAt ? admin.firestore.Timestamp.fromDate(approvedAt) : null,
          createdAt: admin.firestore.Timestamp.fromDate(subDays(startDate, Math.floor(Math.random() * 10) + 5)), // Created 5-15 days before start
          updatedAt: approvedAt ? admin.firestore.Timestamp.fromDate(approvedAt) : admin.firestore.Timestamp.fromDate(subDays(startDate, Math.floor(Math.random() * 10) + 5))
        });
      }
    }
    
    // Add time off requests to Firestore
    const requestPromises = timeOffRequests.map(request => 
      db.collection(STAFF_TIME_OFF_COLLECTION).add(request)
    );
    
    await Promise.all(requestPromises);
    
    console.log(`Added ${timeOffRequests.length} new time off requests.`);
    console.log('Time off requests seeded successfully!');
  } catch (error) {
    console.error('Error seeding time off requests:', error);
  }
}

// Run the seed function
seedTimeOffRequests()
  .then(() => {
    console.log('Script completed successfully!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
