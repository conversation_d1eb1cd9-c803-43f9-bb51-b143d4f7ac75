import React from 'react';
import {
  <PERSON>,
  Alert,
  AlertTitle,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  <PERSON>lapse,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Warning as WarningIcon,
  Error as ErrorIcon,
  AccessTime as TimeIcon,
  EventBusy as EventBusyIcon,
  HourglassEmpty as HourglassIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore,
  ExpandLess,
  Info as InfoIcon
} from '@mui/icons-material';
import { ScheduleConflict, CONFLICT_SEVERITY_ERROR, CONFLICT_SEVERITY_WARNING } from '../../services/scheduleConflictService';

interface ConflictAlertProps {
  conflicts: ScheduleConflict[];
  onIgnore?: (conflict: ScheduleConflict) => void;
}

const ConflictAlert: React.FC<ConflictAlertProps> = ({ conflicts, onIgnore }) => {
  const [expanded, setExpanded] = React.useState<boolean>(true);

  if (conflicts.length === 0) {
    return null;
  }

  const errorConflicts = conflicts.filter(conflict => conflict.severity === CONFLICT_SEVERITY_ERROR);
  const warningConflicts = conflicts.filter(conflict => conflict.severity === CONFLICT_SEVERITY_WARNING);

  const handleToggleExpand = () => {
    setExpanded(!expanded);
  };

  const getConflictIcon = (conflict: ScheduleConflict) => {
    switch (conflict.type) {
      case 'shift_overlap':
        return <TimeIcon color={conflict.severity === CONFLICT_SEVERITY_ERROR ? 'error' : 'warning'} />;
      case 'time_off_conflict':
        return <EventBusyIcon color={conflict.severity === CONFLICT_SEVERITY_ERROR ? 'error' : 'warning'} />;
      case 'consecutive_shifts':
        return <HourglassIcon color={conflict.severity === CONFLICT_SEVERITY_ERROR ? 'error' : 'warning'} />;
      case 'max_hours_exceeded':
        return <TimeIcon color={conflict.severity === CONFLICT_SEVERITY_ERROR ? 'error' : 'warning'} />;
      default:
        return <InfoIcon color={conflict.severity === CONFLICT_SEVERITY_ERROR ? 'error' : 'warning'} />;
    }
  };

  return (
    <Box sx={{ mb: 3 }}>
      <Alert
        severity={errorConflicts.length > 0 ? 'error' : 'warning'}
        action={
          <IconButton
            aria-label="expand"
            color="inherit"
            size="small"
            onClick={handleToggleExpand}
          >
            {expanded ? <ExpandLess /> : <ExpandMore />}
          </IconButton>
        }
      >
        <AlertTitle>
          {errorConflicts.length > 0 ? (
            <>
              <ErrorIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 1 }} />
              Schedule Conflicts Detected
            </>
          ) : (
            <>
              <WarningIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 1 }} />
              Schedule Warnings
            </>
          )}
        </AlertTitle>

        {errorConflicts.length > 0 && (
          <Box component="span" sx={{ fontWeight: 'bold' }}>
            {errorConflicts.length} error{errorConflicts.length !== 1 ? 's' : ''}
          </Box>
        )}

        {errorConflicts.length > 0 && warningConflicts.length > 0 && ' and '}

        {warningConflicts.length > 0 && (
          <Box component="span" sx={{ fontWeight: errorConflicts.length > 0 ? 'normal' : 'bold' }}>
            {warningConflicts.length} warning{warningConflicts.length !== 1 ? 's' : ''}
          </Box>
        )}

        {' detected in the schedule.'}

        <Collapse in={expanded} timeout="auto" unmountOnExit>
          <List dense sx={{ mt: 1 }}>
            {conflicts.map((conflict, index) => (
              <ListItem key={index} sx={{
                bgcolor: conflict.severity === CONFLICT_SEVERITY_ERROR ? 'error.light' : 'warning.light',
                borderRadius: 1,
                mb: 1,
                '&:last-child': { mb: 0 }
              }}>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  {getConflictIcon(conflict)}
                </ListItemIcon>
                <ListItemText
                  primary={conflict.message}
                  secondary={`Staff: ${conflict.staffName}`}
                />
                {onIgnore && (
                  <Tooltip title="Ignore this warning">
                    <IconButton
                      edge="end"
                      aria-label="ignore"
                      onClick={() => onIgnore(conflict)}
                      size="small"
                    >
                      <InfoIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
              </ListItem>
            ))}
          </List>
        </Collapse>
      </Alert>
    </Box>
  );
};

export default ConflictAlert;
