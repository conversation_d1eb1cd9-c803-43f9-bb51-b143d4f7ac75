import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  InputAdornment,
  Grid,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Cancel as CancelIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { auth } from '../../firebase/config';
import { getHotelsForVendor, HotelData } from '../../firebase/hotelService';
import { searchBookings, BookingData, BookingSearchParams } from '../../services/bookingService';

// Define TabPanel component for tab content
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`bookings-tabpanel-${index}`}
      aria-labelledby={`bookings-tab-${index}`}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const VendorBookingsManager: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hotels, setHotels] = useState<HotelData[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  
  // Bookings state
  const [bookings, setBookings] = useState<BookingData[]>([]);
  const [filteredBookings, setFilteredBookings] = useState<BookingData[]>([]);
  const [lastDoc, setLastDoc] = useState<any>(null);

  // Load data on component mount
  useEffect(() => {
    fetchHotels();
  }, []);

  // Fetch bookings when hotel or tab changes
  useEffect(() => {
    if (selectedHotel) {
      fetchBookings();
    }
  }, [selectedHotel, tabValue]);

  // Apply filters when search term changes
  useEffect(() => {
    applyFilters();
  }, [bookings, searchTerm]);

  // Fetch hotels
  const fetchHotels = async () => {
    try {
      setLoading(true);
      if (!auth.currentUser) {
        setError('You must be logged in to view bookings');
        setLoading(false);
        return;
      }

      const hotelsList = await getHotelsForVendor(auth.currentUser.uid);
      setHotels(hotelsList);
      
      if (hotelsList.length > 0) {
        setSelectedHotel(hotelsList[0].id || '');
      }
      
      setLoading(false);
    } catch (err) {
      console.error('Error fetching hotels:', err);
      setError('Failed to load hotels. Please try again later.');
      setLoading(false);
    }
  };

  // Fetch bookings
  const fetchBookings = async () => {
    try {
      setLoading(true);
      if (!selectedHotel) return;

      // Prepare search parameters based on current tab
      const searchParams: BookingSearchParams = {
        hotelId: selectedHotel,
        limit: 20
      };

      // Add status filters based on tab
      switch (tabValue) {
        case 1: // Upcoming
          searchParams.bookingStatus = ['confirmed'];
          searchParams.startDate = new Date(); // From today
          break;
        case 2: // Current
          searchParams.bookingStatus = ['checked_in'];
          break;
        case 3: // Past
          searchParams.bookingStatus = ['checked_out'];
          searchParams.endDate = new Date(); // Until today
          break;
        case 4: // Cancelled
          searchParams.bookingStatus = ['cancelled', 'no_show'];
          break;
        default: // All bookings
          break;
      }

      // Add search term if specified
      if (searchTerm && typeof searchTerm === 'string') {
        try {
          if (searchTerm.startsWith('BK-')) {
            searchParams.bookingNumber = searchTerm;
          } else if (searchTerm.includes('@')) {
            searchParams.guestEmail = searchTerm;
          } else if (/^\d+$/.test(searchTerm)) {
            searchParams.guestPhone = searchTerm;
          } else {
            searchParams.guestName = searchTerm;
          }
        } catch (error) {
          console.error('Error processing search term:', error);
        }
      }

      const result = await searchBookings(searchParams);

      setBookings(result.bookings);
      setLastDoc(result.lastDoc);
      
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching bookings:', err);
      setError(err.message || 'Failed to load bookings');
      setLoading(false);
    }
  };

  // Apply filters to bookings
  const applyFilters = () => {
    try {
      // Make a safe copy of bookings, ensuring it's an array
      let filtered = Array.isArray(bookings) ? [...bookings] : [];

      // Apply search term filter
      if (searchTerm && searchTerm.trim() !== '') {
        try {
          const term = searchTerm.toLowerCase();
          filtered = filtered.filter(booking => {
            if (!booking) return false;

            // Check booking number
            const bookingNumberMatch = booking.bookingNumber && 
              typeof booking.bookingNumber === 'string' &&
              booking.bookingNumber.toLowerCase().includes(term);

            // Check guest name
            let guestNameMatch = false;
            if (booking.guestInfo) {
              const firstName = booking.guestInfo.firstName || '';
              const lastName = booking.guestInfo.lastName || '';
              if (firstName || lastName) {
                const fullName = `${firstName} ${lastName}`.trim();
                guestNameMatch = fullName.toLowerCase().includes(term);
              }
            }

            // Check email
            const emailMatch = booking.guestInfo &&
              booking.guestInfo.email &&
              typeof booking.guestInfo.email === 'string' &&
              booking.guestInfo.email.toLowerCase().includes(term);

            // Check phone
            const phoneMatch = booking.guestInfo &&
              booking.guestInfo.phone &&
              typeof booking.guestInfo.phone === 'string' &&
              booking.guestInfo.phone.includes(term);

            return bookingNumberMatch || guestNameMatch || emailMatch || phoneMatch;
          });
        } catch (error) {
          console.error('Error applying search term filter:', error);
        }
      }

      setFilteredBookings(filtered);
    } catch (error) {
      console.error('Error in applyFilters:', error);
      // Set to empty array as fallback
      setFilteredBookings([]);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle hotel change
  const handleHotelChange = (event: any) => {
    setSelectedHotel(event.target.value);
  };

  // Handle search term change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchBookings();
  };
  
  // Format date
  const formatDate = (date: any) => {
    if (!date) return 'N/A';

    try {
      // Handle Firestore Timestamp
      if (date && typeof date.toDate === 'function') {
        date = date.toDate();
      }

      // Handle JavaScript Date
      if (date instanceof Date) {
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }

      return 'N/A';
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'N/A';
    }
  };
  
  // Get booking status color
  const getBookingStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'checked_in':
        return 'primary';
      case 'checked_out':
        return 'default';
      case 'cancelled':
        return 'error';
      case 'no_show':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Bookings Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          disabled={!selectedHotel}
        >
          New Booking
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Hotel selector and search */}
      {hotels.length > 0 && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel id="hotel-select-label">Select Hotel</InputLabel>
                <Select
                  labelId="hotel-select-label"
                  value={selectedHotel}
                  label="Select Hotel"
                  onChange={handleHotelChange}
                >
                  {hotels.map((hotel) => (
                    <MenuItem key={hotel.id} value={hotel.id}>
                      {hotel.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search by booking #, guest name, email or phone"
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  )
                }}
              />
            </Grid>

            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleRefresh}
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
        </Paper>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : hotels.length === 0 ? (
        <Alert severity="info">
          You need to create at least one hotel before you can manage bookings.
        </Alert>
      ) : (
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab label="All Bookings" />
            <Tab label="Upcoming" />
            <Tab label="Current" />
            <Tab label="Past" />
            <Tab label="Cancelled" />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Box>
                <Typography variant="h6" gutterBottom>
                  All Bookings
                </Typography>
                <Typography variant="body1">
                  View and manage all bookings for your hotel.
                </Typography>
              </Box>
            </Box>

            {filteredBookings.length === 0 ? (
              <Alert severity="info" sx={{ mb: 3 }}>
                No bookings found. Try adjusting your search or filters.
              </Alert>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Booking #</TableCell>
                      <TableCell>Guest</TableCell>
                      <TableCell>Dates</TableCell>
                      <TableCell>Room</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Total</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredBookings.map((booking) => (
                      <TableRow key={booking.id}>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {booking.bookingNumber || 'N/A'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {booking.createdAt ? formatDate(booking.createdAt) : ''}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {booking.guestInfo ? `${booking.guestInfo.firstName || ''} ${booking.guestInfo.lastName || ''}`.trim() : 'N/A'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {booking.guestInfo && booking.guestInfo.email ? booking.guestInfo.email : 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {formatDate(booking.checkInDate)}
                          </Typography>
                          <Typography variant="body2">
                            to {formatDate(booking.checkOutDate)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            Room {booking.roomId || 'N/A'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {booking.adults || 0} adults, {booking.children || 0} children
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={booking.bookingStatus ? booking.bookingStatus.replace('_', ' ') : 'Unknown'}
                            color={getBookingStatusColor(booking.bookingStatus) as any}
                            size="small"
                          />
                          <Typography variant="caption" display="block" color="text.secondary" sx={{ mt: 0.5 }}>
                            {booking.paymentStatus ? booking.paymentStatus.replace('_', ' ') : 'Unknown'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          ${booking.totalAmount || 0}
                        </TableCell>
                        <TableCell align="right">
                          <Tooltip title="View Details">
                            <IconButton size="small" color="primary">
                              <VisibilityIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          {booking.bookingStatus === 'confirmed' && (
                            <Tooltip title="Check In">
                              <IconButton size="small" color="success">
                                <CheckCircleIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                          {(booking.bookingStatus === 'confirmed' || booking.bookingStatus === 'pending') && (
                            <>
                              <Tooltip title="Edit">
                                <IconButton size="small" color="primary">
                                  <EditIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Cancel">
                                <IconButton size="small" color="error">
                                  <CancelIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </TabPanel>

          {/* Other tab panels would go here */}
        </Paper>
      )}
    </Box>
  );
};

export default VendorBookingsManager;
