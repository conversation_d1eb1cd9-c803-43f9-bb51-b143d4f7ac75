import React, { useState, useEffect } from 'react';
import { Outlet, useNavigate, useLocation, Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Tooltip,
  useTheme,
  useMediaQuery,
  Collapse,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  Hotel as HotelIcon,
  MeetingRoom as RoomIcon,
  People as PeopleIcon,
  BookOnline as BookingIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  ChevronLeft as ChevronLeftIcon,
  AttachMoney as MoneyIcon,
  NotificationsActive as NotificationIcon,
  BarChart as ReportIcon,
  VerifiedUser as VerifiedUserIcon,
  RoomService as RoomServiceIcon,
  ExpandLess,
  ExpandMore,
} from '@mui/icons-material';
import { signOut } from '../../firebase/auth';
import { auth } from '../../firebase/config';
import VendorNotificationMenu from '../notifications/VendorNotificationMenu';
import ServiceRequestNotification from '../notifications/ServiceRequestNotification';

const drawerWidth = 240;

const VendorLayout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [open, setOpen] = useState(!isMobile);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [expandedMenus, setExpandedMenus] = useState<{ [key: string]: boolean }>({
    Verifications: true, // Default expanded
  });

  // Track which menu items have sub-items
  const [menuWithSubItems, setMenuWithSubItems] = useState<{ [key: string]: boolean }>({});

  const handleDrawerToggle = () => {
    setOpen(!open);
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = async () => {
    try {
      await signOut();
      navigate('/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleToggleSubMenu = (menuName: string) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuName]: !prev[menuName]
    }));
  };

  const menuItems = [
    { text: 'Dashboard', icon: <DashboardIcon />, path: '/vendor' },
    { text: 'My Hotel', icon: <HotelIcon />, path: '/vendor/hotels' },
    { text: 'Rooms', icon: <RoomIcon />, path: '/vendor/rooms' },
    { text: 'Bookings', icon: <BookingIcon />, path: '/vendor/bookings' },
    {
      text: 'Services',
      icon: <RoomServiceIcon />,
      path: '/vendor/services',
      subItems: [
        { text: 'Dashboard', path: '/vendor/services' },
        { text: 'Cleaning Requests', path: '/vendor/services/cleaning' },
        { text: 'Food Orders', path: '/vendor/services/food' },
        { text: 'Maintenance', path: '/vendor/services/maintenance' },
      ]
    },
    {
      text: 'Verifications',
      icon: <VerifiedUserIcon />,
      path: '/vendor/verifications',
      subItems: [
        { text: 'Verification List', path: '/vendor/verifications' },
        { text: 'Verification Reports', path: '/vendor/verification-reports' },
      ]
    },
    { text: 'Pricing', icon: <MoneyIcon />, path: '/vendor/pricing' },
    { text: 'Notifications', icon: <NotificationIcon />, path: '/vendor/notifications' },
    { text: 'Reports', icon: <ReportIcon />, path: '/vendor/reports' },
    {
      text: 'Staff',
      icon: <PeopleIcon />,
      path: '/vendor/staff',
      subItems: [
        { text: 'Staff List', path: '/vendor/staff' },
        { text: 'Staff Management', path: '/vendor/staff-management' },
        { text: 'Staff Scheduling', path: '/vendor/staff-scheduling' },
      ]
    },
    { text: 'Settings', icon: <SettingsIcon />, path: '/vendor/settings' },
  ];

  // Initialize menu with sub-items on component mount
  useEffect(() => {
    const menuWithSubs = menuItems.reduce((acc, item) => {
      if (item.subItems && item.subItems.length > 0) {
        acc[item.text] = true;
      }
      return acc;
    }, {} as { [key: string]: boolean });

    setMenuWithSubItems(menuWithSubs);
  }, []);

  const drawer = (
    <>
      <Toolbar
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          px: [1],
          py: 1,
          minHeight: '70px',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <img src="/link_in_blink.png" alt="Link In Blink Logo" style={{ height: '59px' }} />
        </Box>
        <IconButton onClick={handleDrawerToggle}>
          <ChevronLeftIcon />
        </IconButton>
      </Toolbar>
      <Divider />
      <List component="nav">
        {menuItems.map((item) => (
          <React.Fragment key={item.text}>
            <ListItem disablePadding>
              <ListItemButton
                {...(item.subItems ? {
                  onClick: () => handleToggleSubMenu(item.text)
                } : {
                  component: RouterLink,
                  to: item.path
                })}
                selected={
                  location.pathname === item.path ||
                  (item.subItems && item.subItems.some(subItem => location.pathname === subItem.path))
                }
                sx={{
                  '&.Mui-selected': {
                    backgroundColor: 'primary.light',
                    '&:hover': {
                      backgroundColor: 'primary.light',
                    },
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    color: (location.pathname === item.path ||
                      (item.subItems && item.subItems.some(subItem => location.pathname === subItem.path)))
                      ? 'primary.main' : 'inherit',
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText primary={item.text} />
                {item.subItems && item.subItems.length > 0 && (
                  expandedMenus[item.text] ? <ExpandLess /> : <ExpandMore />
                )}
              </ListItemButton>
            </ListItem>

            {item.subItems && item.subItems.length > 0 && (
              <Collapse in={expandedMenus[item.text] || false} timeout="auto" unmountOnExit>
                <List component="div" disablePadding>
                  {item.subItems.map((subItem) => (
                    <ListItem key={subItem.text} disablePadding>
                      <ListItemButton
                        component={RouterLink}
                        to={subItem.path}
                        selected={location.pathname === subItem.path}
                        sx={{
                          pl: 4,
                          '&.Mui-selected': {
                            backgroundColor: 'primary.light',
                            '&:hover': {
                              backgroundColor: 'primary.light',
                            },
                          },
                        }}
                      >
                        <ListItemText primary={subItem.text} />
                      </ListItemButton>
                    </ListItem>
                  ))}
                </List>
              </Collapse>
            )}
          </React.Fragment>
        ))}
      </List>
    </>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar
        position="fixed"
        sx={{
          zIndex: (theme) => theme.zIndex.drawer + 1,
          ml: { sm: open ? drawerWidth : 0 },
          width: { sm: open ? `calc(100% - ${drawerWidth}px)` : '100%' },
          transition: (theme) =>
            theme.transitions.create(['width', 'margin'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.leavingScreen,
            }),
        }}
      >
        <Toolbar sx={{ minHeight: { xs: '64px' } }}>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 1 }}
          >
            <MenuIcon />
          </IconButton>
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{
              flexGrow: 1,
              fontSize: { xs: '1rem', sm: '1.25rem' } // Smaller font on mobile
            }}
          >
            Vendor Dashboard
          </Typography>

          {/* Notifications */}
          <ServiceRequestNotification />
          <VendorNotificationMenu hotelId="hotel123" />

          <Tooltip title="Account settings">
            <IconButton
              onClick={handleProfileMenuOpen}
              size="small"
              sx={{ ml: { xs: 1, sm: 2 } }}
              aria-controls={Boolean(anchorEl) ? 'account-menu' : undefined}
              aria-haspopup="true"
              aria-expanded={Boolean(anchorEl) ? 'true' : undefined}
            >
              <Avatar sx={{ width: { xs: 28, sm: 32 }, height: { xs: 28, sm: 32 } }}>
                {auth.currentUser?.displayName?.[0] || 'V'}
              </Avatar>
            </IconButton>
          </Tooltip>
        </Toolbar>
      </AppBar>

      <Drawer
        variant={isMobile ? 'temporary' : 'persistent'}
        open={open}
        onClose={isMobile ? handleDrawerToggle : undefined}
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
          },
        }}
      >
        {drawer}
      </Drawer>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 2, sm: 3 }, // Smaller padding on mobile
          width: { sm: `calc(100% - ${open ? drawerWidth : 0}px)` },
          transition: (theme) =>
            theme.transitions.create(['width', 'margin'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.leavingScreen,
            }),
          overflowX: 'hidden', // Prevent horizontal scrolling
        }}
      >
        <Toolbar />
        <Outlet />
      </Box>

      <Menu
        anchorEl={anchorEl}
        id="account-menu"
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        onClick={handleProfileMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => navigate('/vendor/settings')}>
          <ListItemIcon>
            <SettingsIcon fontSize="small" />
          </ListItemIcon>
          Settings
        </MenuItem>
        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <LogoutIcon fontSize="small" />
          </ListItemIcon>
          Logout
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default VendorLayout;
