import {
  collection,
  doc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  getDocs,
  getDoc,
  serverTimestamp,
  Timestamp,
  orderBy,
  limit,
  startAfter
} from 'firebase/firestore';
import { db, storage } from '../firebase/config';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';

// Interface for room data
export interface RoomData {
  id?: string;
  hotelId: string;
  name: string;
  type: string;
  description?: string;
  basePrice: number;
  capacity: number;
  beds: {
    single: number;
    double: number;
    queen: number;
    king: number;
  };
  size?: number; // in square meters/feet
  amenities?: string[];
  images?: string[];
  status: 'active' | 'maintenance' | 'inactive';
  floor?: number;
  roomNumber?: string;
  isAccessible?: boolean;
  maxAdults?: number;
  maxChildren?: number;
  smokingAllowed?: boolean;
  petsAllowed?: boolean;
  createdAt?: any;
  updatedAt?: any;
}

// Interface for room availability
export interface RoomAvailability {
  id?: string;
  roomId: string;
  date: Timestamp;
  isAvailable: boolean;
  price?: number; // Override price for specific date
  minimumStay?: number;
  blockedReason?: string;
  updatedAt?: any;
}

// Interface for room type
export interface RoomType {
  id?: string;
  hotelId: string;
  name: string;
  description?: string;
  basePrice: number;
  capacity: number;
  amenities?: string[];
  images?: string[];
  createdAt?: any;
  updatedAt?: any;
}

/**
 * Create a new room
 */
export const createRoom = async (roomData: RoomData, imageFiles?: File[]): Promise<string> => {
  try {
    // Create a new document reference
    const roomRef = doc(collection(db, 'rooms'));

    // Upload images if provided
    const imageUrls: string[] = [];

    if (imageFiles && imageFiles.length > 0) {
      for (const file of imageFiles) {
        const storageRef = ref(storage, `rooms/${roomRef.id}/${file.name}`);
        await uploadBytes(storageRef, file);
        const url = await getDownloadURL(storageRef);
        imageUrls.push(url);
      }
    }

    // Add the room data with timestamps and image URLs
    await setDoc(roomRef, {
      ...roomData,
      images: [...(roomData.images || []), ...imageUrls],
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return roomRef.id;
  } catch (error) {
    console.error('Error creating room:', error);
    throw error;
  }
};

/**
 * Get all rooms for a specific hotel
 */
export const getRoomsForHotel = async (hotelId: string) => {
  try {
    const roomsQuery = query(
      collection(db, 'rooms'),
      where('hotelId', '==', hotelId),
      orderBy('name')
    );

    const roomsSnapshot = await getDocs(roomsQuery);
    const rooms: RoomData[] = [];

    roomsSnapshot.forEach((doc) => {
      rooms.push({ id: doc.id, ...doc.data() } as RoomData);
    });

    return rooms;
  } catch (error) {
    console.error('Error getting rooms:', error);
    throw error;
  }
};

/**
 * Get rooms by type for a specific hotel
 */
export const getRoomsByType = async (hotelId: string, roomType: string) => {
  try {
    const roomsQuery = query(
      collection(db, 'rooms'),
      where('hotelId', '==', hotelId),
      where('type', '==', roomType)
    );

    const roomsSnapshot = await getDocs(roomsQuery);
    const rooms: RoomData[] = [];

    roomsSnapshot.forEach((doc) => {
      rooms.push({ id: doc.id, ...doc.data() } as RoomData);
    });

    return rooms;
  } catch (error) {
    console.error('Error getting rooms by type:', error);
    throw error;
  }
};

/**
 * Get a room by ID
 */
export const getRoomById = async (roomId: string) => {
  try {
    const roomDoc = await getDoc(doc(db, 'rooms', roomId));

    if (roomDoc.exists()) {
      return { id: roomDoc.id, ...roomDoc.data() } as RoomData;
    } else {
      throw new Error('Room not found');
    }
  } catch (error) {
    console.error('Error getting room:', error);
    throw error;
  }
};

/**
 * Update a room
 */
export const updateRoom = async (roomId: string, roomData: Partial<RoomData>, imageFiles?: File[], deleteImageUrls?: string[]): Promise<boolean> => {
  try {
    const roomRef = doc(db, 'rooms', roomId);

    // Get current room data to access existing images
    const roomDoc = await getDoc(roomRef);
    if (!roomDoc.exists()) {
      throw new Error('Room not found');
    }

    const currentRoom = roomDoc.data() as RoomData;
    let updatedImages = [...(currentRoom.images || [])];

    // Delete images if specified
    if (deleteImageUrls && deleteImageUrls.length > 0) {
      // Remove from storage
      for (const url of deleteImageUrls) {
        try {
          // Extract the path from the URL
          const urlPath = url.split('?')[0].split('/o/')[1];
          if (urlPath) {
            const decodedPath = decodeURIComponent(urlPath);
            const imageRef = ref(storage, decodedPath);
            await deleteObject(imageRef);
          }
        } catch (err) {
          console.error('Error deleting image from storage:', err);
          // Continue with other deletions even if one fails
        }
      }

      // Remove from images array
      updatedImages = updatedImages.filter(url => !deleteImageUrls.includes(url));
    }

    // Upload new images if provided
    if (imageFiles && imageFiles.length > 0) {
      for (const file of imageFiles) {
        const storageRef = ref(storage, `rooms/${roomId}/${file.name}`);
        await uploadBytes(storageRef, file);
        const url = await getDownloadURL(storageRef);
        updatedImages.push(url);
      }
    }

    // Update the room data
    await updateDoc(roomRef, {
      ...roomData,
      images: updatedImages,
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('Error updating room:', error);
    throw error;
  }
};

/**
 * Delete a room
 */
export const deleteRoom = async (roomId: string): Promise<boolean> => {
  try {
    // Get room data to delete images
    const roomDoc = await getDoc(doc(db, 'rooms', roomId));

    if (roomDoc.exists()) {
      const roomData = roomDoc.data() as RoomData;

      // Delete images from storage
      if (roomData.images && roomData.images.length > 0) {
        for (const url of roomData.images) {
          try {
            // Extract the path from the URL
            const urlPath = url.split('?')[0].split('/o/')[1];
            if (urlPath) {
              const decodedPath = decodeURIComponent(urlPath);
              const imageRef = ref(storage, decodedPath);
              await deleteObject(imageRef);
            }
          } catch (err) {
            console.error('Error deleting image from storage:', err);
            // Continue with other deletions even if one fails
          }
        }
      }
    }

    // Delete the room document
    await deleteDoc(doc(db, 'rooms', roomId));

    return true;
  } catch (error) {
    console.error('Error deleting room:', error);
    throw error;
  }
};

/**
 * Get room availability for a date range
 */
export const getRoomAvailability = async (roomId: string, startDate: Date, endDate: Date) => {
  try {
    const startTimestamp = Timestamp.fromDate(startDate);
    const endTimestamp = Timestamp.fromDate(endDate);

    const availabilityQuery = query(
      collection(db, 'roomAvailability'),
      where('roomId', '==', roomId),
      where('date', '>=', startTimestamp),
      where('date', '<=', endTimestamp)
    );

    const availabilitySnapshot = await getDocs(availabilityQuery);
    const availability: RoomAvailability[] = [];

    availabilitySnapshot.forEach((doc) => {
      availability.push({ id: doc.id, ...doc.data() } as RoomAvailability);
    });

    return availability;
  } catch (error) {
    console.error('Error getting room availability:', error);
    throw error;
  }
};

/**
 * Update room availability for a specific date
 */
export const updateRoomAvailability = async (roomId: string, date: Date, isAvailable: boolean, price?: number, minimumStay?: number, blockedReason?: string): Promise<void> => {
  try {
    const dateTimestamp = Timestamp.fromDate(date);

    // Check if availability record exists
    const availabilityQuery = query(
      collection(db, 'roomAvailability'),
      where('roomId', '==', roomId),
      where('date', '==', dateTimestamp)
    );

    const availabilitySnapshot = await getDocs(availabilityQuery);

    if (!availabilitySnapshot.empty) {
      // Update existing record
      const availabilityDoc = availabilitySnapshot.docs[0];
      await updateDoc(availabilityDoc.ref, {
        isAvailable,
        price,
        minimumStay,
        blockedReason: isAvailable ? null : blockedReason,
        updatedAt: serverTimestamp()
      });
    } else {
      // Create new record
      const availabilityRef = doc(collection(db, 'roomAvailability'));
      await setDoc(availabilityRef, {
        roomId,
        date: dateTimestamp,
        isAvailable,
        price,
        minimumStay,
        blockedReason: isAvailable ? null : blockedReason,
        updatedAt: serverTimestamp()
      });
    }
  } catch (error) {
    console.error('Error updating room availability:', error);
    throw error;
  }
};

/**
 * Update room availability for a date range
 */
export const updateRoomAvailabilityRange = async (roomId: string, startDate: Date, endDate: Date, isAvailable: boolean, price?: number, minimumStay?: number, blockedReason?: string): Promise<void> => {
  try {
    // Create an array of dates in the range
    const dates: Date[] = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      dates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Update availability for each date
    for (const date of dates) {
      await updateRoomAvailability(roomId, date, isAvailable, price, minimumStay, blockedReason);
    }
  } catch (error) {
    console.error('Error updating room availability range:', error);
    throw error;
  }
};

/**
 * Get all room types for a hotel
 */
export const getRoomTypes = async (hotelId: string): Promise<RoomType[]> => {
  try {
    const roomTypesQuery = query(
      collection(db, 'roomTypes'),
      where('hotelId', '==', hotelId)
    );

    const roomTypesSnapshot = await getDocs(roomTypesQuery);
    const roomTypes: RoomType[] = [];

    roomTypesSnapshot.forEach((doc) => {
      roomTypes.push({ id: doc.id, ...doc.data() } as RoomType);
    });

    return roomTypes;
  } catch (error) {
    console.error('Error getting room types:', error);
    throw error;
  }
};

/**
 * Create a room type
 */
export const createRoomType = async (roomType: RoomType): Promise<string> => {
  try {
    const roomTypeRef = doc(collection(db, 'roomTypes'));

    await setDoc(roomTypeRef, {
      ...roomType,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return roomTypeRef.id;
  } catch (error) {
    console.error('Error creating room type:', error);
    throw error;
  }
};

/**
 * Update a room type
 */
export const updateRoomType = async (roomTypeId: string, roomType: Partial<RoomType>): Promise<boolean> => {
  try {
    const roomTypeRef = doc(db, 'roomTypes', roomTypeId);

    await updateDoc(roomTypeRef, {
      ...roomType,
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('Error updating room type:', error);
    throw error;
  }
};

/**
 * Delete a room type
 */
export const deleteRoomType = async (roomTypeId: string): Promise<boolean> => {
  try {
    await deleteDoc(doc(db, 'roomTypes', roomTypeId));
    return true;
  } catch (error) {
    console.error('Error deleting room type:', error);
    throw error;
  }
};
