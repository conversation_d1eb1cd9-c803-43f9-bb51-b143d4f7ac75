import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  Avatar,
  Divider,
  Card,
  CardContent,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  IconButton,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Save as SaveIcon,
  Edit as EditIcon,
  Person as PersonIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  History as HistoryIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Business as BusinessIcon,
  PhotoCamera as PhotoCameraIcon
} from '@mui/icons-material';

// TabPanel component for tab content
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const UserProfile: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [editMode, setEditMode] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  // User profile state
  const [userProfile, setUserProfile] = useState({
    firstName: 'Rajesh',
    lastName: 'Kumar',
    email: '<EMAIL>',
    phone: '+91 9876543210',
    role: 'Hotel Manager',
    hotelName: 'Grand Hotel',
    address: '123 Main Street, Mumbai, Maharashtra',
    bio: 'Experienced hotel manager with over 8 years in the luxury hospitality industry.',
    joinDate: '15/03/2022'
  });

  // Password change state
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Activity history
  const activityHistory = [
    { id: 1, action: 'Login', timestamp: '2023-06-15 09:30:45', details: 'Logged in from Mumbai, India' },
    { id: 2, action: 'Updated Room', timestamp: '2023-06-14 14:22:10', details: 'Updated Deluxe Room details' },
    { id: 3, action: 'Added Promotion', timestamp: '2023-06-12 16:45:22', details: 'Added Summer Special promotion' },
    { id: 4, action: 'Confirmed Booking', timestamp: '2023-06-10 11:15:32', details: 'Confirmed booking #BK12345' },
    { id: 5, action: 'Login', timestamp: '2023-06-10 09:05:18', details: 'Logged in from Mumbai, India' }
  ];

  // Notification preferences
  const [notificationPrefs, setNotificationPrefs] = useState({
    emailNotifications: true,
    smsNotifications: false,
    newBookingAlerts: true,
    bookingCancellationAlerts: true,
    paymentAlerts: true,
    systemUpdates: false
  });

  // Handle tab change
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle profile change
  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setUserProfile(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle password change
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle notification preference toggle
  const handleNotificationToggle = (name: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setNotificationPrefs(prev => ({
      ...prev,
      [name]: e.target.checked
    }));
  };

  // Handle save profile
  const handleSaveProfile = () => {
    // In a real app, this would save to Firebase or another backend
    setEditMode(false);
    setSnackbar({
      open: true,
      message: 'Profile updated successfully',
      severity: 'success'
    });
  };

  // Handle save password
  const handleSavePassword = () => {
    // Validate passwords
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setSnackbar({
        open: true,
        message: 'New passwords do not match',
        severity: 'error'
      });
      return;
    }

    // In a real app, this would update the password in Firebase Auth
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setSnackbar({
      open: true,
      message: 'Password updated successfully',
      severity: 'success'
    });
  };

  // Handle close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          My Profile
        </Typography>
        {tabValue === 0 && (
          <Button
            variant="contained"
            startIcon={editMode ? <SaveIcon /> : <EditIcon />}
            onClick={editMode ? handleSaveProfile : () => setEditMode(true)}
          >
            {editMode ? 'Save Profile' : 'Edit Profile'}
          </Button>
        )}
      </Box>

      <Grid container spacing={3}>
        {/* Profile Summary Card */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', p: 3 }}>
              <Box sx={{ position: 'relative' }}>
                <Avatar
                  src="/assets/avatar.jpg"
                  alt={`${userProfile.firstName} ${userProfile.lastName}`}
                  sx={{ width: 120, height: 120, mb: 2 }}
                />
                <IconButton
                  sx={{
                    position: 'absolute',
                    right: -10,
                    bottom: 10,
                    backgroundColor: 'primary.main',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: 'primary.dark',
                    },
                  }}
                  size="small"
                >
                  <PhotoCameraIcon fontSize="small" />
                </IconButton>
              </Box>
              <Typography variant="h5" gutterBottom>
                {userProfile.firstName} {userProfile.lastName}
              </Typography>
              <Chip label={userProfile.role} color="primary" sx={{ mb: 2 }} />
              <Divider sx={{ width: '100%', mb: 2 }} />
              <List sx={{ width: '100%' }}>
                <ListItem>
                  <ListItemIcon>
                    <EmailIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Email"
                    secondary={userProfile.email}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <PhoneIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Phone"
                    secondary={userProfile.phone}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <BusinessIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Hotel"
                    secondary={userProfile.hotelName}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <LocationIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Address"
                    secondary={userProfile.address}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Tabs and Content */}
        <Grid item xs={12} md={8}>
          <Paper>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant="scrollable"
              scrollButtons="auto"
            >
              <Tab icon={<PersonIcon />} label="Profile" />
              <Tab icon={<SecurityIcon />} label="Security" />
              <Tab icon={<NotificationsIcon />} label="Notifications" />
              <Tab icon={<HistoryIcon />} label="Activity" />
            </Tabs>

            {/* Profile Tab */}
            <TabPanel value={tabValue} index={0}>
              <Typography variant="h6" gutterBottom>
                Personal Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="First Name"
                    name="firstName"
                    value={userProfile.firstName}
                    onChange={handleProfileChange}
                    margin="normal"
                    disabled={!editMode}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Last Name"
                    name="lastName"
                    value={userProfile.lastName}
                    onChange={handleProfileChange}
                    margin="normal"
                    disabled={!editMode}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    name="email"
                    value={userProfile.email}
                    onChange={handleProfileChange}
                    margin="normal"
                    disabled={!editMode}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Phone"
                    name="phone"
                    value={userProfile.phone}
                    onChange={handleProfileChange}
                    margin="normal"
                    disabled={!editMode}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Hotel Name"
                    name="hotelName"
                    value={userProfile.hotelName}
                    onChange={handleProfileChange}
                    margin="normal"
                    disabled={!editMode}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Role"
                    name="role"
                    value={userProfile.role}
                    onChange={handleProfileChange}
                    margin="normal"
                    disabled={true} // Role cannot be changed by user
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Address"
                    name="address"
                    value={userProfile.address}
                    onChange={handleProfileChange}
                    margin="normal"
                    disabled={!editMode}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Bio"
                    name="bio"
                    value={userProfile.bio}
                    onChange={handleProfileChange}
                    margin="normal"
                    multiline
                    rows={4}
                    disabled={!editMode}
                  />
                </Grid>
              </Grid>
            </TabPanel>

            {/* Security Tab */}
            <TabPanel value={tabValue} index={1}>
              <Typography variant="h6" gutterBottom>
                Change Password
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Current Password"
                    name="currentPassword"
                    type="password"
                    value={passwordData.currentPassword}
                    onChange={handlePasswordChange}
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="New Password"
                    name="newPassword"
                    type="password"
                    value={passwordData.newPassword}
                    onChange={handlePasswordChange}
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Confirm New Password"
                    name="confirmPassword"
                    type="password"
                    value={passwordData.confirmPassword}
                    onChange={handlePasswordChange}
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleSavePassword}
                    disabled={!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}
                  >
                    Update Password
                  </Button>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Notifications Tab */}
            <TabPanel value={tabValue} index={2}>
              <Typography variant="h6" gutterBottom>
                Notification Preferences
              </Typography>
              <List>
                <ListItem>
                  <ListItemText
                    primary="Email Notifications"
                    secondary="Receive notifications via email"
                  />
                  <Button
                    variant={notificationPrefs.emailNotifications ? "contained" : "outlined"}
                    color="primary"
                    onClick={() => setNotificationPrefs(prev => ({ ...prev, emailNotifications: !prev.emailNotifications }))}
                  >
                    {notificationPrefs.emailNotifications ? "Enabled" : "Disabled"}
                  </Button>
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="SMS Notifications"
                    secondary="Receive notifications via SMS"
                  />
                  <Button
                    variant={notificationPrefs.smsNotifications ? "contained" : "outlined"}
                    color="primary"
                    onClick={() => setNotificationPrefs(prev => ({ ...prev, smsNotifications: !prev.smsNotifications }))}
                  >
                    {notificationPrefs.smsNotifications ? "Enabled" : "Disabled"}
                  </Button>
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="New Booking Alerts"
                    secondary="Receive alerts when new bookings are made"
                  />
                  <Button
                    variant={notificationPrefs.newBookingAlerts ? "contained" : "outlined"}
                    color="primary"
                    onClick={() => setNotificationPrefs(prev => ({ ...prev, newBookingAlerts: !prev.newBookingAlerts }))}
                  >
                    {notificationPrefs.newBookingAlerts ? "Enabled" : "Disabled"}
                  </Button>
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Booking Cancellation Alerts"
                    secondary="Receive alerts when bookings are cancelled"
                  />
                  <Button
                    variant={notificationPrefs.bookingCancellationAlerts ? "contained" : "outlined"}
                    color="primary"
                    onClick={() => setNotificationPrefs(prev => ({ ...prev, bookingCancellationAlerts: !prev.bookingCancellationAlerts }))}
                  >
                    {notificationPrefs.bookingCancellationAlerts ? "Enabled" : "Disabled"}
                  </Button>
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Payment Alerts"
                    secondary="Receive alerts about payments"
                  />
                  <Button
                    variant={notificationPrefs.paymentAlerts ? "contained" : "outlined"}
                    color="primary"
                    onClick={() => setNotificationPrefs(prev => ({ ...prev, paymentAlerts: !prev.paymentAlerts }))}
                  >
                    {notificationPrefs.paymentAlerts ? "Enabled" : "Disabled"}
                  </Button>
                </ListItem>
              </List>
            </TabPanel>

            {/* Activity Tab */}
            <TabPanel value={tabValue} index={3}>
              <Typography variant="h6" gutterBottom>
                Recent Activity
              </Typography>
              <List>
                {activityHistory.map((activity) => (
                  <ListItem key={activity.id} divider>
                    <ListItemText
                      primary={activity.action}
                      secondary={
                        <React.Fragment>
                          <Typography variant="body2" component="span" color="text.primary">
                            {activity.timestamp}
                          </Typography>
                          {" — "}{activity.details}
                        </React.Fragment>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </TabPanel>
          </Paper>
        </Grid>
      </Grid>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default UserProfile;
