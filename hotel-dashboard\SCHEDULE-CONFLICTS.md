# Schedule Conflict Detection

The hotel booking system includes a robust schedule conflict detection system that helps prevent scheduling issues and ensures proper staffing. This document explains how the conflict detection works and how to use it.

## Types of Conflicts

The system detects several types of scheduling conflicts:

### 1. Shift Overlap

Occurs when a staff member is scheduled for two shifts that overlap in time. This is a critical conflict that should be resolved to prevent double-booking staff.

**Example:** A staff member is scheduled for a morning shift (6:00 - 14:00) and also for a custom shift (12:00 - 16:00) on the same day.

### 2. Time Off Conflicts

Occurs when a staff member is scheduled for a shift during their approved time off. This is a critical conflict that should be resolved to respect staff time off.

**Example:** A staff member has approved vacation from June 1-5, but is scheduled for a shift on June 3.

### 3. Consecutive Shifts

Occurs when a staff member is scheduled for shifts with insufficient rest time between them. This is a warning that may lead to staff fatigue.

**Example:** A staff member finishes a night shift at 6:00 AM and is scheduled to start another shift at 12:00 PM, giving only 6 hours of rest (less than the minimum 8 hours).

### 4. Maximum Hours Exceeded

Occurs when a staff member is scheduled for more than the maximum allowed hours in a day or week. This is a warning that may lead to staff fatigue and potential labor law violations.

**Example:** A staff member is scheduled for 14 hours in a single day, exceeding the maximum of 12 hours.

## Conflict Severity

Conflicts are categorized by severity:

- **Error:** Critical conflicts that should be resolved (shift overlaps, time off conflicts)
- **Warning:** Non-critical issues that should be reviewed (consecutive shifts, maximum hours exceeded)

## How Conflict Detection Works

The conflict detection system works as follows:

1. When a shift is created or updated, the system checks for potential conflicts
2. If conflicts are detected, they are displayed to the user in a dialog
3. The user can choose to proceed despite the conflicts or cancel the operation
4. Conflicts are also displayed in the calendar view for reference

## Using Conflict Detection

### When Creating/Editing Shifts

When you create or edit a shift, the system automatically checks for conflicts. If conflicts are detected:

1. A dialog will appear showing the conflicts
2. You can review the conflicts and decide whether to proceed
3. For error-level conflicts, it's recommended to resolve them before proceeding
4. For warning-level conflicts, you may proceed if necessary but should be aware of the potential issues

### Viewing Conflicts

Conflicts are displayed in two ways:

1. **Conflict Dialog:** Appears when conflicts are detected during shift creation/editing
2. **Conflict Alert:** Displayed in the calendar view when conflicts exist in the schedule

## Conflict Resolution

To resolve conflicts, you can:

1. **Shift Overlap:** Adjust the start/end times of one of the shifts or assign to a different staff member
2. **Time Off Conflicts:** Reschedule the shift to a time when the staff member is available
3. **Consecutive Shifts:** Ensure sufficient rest time between shifts (at least 8 hours)
4. **Maximum Hours:** Reduce the number of hours scheduled for the staff member

## Configuration

The conflict detection system uses the following default settings:

- **Minimum Rest Hours:** 8 hours between shifts
- **Maximum Hours Per Day:** 12 hours
- **Maximum Hours Per Week:** 48 hours

These settings can be adjusted in the `scheduleConflictService.ts` file if needed.

## Benefits of Conflict Detection

The schedule conflict detection system provides several benefits:

1. **Prevents Double-Booking:** Ensures staff members are not scheduled for overlapping shifts
2. **Respects Time Off:** Prevents scheduling during approved time off periods
3. **Promotes Staff Well-Being:** Helps ensure adequate rest between shifts
4. **Ensures Compliance:** Helps maintain compliance with labor regulations
5. **Improves Scheduling Efficiency:** Identifies potential issues before they become problems

By using the conflict detection system, you can create more efficient and fair schedules for your staff while ensuring proper coverage for all hotel services.
