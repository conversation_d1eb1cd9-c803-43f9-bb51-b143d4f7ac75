import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

/// Direct implementation of the Sandbox API for Aadhaar verification
class DirectSandboxApi {
  // API credentials
  static const String apiKey = 'key_live_7G8Je89NyU85Fq5bzCq8cUMeYeu8yyy5';
  static const String apiSecret =
      'secret_live_Otu3vrajbOL3IVPS3g7l6y0XFzU6sChB';

  // API endpoints
  static const String baseUrl = 'https://api.sandbox.co.in';
  static const String authEndpoint = '/authenticate';
  static const String generateOtpEndpoint = '/kyc/aadhaar/okyc/otp';
  static const String verifyOtpEndpoint = '/kyc/aadhaar/okyc/otp/verify';

  // API version
  static const String apiVersion = '2.0';

  // Enable mock mode for testing
  static const bool _useMockMode = true;

  // HTTP client
  final http.Client _client = http.Client();

  /// Authenticate with the Sandbox API
  Future<String?> authenticate() async {
    try {
      final uri = Uri.parse('$baseUrl$authEndpoint');
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-api-key': apiKey,
        'x-api-secret': apiSecret,
        'x-api-version': apiVersion,
      };

      debugPrint('Authenticating with Sandbox API...');
      debugPrint('URL: $uri');

      final response = await _client
          .post(
            uri,
            headers: headers,
          )
          .timeout(const Duration(seconds: 30));

      debugPrint('Authentication response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData.containsKey('access_token')) {
          final accessToken = responseData['access_token'];
          debugPrint('Authentication successful');
          return accessToken;
        }
      }

      debugPrint('Authentication failed: ${response.body}');
      return null;
    } catch (e) {
      debugPrint('Error during authentication: $e');
      return null;
    }
  }

  /// Generate OTP for Aadhaar verification
  Future<Map<String, dynamic>> generateOtp(
      String aadhaarNumber, String accessToken) async {
    try {
      // Validate Aadhaar number format
      if (aadhaarNumber.length != 12 || int.tryParse(aadhaarNumber) == null) {
        return {
          'success': false,
          'error': 'Invalid Aadhaar number format. It should be 12 digits.',
        };
      }

      // Use mock mode for testing
      if (_useMockMode) {
        debugPrint(
            'MOCK MODE: Generating OTP for Aadhaar number: $aadhaarNumber');

        // Simulate API delay
        await Future.delayed(const Duration(seconds: 1));

        // Generate a mock reference ID
        final mockReferenceId =
            'mock-ref-${DateTime.now().millisecondsSinceEpoch}';

        debugPrint('MOCK MODE: Generated reference ID: $mockReferenceId');
        debugPrint(
            'MOCK MODE: OTP sent successfully to the registered mobile number');
        debugPrint('MOCK MODE: For testing, use any 6-digit OTP like 123456');

        return {
          'success': true,
          'reference_id': mockReferenceId,
          'message':
              'OTP sent successfully to your registered mobile number (MOCK MODE)',
        };
      }

      final uri = Uri.parse('$baseUrl$generateOtpEndpoint');
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-api-key': apiKey,
        'x-api-version': apiVersion,
        'Authorization': accessToken,
      };

      // Try a different request format based on API documentation
      final body = jsonEncode({
        'data': {'aadhaar_number': aadhaarNumber},
        'consent': true,
        'consent_text':
            'I hereby declare my consent for fetching my Aadhaar details using Aadhaar Number/Virtual ID for the purpose of KYC verification.'
      });

      debugPrint('Generating OTP for Aadhaar: $aadhaarNumber');
      debugPrint('URL: $uri');
      debugPrint('Request headers: $headers');
      debugPrint('Request body: $body');

      final response = await _client
          .post(
            uri,
            headers: headers,
            body: body,
          )
          .timeout(const Duration(seconds: 30));

      debugPrint('Generate OTP response status: ${response.statusCode}');
      debugPrint('Generate OTP response body: ${response.body}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        if (responseData.containsKey('reference_id')) {
          return {
            'success': true,
            'reference_id': responseData['reference_id'],
            'message': responseData['message'] ?? 'OTP sent successfully',
          };
        } else {
          return {
            'success': false,
            'error': responseData['message'] ?? 'Failed to generate OTP',
          };
        }
      } else {
        return {
          'success': false,
          'error': responseData['message'] ??
              'Failed to generate OTP. Status code: ${response.statusCode}',
        };
      }
    } catch (e) {
      debugPrint('Error generating OTP: $e');
      return {
        'success': false,
        'error': 'Error: ${e.toString()}',
      };
    }
  }

  /// Verify OTP for Aadhaar verification
  Future<Map<String, dynamic>> verifyOtp(
      String referenceId, String otp, String accessToken) async {
    try {
      // Validate OTP format
      if (otp.length != 6 || int.tryParse(otp) == null) {
        return {
          'success': false,
          'error': 'Invalid OTP format. It should be 6 digits.',
        };
      }

      // Use mock mode for testing
      if (_useMockMode) {
        debugPrint('MOCK MODE: Verifying OTP for reference ID: $referenceId');
        debugPrint('MOCK MODE: OTP entered: $otp');

        // Simulate API delay
        await Future.delayed(const Duration(seconds: 1));

        debugPrint('MOCK MODE: OTP verification successful');

        // Return mock user data
        return {
          'success': true,
          'data': {
            'status': 'valid',
            'message': 'Aadhaar Card Exists (MOCK)',
            'care_of': 'S/O: John Doe',
            'dob': '1990',
            'full_address': 'Main Street, Bangalore, Karnataka',
            'gender': 'M',
            'name': 'John Doe',
            'year_of_birth': '1990',
            'photo': 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...'
          }
        };
      }

      final uri = Uri.parse('$baseUrl$verifyOtpEndpoint');
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-api-key': apiKey,
        'x-api-version': apiVersion,
        'Authorization': accessToken,
      };

      // Try a different request format based on API documentation
      final body = jsonEncode({
        'data': {'reference_id': referenceId, 'otp': otp},
        'consent': true,
        'consent_text':
            'I hereby declare my consent for fetching my Aadhaar details using Aadhaar Number/Virtual ID for the purpose of KYC verification.'
      });

      debugPrint('Verifying OTP for reference ID: $referenceId');
      debugPrint('URL: $uri');
      debugPrint('Request headers: $headers');
      debugPrint('Request body: $body');

      final response = await _client
          .post(
            uri,
            headers: headers,
            body: body,
          )
          .timeout(const Duration(seconds: 30));

      debugPrint('Verify OTP response status: ${response.statusCode}');
      debugPrint('Verify OTP response body: ${response.body}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        if (responseData['status']?.toLowerCase() == 'valid') {
          return {
            'success': true,
            'data': responseData,
          };
        } else {
          return {
            'success': false,
            'error': responseData['message'] ?? 'Failed to verify OTP',
          };
        }
      } else {
        return {
          'success': false,
          'error': responseData['message'] ??
              'Failed to verify OTP. Status code: ${response.statusCode}',
        };
      }
    } catch (e) {
      debugPrint('Error verifying OTP: $e');
      return {
        'success': false,
        'error': 'Error: ${e.toString()}',
      };
    }
  }

  /// Close the HTTP client
  void dispose() {
    _client.close();
  }
}
