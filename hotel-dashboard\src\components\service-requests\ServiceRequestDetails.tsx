import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  AccessTime as TimeIcon,
  Room as RoomIcon,
  Person as PersonIcon,
  Notes as NotesIcon,
  Flag as PriorityIcon,
  CheckCircle as CompletedIcon,
  CleaningServices as CleaningIcon,
  Restaurant as FoodIcon,
  Build as MaintenanceIcon,
  AttachMoney as MoneyIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { 
  ServiceRequest, 
  CleaningRequest, 
  FoodOrder, 
  MaintenanceRequest 
} from '../../services/serviceRequestService';

interface ServiceRequestDetailsProps {
  request: ServiceRequest;
}

const ServiceRequestDetails: React.FC<ServiceRequestDetailsProps> = ({ request }) => {
  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return format(date, 'MMM d, yyyy h:mm a');
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'pending':
        return <Chip label="Pending" color="warning" />;
      case 'in_progress':
        return <Chip label="In Progress" color="info" />;
      case 'completed':
        return <Chip label="Completed" color="success" />;
      case 'cancelled':
        return <Chip label="Cancelled" color="error" />;
      default:
        return <Chip label={status} />;
    }
  };

  const getPriorityChip = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Chip label="High" color="error" />;
      case 'medium':
        return <Chip label="Medium" color="warning" />;
      case 'low':
        return <Chip label="Low" color="info" />;
      default:
        return <Chip label={priority} />;
    }
  };

  const getServiceTypeIcon = (type: string) => {
    switch (type) {
      case 'cleaning':
        return <CleaningIcon fontSize="large" color="primary" />;
      case 'food':
        return <FoodIcon fontSize="large" color="primary" />;
      case 'maintenance':
        return <MaintenanceIcon fontSize="large" color="primary" />;
      default:
        return null;
    }
  };

  const renderCleaningDetails = (cleaningRequest: CleaningRequest) => {
    return (
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Cleaning Details
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2">Cleaning Type:</Typography>
              <Typography variant="body1">
                {cleaningRequest.cleaningType ? formatCleaningType(cleaningRequest.cleaningType) : 'Regular Cleaning'}
              </Typography>
            </Grid>
            {cleaningRequest.items && cleaningRequest.items.length > 0 && (
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Items to Clean:</Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                  {cleaningRequest.items.map((item, index) => (
                    <Chip key={index} label={item} size="small" />
                  ))}
                </Box>
              </Grid>
            )}
          </Grid>
        </CardContent>
      </Card>
    );
  };

  const renderFoodDetails = (foodOrder: FoodOrder) => {
    return (
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Food Order Details
          </Typography>
          <Grid container spacing={2}>
            {foodOrder.deliveryTime && (
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Delivery Time:</Typography>
                <Typography variant="body1">
                  {formatDate(foodOrder.deliveryTime)}
                </Typography>
              </Grid>
            )}
            {foodOrder.paymentMethod && (
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Payment Method:</Typography>
                <Typography variant="body1">
                  {foodOrder.paymentMethod}
                </Typography>
              </Grid>
            )}
          </Grid>
          
          <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
            Order Items:
          </Typography>
          <TableContainer component={Paper} variant="outlined">
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Item</TableCell>
                  <TableCell align="right">Quantity</TableCell>
                  <TableCell align="right">Price</TableCell>
                  <TableCell align="right">Total</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {foodOrder.items.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      {item.name}
                      {item.specialInstructions && (
                        <Typography variant="caption" display="block" color="text.secondary">
                          Note: {item.specialInstructions}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell align="right">{item.quantity}</TableCell>
                    <TableCell align="right">${item.price.toFixed(2)}</TableCell>
                    <TableCell align="right">${(item.quantity * item.price).toFixed(2)}</TableCell>
                  </TableRow>
                ))}
                <TableRow>
                  <TableCell colSpan={3} align="right" sx={{ fontWeight: 'bold' }}>
                    Total:
                  </TableCell>
                  <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                    ${foodOrder.totalAmount.toFixed(2)}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    );
  };

  const renderMaintenanceDetails = (maintenanceRequest: MaintenanceRequest) => {
    return (
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Maintenance Details
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2">Issue Type:</Typography>
              <Typography variant="body1">
                {formatIssueType(maintenanceRequest.issueType)}
              </Typography>
            </Grid>
            {maintenanceRequest.estimatedCompletionTime && (
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Estimated Completion:</Typography>
                <Typography variant="body1">
                  {formatDate(maintenanceRequest.estimatedCompletionTime)}
                </Typography>
              </Grid>
            )}
            <Grid item xs={12}>
              <Typography variant="subtitle2">Description:</Typography>
              <Typography variant="body1">
                {maintenanceRequest.description}
              </Typography>
            </Grid>
            {maintenanceRequest.partsRequired && maintenanceRequest.partsRequired.length > 0 && (
              <Grid item xs={12}>
                <Typography variant="subtitle2">Parts Required:</Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                  {maintenanceRequest.partsRequired.map((part, index) => (
                    <Chip key={index} label={part} size="small" />
                  ))}
                </Box>
              </Grid>
            )}
          </Grid>
        </CardContent>
      </Card>
    );
  };

  const formatCleaningType = (type: string) => {
    switch (type) {
      case 'regular':
        return 'Regular Cleaning';
      case 'deep':
        return 'Deep Cleaning';
      case 'turndown':
        return 'Turndown Service';
      case 'special':
        return 'Special Request';
      default:
        return type;
    }
  };

  const formatIssueType = (type: string) => {
    switch (type) {
      case 'plumbing':
        return 'Plumbing';
      case 'electrical':
        return 'Electrical';
      case 'hvac':
        return 'HVAC/Climate Control';
      case 'furniture':
        return 'Furniture';
      case 'appliance':
        return 'Appliance';
      case 'structural':
        return 'Structural';
      case 'other':
        return 'Other';
      default:
        return type;
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        {getServiceTypeIcon(request.type)}
        <Box sx={{ ml: 2 }}>
          <Typography variant="h5">
            {request.type.charAt(0).toUpperCase() + request.type.slice(1)} Request
          </Typography>
          <Typography variant="body2" color="text.secondary">
            ID: {request.id}
          </Typography>
        </Box>
        <Box sx={{ ml: 'auto' }}>
          {getStatusChip(request.status)}
        </Box>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Request Information
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <RoomIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Room Number" 
                    secondary={request.roomNumber} 
                  />
                </ListItem>
                {request.guestName && (
                  <ListItem>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Guest Name" 
                      secondary={request.guestName} 
                    />
                  </ListItem>
                )}
                <ListItem>
                  <ListItemIcon>
                    <TimeIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Request Time" 
                    secondary={formatDate(request.requestTime)} 
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <PriorityIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Priority" 
                    secondary={getPriorityChip(request.priority)} 
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Status Information
              </Typography>
              <List dense>
                {request.assignedTo && (
                  <ListItem>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Assigned To" 
                      secondary={request.assignedTo} 
                    />
                  </ListItem>
                )}
                {request.completedTime && (
                  <ListItem>
                    <ListItemIcon>
                      <CompletedIcon />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Completed Time" 
                      secondary={formatDate(request.completedTime)} 
                    />
                  </ListItem>
                )}
                <ListItem>
                  <ListItemIcon>
                    <TimeIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Created At" 
                    secondary={formatDate(request.createdAt)} 
                  />
                </ListItem>
                {request.updatedAt && (
                  <ListItem>
                    <ListItemIcon>
                      <TimeIcon />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Last Updated" 
                      secondary={formatDate(request.updatedAt)} 
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {request.notes && (
        <Card sx={{ mt: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <NotesIcon sx={{ mr: 1 }} />
              <Typography variant="h6">
                Notes
              </Typography>
            </Box>
            <Typography variant="body1">
              {request.notes}
            </Typography>
          </CardContent>
        </Card>
      )}

      {/* Render type-specific details */}
      {request.type === 'cleaning' && renderCleaningDetails(request as CleaningRequest)}
      {request.type === 'food' && renderFoodDetails(request as FoodOrder)}
      {request.type === 'maintenance' && renderMaintenanceDetails(request as MaintenanceRequest)}
    </Box>
  );
};

export default ServiceRequestDetails;
