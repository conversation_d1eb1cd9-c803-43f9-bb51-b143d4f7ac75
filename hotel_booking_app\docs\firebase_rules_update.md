# Firebase Security Rules Update

To fix the permission issues with the Aadhaar verification functionality, you need to update your Firebase security rules. Follow these steps:

## Step 1: Go to Firebase Console

1. Open your web browser and go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project

## Step 2: Navigate to Firestore Rules

1. In the left sidebar, click on "Firestore Database"
2. Click on the "Rules" tab

## Step 3: Update the Rules

Replace your current rules with the following:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Base rules
    match /{document=**} {
      allow read, write: if false; // Default deny all
    }
    
    // User profiles
    match /users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Aadhaar verifications - More permissive for testing
    match /aadhaar_verifications/{verificationId} {
      // Allow authenticated users to read/write
      allow read, write: if request.auth != null;
    }
    
    // Hotels
    match /hotels/{hotelId} {
      allow read: if true; // Public read access
      allow write: if request.auth != null;
    }
    
    // Bookings
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null;
    }
    
    // Service requests
    match /service_requests/{requestId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## Step 4: Publish the Rules

1. Click the "Publish" button to apply the new rules

## Step 5: Verify the Rules

1. Go back to your app and try the Aadhaar verification process again
2. The permission errors should now be resolved

## Important Notes

- These rules are intentionally permissive for testing purposes
- In a production environment, you should implement more restrictive rules
- The current rules allow any authenticated user to read and write to the Aadhaar verification collection
- For production, you should restrict access based on user roles and ownership

## Future Improvements

For a production environment, consider implementing more restrictive rules like:

```javascript
// Aadhaar verifications - Production version
match /aadhaar_verifications/{verificationId} {
  // Allow users to read their own verification
  allow read: if request.auth != null && 
               (resource.data.userId == request.auth.uid || isAdmin());
  
  // Allow users to create/update their own verification
  allow create: if request.auth != null;
  allow update: if request.auth != null && 
                 (resource.data.userId == request.auth.uid || isAdmin());
  
  // Only admins can delete verifications
  allow delete: if isAdmin();
}

// Helper function to check if user is admin
function isAdmin() {
  return request.auth != null && 
         exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
}
```

These more restrictive rules would ensure that users can only access their own verification data, while administrators can access all verification data.
