import 'package:flutter/material.dart';

enum AmenityCategory {
  essential,
  roomFeature,
  bathroom,
  food,
  entertainment,
  service,
  accessibility,
  safety,
  outdoor,
  view,
  transport,
  business,
  wellness,
  family,
  sustainability,
  other
}

class Amenity {
  final String id;
  final String name;
  final String description;
  final AmenityCategory category;
  final IconData icon;
  final bool isPremium;
  final bool isHighlighted;

  Amenity({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.icon,
    this.isPremium = false,
    this.isHighlighted = false,
  });

  factory Amenity.fromString(String amenityString) {
    // Default values
    String id = amenityString.toLowerCase().replaceAll(' ', '_');
    String name = amenityString;
    String description = '';
    AmenityCategory category = AmenityCategory.other;
    IconData icon = Icons.check_circle_outline;
    bool isPremium = false;
    bool isHighlighted = false;

    // Map common amenities to their details
    switch (amenityString.toLowerCase()) {
      // Essential amenities
      case 'wifi':
      case 'free wifi':
        id = 'wifi';
        name = 'WiFi';
        description = 'Free WiFi available throughout the property';
        category = AmenityCategory.essential;
        icon = Icons.wifi;
        break;
      case 'air conditioning':
      case 'ac':
        id = 'air_conditioning';
        name = 'Air Conditioning';
        description = 'Climate control available';
        category = AmenityCategory.essential;
        icon = Icons.ac_unit;
        break;
      case 'heating':
        id = 'heating';
        name = 'Heating';
        description = 'Heating available';
        category = AmenityCategory.essential;
        icon = Icons.whatshot;
        break;
      case 'tv':
      case 'television':
        id = 'tv';
        name = 'TV';
        description = 'Television with cable/satellite channels';
        category = AmenityCategory.entertainment;
        icon = Icons.tv;
        break;

      // Room features
      case 'balcony':
        id = 'balcony';
        name = 'Balcony';
        description = 'Private balcony';
        category = AmenityCategory.roomFeature;
        icon = Icons.balcony;
        break;
      case 'kitchen':
        id = 'kitchen';
        name = 'Kitchen';
        description = 'Fully equipped kitchen';
        category = AmenityCategory.roomFeature;
        icon = Icons.kitchen;
        break;
      case 'refrigerator':
      case 'fridge':
        id = 'refrigerator';
        name = 'Refrigerator';
        description = 'In-room refrigerator';
        category = AmenityCategory.roomFeature;
        icon = Icons.kitchen;
        break;
      case 'minibar':
        id = 'minibar';
        name = 'Minibar';
        description = 'In-room minibar with beverages and snacks';
        category = AmenityCategory.roomFeature;
        icon = Icons.local_bar;
        isPremium = true;
        break;
      case 'coffee maker':
        id = 'coffee_maker';
        name = 'Coffee Maker';
        description = 'In-room coffee maker';
        category = AmenityCategory.roomFeature;
        icon = Icons.coffee;
        break;

      // Bathroom amenities
      case 'private bathroom':
        id = 'private_bathroom';
        name = 'Private Bathroom';
        description = 'En-suite bathroom';
        category = AmenityCategory.bathroom;
        icon = Icons.bathroom;
        break;
      case 'bathtub':
        id = 'bathtub';
        name = 'Bathtub';
        description = 'Private bathtub';
        category = AmenityCategory.bathroom;
        icon = Icons.bathtub;
        break;
      case 'shower':
        id = 'shower';
        name = 'Shower';
        description = 'Private shower';
        category = AmenityCategory.bathroom;
        icon = Icons.shower;
        break;
      case 'toiletries':
        id = 'toiletries';
        name = 'Toiletries';
        description = 'Complimentary toiletries';
        category = AmenityCategory.bathroom;
        icon = Icons.soap;
        break;
      case 'hairdryer':
        id = 'hairdryer';
        name = 'Hair Dryer';
        description = 'Hair dryer in bathroom';
        category = AmenityCategory.bathroom;
        icon = Icons.dry;
        break;

      // Food and dining
      case 'breakfast':
      case 'free breakfast':
        id = 'breakfast';
        name = 'Breakfast';
        description = 'Complimentary breakfast';
        category = AmenityCategory.food;
        icon = Icons.free_breakfast;
        isHighlighted = true;
        break;
      case 'restaurant':
        id = 'restaurant';
        name = 'Restaurant';
        description = 'On-site restaurant';
        category = AmenityCategory.food;
        icon = Icons.restaurant;
        break;
      case 'bar':
        id = 'bar';
        name = 'Bar';
        description = 'On-site bar/lounge';
        category = AmenityCategory.food;
        icon = Icons.local_bar;
        break;
      case 'room service':
        id = 'room_service';
        name = 'Room Service';
        description = 'Food and beverage delivered to your room';
        category = AmenityCategory.food;
        icon = Icons.room_service;
        isPremium = true;
        break;

      // Entertainment and recreation
      case 'pool':
      case 'swimming pool':
        id = 'pool';
        name = 'Swimming Pool';
        description = 'Swimming pool available';
        category = AmenityCategory.outdoor;
        icon = Icons.pool;
        isHighlighted = true;
        break;
      case 'gym':
      case 'fitness center':
        id = 'gym';
        name = 'Fitness Center';
        description = 'On-site fitness center/gym';
        category = AmenityCategory.wellness;
        icon = Icons.fitness_center;
        break;
      case 'spa':
        id = 'spa';
        name = 'Spa';
        description = 'On-site spa services';
        category = AmenityCategory.wellness;
        icon = Icons.spa;
        isPremium = true;
        break;
      case 'sauna':
        id = 'sauna';
        name = 'Sauna';
        description = 'Sauna facilities available';
        category = AmenityCategory.wellness;
        icon = Icons.hot_tub;
        isPremium = true;
        break;

      // Services
      case 'parking':
      case 'free parking':
        id = 'parking';
        name = 'Parking';
        description = 'On-site parking available';
        category = AmenityCategory.service;
        icon = Icons.local_parking;
        break;
      case 'airport shuttle':
        id = 'airport_shuttle';
        name = 'Airport Shuttle';
        description = 'Transportation to/from airport';
        category = AmenityCategory.transport;
        icon = Icons.airport_shuttle;
        isPremium = true;
        break;
      case '24-hour front desk':
      case '24 hour reception':
        id = '24h_reception';
        name = '24-Hour Reception';
        description = 'Front desk available 24/7';
        category = AmenityCategory.service;
        icon = Icons.access_time;
        break;
      case 'concierge':
        id = 'concierge';
        name = 'Concierge';
        description = 'Concierge service available';
        category = AmenityCategory.service;
        icon = Icons.support_agent;
        isPremium = true;
        break;
      case 'laundry':
        id = 'laundry';
        name = 'Laundry';
        description = 'Laundry service available';
        category = AmenityCategory.service;
        icon = Icons.local_laundry_service;
        break;

      // Business amenities
      case 'business center':
        id = 'business_center';
        name = 'Business Center';
        description = 'Business facilities available';
        category = AmenityCategory.business;
        icon = Icons.business_center;
        break;
      case 'meeting rooms':
        id = 'meeting_rooms';
        name = 'Meeting Rooms';
        description = 'Conference/meeting rooms available';
        category = AmenityCategory.business;
        icon = Icons.meeting_room;
        break;

      // Accessibility
      case 'wheelchair accessible':
        id = 'wheelchair_accessible';
        name = 'Wheelchair Accessible';
        description = 'Accessible for guests with mobility concerns';
        category = AmenityCategory.accessibility;
        icon = Icons.accessible;
        break;
      case 'elevator':
      case 'lift':
        id = 'elevator';
        name = 'Elevator';
        description = 'Elevator/lift available';
        category = AmenityCategory.accessibility;
        icon = Icons.elevator;
        break;

      // Default case for unknown amenities
      default:
        id = amenityString.toLowerCase().replaceAll(' ', '_');
        name = amenityString;
        description = amenityString;
        category = AmenityCategory.other;
        icon = Icons.check_circle_outline;
    }

    return Amenity(
      id: id,
      name: name,
      description: description,
      category: category,
      icon: icon,
      isPremium: isPremium,
      isHighlighted: isHighlighted,
    );
  }

  static String categoryToString(AmenityCategory category) {
    switch (category) {
      case AmenityCategory.essential:
        return 'Essential';
      case AmenityCategory.roomFeature:
        return 'Room Features';
      case AmenityCategory.bathroom:
        return 'Bathroom';
      case AmenityCategory.food:
        return 'Food & Dining';
      case AmenityCategory.entertainment:
        return 'Entertainment';
      case AmenityCategory.service:
        return 'Services';
      case AmenityCategory.accessibility:
        return 'Accessibility';
      case AmenityCategory.safety:
        return 'Safety & Security';
      case AmenityCategory.outdoor:
        return 'Outdoor';
      case AmenityCategory.view:
        return 'View';
      case AmenityCategory.transport:
        return 'Transportation';
      case AmenityCategory.business:
        return 'Business';
      case AmenityCategory.wellness:
        return 'Wellness';
      case AmenityCategory.family:
        return 'Family';
      case AmenityCategory.sustainability:
        return 'Sustainability';
      case AmenityCategory.other:
        return 'Other';
    }
  }
}
