import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Button,
  ButtonGroup,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Stack,
  IconButton,
  Tooltip,
  useTheme
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AttachMoney as MoneyIcon,
  Hotel as HotelIcon,
  Person as PersonIcon,
  BookOnline as BookingIcon,
  Bed as BedIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { auth } from '../../firebase/config';
import { getVendorAnalytics, AnalyticsData } from '../../services/analyticsService';
import { getHotelsForVendor, HotelData } from '../../firebase/hotelService';
import MobileAppBookings from '../../components/MobileAppBookingsSimple';
import MobileAppReviews from '../../components/MobileAppReviews';
import VerificationStats from '../../components/VerificationStats';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
  BarChart,
  Bar
} from 'recharts';

const Dashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [hotels, setHotels] = useState<HotelData[]>([]);
  const [period, setPeriod] = useState<'day' | 'week' | 'month' | 'year'>('month');
  const [recentBookings, setRecentBookings] = useState<any[]>([]);
  const [pendingTasks, setPendingTasks] = useState<any[]>([]);

  // Load data on component mount
  useEffect(() => {
    fetchData();
  }, [period]);

  const fetchData = async () => {
    try {
      setLoading(true);
      if (!auth.currentUser) return;

      // Fetch analytics data
      const analyticsData = await getVendorAnalytics(auth.currentUser.uid, period);
      setAnalytics(analyticsData);

      // Fetch hotels
      const hotelsList = await getHotelsForVendor(auth.currentUser.uid);
      setHotels(hotelsList);

      // Fetch recent bookings from Firebase
      // TODO: Implement real booking fetching from Firebase
      setRecentBookings([]);

      // Fetch pending tasks (placeholder data for now)
      setPendingTasks([
        { id: 1, type: 'booking', title: 'New booking request', description: 'Booking #1234 needs approval', priority: 'high' },
        { id: 2, type: 'maintenance', title: 'Room maintenance', description: 'Room 101 needs maintenance', priority: 'medium' },
        { id: 3, type: 'review', title: 'New review', description: 'New 3-star review for Grand Hotel', priority: 'low' },
      ]);

      setLoading(false);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchData();
  };

  const handlePeriodChange = (newPeriod: 'day' | 'week' | 'month' | 'year') => {
    setPeriod(newPeriod);
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Calculate percentage change
  const calculateChange = (current: number, previous: number) => {
    if (previous === 0) return 100;
    return ((current - previous) / previous) * 100;
  };

  // Colors for charts
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
    theme.palette.info.main,
  ];

  // Prepare data for pie chart
  const prepareSourceData = () => {
    if (!analytics || !analytics.bookingsBySource) return [];

    return Object.entries(analytics.bookingsBySource).map(([source, count], index) => ({
      name: source,
      value: count,
      color: COLORS[index % COLORS.length],
    }));
  };

  // Prepare data for room type revenue chart
  const prepareRoomTypeData = () => {
    if (!analytics || !analytics.revenueByRoomType) return [];

    return Object.entries(analytics.revenueByRoomType).map(([type, revenue], index) => ({
      name: type,
      revenue,
      color: COLORS[index % COLORS.length],
    }));
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Dashboard
        </Typography>
        <Box>
          <ButtonGroup variant="outlined" size="small" sx={{ mr: 2 }}>
            <Button
              onClick={() => handlePeriodChange('day')}
              variant={period === 'day' ? 'contained' : 'outlined'}
            >
              Day
            </Button>
            <Button
              onClick={() => handlePeriodChange('week')}
              variant={period === 'week' ? 'contained' : 'outlined'}
            >
              Week
            </Button>
            <Button
              onClick={() => handlePeriodChange('month')}
              variant={period === 'month' ? 'contained' : 'outlined'}
            >
              Month
            </Button>
            <Button
              onClick={() => handlePeriodChange('year')}
              variant={period === 'year' ? 'contained' : 'outlined'}
            >
              Year
            </Button>
          </ButtonGroup>
          <Button
            startIcon={<RefreshIcon />}
            variant="outlined"
            onClick={handleRefresh}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          {/* Stats Cards */}
          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={2}
              sx={{
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                height: 140,
                bgcolor: 'primary.light',
                color: 'primary.contrastText',
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="h6" component="div">
                  Revenue
                </Typography>
                <MoneyIcon />
              </Box>
              <Typography variant="h3" component="div" sx={{ mt: 2 }}>
                {analytics ? formatCurrency(analytics.totalRevenue) : '$0'}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {analytics && analytics.totalRevenue > 0 ? (
                  <>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                      <TrendingUpIcon fontSize="small" sx={{ mr: 0.5 }} />
                      +18% this {period}
                    </Typography>
                  </>
                ) : (
                  <Typography variant="body2">No data for this period</Typography>
                )}
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={2}
              sx={{
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                height: 140,
                bgcolor: 'success.light',
                color: 'success.contrastText',
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="h6" component="div">
                  Bookings
                </Typography>
                <BookingIcon />
              </Box>
              <Typography variant="h3" component="div" sx={{ mt: 2 }}>
                {analytics ? analytics.totalBookings : 0}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {analytics && analytics.totalBookings > 0 ? (
                  <>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                      <TrendingUpIcon fontSize="small" sx={{ mr: 0.5 }} />
                      +12% this {period}
                    </Typography>
                  </>
                ) : (
                  <Typography variant="body2">No data for this period</Typography>
                )}
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={2}
              sx={{
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                height: 140,
                bgcolor: 'warning.light',
                color: 'warning.contrastText',
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="h6" component="div">
                  Occupancy
                </Typography>
                <BedIcon />
              </Box>
              <Typography variant="h3" component="div" sx={{ mt: 2 }}>
                {analytics ? `${Math.round(analytics.occupancyRate)}%` : '0%'}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {analytics && analytics.occupancyRate > 0 ? (
                  <>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                      <TrendingUpIcon fontSize="small" sx={{ mr: 0.5 }} />
                      +5% this {period}
                    </Typography>
                  </>
                ) : (
                  <Typography variant="body2">No data for this period</Typography>
                )}
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={2}
              sx={{
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                height: 140,
                bgcolor: 'info.light',
                color: 'info.contrastText',
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="h6" component="div">
                  ADR
                </Typography>
                <HotelIcon />
              </Box>
              <Typography variant="h3" component="div" sx={{ mt: 2 }}>
                {analytics ? formatCurrency(analytics.averageDailyRate) : '$0'}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {analytics && analytics.averageDailyRate > 0 ? (
                  <>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                      <TrendingUpIcon fontSize="small" sx={{ mr: 0.5 }} />
                      +3% this {period}
                    </Typography>
                  </>
                ) : (
                  <Typography variant="body2">No data for this period</Typography>
                )}
              </Box>
            </Paper>
          </Grid>

          {/* Revenue Chart */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardHeader
                title="Revenue Trends"
                action={
                  <Tooltip title="View detailed reports">
                    <IconButton onClick={() => navigate('/vendor/reports')}>
                      <VisibilityIcon />
                    </IconButton>
                  </Tooltip>
                }
              />
              <Divider />
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart
                    data={analytics?.monthlyRevenue || []}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <RechartsTooltip
                      formatter={(value: any) => formatCurrency(value)}
                      labelFormatter={(label) => {
                        const [year, month] = label.split('-');
                        return `${new Date(parseInt(year), parseInt(month) - 1).toLocaleString('default', { month: 'long' })} ${year}`;
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="revenue"
                      stroke={theme.palette.primary.main}
                      activeDot={{ r: 8 }}
                      name="Revenue"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Booking Sources */}
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '100%' }}>
              <CardHeader title="Booking Sources" />
              <Divider />
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={prepareSourceData()}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      nameKey="name"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {prepareSourceData().map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Room Type Revenue */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Revenue by Room Type" />
              <Divider />
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={prepareRoomTypeData()}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <RechartsTooltip formatter={(value: any) => formatCurrency(value)} />
                    <Bar dataKey="revenue" name="Revenue">
                      {prepareRoomTypeData().map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Aadhaar Verification Stats */}
          <Grid item xs={12} md={6}>
            <VerificationStats />
          </Grid>

          {/* Recent Bookings */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: '100%' }}>
              <CardHeader
                title="Recent Bookings"
                action={
                  <Button
                    size="small"
                    onClick={() => navigate('/vendor/bookings')}
                  >
                    View All
                  </Button>
                }
              />
              <Divider />
              <List sx={{ p: 0 }}>
                {recentBookings.length === 0 ? (
                  <ListItem>
                    <ListItemText primary="No recent bookings" />
                  </ListItem>
                ) : (
                  recentBookings.map((booking) => (
                    <React.Fragment key={booking.id}>
                      <ListItem alignItems="flex-start">
                        <ListItemAvatar>
                          <Avatar>
                            {booking.guest.charAt(0)}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={booking.guest}
                          secondary={
                            <React.Fragment>
                              <Typography
                                sx={{ display: 'inline' }}
                                component="span"
                                variant="body2"
                                color="text.primary"
                              >
                                {booking.hotel}
                              </Typography>
                              {` — ${booking.checkIn} to ${booking.checkOut}`}
                            </React.Fragment>
                          }
                        />
                        <Box>
                          <Chip
                            label={booking.status}
                            color={booking.status === 'Confirmed' ? 'success' : 'warning'}
                            size="small"
                          />
                        </Box>
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))
                )}
              </List>
            </Card>
          </Grid>

          {/* Mobile App Bookings */}
          <Grid item xs={12} md={6}>
            {hotels.length > 0 && (
              <MobileAppBookings
                hotelId={hotels[0].id || ''}
                onViewBooking={(booking) => {
                  if (booking) {
                    // Handle view specific booking
                    console.log('View booking:', booking);
                  } else {
                    // View all mobile bookings
                    navigate('/vendor/bookings');
                  }
                }}
                onCheckIn={(booking) => {
                  // Handle check in
                  console.log('Check in booking:', booking);
                }}
                onCancel={(booking) => {
                  // Handle cancel
                  console.log('Cancel booking:', booking);
                }}
              />
            )}
          </Grid>

          {/* Mobile App Reviews */}
          <Grid item xs={12} md={6}>
            {hotels.length > 0 && auth.currentUser && (
              <MobileAppReviews
                hotelId={hotels[0].id || ''}
                vendorId={auth.currentUser.uid}
              />
            )}
          </Grid>

          {/* Pending Tasks */}
          <Grid item xs={12}>
            <Card>
              <CardHeader
                title="Pending Tasks"
                action={
                  <Button
                    size="small"
                    onClick={() => navigate('/vendor/tasks')}
                  >
                    View All
                  </Button>
                }
              />
              <Divider />
              <List sx={{ p: 0 }}>
                {pendingTasks.length === 0 ? (
                  <ListItem>
                    <ListItemText primary="No pending tasks" />
                  </ListItem>
                ) : (
                  pendingTasks.map((task) => (
                    <React.Fragment key={task.id}>
                      <ListItem alignItems="flex-start">
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor:
                            task.priority === 'high' ? 'error.main' :
                            task.priority === 'medium' ? 'warning.main' : 'info.main'
                          }}>
                            {task.type.charAt(0).toUpperCase()}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={task.title}
                          secondary={task.description}
                        />
                        <Box>
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => {/* Handle task action */}}
                          >
                            Take Action
                          </Button>
                        </Box>
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))
                )}
              </List>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default Dashboard;
