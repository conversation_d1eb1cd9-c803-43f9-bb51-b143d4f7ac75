import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/data/models/service_request_model.dart';

/// Service for handling service request notifications
class ServiceRequestNotificationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Create a notification for a hotel vendor when a service request is created
  Future<void> sendServiceRequestNotificationToVendor({
    required BaseServiceRequest request,
    required String vendorId,
  }) async {
    try {
      // Create notification for vendor mobile app
      final mobileNotification = {
        'userId': vendorId,
        'title': _getServiceRequestTitle(request.type),
        'message': _getServiceRequestMessage(request),
        'type': 'serviceRequest',
        'read': false,
        'hotelId': request.hotelId,
        'actionUrl': '/vendor/services/${request.type}?id=${request.id}',
        'createdAt': FieldValue.serverTimestamp(),
      };

      // Add notification to mobile notifications collection
      await _firestore
          .collection(AppConstants.mobileNotificationsCollection)
          .add(mobileNotification);

      // Create notification for vendor dashboard
      final vendorDashboardNotification = {
        'title': _getServiceRequestTitle(request.type),
        'message': _getServiceRequestMessage(request),
        'type': 'service_request',
        'status': 'unread',
        'vendorId': vendorId,
        'hotelId': request.hotelId,
        'relatedId': request.id,
        'relatedType': request.type,
        'priority': request.priority,
        'createdAt': FieldValue.serverTimestamp(),
        'actions': [
          {
            'label': 'View Request',
            'url': '/vendor/services/${request.type}?id=${request.id}',
          }
        ],
      };

      // Add notification to vendor notifications collection
      await _firestore
          .collection('vendorNotifications')
          .add(vendorDashboardNotification);
    } catch (e) {
      debugPrint('Error sending service request notification: $e');
      // Don't throw the error to prevent blocking the service request creation
    }
  }

  /// Create a notification for a user when a service request status changes
  Future<void> sendServiceRequestStatusUpdateToUser({
    required BaseServiceRequest request,
    required String previousStatus,
  }) async {
    try {
      if (request.guestId == null) return;

      // Create notification for user
      final userNotification = {
        'userId': request.guestId!,
        'title': 'Service Request Update',
        'message': _getStatusUpdateMessage(request, previousStatus),
        'type': 'serviceRequest',
        'read': false,
        'hotelId': request.hotelId,
        'createdAt': FieldValue.serverTimestamp(),
      };

      // Add notification to Firestore
      await _firestore
          .collection(AppConstants.mobileNotificationsCollection)
          .add(userNotification);

      // Also update the status in the vendor dashboard notification if it exists
      try {
        // Find the related vendor notification
        final vendorNotificationsQuery = await _firestore
            .collection('vendorNotifications')
            .where('relatedId', isEqualTo: request.id)
            .where('type', isEqualTo: 'service_request')
            .get();

        // Update the notification with the new status
        for (final doc in vendorNotificationsQuery.docs) {
          await _firestore
              .collection('vendorNotifications')
              .doc(doc.id)
              .update({
            'message':
                'Status updated from $previousStatus to ${request.status}: ${_getServiceRequestMessage(request)}',
            'updatedAt': FieldValue.serverTimestamp(),
          });
        }
      } catch (e) {
        debugPrint('Error updating vendor notification: $e');
        // Don't throw the error to prevent blocking the service request update
      }
    } catch (e) {
      debugPrint('Error sending service request status update: $e');
      // Don't throw the error to prevent blocking the service request update
    }
  }

  /// Get the title for a service request notification
  String _getServiceRequestTitle(String type) {
    switch (type) {
      case 'food':
        return 'New Food Order';
      case 'cleaning':
        return 'New Cleaning Request';
      case 'maintenance':
        return 'New Maintenance Request';
      default:
        return 'New Service Request';
    }
  }

  /// Get the message for a service request notification
  String _getServiceRequestMessage(BaseServiceRequest request) {
    final roomInfo = 'Room ${request.roomNumber}';
    final guestInfo = request.guestName != null && request.guestName!.isNotEmpty
        ? ' from ${request.guestName}'
        : '';
    final priorityInfo = request.priority == 'high' ? ' (High Priority)' : '';

    switch (request.type) {
      case 'food':
        final foodOrder = request as FoodOrder;
        final itemCount = foodOrder.items.length;
        final itemText = itemCount == 1 ? 'item' : 'items';
        return '$roomInfo has ordered food$guestInfo: $itemCount $itemText$priorityInfo';

      case 'cleaning':
        final cleaningRequest = request as CleaningRequest;
        final cleaningType = cleaningRequest.cleaningType != null
            ? ' (${cleaningRequest.cleaningType})'
            : '';
        return '$roomInfo has requested cleaning service$guestInfo$cleaningType$priorityInfo';

      case 'maintenance':
        final maintenanceRequest = request as MaintenanceRequest;
        return '$roomInfo has reported a ${maintenanceRequest.issueType} issue$guestInfo$priorityInfo';

      default:
        return '$roomInfo has requested service$guestInfo$priorityInfo';
    }
  }

  /// Get the message for a service request status update
  String _getStatusUpdateMessage(
      BaseServiceRequest request, String previousStatus) {
    final serviceType = _getServiceTypeName(request.type);

    switch (request.status) {
      case 'in_progress':
        return 'Your $serviceType request for Room ${request.roomNumber} is now being processed.';
      case 'completed':
        return 'Your $serviceType request for Room ${request.roomNumber} has been completed.';
      case 'cancelled':
        return 'Your $serviceType request for Room ${request.roomNumber} has been cancelled.';
      default:
        return 'Your $serviceType request for Room ${request.roomNumber} status has changed from $previousStatus to ${request.status}.';
    }
  }

  /// Get the service type name
  String _getServiceTypeName(String type) {
    switch (type) {
      case 'food':
        return 'food order';
      case 'cleaning':
        return 'cleaning';
      case 'maintenance':
        return 'maintenance';
      default:
        return 'service';
    }
  }
}
