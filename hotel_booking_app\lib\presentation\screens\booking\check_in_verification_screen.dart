import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:hotel_booking_app/core/theme/app_theme.dart';
import 'package:hotel_booking_app/data/models/booking_model.dart';
import 'package:hotel_booking_app/data/models/aadhaar_verification_model.dart';
import 'package:hotel_booking_app/data/services/aadhaar_verification_service.dart';
import 'package:hotel_booking_app/data/services/booking_service.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_button.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_text_field.dart';

class CheckInVerificationScreen extends StatefulWidget {
  final Booking booking;
  
  const CheckInVerificationScreen({
    super.key,
    required this.booking,
  });

  @override
  State<CheckInVerificationScreen> createState() => _CheckInVerificationScreenState();
}

class _CheckInVerificationScreenState extends State<CheckInVerificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _aadhaarController = TextEditingController();
  final _otpController = TextEditingController();
  
  bool _otpSent = false;
  String? _verificationId;
  bool _isLoading = false;
  
  @override
  void dispose() {
    _aadhaarController.dispose();
    _otpController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final aadhaarService = Provider.of<AadhaarVerificationService>(context);
    final bookingService = Provider.of<BookingService>(context);
    final verification = aadhaarService.currentVerification;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Check-in Verification'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Booking details card
            _buildBookingDetailsCard(widget.booking),
            
            const SizedBox(height: 24),
            
            // Verification status
            if (verification != null && 
                verification.status == AadhaarVerificationStatus.verified) ...[
              _buildVerifiedStatusCard(verification),
              const SizedBox(height: 24),
              CustomButton(
                text: 'Complete Check-in',
                onPressed: () => _completeCheckIn(context, bookingService),
              ),
            ] else ...[
              // Verification required message
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.orange.shade800),
                        const SizedBox(width: 8),
                        const Text(
                          'Aadhaar Verification Required',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'To complete check-in, please verify your identity with Aadhaar.',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Verification form
              if (!_otpSent)
                _buildVerificationForm(aadhaarService),
                
              // OTP verification form
              if (_otpSent && _verificationId != null)
                _buildOtpVerificationForm(aadhaarService, bookingService),
            ],
            
            const SizedBox(height: 24),
            
            // Information about verification
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Why is verification required?',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Aadhaar verification is required by hotels for security purposes and to comply with local regulations. Your information is kept secure and only shared with the hotel for check-in purposes.',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildBookingDetailsCard(Booking booking) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.hotel, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    booking.hotelName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const Divider(height: 24),
            _buildInfoRow('Room', booking.roomName),
            const SizedBox(height: 8),
            _buildInfoRow(
              'Check-in',
              '${_formatDate(booking.checkIn)} (Today)',
              iconColor: Colors.green,
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              'Check-out',
              _formatDate(booking.checkOut),
            ),
            const SizedBox(height: 8),
            _buildInfoRow('Guests', '${booking.guests} Person(s)'),
            const SizedBox(height: 8),
            _buildInfoRow(
              'Booking ID',
              booking.id.substring(0, 8).toUpperCase(),
              iconColor: Colors.blue,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildInfoRow(String label, String value, {Color? iconColor}) {
    return Row(
      children: [
        Icon(
          _getIconForLabel(label),
          size: 16,
          color: iconColor ?? Colors.grey.shade600,
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade700,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
  
  IconData _getIconForLabel(String label) {
    switch (label) {
      case 'Room':
        return Icons.bed;
      case 'Check-in':
        return Icons.login;
      case 'Check-out':
        return Icons.logout;
      case 'Guests':
        return Icons.people;
      case 'Booking ID':
        return Icons.confirmation_number;
      default:
        return Icons.info_outline;
    }
  }
  
  Widget _buildVerifiedStatusCard(AadhaarVerification verification) {
    return Card(
      elevation: 2,
      color: Colors.green.shade50,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.green.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.green),
                const SizedBox(width: 8),
                const Text(
                  'Aadhaar Verified',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Verified',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Name: ${verification.fullName}',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 8),
            Text(
              'Aadhaar: ${verification.maskedAadhaarNumber}',
              style: const TextStyle(fontSize: 16),
            ),
            if (verification.verifiedAt != null) ...[
              const SizedBox(height: 8),
              Text(
                'Verified on: ${_formatDate(verification.verifiedAt!)}',
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildVerificationForm(AadhaarVerificationService aadhaarService) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Enter your Aadhaar details',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Aadhaar number input
          CustomTextField(
            controller: _aadhaarController,
            labelText: 'Aadhaar Number',
            hintText: 'Enter your 12-digit Aadhaar number',
            prefixIcon: const Icon(Icons.credit_card),
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(12),
              _AadhaarNumberFormatter(),
            ],
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your Aadhaar number';
              }
              // Remove spaces and check length
              final cleanValue = value.replaceAll(' ', '');
              if (cleanValue.length != 12) {
                return 'Aadhaar number must be 12 digits';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 24),
          
          // Error message
          if (aadhaarService.error != null)
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Text(
                aadhaarService.error!,
                style: TextStyle(color: Colors.red.shade800),
              ),
            ),
          
          // Submit button
          CustomButton(
            text: 'Send OTP',
            isLoading: aadhaarService.isLoading || _isLoading,
            onPressed: () async {
              if (_formKey.currentState!.validate()) {
                // Remove spaces from Aadhaar number
                final aadhaarNumber = _aadhaarController.text.replaceAll(' ', '');
                
                // Initiate verification
                final verificationId = await aadhaarService.initiateVerification(aadhaarNumber);
                
                if (verificationId != null) {
                  setState(() {
                    _otpSent = true;
                    _verificationId = verificationId;
                  });
                }
              }
            },
          ),
        ],
      ),
    );
  }
  
  Widget _buildOtpVerificationForm(
    AadhaarVerificationService aadhaarService,
    BookingService bookingService,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Enter OTP',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'An OTP has been sent to your registered mobile number',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 16),
        
        // OTP input
        CustomTextField(
          controller: _otpController,
          labelText: 'OTP',
          hintText: 'Enter 6-digit OTP',
          prefixIcon: const Icon(Icons.lock_outline),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(6),
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter the OTP';
            }
            if (value.length != 6) {
              return 'OTP must be 6 digits';
            }
            return null;
          },
        ),
        
        const SizedBox(height: 24),
        
        // Verify OTP button
        CustomButton(
          text: 'Verify OTP',
          isLoading: aadhaarService.isLoading || _isLoading,
          onPressed: () async {
            if (_otpController.text.length == 6 && _verificationId != null) {
              final success = await aadhaarService.verifyOtp(
                _verificationId!,
                _otpController.text,
              );
              
              if (success) {
                setState(() {
                  _otpSent = false;
                  _verificationId = null;
                  _otpController.clear();
                });
                
                if (mounted) {
                  _completeCheckIn(context, bookingService);
                }
              }
            }
          },
        ),
        
        const SizedBox(height: 16),
        
        // Resend OTP option
        Center(
          child: TextButton(
            onPressed: aadhaarService.isLoading || _isLoading
                ? null
                : () async {
                    // Re-initiate verification to resend OTP
                    final aadhaarNumber = _aadhaarController.text.replaceAll(' ', '');
                    final verificationId = await aadhaarService.initiateVerification(aadhaarNumber);
                    
                    if (verificationId != null) {
                      setState(() {
                        _verificationId = verificationId;
                      });
                      
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('OTP resent successfully'),
                          ),
                        );
                      }
                    }
                  },
            child: const Text('Resend OTP'),
          ),
        ),
      ],
    );
  }
  
  Future<void> _completeCheckIn(BuildContext context, BookingService bookingService) async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Update booking status to checked in
      final success = await bookingService.updateBookingStatus(
        widget.booking.id,
        'checked_in',
      );
      
      setState(() {
        _isLoading = false;
      });
      
      if (success && mounted) {
        // Show success dialog
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: const Text('Check-in Successful'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 64,
                ),
                const SizedBox(height: 16),
                const Text(
                  'You have successfully checked in to your room.',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'Hotel: ${widget.booking.hotelName}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('Room: ${widget.booking.roomName}'),
              ],
            ),
            actions: [
              CustomButton(
                text: 'Done',
                onPressed: () {
                  Navigator.of(context).pop(); // Close dialog
                  Navigator.of(context).pop(); // Go back to previous screen
                },
              ),
            ],
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error during check-in: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Custom formatter for Aadhaar number (XXXX XXXX XXXX format)
class _AadhaarNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) {
      return newValue;
    }
    
    // Remove all spaces
    final text = newValue.text.replaceAll(' ', '');
    
    // Format with spaces after every 4 digits
    final buffer = StringBuffer();
    for (int i = 0; i < text.length; i++) {
      if (i > 0 && i % 4 == 0) {
        buffer.write(' ');
      }
      buffer.write(text[i]);
    }
    
    final formattedText = buffer.toString();
    
    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: formattedText.length),
    );
  }
}
