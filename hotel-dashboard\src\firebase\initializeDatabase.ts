import { collection, doc, setDoc, serverTimestamp, getDocs } from 'firebase/firestore';
import { db } from './config';
import { ROLE_SUPER_ADMIN, ROLE_VENDOR, ROLE_STAFF } from './auth';

// Sample data for initializing the database
const sampleData = {
  users: [
    {
      id: 'admin1',
      email: '<EMAIL>',
      displayName: 'Admin User',
      role: ROLE_SUPER_ADMIN,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    },
    {
      id: 'vendor1',
      email: '<EMAIL>',
      displayName: 'Vendor User',
      role: ROLE_VENDOR,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    },
    {
      id: 'staff1',
      email: '<EMAIL>',
      displayName: 'Staff User',
      role: ROLE_STAFF,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    }
  ],
  hotels: [
    {
      id: 'hotel1',
      name: 'Grand Hotel',
      description: 'A luxurious hotel in the heart of the city',
      address: '123 Main Street',
      city: 'New York',
      country: 'USA',
      zipCode: '10001',
      phone: '******-456-7890',
      email: '<EMAIL>',
      website: 'https://www.grandhotel.com',
      rating: 4.5,
      price: 200,
      amenities: ['WiFi', 'Pool', 'Spa', 'Gym', 'Restaurant'],
      images: [
        'https://source.unsplash.com/random/800x600?hotel',
        'https://source.unsplash.com/random/800x600?hotel-room',
        'https://source.unsplash.com/random/800x600?hotel-lobby'
      ],
      latitude: 40.7128,
      longitude: -74.0060,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      vendorId: 'vendor1'
    },
    {
      id: 'hotel2',
      name: 'Seaside Resort',
      description: 'A beautiful resort by the beach',
      address: '456 Ocean Drive',
      city: 'Miami',
      country: 'USA',
      zipCode: '33139',
      phone: '******-654-3210',
      email: '<EMAIL>',
      website: 'https://www.seasideresort.com',
      rating: 4.8,
      price: 350,
      amenities: ['WiFi', 'Pool', 'Beach Access', 'Spa', 'Gym', 'Restaurant', 'Bar'],
      images: [
        'https://source.unsplash.com/random/800x600?resort',
        'https://source.unsplash.com/random/800x600?beach-hotel',
        'https://source.unsplash.com/random/800x600?resort-pool'
      ],
      latitude: 25.7617,
      longitude: -80.1918,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      vendorId: 'vendor1'
    }
  ],
  rooms: [
    {
      id: 'room1',
      hotelId: 'hotel1',
      name: 'Deluxe Room',
      description: 'A spacious room with a king-size bed',
      price: 200,
      capacity: 2,
      amenities: ['WiFi', 'TV', 'Mini Bar', 'Air Conditioning'],
      images: [
        'https://source.unsplash.com/random/800x600?hotel-room',
        'https://source.unsplash.com/random/800x600?hotel-bathroom'
      ],
      status: 'available',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    },
    {
      id: 'room2',
      hotelId: 'hotel1',
      name: 'Suite',
      description: 'A luxurious suite with a separate living area',
      price: 350,
      capacity: 4,
      amenities: ['WiFi', 'TV', 'Mini Bar', 'Air Conditioning', 'Jacuzzi'],
      images: [
        'https://source.unsplash.com/random/800x600?hotel-suite',
        'https://source.unsplash.com/random/800x600?hotel-living-room'
      ],
      status: 'available',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    },
    {
      id: 'room3',
      hotelId: 'hotel2',
      name: 'Ocean View Room',
      description: 'A beautiful room with an ocean view',
      price: 300,
      capacity: 2,
      amenities: ['WiFi', 'TV', 'Mini Bar', 'Air Conditioning', 'Balcony'],
      images: [
        'https://source.unsplash.com/random/800x600?ocean-view-room',
        'https://source.unsplash.com/random/800x600?hotel-balcony'
      ],
      status: 'available',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    },
    {
      id: 'room4',
      hotelId: 'hotel2',
      name: 'Beach Front Villa',
      description: 'A spacious villa right on the beach',
      price: 500,
      capacity: 6,
      amenities: ['WiFi', 'TV', 'Kitchen', 'Air Conditioning', 'Private Pool', 'Beach Access'],
      images: [
        'https://source.unsplash.com/random/800x600?beach-villa',
        'https://source.unsplash.com/random/800x600?villa-pool'
      ],
      status: 'available',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    }
  ]
};

// Function to initialize the database with sample data
export const initializeDatabase = async () => {
  try {
    // Add users
    for (const user of sampleData.users) {
      await setDoc(doc(db, 'users', user.id), user);
    }

    // Add hotels
    for (const hotel of sampleData.hotels) {
      await setDoc(doc(db, 'hotels', hotel.id), hotel);
    }

    // Add rooms
    for (const room of sampleData.rooms) {
      await setDoc(doc(db, 'rooms', room.id), room);
    }

    console.log('Database initialized successfully');
    return true;
  } catch (error) {
    console.error('Error initializing database:', error);
    return false;
  }
};

// Function to check if the database is already initialized
export const isDatabaseInitialized = async () => {
  try {
    const hotelsCollection = collection(db, 'hotels');
    const hotelsSnapshot = await getDocs(hotelsCollection);
    return !hotelsSnapshot.empty;
  } catch (error) {
    console.error('Error checking database initialization:', error);
    return false;
  }
};
