import 'package:cloud_firestore/cloud_firestore.dart';

class Room {
  final String id;
  final String hotelId;
  final String name;
  final String description;
  final double price;
  final int capacity;
  final List<String> amenities;
  final List<String> images;
  final String status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? type;
  final int? size;
  final String? bedType;
  final bool? hasWifi;
  final bool? hasTV;
  final bool? hasAC;
  final bool? hasMinibar;
  final bool? hasBalcony;
  final bool? hasPrivateBathroom;

  Room({
    required this.id,
    required this.hotelId,
    required this.name,
    required this.description,
    required this.price,
    required this.capacity,
    required this.amenities,
    required this.images,
    required this.status,
    this.createdAt,
    this.updatedAt,
    this.type,
    this.size,
    this.bedType,
    this.hasWifi,
    this.hasTV,
    this.hasAC,
    this.hasMinibar,
    this.hasBalcony,
    this.hasPrivateBathroom,
  });

  factory Room.fromMap(Map<String, dynamic> map) {
    return Room(
      id: map['id'] ?? '',
      hotelId: map['hotelId'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      price: (map['price'] ?? 0).toDouble(),
      capacity: map['capacity'] ?? 0,
      amenities: List<String>.from(map['amenities'] ?? []),
      images: List<String>.from(map['images'] ?? []),
      status: map['status'] ?? 'available',
      createdAt: map['createdAt'] != null
          ? (map['createdAt'] as Timestamp).toDate()
          : null,
      updatedAt: map['updatedAt'] != null
          ? (map['updatedAt'] as Timestamp).toDate()
          : null,
      type: map['type'],
      size: map['size'],
      bedType: map['bedType'],
      hasWifi: map['hasWifi'],
      hasTV: map['hasTV'],
      hasAC: map['hasAC'],
      hasMinibar: map['hasMinibar'],
      hasBalcony: map['hasBalcony'],
      hasPrivateBathroom: map['hasPrivateBathroom'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'hotelId': hotelId,
      'name': name,
      'description': description,
      'price': price,
      'capacity': capacity,
      'amenities': amenities,
      'images': images,
      'status': status,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'type': type,
      'size': size,
      'bedType': bedType,
      'hasWifi': hasWifi,
      'hasTV': hasTV,
      'hasAC': hasAC,
      'hasMinibar': hasMinibar,
      'hasBalcony': hasBalcony,
      'hasPrivateBathroom': hasPrivateBathroom,
    };
  }

  Room copyWith({
    String? id,
    String? hotelId,
    String? name,
    String? description,
    double? price,
    int? capacity,
    List<String>? amenities,
    List<String>? images,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? type,
    int? size,
    String? bedType,
    bool? hasWifi,
    bool? hasTV,
    bool? hasAC,
    bool? hasMinibar,
    bool? hasBalcony,
    bool? hasPrivateBathroom,
  }) {
    return Room(
      id: id ?? this.id,
      hotelId: hotelId ?? this.hotelId,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      capacity: capacity ?? this.capacity,
      amenities: amenities ?? this.amenities,
      images: images ?? this.images,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      type: type ?? this.type,
      size: size ?? this.size,
      bedType: bedType ?? this.bedType,
      hasWifi: hasWifi ?? this.hasWifi,
      hasTV: hasTV ?? this.hasTV,
      hasAC: hasAC ?? this.hasAC,
      hasMinibar: hasMinibar ?? this.hasMinibar,
      hasBalcony: hasBalcony ?? this.hasBalcony,
      hasPrivateBathroom: hasPrivateBathroom ?? this.hasPrivateBathroom,
    );
  }
}
