import {
  collection,
  doc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  getDocs,
  getDoc,
  serverTimestamp,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { db } from './config';

// Interface for booking data
export interface BookingData {
  id?: string;
  hotelId: string;
  roomId: string;
  userId: string;
  guestName: string;
  guestEmail: string;
  guestPhone?: string;
  checkInDate: Timestamp | Date;
  checkOutDate: Timestamp | Date;
  adults: number;
  children: number;
  totalAmount: number;
  paymentStatus: 'pending' | 'paid' | 'refunded' | 'cancelled';
  bookingStatus: 'confirmed' | 'pending' | 'cancelled' | 'completed';
  specialRequests?: string;
  createdAt?: any;
  updatedAt?: any;
}

/**
 * Create a new booking
 */
export const createBooking = async (bookingData: BookingData): Promise<string> => {
  try {
    // Create a new document reference
    const bookingRef = doc(collection(db, 'bookings'));

    // Convert Date objects to Firestore Timestamps
    const processedData = {
      ...bookingData,
      checkInDate: bookingData.checkInDate instanceof Date
        ? Timestamp.fromDate(bookingData.checkInDate)
        : bookingData.checkInDate,
      checkOutDate: bookingData.checkOutDate instanceof Date
        ? Timestamp.fromDate(bookingData.checkOutDate)
        : bookingData.checkOutDate,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    // Add the booking data with timestamps
    await setDoc(bookingRef, processedData);

    return bookingRef.id;
  } catch (error) {
    console.error('Error creating booking:', error);
    throw error;
  }
};

/**
 * Get all bookings for a specific hotel
 */
export const getBookingsForHotel = async (hotelId: string) => {
  try {
    const bookingsQuery = query(
      collection(db, 'bookings'),
      where('hotelId', '==', hotelId)
    );

    const bookingsSnapshot = await getDocs(bookingsQuery);
    const bookings: BookingData[] = [];

    bookingsSnapshot.forEach((doc) => {
      bookings.push({ id: doc.id, ...doc.data() } as BookingData);
    });

    // Sort manually by createdAt in descending order
    bookings.sort((a, b) => {
      const dateA = a.createdAt ? new Date(a.createdAt.seconds * 1000) : new Date(0);
      const dateB = b.createdAt ? new Date(b.createdAt.seconds * 1000) : new Date(0);
      return dateB.getTime() - dateA.getTime();
    });

    return bookings;
  } catch (error) {
    console.error('Error getting bookings:', error);
    return [];
  }
};

/**
 * Get all bookings for a specific user
 */
export const getBookingsForUser = async (userId: string) => {
  try {
    const bookingsQuery = query(
      collection(db, 'bookings'),
      where('userId', '==', userId)
    );

    const bookingsSnapshot = await getDocs(bookingsQuery);
    const bookings: BookingData[] = [];

    bookingsSnapshot.forEach((doc) => {
      bookings.push({ id: doc.id, ...doc.data() } as BookingData);
    });

    // Sort manually by createdAt in descending order
    bookings.sort((a, b) => {
      const dateA = a.createdAt ? new Date(a.createdAt.seconds * 1000) : new Date(0);
      const dateB = b.createdAt ? new Date(b.createdAt.seconds * 1000) : new Date(0);
      return dateB.getTime() - dateA.getTime();
    });

    return bookings;
  } catch (error) {
    console.error('Error getting user bookings:', error);
    return [];
  }
};

/**
 * Get all bookings (for admin)
 */
export const getAllBookings = async () => {
  try {
    // First try to get real bookings from Firestore
    try {
      const bookingsSnapshot = await getDocs(collection(db, 'bookings'));
      const bookings: BookingData[] = [];

      bookingsSnapshot.forEach((doc) => {
        try {
          const data = doc.data();
          bookings.push({
            id: doc.id,
            ...data,
            // Ensure these fields have default values if missing
            totalAmount: data.totalAmount || 0,
            adults: data.adults || 1,
            children: data.children || 0,
            paymentStatus: data.paymentStatus || 'pending',
            bookingStatus: data.bookingStatus || 'pending',
            checkInDate: data.checkInDate || Timestamp.now(),
            checkOutDate: data.checkOutDate || Timestamp.now(),
          } as BookingData);
        } catch (err) {
          console.error('Error processing booking document:', err);
        }
      });

      // If we have real bookings, return them
      if (bookings.length > 0) {
        // Sort manually by createdAt in descending order
        bookings.sort((a, b) => {
          const dateA = a.createdAt ? new Date(a.createdAt.seconds * 1000) : new Date(0);
          const dateB = b.createdAt ? new Date(b.createdAt.seconds * 1000) : new Date(0);
          return dateB.getTime() - dateA.getTime();
        });
        return bookings;
      }
    } catch (err) {
      console.error('Error fetching real bookings:', err);
    }

    // If no real bookings or error occurred, return sample data
    console.log('Returning sample booking data');

    // Generate sample data
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const nextWeek = new Date(now);
    nextWeek.setDate(nextWeek.getDate() + 7);

    const nextWeekPlusOne = new Date(nextWeek);
    nextWeekPlusOne.setDate(nextWeekPlusOne.getDate() + 1);

    const lastWeek = new Date(now);
    lastWeek.setDate(lastWeek.getDate() - 7);

    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);

    return [
      {
        id: 'sample-booking-1',
        hotelId: 'sample-hotel-1',
        roomId: 'sample-room-1',
        userId: 'sample-user-1',
        guestName: 'John Doe',
        guestEmail: '<EMAIL>',
        guestPhone: '+1234567890',
        checkInDate: Timestamp.fromDate(now),
        checkOutDate: Timestamp.fromDate(tomorrow),
        adults: 2,
        children: 1,
        totalAmount: 150,
        paymentStatus: 'paid' as 'pending' | 'paid' | 'refunded' | 'cancelled',
        bookingStatus: 'confirmed' as 'confirmed' | 'pending' | 'cancelled' | 'completed',
        specialRequests: 'Early check-in requested',
        createdAt: Timestamp.fromDate(lastWeek),
        updatedAt: Timestamp.fromDate(lastWeek)
      },
      {
        id: 'sample-booking-2',
        hotelId: 'sample-hotel-2',
        roomId: 'sample-room-2',
        userId: 'sample-user-2',
        guestName: 'Jane Smith',
        guestEmail: '<EMAIL>',
        guestPhone: '+1987654321',
        checkInDate: Timestamp.fromDate(nextWeek),
        checkOutDate: Timestamp.fromDate(nextWeekPlusOne),
        adults: 1,
        children: 0,
        totalAmount: 120,
        paymentStatus: 'pending' as 'pending' | 'paid' | 'refunded' | 'cancelled',
        bookingStatus: 'pending' as 'confirmed' | 'pending' | 'cancelled' | 'completed',
        specialRequests: 'High floor room preferred',
        createdAt: Timestamp.fromDate(yesterday),
        updatedAt: Timestamp.fromDate(yesterday)
      },
      {
        id: 'sample-booking-3',
        hotelId: 'sample-hotel-1',
        roomId: 'sample-room-3',
        userId: 'sample-user-3',
        guestName: 'Robert Johnson',
        guestEmail: '<EMAIL>',
        guestPhone: '+1122334455',
        checkInDate: Timestamp.fromDate(lastWeek),
        checkOutDate: Timestamp.fromDate(yesterday),
        adults: 2,
        children: 2,
        totalAmount: 300,
        paymentStatus: 'paid' as 'pending' | 'paid' | 'refunded' | 'cancelled',
        bookingStatus: 'completed' as 'confirmed' | 'pending' | 'cancelled' | 'completed',
        specialRequests: 'Extra pillows needed',
        createdAt: Timestamp.fromDate(new Date(lastWeek.getTime() - 86400000 * 7)), // 7 days before lastWeek
        updatedAt: Timestamp.fromDate(yesterday)
      },
      {
        id: 'sample-booking-4',
        hotelId: 'sample-hotel-3',
        roomId: 'sample-room-4',
        userId: 'sample-user-4',
        guestName: 'Emily Davis',
        guestEmail: '<EMAIL>',
        guestPhone: '+1567891234',
        checkInDate: Timestamp.fromDate(tomorrow),
        checkOutDate: Timestamp.fromDate(new Date(tomorrow.getTime() + 86400000 * 3)), // 3 days after tomorrow
        adults: 2,
        children: 0,
        totalAmount: 450,
        paymentStatus: 'paid' as 'pending' | 'paid' | 'refunded' | 'cancelled',
        bookingStatus: 'confirmed' as 'confirmed' | 'pending' | 'cancelled' | 'completed',
        specialRequests: 'Quiet room away from elevator',
        createdAt: Timestamp.fromDate(new Date(now.getTime() - 86400000 * 2)), // 2 days ago
        updatedAt: Timestamp.fromDate(now)
      },
      {
        id: 'sample-booking-5',
        hotelId: 'sample-hotel-2',
        roomId: 'sample-room-5',
        userId: 'sample-user-5',
        guestName: 'Michael Wilson',
        guestEmail: '<EMAIL>',
        guestPhone: '+1654987321',
        checkInDate: Timestamp.fromDate(new Date(now.getTime() + 86400000 * 14)), // 14 days from now
        checkOutDate: Timestamp.fromDate(new Date(now.getTime() + 86400000 * 17)), // 17 days from now
        adults: 1,
        children: 1,
        totalAmount: 380,
        paymentStatus: 'pending' as 'pending' | 'paid' | 'refunded' | 'cancelled',
        bookingStatus: 'pending' as 'confirmed' | 'pending' | 'cancelled' | 'completed',
        specialRequests: 'Crib for baby needed',
        createdAt: Timestamp.fromDate(now),
        updatedAt: Timestamp.fromDate(now)
      }
    ];
  } catch (error) {
    console.error('Error getting all bookings:', error);
    // Return empty array instead of throwing
    return [];
  }
};

/**
 * Get a booking by ID
 */
export const getBookingById = async (bookingId: string) => {
  try {
    const bookingDoc = await getDoc(doc(db, 'bookings', bookingId));

    if (bookingDoc.exists()) {
      return { id: bookingDoc.id, ...bookingDoc.data() } as BookingData;
    } else {
      throw new Error('Booking not found');
    }
  } catch (error) {
    console.error('Error getting booking:', error);
    throw error;
  }
};

/**
 * Update a booking
 */
export const updateBooking = async (bookingId: string, bookingData: Partial<BookingData>) => {
  try {
    const bookingRef = doc(db, 'bookings', bookingId);

    // Process dates if they exist in the update data
    const processedData = { ...bookingData };

    if (bookingData.checkInDate instanceof Date) {
      processedData.checkInDate = Timestamp.fromDate(bookingData.checkInDate);
    }

    if (bookingData.checkOutDate instanceof Date) {
      processedData.checkOutDate = Timestamp.fromDate(bookingData.checkOutDate);
    }

    await updateDoc(bookingRef, {
      ...processedData,
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('Error updating booking:', error);
    throw error;
  }
};

/**
 * Delete a booking
 */
export const deleteBooking = async (bookingId: string) => {
  try {
    await deleteDoc(doc(db, 'bookings', bookingId));
    return true;
  } catch (error) {
    console.error('Error deleting booking:', error);
    throw error;
  }
};

/**
 * Get total booking count
 */
export const getTotalBookingCount = async (): Promise<number> => {
  try {
    const bookingsSnapshot = await getDocs(collection(db, 'bookings'));
    return bookingsSnapshot.size;
  } catch (error) {
    console.error('Error getting total booking count:', error);
    throw error;
  }
};

/**
 * Get recent bookings
 */
export const getRecentBookings = async (limitCount: number = 5) => {
  try {
    // Get all bookings
    const bookingsSnapshot = await getDocs(collection(db, 'bookings'));
    const bookings: BookingData[] = [];

    bookingsSnapshot.forEach((doc) => {
      try {
        const data = doc.data();
        bookings.push({
          id: doc.id,
          ...data,
          // Ensure these fields have default values if missing
          totalAmount: data.totalAmount || 0,
          adults: data.adults || 1,
          children: data.children || 0,
          paymentStatus: data.paymentStatus || 'pending',
          bookingStatus: data.bookingStatus || 'pending',
          checkInDate: data.checkInDate || Timestamp.now(),
          checkOutDate: data.checkOutDate || Timestamp.now(),
        } as BookingData);
      } catch (err) {
        console.error('Error processing booking document:', err);
      }
    });

    // Sort manually by createdAt in descending order
    bookings.sort((a, b) => {
      const dateA = a.createdAt ? new Date(a.createdAt.seconds * 1000) : new Date(0);
      const dateB = b.createdAt ? new Date(b.createdAt.seconds * 1000) : new Date(0);
      return dateB.getTime() - dateA.getTime();
    });

    // Return only the first 'limitCount' bookings
    return bookings.slice(0, limitCount);
  } catch (error) {
    console.error('Error getting recent bookings:', error);
    return [];
  }
};
