import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../firebase/config';

// Collection name
const STAFF_AVAILABILITY_COLLECTION = 'staffAvailability';

// Availability types
export const AVAILABILITY_TYPE_PREFERRED = 'preferred';
export const AVAILABILITY_TYPE_AVAILABLE = 'available';
export const AVAILABILITY_TYPE_UNAVAILABLE = 'unavailable';

// Recurring types
export const RECURRING_WEEKLY = 'weekly';
export const RECURRING_MONTHLY = 'monthly';
export const RECURRING_NONE = 'none';

// Staff availability interface
export interface StaffAvailability {
  id?: string;
  staffId: string;
  staffName: string;
  hotelId: string;
  dayOfWeek?: number; // 0-6 (Monday-Sunday)
  startTime: string; // Format: "HH:MM"
  endTime: string; // Format: "HH:MM"
  type: string; // preferred, available, unavailable
  recurring: string; // weekly, monthly, none
  specificDate?: Timestamp; // Used for non-recurring availability
  notes?: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

/**
 * Get availability for a staff member
 */
export const getStaffAvailability = async (
  staffId: string
): Promise<StaffAvailability[]> => {
  try {
    const availabilityQuery = query(
      collection(db, STAFF_AVAILABILITY_COLLECTION),
      where('staffId', '==', staffId),
      orderBy('dayOfWeek', 'asc'),
      orderBy('startTime', 'asc')
    );
    
    const querySnapshot = await getDocs(availabilityQuery);
    const availability: StaffAvailability[] = [];
    
    querySnapshot.forEach((doc) => {
      availability.push({
        id: doc.id,
        ...doc.data() as Omit<StaffAvailability, 'id'>
      });
    });
    
    return availability;
  } catch (error) {
    console.error('Error getting staff availability:', error);
    throw error;
  }
};

/**
 * Get availability for all staff in a hotel
 */
export const getHotelStaffAvailability = async (
  hotelId: string
): Promise<StaffAvailability[]> => {
  try {
    const availabilityQuery = query(
      collection(db, STAFF_AVAILABILITY_COLLECTION),
      where('hotelId', '==', hotelId),
      orderBy('staffName', 'asc'),
      orderBy('dayOfWeek', 'asc'),
      orderBy('startTime', 'asc')
    );
    
    const querySnapshot = await getDocs(availabilityQuery);
    const availability: StaffAvailability[] = [];
    
    querySnapshot.forEach((doc) => {
      availability.push({
        id: doc.id,
        ...doc.data() as Omit<StaffAvailability, 'id'>
      });
    });
    
    return availability;
  } catch (error) {
    console.error('Error getting hotel staff availability:', error);
    throw error;
  }
};

/**
 * Get availability for a specific date
 */
export const getAvailabilityForDate = async (
  hotelId: string,
  date: Date
): Promise<StaffAvailability[]> => {
  try {
    // Get day of week (0-6, Monday-Sunday)
    const dayOfWeek = (date.getDay() + 6) % 7; // Convert from Sunday-Saturday (0-6) to Monday-Sunday (0-6)
    
    // Get recurring weekly availability for this day
    const weeklyQuery = query(
      collection(db, STAFF_AVAILABILITY_COLLECTION),
      where('hotelId', '==', hotelId),
      where('dayOfWeek', '==', dayOfWeek),
      where('recurring', '==', RECURRING_WEEKLY)
    );
    
    const weeklySnapshot = await getDocs(weeklyQuery);
    const weeklyAvailability: StaffAvailability[] = [];
    
    weeklySnapshot.forEach((doc) => {
      weeklyAvailability.push({
        id: doc.id,
        ...doc.data() as Omit<StaffAvailability, 'id'>
      });
    });
    
    // Get specific date availability
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    
    const specificDateQuery = query(
      collection(db, STAFF_AVAILABILITY_COLLECTION),
      where('hotelId', '==', hotelId),
      where('specificDate', '>=', Timestamp.fromDate(startOfDay)),
      where('specificDate', '<=', Timestamp.fromDate(endOfDay)),
      where('recurring', '==', RECURRING_NONE)
    );
    
    const specificDateSnapshot = await getDocs(specificDateQuery);
    const specificDateAvailability: StaffAvailability[] = [];
    
    specificDateSnapshot.forEach((doc) => {
      specificDateAvailability.push({
        id: doc.id,
        ...doc.data() as Omit<StaffAvailability, 'id'>
      });
    });
    
    // Combine and return all availability
    return [...weeklyAvailability, ...specificDateAvailability];
  } catch (error) {
    console.error('Error getting availability for date:', error);
    throw error;
  }
};

/**
 * Create a new staff availability
 */
export const createStaffAvailability = async (
  availability: Omit<StaffAvailability, 'id' | 'createdAt' | 'updatedAt'>
): Promise<StaffAvailability> => {
  try {
    const now = Timestamp.now();
    const newAvailability: Omit<StaffAvailability, 'id'> = {
      ...availability,
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await addDoc(collection(db, STAFF_AVAILABILITY_COLLECTION), newAvailability);
    
    return {
      ...newAvailability,
      id: docRef.id
    };
  } catch (error) {
    console.error('Error creating staff availability:', error);
    throw error;
  }
};

/**
 * Update a staff availability
 */
export const updateStaffAvailability = async (
  availabilityId: string,
  updates: Partial<Omit<StaffAvailability, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<boolean> => {
  try {
    await updateDoc(doc(db, STAFF_AVAILABILITY_COLLECTION, availabilityId), {
      ...updates,
      updatedAt: serverTimestamp()
    });
    
    return true;
  } catch (error) {
    console.error('Error updating staff availability:', error);
    throw error;
  }
};

/**
 * Delete a staff availability
 */
export const deleteStaffAvailability = async (availabilityId: string): Promise<boolean> => {
  try {
    await deleteDoc(doc(db, STAFF_AVAILABILITY_COLLECTION, availabilityId));
    return true;
  } catch (error) {
    console.error('Error deleting staff availability:', error);
    throw error;
  }
};

/**
 * Check if a staff member is available for a shift
 */
export const isStaffAvailableForShift = async (
  staffId: string,
  date: Date,
  startTime: string,
  endTime: string
): Promise<{
  available: boolean;
  reason?: string;
  conflictingAvailability?: StaffAvailability;
}> => {
  try {
    // Get staff availability for this date
    const staffAvailability = await getStaffAvailability(staffId);
    
    // Get day of week (0-6, Monday-Sunday)
    const dayOfWeek = (date.getDay() + 6) % 7; // Convert from Sunday-Saturday (0-6) to Monday-Sunday (0-6)
    
    // Filter for this day's availability
    const dayAvailability = staffAvailability.filter(a => 
      (a.recurring === RECURRING_WEEKLY && a.dayOfWeek === dayOfWeek) ||
      (a.recurring === RECURRING_NONE && a.specificDate && 
        a.specificDate.toDate().getDate() === date.getDate() &&
        a.specificDate.toDate().getMonth() === date.getMonth() &&
        a.specificDate.toDate().getFullYear() === date.getFullYear())
    );
    
    // Check for unavailable periods
    const unavailablePeriods = dayAvailability.filter(a => a.type === AVAILABILITY_TYPE_UNAVAILABLE);
    
    for (const period of unavailablePeriods) {
      // Check if shift overlaps with unavailable period
      if (isTimeOverlap(startTime, endTime, period.startTime, period.endTime)) {
        return {
          available: false,
          reason: 'Staff member is marked as unavailable during this time',
          conflictingAvailability: period
        };
      }
    }
    
    // If there are no preferred or available periods, assume available
    if (dayAvailability.filter(a => 
      a.type === AVAILABILITY_TYPE_PREFERRED || a.type === AVAILABILITY_TYPE_AVAILABLE
    ).length === 0) {
      return { available: true };
    }
    
    // Check if shift is within preferred or available periods
    const availablePeriods = dayAvailability.filter(a => 
      a.type === AVAILABILITY_TYPE_PREFERRED || a.type === AVAILABILITY_TYPE_AVAILABLE
    );
    
    for (const period of availablePeriods) {
      // Check if shift is within available period
      if (isTimeWithin(startTime, endTime, period.startTime, period.endTime)) {
        return { available: true };
      }
    }
    
    // If we get here, the shift is not within any available period
    return {
      available: false,
      reason: 'Shift is outside of staff member\'s available hours'
    };
  } catch (error) {
    console.error('Error checking staff availability:', error);
    throw error;
  }
};

/**
 * Helper function to check if two time ranges overlap
 */
const isTimeOverlap = (
  start1: string,
  end1: string,
  start2: string,
  end2: string
): boolean => {
  // Convert times to minutes since midnight for easier comparison
  const start1Minutes = timeToMinutes(start1);
  const end1Minutes = timeToMinutes(end1);
  const start2Minutes = timeToMinutes(start2);
  const end2Minutes = timeToMinutes(end2);
  
  // Handle overnight shifts
  const isOvernight1 = end1Minutes <= start1Minutes;
  const isOvernight2 = end2Minutes <= start2Minutes;
  
  if (isOvernight1 && isOvernight2) {
    // Both shifts are overnight, they must overlap
    return true;
  } else if (isOvernight1) {
    // First shift is overnight
    return start2Minutes < end1Minutes || start2Minutes >= start1Minutes;
  } else if (isOvernight2) {
    // Second shift is overnight
    return start1Minutes < end2Minutes || start1Minutes >= start2Minutes;
  } else {
    // Neither shift is overnight
    return start1Minutes < end2Minutes && start2Minutes < end1Minutes;
  }
};

/**
 * Helper function to check if one time range is within another
 */
const isTimeWithin = (
  innerStart: string,
  innerEnd: string,
  outerStart: string,
  outerEnd: string
): boolean => {
  // Convert times to minutes since midnight for easier comparison
  const innerStartMinutes = timeToMinutes(innerStart);
  const innerEndMinutes = timeToMinutes(innerEnd);
  const outerStartMinutes = timeToMinutes(outerStart);
  const outerEndMinutes = timeToMinutes(outerEnd);
  
  // Handle overnight shifts
  const isInnerOvernight = innerEndMinutes <= innerStartMinutes;
  const isOuterOvernight = outerEndMinutes <= outerStartMinutes;
  
  if (isOuterOvernight) {
    // Outer period is overnight
    if (isInnerOvernight) {
      // Both periods are overnight
      return innerStartMinutes >= outerStartMinutes && innerEndMinutes <= outerEndMinutes;
    } else {
      // Inner period is not overnight
      return (innerStartMinutes >= outerStartMinutes || innerStartMinutes < outerEndMinutes) &&
             (innerEndMinutes <= outerEndMinutes || innerEndMinutes > outerStartMinutes);
    }
  } else {
    // Outer period is not overnight
    if (isInnerOvernight) {
      // Inner period is overnight, cannot be within a non-overnight period
      return false;
    } else {
      // Neither period is overnight
      return innerStartMinutes >= outerStartMinutes && innerEndMinutes <= outerEndMinutes;
    }
  }
};

/**
 * Helper function to convert time string to minutes since midnight
 */
const timeToMinutes = (time: string): number => {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
};

export default {
  getStaffAvailability,
  getHotelStaffAvailability,
  getAvailabilityForDate,
  createStaffAvailability,
  updateStaffAvailability,
  deleteStaffAvailability,
  isStaffAvailableForShift
};
