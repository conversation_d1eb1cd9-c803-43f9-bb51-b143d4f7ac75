import { Timestamp } from 'firebase/firestore';
import { 
  StaffShift, 
  StaffTimeOff,
  getShiftsForDate,
  getTimeOffRequestsForStaff
} from './staffSchedulingService';
import { areIntervalsOverlapping, isWithinInterval } from 'date-fns';

// Conflict types
export const CONFLICT_TYPE_SHIFT_OVERLAP = 'shift_overlap';
export const CONFLICT_TYPE_TIME_OFF = 'time_off_conflict';
export const CONFLICT_TYPE_CONSECUTIVE_SHIFTS = 'consecutive_shifts';
export const CONFLICT_TYPE_MAX_HOURS = 'max_hours_exceeded';

// Conflict severity
export const CONFLICT_SEVERITY_WARNING = 'warning';
export const CONFLICT_SEVERITY_ERROR = 'error';

// Maximum hours per day and week
export const MAX_HOURS_PER_DAY = 12;
export const MAX_HOURS_PER_WEEK = 48;
export const MIN_REST_HOURS = 8;

// Schedule conflict interface
export interface ScheduleConflict {
  type: string;
  severity: string;
  staffId: string;
  staffName: string;
  message: string;
  details: {
    shift1?: StaffShift;
    shift2?: StaffShift;
    timeOff?: StaffTimeOff;
    hours?: number;
    maxHours?: number;
  };
}

/**
 * Check for shift overlap conflicts
 */
export const checkShiftOverlap = (
  newShift: StaffShift,
  existingShifts: StaffShift[]
): ScheduleConflict[] => {
  const conflicts: ScheduleConflict[] = [];
  
  const newShiftStart = newShift.startTime.toDate();
  const newShiftEnd = newShift.endTime.toDate();
  
  // Check for overlaps with existing shifts
  for (const existingShift of existingShifts) {
    // Skip if it's the same shift (for updates)
    if (existingShift.id === newShift.id) continue;
    
    // Skip if it's for a different staff member
    if (existingShift.staffId !== newShift.staffId) continue;
    
    const existingShiftStart = existingShift.startTime.toDate();
    const existingShiftEnd = existingShift.endTime.toDate();
    
    // Check if shifts overlap
    if (areIntervalsOverlapping(
      { start: newShiftStart, end: newShiftEnd },
      { start: existingShiftStart, end: existingShiftEnd }
    )) {
      conflicts.push({
        type: CONFLICT_TYPE_SHIFT_OVERLAP,
        severity: CONFLICT_SEVERITY_ERROR,
        staffId: newShift.staffId,
        staffName: newShift.staffName,
        message: `Shift overlaps with existing ${existingShift.shiftType} shift (${existingShift.startTime.toDate().toLocaleTimeString()} - ${existingShift.endTime.toDate().toLocaleTimeString()})`,
        details: {
          shift1: newShift,
          shift2: existingShift
        }
      });
    }
    
    // Check for insufficient rest between shifts
    const hoursBetween = (existingShiftStart.getTime() - newShiftEnd.getTime()) / (1000 * 60 * 60);
    if (hoursBetween > 0 && hoursBetween < MIN_REST_HOURS) {
      conflicts.push({
        type: CONFLICT_TYPE_CONSECUTIVE_SHIFTS,
        severity: CONFLICT_SEVERITY_WARNING,
        staffId: newShift.staffId,
        staffName: newShift.staffName,
        message: `Less than ${MIN_REST_HOURS} hours of rest between shifts (${hoursBetween.toFixed(1)} hours)`,
        details: {
          shift1: newShift,
          shift2: existingShift
        }
      });
    }
    
    const hoursBetween2 = (newShiftStart.getTime() - existingShiftEnd.getTime()) / (1000 * 60 * 60);
    if (hoursBetween2 > 0 && hoursBetween2 < MIN_REST_HOURS) {
      conflicts.push({
        type: CONFLICT_TYPE_CONSECUTIVE_SHIFTS,
        severity: CONFLICT_SEVERITY_WARNING,
        staffId: newShift.staffId,
        staffName: newShift.staffName,
        message: `Less than ${MIN_REST_HOURS} hours of rest between shifts (${hoursBetween2.toFixed(1)} hours)`,
        details: {
          shift1: existingShift,
          shift2: newShift
        }
      });
    }
  }
  
  return conflicts;
};

/**
 * Check for time off conflicts
 */
export const checkTimeOffConflicts = (
  shift: StaffShift,
  timeOffRequests: StaffTimeOff[]
): ScheduleConflict[] => {
  const conflicts: ScheduleConflict[] = [];
  
  const shiftStart = shift.startTime.toDate();
  const shiftEnd = shift.endTime.toDate();
  
  // Check for approved time off that overlaps with the shift
  for (const timeOff of timeOffRequests) {
    // Skip if it's for a different staff member
    if (timeOff.staffId !== shift.staffId) continue;
    
    // Skip if not approved
    if (timeOff.status !== 'approved') continue;
    
    const timeOffStart = timeOff.startDate.toDate();
    const timeOffEnd = timeOff.endDate.toDate();
    
    // Set time to end of day for the end date
    timeOffEnd.setHours(23, 59, 59, 999);
    
    // Check if shift overlaps with time off
    if (areIntervalsOverlapping(
      { start: shiftStart, end: shiftEnd },
      { start: timeOffStart, end: timeOffEnd }
    )) {
      conflicts.push({
        type: CONFLICT_TYPE_TIME_OFF,
        severity: CONFLICT_SEVERITY_ERROR,
        staffId: shift.staffId,
        staffName: shift.staffName,
        message: `Shift scheduled during approved time off (${timeOff.type}) from ${timeOff.startDate.toDate().toLocaleDateString()} to ${timeOff.endDate.toDate().toLocaleDateString()}`,
        details: {
          shift1: shift,
          timeOff: timeOff
        }
      });
    }
  }
  
  return conflicts;
};

/**
 * Check for maximum hours conflicts
 */
export const checkMaxHoursConflicts = (
  shift: StaffShift,
  existingShifts: StaffShift[]
): ScheduleConflict[] => {
  const conflicts: ScheduleConflict[] = [];
  
  // Calculate shift duration in hours
  const shiftStart = shift.startTime.toDate();
  const shiftEnd = shift.endTime.toDate();
  const shiftDuration = (shiftEnd.getTime() - shiftStart.getTime()) / (1000 * 60 * 60);
  
  // Get shifts for the same day
  const shiftDate = shift.date.toDate();
  const sameDayShifts = existingShifts.filter(existingShift => {
    // Skip if it's the same shift (for updates)
    if (existingShift.id === shift.id) return false;
    
    // Skip if it's for a different staff member
    if (existingShift.staffId !== shift.staffId) return false;
    
    // Check if it's on the same day
    const existingShiftDate = existingShift.date.toDate();
    return existingShiftDate.getDate() === shiftDate.getDate() &&
           existingShiftDate.getMonth() === shiftDate.getMonth() &&
           existingShiftDate.getFullYear() === shiftDate.getFullYear();
  });
  
  // Calculate total hours for the day
  let totalHoursDay = shiftDuration;
  for (const existingShift of sameDayShifts) {
    const existingShiftStart = existingShift.startTime.toDate();
    const existingShiftEnd = existingShift.endTime.toDate();
    const existingShiftDuration = (existingShiftEnd.getTime() - existingShiftStart.getTime()) / (1000 * 60 * 60);
    totalHoursDay += existingShiftDuration;
  }
  
  // Check if daily hours exceed maximum
  if (totalHoursDay > MAX_HOURS_PER_DAY) {
    conflicts.push({
      type: CONFLICT_TYPE_MAX_HOURS,
      severity: CONFLICT_SEVERITY_WARNING,
      staffId: shift.staffId,
      staffName: shift.staffName,
      message: `Daily working hours (${totalHoursDay.toFixed(1)}) exceed maximum (${MAX_HOURS_PER_DAY})`,
      details: {
        shift1: shift,
        hours: totalHoursDay,
        maxHours: MAX_HOURS_PER_DAY
      }
    });
  }
  
  // Get shifts for the same week
  const weekStart = new Date(shiftDate);
  weekStart.setDate(shiftDate.getDate() - shiftDate.getDay()); // Start of week (Sunday)
  weekStart.setHours(0, 0, 0, 0);
  
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekStart.getDate() + 6); // End of week (Saturday)
  weekEnd.setHours(23, 59, 59, 999);
  
  const sameWeekShifts = existingShifts.filter(existingShift => {
    // Skip if it's the same shift (for updates)
    if (existingShift.id === shift.id) return false;
    
    // Skip if it's for a different staff member
    if (existingShift.staffId !== shift.staffId) return false;
    
    // Check if it's in the same week
    const existingShiftDate = existingShift.date.toDate();
    return existingShiftDate >= weekStart && existingShiftDate <= weekEnd;
  });
  
  // Calculate total hours for the week
  let totalHoursWeek = shiftDuration;
  for (const existingShift of sameWeekShifts) {
    const existingShiftStart = existingShift.startTime.toDate();
    const existingShiftEnd = existingShift.endTime.toDate();
    const existingShiftDuration = (existingShiftEnd.getTime() - existingShiftStart.getTime()) / (1000 * 60 * 60);
    totalHoursWeek += existingShiftDuration;
  }
  
  // Check if weekly hours exceed maximum
  if (totalHoursWeek > MAX_HOURS_PER_WEEK) {
    conflicts.push({
      type: CONFLICT_TYPE_MAX_HOURS,
      severity: CONFLICT_SEVERITY_WARNING,
      staffId: shift.staffId,
      staffName: shift.staffName,
      message: `Weekly working hours (${totalHoursWeek.toFixed(1)}) exceed maximum (${MAX_HOURS_PER_WEEK})`,
      details: {
        shift1: shift,
        hours: totalHoursWeek,
        maxHours: MAX_HOURS_PER_WEEK
      }
    });
  }
  
  return conflicts;
};

/**
 * Check for all schedule conflicts
 */
export const checkAllConflicts = async (
  shift: StaffShift,
  hotelId: string
): Promise<ScheduleConflict[]> => {
  try {
    // Get existing shifts for the date
    const shiftDate = shift.date.toDate();
    const existingShifts = await getShiftsForDate(hotelId, shiftDate);
    
    // Get time off requests for the staff member
    const timeOffRequests = await getTimeOffRequestsForStaff(shift.staffId);
    
    // Check for all types of conflicts
    const shiftOverlapConflicts = checkShiftOverlap(shift, existingShifts);
    const timeOffConflicts = checkTimeOffConflicts(shift, timeOffRequests);
    const maxHoursConflicts = checkMaxHoursConflicts(shift, existingShifts);
    
    // Combine all conflicts
    return [
      ...shiftOverlapConflicts,
      ...timeOffConflicts,
      ...maxHoursConflicts
    ];
  } catch (error) {
    console.error('Error checking schedule conflicts:', error);
    return [];
  }
};

export default {
  checkShiftOverlap,
  checkTimeOffConflicts,
  checkMaxHoursConflicts,
  checkAllConflicts
};
