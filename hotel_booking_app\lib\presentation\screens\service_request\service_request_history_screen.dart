import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:hotel_booking_app/data/models/service_request_model.dart';
import 'package:hotel_booking_app/data/models/booking_model.dart';
import 'package:hotel_booking_app/data/services/auth_service.dart';
import 'package:hotel_booking_app/data/services/service_request_service.dart';
import 'package:hotel_booking_app/presentation/screens/service_request/service_request_details_screen.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_app_bar.dart';
import 'package:hotel_booking_app/presentation/widgets/error_widget.dart';
import 'package:hotel_booking_app/presentation/widgets/loading_widget.dart';

class ServiceRequestHistoryScreen extends StatefulWidget {
  const ServiceRequestHistoryScreen({super.key});

  @override
  State<ServiceRequestHistoryScreen> createState() =>
      _ServiceRequestHistoryScreenState();
}

class _ServiceRequestHistoryScreenState
    extends State<ServiceRequestHistoryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  String? _errorMessage;
  List<BaseServiceRequest> _serviceRequests = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadServiceRequests();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadServiceRequests() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final serviceRequestService =
          Provider.of<ServiceRequestService>(context, listen: false);

      final user = authService.user;
      if (user == null) {
        throw Exception('You must be logged in to view service requests');
      }

      final requests =
          await serviceRequestService.getServiceRequestsForUser(user.uid);

      setState(() {
        _serviceRequests = requests;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  List<BaseServiceRequest> _getFilteredRequests(String status) {
    if (status == 'all') {
      return _serviceRequests;
    }
    return _serviceRequests
        .where((request) => request.status == status)
        .toList();
  }

  String _getServiceTypeIcon(String type) {
    switch (type) {
      case 'cleaning':
        return '🧹';
      case 'food':
        return '🍽️';
      case 'maintenance':
        return '🔧';
      default:
        return '📋';
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'in_progress':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatStatus(String status) {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  }

  String _formatDate(DateTime date) {
    return DateFormat('MMM d, yyyy • h:mm a').format(date);
  }

  void _viewServiceRequestDetails(BaseServiceRequest request) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ServiceRequestDetailsScreen(
          request: request,
          booking: Booking(
            id: 'temp-id',
            hotelId: request.hotelId,
            hotelName: 'Hotel Name',
            roomId: 'temp-room-id',
            roomName: request.roomNumber,
            userId: request.guestId ?? '',
            checkIn: DateTime.now(),
            checkOut: DateTime.now().add(const Duration(days: 1)),
            guests: 1,
            totalPrice: 0,
            status: 'active',
            paymentStatus: 'paid',
            createdAt: DateTime.now(),
          ),
        ),
      ),
    );
  }

  Widget _buildServiceRequestCard(BaseServiceRequest request) {
    final requestTime = request.requestTime.toDate();

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _viewServiceRequestDetails(request),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        _getServiceTypeIcon(request.type),
                        style: const TextStyle(fontSize: 24),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        request.type.substring(0, 1).toUpperCase() +
                            request.type.substring(1),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(request.status).withAlpha(25),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: _getStatusColor(request.status),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      _formatStatus(request.status),
                      style: TextStyle(
                        color: _getStatusColor(request.status),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  const Icon(Icons.hotel, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'Room ${request.roomNumber}',
                    style: const TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.access_time, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    _formatDate(requestTime),
                    style: const TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              if (request.notes.isNotEmpty) ...[
                const SizedBox(height: 12),
                Text(
                  request.notes,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                ),
              ],
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    'Priority: ${request.priority}',
                    style: TextStyle(
                      color: request.priority == 'high'
                          ? Colors.red
                          : request.priority == 'medium'
                              ? Colors.orange
                              : Colors.blue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabContent(String status) {
    final filteredRequests = _getFilteredRequests(status);

    if (filteredRequests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.inbox,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              status == 'all'
                  ? 'No service requests found'
                  : 'No ${_formatStatus(status).toLowerCase()} service requests',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.only(top: 8, bottom: 16),
      itemCount: filteredRequests.length,
      itemBuilder: (context, index) {
        return _buildServiceRequestCard(filteredRequests[index]);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Service Request History',
        showBackButton: true,
      ),
      body: _isLoading
          ? const LoadingWidget()
          : _errorMessage != null
              ? CustomErrorWidget(
                  message: _errorMessage!,
                  onRetry: _loadServiceRequests,
                )
              : Column(
                  children: [
                    TabBar(
                      controller: _tabController,
                      labelColor: Theme.of(context).primaryColor,
                      unselectedLabelColor: Colors.grey,
                      indicatorColor: Theme.of(context).primaryColor,
                      tabs: const [
                        Tab(text: 'All'),
                        Tab(text: 'Pending'),
                        Tab(text: 'Active'),
                        Tab(text: 'Completed'),
                      ],
                    ),
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          _buildTabContent('all'),
                          _buildTabContent('pending'),
                          _buildTabContent('in_progress'),
                          _buildTabContent('completed'),
                        ],
                      ),
                    ),
                  ],
                ),
    );
  }
}
