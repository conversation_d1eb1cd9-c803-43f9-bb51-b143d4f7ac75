import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Button,
  CircularProgress,
  Alert,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import {
  VerifiedUser as VerifiedUserIcon,
  Person as PersonIcon,
  CreditCard as CardIcon,
  CalendarToday as CalendarIcon,
  Home as AddressIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  ArrowBack as BackIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Comment as CommentIcon
} from '@mui/icons-material';
import { getVerificationById, updateVerificationStatus } from '../../services/verificationService';
import { AadhaarVerificationData } from '../../components/AadhaarVerificationCard';
import { format } from 'date-fns';

const VendorVerificationDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [verification, setVerification] = useState<AadhaarVerificationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject' | null>(null);
  const [comment, setComment] = useState('');
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    const fetchVerification = async () => {
      if (!id) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const data = await getVerificationById(id);
        setVerification(data);
      } catch (err: any) {
        setError(err.message || 'Failed to load verification details');
        console.error('Error fetching verification:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchVerification();
  }, [id]);

  const handleBack = () => {
    navigate(-1);
  };

  const openDialog = (type: 'approve' | 'reject') => {
    setActionType(type);
    setDialogOpen(true);
  };

  const closeDialog = () => {
    setDialogOpen(false);
    setActionType(null);
    setComment('');
  };

  const handleStatusUpdate = async () => {
    if (!id || !actionType) return;
    
    setUpdating(true);
    
    try {
      const newStatus = actionType === 'approve' ? 'verified' : 'failed';
      await updateVerificationStatus(id, newStatus, comment);
      
      // Update local state
      if (verification) {
        setVerification({
          ...verification,
          status: newStatus,
          adminComment: comment,
          verifiedAt: new Date()
        });
      }
      
      closeDialog();
    } catch (err: any) {
      setError(err.message || `Failed to ${actionType} verification`);
      console.error(`Error ${actionType}ing verification:`, err);
    } finally {
      setUpdating(false);
    }
  };

  // Format date
  const formatDate = (date?: Date | string) => {
    if (!date) return 'N/A';
    return format(new Date(date), 'MMM dd, yyyy, h:mm a');
  };

  // Get status info
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'verified':
        return {
          color: 'success',
          icon: <VerifiedUserIcon />,
          text: 'Verified'
        };
      case 'pending':
        return {
          color: 'warning',
          icon: <CommentIcon />,
          text: 'Pending'
        };
      case 'failed':
        return {
          color: 'error',
          icon: <RejectIcon />,
          text: 'Failed'
        };
      default:
        return {
          color: 'default',
          icon: <CommentIcon />,
          text: 'Unknown'
        };
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button startIcon={<BackIcon />} onClick={handleBack}>
          Back to Verifications
        </Button>
      </Box>
    );
  }

  if (!verification) {
    return (
      <Box p={3}>
        <Alert severity="warning" sx={{ mb: 3 }}>
          Verification not found
        </Alert>
        <Button startIcon={<BackIcon />} onClick={handleBack}>
          Back to Verifications
        </Button>
      </Box>
    );
  }

  const statusInfo = getStatusInfo(verification.status);

  return (
    <Box>
      <Box display="flex" alignItems="center" mb={3}>
        <Button startIcon={<BackIcon />} onClick={handleBack} sx={{ mr: 2 }}>
          Back
        </Button>
        <Typography variant="h4">Verification Details</Typography>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar
                  src={verification.photoUrl}
                  alt={verification.fullName}
                  sx={{ width: 80, height: 80, mr: 2 }}
                />
                <Box>
                  <Typography variant="h5">{verification.fullName}</Typography>
                  <Chip
                    label={statusInfo.text}
                    color={statusInfo.color as any}
                    icon={statusInfo.icon}
                    sx={{ mt: 1 }}
                  />
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="h6" gutterBottom>
                Personal Information
              </Typography>

              <List>
                <ListItem>
                  <ListItemIcon>
                    <CardIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Aadhaar Number"
                    secondary={verification.maskedAadhaarNumber}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <PersonIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Gender"
                    secondary={verification.gender || 'Not specified'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CalendarIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Date of Birth"
                    secondary={verification.dob ? formatDate(verification.dob) : 'Not specified'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <AddressIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Address"
                    secondary={verification.address || 'Not specified'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <PhoneIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Phone Number"
                    secondary={verification.phoneNumber || 'Not specified'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <EmailIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Email"
                    secondary={verification.email || 'Not specified'}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Verification Status
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">
                  Status
                </Typography>
                <Chip
                  label={statusInfo.text}
                  color={statusInfo.color as any}
                  icon={statusInfo.icon}
                  sx={{ mt: 0.5 }}
                />
              </Box>

              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">
                  Submitted On
                </Typography>
                <Typography variant="body1">
                  {formatDate(verification.createdAt)}
                </Typography>
              </Box>

              {verification.verifiedAt && (
                <Box mb={2}>
                  <Typography variant="body2" color="text.secondary">
                    Verified On
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(verification.verifiedAt)}
                  </Typography>
                </Box>
              )}

              {verification.adminComment && (
                <Box mb={2}>
                  <Typography variant="body2" color="text.secondary">
                    Admin Comment
                  </Typography>
                  <Typography variant="body1">
                    {verification.adminComment}
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>

          {verification.status === 'pending' && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Actions
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Box display="flex" flexDirection="column" gap={2}>
                  <Button
                    variant="contained"
                    color="success"
                    startIcon={<ApproveIcon />}
                    onClick={() => openDialog('approve')}
                    fullWidth
                  >
                    Approve Verification
                  </Button>
                  <Button
                    variant="contained"
                    color="error"
                    startIcon={<RejectIcon />}
                    onClick={() => openDialog('reject')}
                    fullWidth
                  >
                    Reject Verification
                  </Button>
                </Box>
              </CardContent>
            </Card>
          )}
        </Grid>
      </Grid>

      {/* Approval/Rejection Dialog */}
      <Dialog open={dialogOpen} onClose={closeDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {actionType === 'approve' ? 'Approve Verification' : 'Reject Verification'}
        </DialogTitle>
        <DialogContent>
          <Typography paragraph>
            {actionType === 'approve'
              ? 'Are you sure you want to approve this verification?'
              : 'Are you sure you want to reject this verification?'}
          </Typography>
          <TextField
            label="Comment (Optional)"
            multiline
            rows={4}
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            fullWidth
            margin="normal"
            placeholder={
              actionType === 'approve'
                ? 'Add any notes about this approval'
                : 'Please provide a reason for rejection'
            }
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDialog}>Cancel</Button>
          <Button
            onClick={handleStatusUpdate}
            color={actionType === 'approve' ? 'success' : 'error'}
            variant="contained"
            disabled={updating}
          >
            {updating ? (
              <CircularProgress size={24} />
            ) : actionType === 'approve' ? (
              'Approve'
            ) : (
              'Reject'
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VendorVerificationDetails;
