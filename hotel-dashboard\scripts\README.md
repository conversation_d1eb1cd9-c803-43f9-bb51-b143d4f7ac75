# Firebase Initialization Scripts

This directory contains scripts to initialize your Firebase Firestore database with collections and sample data for the hotel booking system.

## Prerequisites

1. Firebase project set up
2. Firebase CLI installed and logged in
3. Service account key for Firebase Admin SDK

## Getting a Service Account Key

To use the `init-firestore-admin.js` script, you need a service account key:

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Click on the gear icon (⚙️) next to "Project Overview" to go to Project settings
4. Go to the "Service accounts" tab
5. Click "Generate new private key" button
6. Save the downloaded JSON file as `service-account.json` in the `scripts` directory

## Initializing Firestore

### Using Firebase Admin SDK (Recommended)

This method uses the Firebase Admin SDK which has full access to your Firestore database:

```bash
npm run init-firestore-admin
```

### Using Firebase Client SDK

This method uses the Firebase Client SDK and requires proper security rules:

```bash
npm run init-firestore
```

## What Gets Created

The initialization scripts create the following:

1. **Users Collection**:
   - Super admin user
   - Vendor user

2. **Hotels Collection**:
   - Sample hotel

3. **Room Types Collection**:
   - Standard room type
   - Deluxe room type
   - Suite room type

4. **Rooms Collection**:
   - Multiple rooms of different types

5. **Pricing Rules Collection**:
   - Weekend premium rule
   - Summer season rule
   - Long stay discount rule

6. **Permissions Collection**:
   - Role-based permissions for super admin, vendor, and staff

## Troubleshooting

If you encounter any issues:

1. Make sure your service account key is correctly placed in the scripts directory
2. Check that you have the necessary permissions in your Firebase project
3. Verify that your Firestore security rules allow the operations
4. Check the Firebase console for any error messages
