/**
 * <PERSON><PERSON><PERSON> to create sample notifications for testing
 * 
 * This script:
 * 1. Creates sample notifications for the admin user
 * 
 * Run with: node scripts/create-sample-notification.js
 */

const { initializeApp } = require('firebase/app');
const { 
  getAuth, 
  signInWithEmailAndPassword,
  signOut
} = require('firebase/auth');
const { 
  getFirestore,
  collection,
  doc,
  setDoc,
  serverTimestamp
} = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI",
  authDomain: "linkinblink-hotel.firebaseapp.com",
  projectId: "linkinblink-hotel",
  storageBucket: "linkinblink-hotel.firebasestorage.app",
  messagingSenderId: "132613661307",
  appId: "1:132613661307:web:a9e5c8f4c2b8e9a9e5c8f4",
  measurementId: "G-MEASUREMENT_ID"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Sample notifications
const sampleNotifications = [
  {
    title: 'New Booking',
    message: 'John Doe has booked a Deluxe Room at Grand Hotel',
    type: 'booking',
    isRead: false,
    relatedId: 'booking1'
  },
  {
    title: 'System Update',
    message: 'The system will be undergoing maintenance on Saturday at 2 AM',
    type: 'system',
    isRead: false
  },
  {
    title: 'Payment Received',
    message: 'Payment of $350 has been received for booking #12345',
    type: 'info',
    isRead: true,
    relatedId: 'booking2'
  },
  {
    title: 'Booking Cancelled',
    message: 'A booking has been cancelled for Seaside Resort',
    type: 'alert',
    isRead: false,
    relatedId: 'booking3'
  }
];

/**
 * Create sample notifications
 */
async function createSampleNotifications() {
  try {
    console.log('Signing in as admin...');
    
    // Sign in as admin
    await signInWithEmailAndPassword(auth, '<EMAIL>', 'Admin123!');
    console.log('Successfully signed in as admin');
    
    // Get the current user
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('Failed to get current user');
    }
    
    console.log('Creating sample notifications...');
    
    // Create notifications
    for (const notification of sampleNotifications) {
      const notificationRef = doc(collection(db, 'notifications'));
      
      await setDoc(notificationRef, {
        ...notification,
        userId: user.uid,
        createdAt: serverTimestamp()
      });
      
      console.log(`Notification "${notification.title}" created successfully`);
    }
    
    console.log('Sample notifications created successfully');
    
    // Sign out
    await signOut(auth);
    console.log('Signed out');
    
    console.log('Script completed');
    process.exit(0);
  } catch (error) {
    console.error('Error creating sample notifications:', error);
    process.exit(1);
  }
}

// Run the function
createSampleNotifications();
