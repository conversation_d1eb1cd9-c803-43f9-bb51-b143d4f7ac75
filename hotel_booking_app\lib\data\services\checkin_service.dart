import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/data/models/booking_model.dart';
import 'package:hotel_booking_app/data/models/aadhaar_verification_model.dart';
import 'package:hotel_booking_app/data/services/aadhaar_verification_service.dart';
import 'package:hotel_booking_app/data/services/pdf_service.dart';

class CheckinService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AadhaarVerificationService _aadhaarVerificationService = AadhaarVerificationService();
  
  bool _isLoading = false;
  String? _error;
  Booking? _currentBooking;
  
  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  Booking? get currentBooking => _currentBooking;
  
  // Check if a booking can be checked in
  Future<bool> canCheckIn(String bookingId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Get the booking
      final docSnapshot = await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(bookingId)
          .get();
      
      if (!docSnapshot.exists) {
        _error = 'Booking not found';
        _isLoading = false;
        notifyListeners();
        return false;
      }
      
      final booking = Booking.fromMap({
        'id': docSnapshot.id,
        ...docSnapshot.data()!,
      });
      
      _currentBooking = booking;
      
      // Check if the booking is already checked in
      if (booking.checkedInAt != null) {
        _error = 'Booking is already checked in';
        _isLoading = false;
        notifyListeners();
        return false;
      }
      
      // Check if the booking is confirmed
      if (booking.status != AppConstants.statusConfirmed) {
        _error = 'Booking is not confirmed';
        _isLoading = false;
        notifyListeners();
        return false;
      }
      
      // Check if the booking is paid
      if (booking.paymentStatus != AppConstants.paymentCompleted) {
        _error = 'Booking is not paid';
        _isLoading = false;
        notifyListeners();
        return false;
      }
      
      // Check if the user has verified Aadhaar
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        _error = 'User not authenticated';
        _isLoading = false;
        notifyListeners();
        return false;
      }
      
      // Check if the booking belongs to the current user
      if (booking.userId != userId) {
        _error = 'Booking does not belong to the current user';
        _isLoading = false;
        notifyListeners();
        return false;
      }
      
      // Check if the user has verified Aadhaar
      final verification = await _aadhaarVerificationService.getVerificationByUserId(userId);
      if (verification == null || verification.status != AadhaarVerificationStatus.verified) {
        _error = 'Aadhaar verification is required for check-in';
        _isLoading = false;
        notifyListeners();
        return false;
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }
  
  // Check in a booking
  Future<bool> checkIn(String bookingId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Check if the booking can be checked in
      final canCheck = await canCheckIn(bookingId);
      if (!canCheck) {
        return false;
      }
      
      // Get the user's Aadhaar verification
      final userId = _auth.currentUser!.uid;
      final verification = await _aadhaarVerificationService.getVerificationByUserId(userId);
      
      // Update the booking
      await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(bookingId)
          .update({
        'status': AppConstants.statusCheckedIn,
        'checkedInAt': FieldValue.serverTimestamp(),
        'isAadhaarVerified': true,
        'aadhaarVerifiedAt': verification!.verifiedAt != null
            ? Timestamp.fromDate(verification.verifiedAt!)
            : FieldValue.serverTimestamp(),
        'aadhaarVerificationId': verification.id,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      // Get the updated booking
      final docSnapshot = await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(bookingId)
          .get();
      
      _currentBooking = Booking.fromMap({
        'id': docSnapshot.id,
        ...docSnapshot.data()!,
      });
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }
  
  // Check out a booking
  Future<bool> checkOut(String bookingId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Get the booking
      final docSnapshot = await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(bookingId)
          .get();
      
      if (!docSnapshot.exists) {
        _error = 'Booking not found';
        _isLoading = false;
        notifyListeners();
        return false;
      }
      
      final booking = Booking.fromMap({
        'id': docSnapshot.id,
        ...docSnapshot.data()!,
      });
      
      // Check if the booking is checked in
      if (booking.checkedInAt == null) {
        _error = 'Booking is not checked in';
        _isLoading = false;
        notifyListeners();
        return false;
      }
      
      // Check if the booking is already checked out
      if (booking.checkedOutAt != null) {
        _error = 'Booking is already checked out';
        _isLoading = false;
        notifyListeners();
        return false;
      }
      
      // Check if the booking belongs to the current user
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        _error = 'User not authenticated';
        _isLoading = false;
        notifyListeners();
        return false;
      }
      
      if (booking.userId != userId) {
        _error = 'Booking does not belong to the current user';
        _isLoading = false;
        notifyListeners();
        return false;
      }
      
      // Update the booking
      await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(bookingId)
          .update({
        'status': AppConstants.statusCheckedOut,
        'checkedOutAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      // Get the updated booking
      final updatedSnapshot = await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(bookingId)
          .get();
      
      _currentBooking = Booking.fromMap({
        'id': updatedSnapshot.id,
        ...updatedSnapshot.data()!,
      });
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }
  
  // Generate check-in confirmation PDF
  Future<String?> generateCheckinConfirmation(String bookingId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Get the booking
      final docSnapshot = await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(bookingId)
          .get();
      
      if (!docSnapshot.exists) {
        _error = 'Booking not found';
        _isLoading = false;
        notifyListeners();
        return null;
      }
      
      final booking = Booking.fromMap({
        'id': docSnapshot.id,
        ...docSnapshot.data()!,
      });
      
      // Check if the booking is checked in
      if (booking.checkedInAt == null) {
        _error = 'Booking is not checked in';
        _isLoading = false;
        notifyListeners();
        return null;
      }
      
      // Get the user's Aadhaar verification
      AadhaarVerification? verification;
      if (booking.aadhaarVerificationId != null) {
        verification = await _aadhaarVerificationService.getVerificationById(booking.aadhaarVerificationId!);
      }
      
      // Generate the PDF
      final pdfBytes = await PdfService.generateBookingConfirmation(
        booking,
        isCheckedIn: true,
        aadhaarVerification: verification,
      );
      
      // Save the PDF
      final fileName = 'checkin_confirmation_${booking.id.substring(0, 8)}.pdf';
      final filePath = await PdfService.savePdfFile(fileName, pdfBytes);
      
      _isLoading = false;
      notifyListeners();
      return filePath;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return null;
    }
  }
  
  // Generate booking confirmation PDF
  Future<String?> generateBookingConfirmation(String bookingId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Get the booking
      final docSnapshot = await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(bookingId)
          .get();
      
      if (!docSnapshot.exists) {
        _error = 'Booking not found';
        _isLoading = false;
        notifyListeners();
        return null;
      }
      
      final booking = Booking.fromMap({
        'id': docSnapshot.id,
        ...docSnapshot.data()!,
      });
      
      // Generate the PDF
      final pdfBytes = await PdfService.generateBookingConfirmation(booking);
      
      // Save the PDF
      final fileName = 'booking_confirmation_${booking.id.substring(0, 8)}.pdf';
      final filePath = await PdfService.savePdfFile(fileName, pdfBytes);
      
      _isLoading = false;
      notifyListeners();
      return filePath;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return null;
    }
  }
}
