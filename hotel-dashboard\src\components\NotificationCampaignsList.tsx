import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Button,
  CircularProgress,
  Alert,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  Send as SendIcon,
  Cancel as CancelIcon,
  BarChart as ChartIcon,
  Refresh as RefreshIcon,
  NotificationsActive as NotificationIcon
} from '@mui/icons-material';
import { 
  getNotificationCampaigns, 
  processCampaign, 
  cancelNotificationCampaign, 
  NotificationCampaign 
} from '../services/notificationService';

interface NotificationCampaignsListProps {
  hotelId: string;
  onCreateNew?: () => void;
}

const NotificationCampaignsList: React.FC<NotificationCampaignsListProps> = ({
  hotelId,
  onCreateNew
}) => {
  const [campaigns, setCampaigns] = useState<NotificationCampaign[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedCampaign, setSelectedCampaign] = useState<NotificationCampaign | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [statsOpen, setStatsOpen] = useState(false);
  const [processingCampaign, setProcessingCampaign] = useState<string | null>(null);
  
  // Load campaigns on component mount and when hotelId changes
  useEffect(() => {
    if (hotelId) {
      fetchCampaigns();
    }
  }, [hotelId]);
  
  // Fetch notification campaigns
  const fetchCampaigns = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const campaignsList = await getNotificationCampaigns(hotelId);
      setCampaigns(campaignsList);
      
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching campaigns:', err);
      setError(err.message || 'Failed to load notification campaigns');
      setLoading(false);
    }
  };
  
  // Handle view campaign details
  const handleViewDetails = (campaign: NotificationCampaign) => {
    setSelectedCampaign(campaign);
    setDetailsOpen(true);
  };
  
  // Handle view campaign stats
  const handleViewStats = (campaign: NotificationCampaign) => {
    setSelectedCampaign(campaign);
    setStatsOpen(true);
  };
  
  // Handle send campaign now
  const handleSendCampaign = async (campaignId: string) => {
    try {
      setProcessingCampaign(campaignId);
      
      await processCampaign(campaignId);
      
      // Refresh campaigns list
      fetchCampaigns();
      
      setProcessingCampaign(null);
    } catch (err: any) {
      console.error('Error sending campaign:', err);
      setError(err.message || 'Failed to send notification campaign');
      setProcessingCampaign(null);
    }
  };
  
  // Handle cancel campaign
  const handleCancelCampaign = async (campaignId: string) => {
    try {
      setProcessingCampaign(campaignId);
      
      await cancelNotificationCampaign(campaignId);
      
      // Refresh campaigns list
      fetchCampaigns();
      
      setProcessingCampaign(null);
    } catch (err: any) {
      console.error('Error cancelling campaign:', err);
      setError(err.message || 'Failed to cancel notification campaign');
      setProcessingCampaign(null);
    }
  };
  
  // Close details dialog
  const handleCloseDetails = () => {
    setDetailsOpen(false);
    setSelectedCampaign(null);
  };
  
  // Close stats dialog
  const handleCloseStats = () => {
    setStatsOpen(false);
    setSelectedCampaign(null);
  };
  
  // Format date
  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleString();
  };
  
  // Get status chip
  const getStatusChip = (status: string) => {
    switch (status) {
      case 'draft':
        return <Chip label="Draft" color="default" size="small" />;
      case 'scheduled':
        return <Chip label="Scheduled" color="primary" size="small" />;
      case 'sent':
        return <Chip label="Sent" color="success" size="small" />;
      case 'cancelled':
        return <Chip label="Cancelled" color="error" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };
  
  return (
    <Paper sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <NotificationIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6">
            Notification Campaigns
          </Typography>
        </Box>
        <Box>
          <Button
            startIcon={<RefreshIcon />}
            variant="outlined"
            onClick={fetchCampaigns}
            sx={{ mr: 2 }}
          >
            Refresh
          </Button>
          <Button
            startIcon={<NotificationIcon />}
            variant="contained"
            onClick={onCreateNew}
          >
            Create New Campaign
          </Button>
        </Box>
      </Box>
      
      <Divider sx={{ mb: 3 }} />
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : campaigns.length === 0 ? (
        <Alert severity="info">
          No notification campaigns found. Create your first campaign to engage with your mobile app users.
        </Alert>
      ) : (
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Campaign Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Scheduled/Sent</TableCell>
                <TableCell>Stats</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {campaigns.map((campaign) => (
                <TableRow key={campaign.id}>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {campaign.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {campaign.title}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    {campaign.type.charAt(0).toUpperCase() + campaign.type.slice(1)}
                  </TableCell>
                  <TableCell>
                    {getStatusChip(campaign.status)}
                  </TableCell>
                  <TableCell>
                    {campaign.createdAt ? formatDate(campaign.createdAt) : 'N/A'}
                  </TableCell>
                  <TableCell>
                    {campaign.status === 'scheduled' ? (
                      campaign.schedule?.scheduledDate ? 
                        formatDate(campaign.schedule.scheduledDate) : 
                        'Not scheduled'
                    ) : campaign.status === 'sent' ? (
                      campaign.sentAt ? formatDate(campaign.sentAt) : 'Sent'
                    ) : (
                      '-'
                    )}
                  </TableCell>
                  <TableCell>
                    {campaign.status === 'sent' && campaign.stats ? (
                      <Typography variant="body2">
                        {campaign.stats.totalSent} sent, {campaign.stats.opened} opened
                      </Typography>
                    ) : (
                      '-'
                    )}
                  </TableCell>
                  <TableCell align="right">
                    <Tooltip title="View Details">
                      <IconButton
                        size="small"
                        onClick={() => handleViewDetails(campaign)}
                      >
                        <VisibilityIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    
                    {campaign.status === 'sent' && (
                      <Tooltip title="View Stats">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleViewStats(campaign)}
                        >
                          <ChartIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                    
                    {(campaign.status === 'draft' || campaign.status === 'scheduled') && (
                      <Tooltip title="Send Now">
                        <IconButton
                          size="small"
                          color="success"
                          onClick={() => campaign.id && handleSendCampaign(campaign.id)}
                          disabled={!!processingCampaign}
                        >
                          {processingCampaign === campaign.id ? (
                            <CircularProgress size={20} />
                          ) : (
                            <SendIcon fontSize="small" />
                          )}
                        </IconButton>
                      </Tooltip>
                    )}
                    
                    {(campaign.status === 'draft' || campaign.status === 'scheduled') && (
                      <Tooltip title="Cancel Campaign">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => campaign.id && handleCancelCampaign(campaign.id)}
                          disabled={!!processingCampaign}
                        >
                          <CancelIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
      
      {/* Campaign Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={handleCloseDetails}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Campaign Details</DialogTitle>
        <DialogContent>
          {selectedCampaign && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedCampaign.name}
              </Typography>
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="subtitle1" gutterBottom fontWeight="medium">
                Notification Content
              </Typography>
              
              <Box sx={{ 
                p: 2, 
                bgcolor: 'background.paper', 
                borderRadius: 1,
                boxShadow: 1,
                mb: 3
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <NotificationIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="subtitle2" color="primary">
                    {selectedCampaign.type.charAt(0).toUpperCase() + selectedCampaign.type.slice(1)} Notification
                  </Typography>
                </Box>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  {selectedCampaign.title}
                </Typography>
                <Typography variant="body2">
                  {selectedCampaign.message}
                </Typography>
              </Box>
              
              <Typography variant="subtitle1" gutterBottom fontWeight="medium">
                Target Audience
              </Typography>
              <Typography variant="body2" paragraph>
                {selectedCampaign.targetAudience?.allUsers ? 
                  'All users' : 
                  'Filtered users'
                }
                {selectedCampaign.targetAudience?.filters?.hasUpcomingBooking && 
                  ' • Users with upcoming bookings'}
                {selectedCampaign.targetAudience?.filters?.hasPastBooking && 
                  ' • Users with past bookings'}
              </Typography>
              
              <Typography variant="subtitle1" gutterBottom fontWeight="medium">
                Schedule
              </Typography>
              <Typography variant="body2" paragraph>
                {selectedCampaign.schedule?.sendImmediately ? 
                  'Send immediately' : 
                  `Scheduled for ${selectedCampaign.schedule?.scheduledDate ? 
                    formatDate(selectedCampaign.schedule.scheduledDate) : 
                    'later'}`
                }
              </Typography>
              
              <Typography variant="subtitle1" gutterBottom fontWeight="medium">
                Status
              </Typography>
              <Box sx={{ mb: 2 }}>
                {getStatusChip(selectedCampaign.status)}
                {selectedCampaign.status === 'sent' && selectedCampaign.sentAt && (
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Sent at: {formatDate(selectedCampaign.sentAt)}
                  </Typography>
                )}
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDetails}>Close</Button>
          {selectedCampaign && selectedCampaign.status === 'draft' && (
            <Button 
              color="primary"
              startIcon={<SendIcon />}
              onClick={() => {
                handleCloseDetails();
                selectedCampaign.id && handleSendCampaign(selectedCampaign.id);
              }}
            >
              Send Now
            </Button>
          )}
        </DialogActions>
      </Dialog>
      
      {/* Campaign Stats Dialog */}
      <Dialog
        open={statsOpen}
        onClose={handleCloseStats}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Campaign Statistics</DialogTitle>
        <DialogContent>
          {selectedCampaign && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedCampaign.name}
              </Typography>
              
              <Divider sx={{ my: 2 }} />
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 3 }}>
                <Paper sx={{ p: 2, flexGrow: 1, minWidth: '200px' }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Total Sent
                  </Typography>
                  <Typography variant="h4">
                    {selectedCampaign.stats?.totalSent || 0}
                  </Typography>
                </Paper>
                
                <Paper sx={{ p: 2, flexGrow: 1, minWidth: '200px' }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Delivered
                  </Typography>
                  <Typography variant="h4">
                    {selectedCampaign.stats?.delivered || 0}
                  </Typography>
                </Paper>
                
                <Paper sx={{ p: 2, flexGrow: 1, minWidth: '200px' }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Opened
                  </Typography>
                  <Typography variant="h4">
                    {selectedCampaign.stats?.opened || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedCampaign.stats?.totalSent ? 
                      `${Math.round((selectedCampaign.stats.opened / selectedCampaign.stats.totalSent) * 100)}%` : 
                      '0%'
                    }
                  </Typography>
                </Paper>
                
                <Paper sx={{ p: 2, flexGrow: 1, minWidth: '200px' }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Clicked
                  </Typography>
                  <Typography variant="h4">
                    {selectedCampaign.stats?.clicked || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedCampaign.stats?.totalSent ? 
                      `${Math.round((selectedCampaign.stats.clicked / selectedCampaign.stats.totalSent) * 100)}%` : 
                      '0%'
                    }
                  </Typography>
                </Paper>
              </Box>
              
              <Typography variant="subtitle1" gutterBottom>
                Campaign Details
              </Typography>
              <Typography variant="body2">
                <strong>Type:</strong> {selectedCampaign.type.charAt(0).toUpperCase() + selectedCampaign.type.slice(1)}
              </Typography>
              <Typography variant="body2">
                <strong>Sent at:</strong> {selectedCampaign.sentAt ? formatDate(selectedCampaign.sentAt) : 'N/A'}
              </Typography>
              <Typography variant="body2">
                <strong>Target:</strong> {selectedCampaign.targetAudience?.allUsers ? 'All users' : 'Filtered users'}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseStats}>Close</Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default NotificationCampaignsList;
