import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  serverTimestamp,
  DocumentSnapshot,
  writeBatch
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { MobileNotification } from './mobileAppService';

/**
 * Interface for notification template
 */
export interface NotificationTemplate {
  id?: string;
  name: string;
  title: string;
  message: string;
  type: 'booking' | 'promotion' | 'system' | 'loyalty';
  createdBy: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

/**
 * Interface for notification campaign
 */
export interface NotificationCampaign {
  id?: string;
  name: string;
  hotelId: string;
  title: string;
  message: string;
  type: 'booking' | 'promotion' | 'system' | 'loyalty';
  targetAudience: {
    allUsers?: boolean;
    userIds?: string[];
    filters?: {
      hasUpcomingBooking?: boolean;
      hasPastBooking?: boolean;
      loyaltyTier?: string[];
      lastVisitDays?: number;
      location?: string;
    };
  };
  schedule?: {
    sendImmediately: boolean;
    scheduledDate?: Timestamp;
  };
  status: 'draft' | 'scheduled' | 'sent' | 'cancelled';
  stats?: {
    totalSent: number;
    delivered: number;
    opened: number;
    clicked: number;
  };
  createdBy: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
  sentAt?: Timestamp;
}

/**
 * Create a notification template
 */
export const createNotificationTemplate = async (template: Omit<NotificationTemplate, 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    const templateRef = await addDoc(collection(db, 'notificationTemplates'), {
      ...template,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return templateRef.id;
  } catch (error) {
    console.error('Error creating notification template:', error);
    throw error;
  }
};

/**
 * Get notification templates
 */
export const getNotificationTemplates = async (createdBy: string): Promise<NotificationTemplate[]> => {
  try {
    const q = query(
      collection(db, 'notificationTemplates'),
      where('createdBy', '==', createdBy),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const templates: NotificationTemplate[] = [];

    querySnapshot.forEach((doc) => {
      templates.push({ id: doc.id, ...(doc.data() as object) } as NotificationTemplate);
    });

    return templates;
  } catch (error) {
    console.error('Error getting notification templates:', error);
    throw error;
  }
};

/**
 * Create a notification campaign
 */
export const createNotificationCampaign = async (campaign: Omit<NotificationCampaign, 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    // Set initial stats
    const campaignWithStats = {
      ...campaign,
      stats: {
        totalSent: 0,
        delivered: 0,
        opened: 0,
        clicked: 0
      }
    };

    const campaignRef = await addDoc(collection(db, 'notificationCampaigns'), {
      ...campaignWithStats,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // If campaign is set to send immediately, process it
    if (campaign.schedule?.sendImmediately) {
      await processCampaign(campaignRef.id);
    }

    return campaignRef.id;
  } catch (error) {
    console.error('Error creating notification campaign:', error);
    throw error;
  }
};

/**
 * Get notification campaigns for a hotel
 */
export const getNotificationCampaigns = async (hotelId: string): Promise<NotificationCampaign[]> => {
  try {
    const q = query(
      collection(db, 'notificationCampaigns'),
      where('hotelId', '==', hotelId),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const campaigns: NotificationCampaign[] = [];

    querySnapshot.forEach((doc) => {
      campaigns.push({ id: doc.id, ...(doc.data() as object) } as NotificationCampaign);
    });

    return campaigns;
  } catch (error) {
    console.error('Error getting notification campaigns:', error);
    throw error;
  }
};

/**
 * Process a notification campaign (send notifications to target users)
 */
export const processCampaign = async (campaignId: string): Promise<boolean> => {
  try {
    // Get campaign data
    const campaignDoc = await getDoc(doc(db, 'notificationCampaigns', campaignId));

    if (!campaignDoc.exists()) {
      throw new Error('Campaign not found');
    }

    const campaign = { id: campaignDoc.id, ...(campaignDoc.data() as object) } as NotificationCampaign;

    // Get target users
    const targetUserIds = await getTargetUserIds(campaign);

    if (targetUserIds.length === 0) {
      console.log('No target users found for campaign');
      return false;
    }

    // Send notifications to target users
    await sendNotificationsToUsers(campaign, targetUserIds);

    // Update campaign status and stats
    await updateDoc(doc(db, 'notificationCampaigns', campaignId), {
      status: 'sent',
      sentAt: serverTimestamp(),
      'stats.totalSent': targetUserIds.length,
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('Error processing campaign:', error);
    throw error;
  }
};

/**
 * Get target user IDs based on campaign targeting criteria
 */
const getTargetUserIds = async (campaign: NotificationCampaign): Promise<string[]> => {
  try {
    const { targetAudience } = campaign;

    // If specific user IDs are provided, use them
    if (targetAudience.userIds && targetAudience.userIds.length > 0) {
      return targetAudience.userIds;
    }

    // If targeting all users, get all user IDs
    if (targetAudience.allUsers) {
      const userProfiles = await getDocs(collection(db, 'mobileUserProfiles'));
      return userProfiles.docs.map(doc => doc.data().userId);
    }

    // Apply filters
    let userIds: string[] = [];

    if (targetAudience.filters) {
      // This is a simplified implementation
      // In a real app, you would implement more sophisticated filtering

      // Example: Users with upcoming bookings
      if (targetAudience.filters.hasUpcomingBooking) {
        const today = new Date();
        const bookingsQuery = query(
          collection(db, 'bookings'),
          where('hotelId', '==', campaign.hotelId),
          where('checkInDate', '>=', today),
          where('bookingStatus', '==', 'confirmed')
        );

        const bookings = await getDocs(bookingsQuery);
        const bookingUserIds = bookings.docs.map(doc => doc.data().userId).filter(Boolean);

        // Combine arrays and remove duplicates
const combinedIds = Array.from(new Set([...userIds, ...bookingUserIds]));
userIds = combinedIds;
      }

      // Example: Users with past bookings
      if (targetAudience.filters.hasPastBooking) {
        const today = new Date();
        const bookingsQuery = query(
          collection(db, 'bookings'),
          where('hotelId', '==', campaign.hotelId),
          where('checkOutDate', '<', today),
          where('bookingStatus', '==', 'checked_out')
        );

        const bookings = await getDocs(bookingsQuery);
        const bookingUserIds = bookings.docs.map(doc => doc.data().userId).filter(Boolean);

        // Combine arrays and remove duplicates
const combinedIds = Array.from(new Set([...userIds, ...bookingUserIds]));
userIds = combinedIds;
      }

      // Additional filters would be implemented here
    }

    return userIds;
  } catch (error) {
    console.error('Error getting target user IDs:', error);
    throw error;
  }
};

/**
 * Send notifications to target users
 */
const sendNotificationsToUsers = async (campaign: NotificationCampaign, userIds: string[]): Promise<void> => {
  try {
    const batch = writeBatch(db);
    let count = 0;
    const batchLimit = 500; // Firestore batch limit

    for (const userId of userIds) {
      // Create notification document
      const notificationRef = doc(collection(db, 'mobileNotifications'));

      const notification: Omit<MobileNotification, 'id'> = {
        userId,
        title: campaign.title,
        message: campaign.message,
        type: campaign.type,
        read: false,
        hotelId: campaign.hotelId,
        createdAt: Timestamp.now()
      };

      batch.set(notificationRef, notification);

      count++;

      // If batch limit reached, commit and create a new batch
      if (count >= batchLimit) {
        await batch.commit();
        count = 0;
      }
    }

    // Commit any remaining operations
    if (count > 0) {
      await batch.commit();
    }
  } catch (error) {
    console.error('Error sending notifications to users:', error);
    throw error;
  }
};

/**
 * Cancel a scheduled notification campaign
 */
export const cancelNotificationCampaign = async (campaignId: string): Promise<boolean> => {
  try {
    const campaignDoc = await getDoc(doc(db, 'notificationCampaigns', campaignId));

    if (!campaignDoc.exists()) {
      throw new Error('Campaign not found');
    }

    const campaign = { id: campaignDoc.id, ...(campaignDoc.data() as object) } as NotificationCampaign;

    if (campaign.status === 'sent') {
      throw new Error('Cannot cancel a campaign that has already been sent');
    }

    await updateDoc(doc(db, 'notificationCampaigns', campaignId), {
      status: 'cancelled',
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('Error cancelling notification campaign:', error);
    throw error;
  }
};

/**
 * Update notification campaign stats
 */
export const updateCampaignStats = async (
  campaignId: string,
  stats: Partial<NotificationCampaign['stats']>
): Promise<boolean> => {
  try {
    const campaignDoc = await getDoc(doc(db, 'notificationCampaigns', campaignId));

    if (!campaignDoc.exists()) {
      throw new Error('Campaign not found');
    }

    const campaign = { id: campaignDoc.id, ...(campaignDoc.data() as object) } as NotificationCampaign;
    const currentStats = campaign.stats || { totalSent: 0, delivered: 0, opened: 0, clicked: 0 };

    await updateDoc(doc(db, 'notificationCampaigns', campaignId), {
      stats: {
        ...currentStats,
        ...stats
      },
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('Error updating campaign stats:', error);
    throw error;
  }
};
