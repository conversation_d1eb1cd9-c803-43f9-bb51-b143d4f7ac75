// File generated by FlutterFire CLI.
// This file should be replaced with the actual firebase_options.dart file generated by FlutterFire CLI.
// This is a placeholder file.

import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return web; // Use web config for Windows
      case TargetPlatform.linux:
        return web; // Use web config for Linux
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  // Firebase configuration for linkinblink-hotel project
  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI',
    appId: '1:132613661307:web:a9e5c8f4c2b8e9a9e5c8f4',
    messagingSenderId: '132613661307',
    projectId: 'linkinblink-hotel',
    authDomain: 'linkinblink-hotel.firebaseapp.com',
    storageBucket: 'linkinblink-hotel.appspot.com',
    measurementId: 'G-MEASUREMENT_ID',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI',
    appId: '1:132613661307:android:6b0006c4314455ad1e78de',
    messagingSenderId: '132613661307',
    projectId: 'linkinblink-hotel',
    storageBucket: 'linkinblink-hotel.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAxRWNvZf8J8Bh0fvvTLG9_KFa8GEbsuPw',
    appId: '1:132613661307:ios:ad45f8aba719d9131e78de',
    messagingSenderId: '132613661307',
    projectId: 'linkinblink-hotel',
    storageBucket: 'linkinblink-hotel.firebasestorage.app',
    androidClientId: '132613661307-cgsam4dh6dv7vjhfg1om1tpstgk1b1fi.apps.googleusercontent.com',
    iosClientId: '132613661307-2hp3k02gjtpjt43ls6gq3h49rcio5lcs.apps.googleusercontent.com',
    iosBundleId: 'com.linkinblink.hotelBookingApp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCjcwupQa21Ck7TEkoHSyCJecGeNIvPjRI',
    appId: '1:132613661307:macos:a9e5c8f4c2b8e9a9e5c8f4',
    messagingSenderId: '132613661307',
    projectId: 'linkinblink-hotel',
    storageBucket: 'linkinblink-hotel.appspot.com',
    iosBundleId: 'com.linkinblink.hotelBookingApp',
  );
}