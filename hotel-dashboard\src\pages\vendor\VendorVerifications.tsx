import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Button,
  CircularProgress,
  Alert,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  InputAdornment
} from '@mui/material';
import {
  VerifiedUser as VerifiedUserIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  PendingActions as PendingIcon,
  ErrorOutline as ErrorIcon,
  HelpOutline as HelpIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { getVerifications, getVerificationStats } from '../../services/verificationService';
import { AadhaarVerificationData } from '../../components/AadhaarVerificationCard';
import { format } from 'date-fns';

const VendorVerifications: React.FC = () => {
  const navigate = useNavigate();
  const [verifications, setVerifications] = useState<AadhaarVerificationData[]>([]);
  const [filteredVerifications, setFilteredVerifications] = useState<AadhaarVerificationData[]>([]);
  const [stats, setStats] = useState({
    total: 0,
    verified: 0,
    pending: 0,
    failed: 0,
    notVerified: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Load data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch verification stats
      const statsData = await getVerificationStats();
      setStats(statsData);

      // Fetch all verifications
      const verificationsData = await getVerifications();
      setVerifications(verificationsData);
      setFilteredVerifications(verificationsData);
    } catch (err: any) {
      setError(err.message || 'Failed to load verifications');
      console.error('Error fetching verifications:', err);
    } finally {
      setLoading(false);
    }
  };

  // Filter verifications based on status and search term
  const filterVerifications = useCallback(() => {
    let filtered = [...verifications];

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(v => v.status === statusFilter);
    }

    // Apply search filter
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(
        v => 
          v.fullName.toLowerCase().includes(search) ||
          v.maskedAadhaarNumber.includes(search) ||
          (v.userId && v.userId.toLowerCase().includes(search))
      );
    }

    setFilteredVerifications(filtered);
  }, [verifications, statusFilter, searchTerm]);

  // Apply filters when they change
  useEffect(() => {
    filterVerifications();
  }, [statusFilter, searchTerm, verifications, filterVerifications]);

  const handleStatusFilterChange = (event: SelectChangeEvent) => {
    setStatusFilter(event.target.value);
    setPage(0);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleRefresh = () => {
    fetchData();
  };

  const handleViewDetails = (id: string) => {
    navigate(`/vendor/verifications/${id}`);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Format date
  const formatDate = (date?: Date) => {
    if (!date) return 'N/A';
    return format(new Date(date), 'MMM dd, yyyy');
  };

  // Get status color and icon
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'verified':
        return {
          color: 'success',
          icon: <VerifiedUserIcon fontSize="small" />,
          text: 'Verified'
        };
      case 'pending':
        return {
          color: 'warning',
          icon: <PendingIcon fontSize="small" />,
          text: 'Pending'
        };
      case 'failed':
        return {
          color: 'error',
          icon: <ErrorIcon fontSize="small" />,
          text: 'Failed'
        };
      case 'notVerified':
        return {
          color: 'default',
          icon: <HelpIcon fontSize="small" />,
          text: 'Not Verified'
        };
      default:
        return {
          color: 'default',
          icon: <HelpIcon fontSize="small" />,
          text: 'Unknown'
        };
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Aadhaar Verifications
      </Typography>

      {/* Stats Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography variant="h6" align="center">
                {stats.total}
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center">
                Total
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent sx={{ bgcolor: 'success.light', color: 'success.contrastText' }}>
              <Typography variant="h6" align="center">
                {stats.verified}
              </Typography>
              <Typography variant="body2" align="center">
                Verified
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent sx={{ bgcolor: 'warning.light', color: 'warning.contrastText' }}>
              <Typography variant="h6" align="center">
                {stats.pending}
              </Typography>
              <Typography variant="body2" align="center">
                Pending
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent sx={{ bgcolor: 'error.light', color: 'error.contrastText' }}>
              <Typography variant="h6" align="center">
                {stats.failed}
              </Typography>
              <Typography variant="body2" align="center">
                Failed
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent sx={{ bgcolor: 'grey.300' }}>
              <Typography variant="h6" align="center">
                {stats.notVerified}
              </Typography>
              <Typography variant="body2" align="center">
                Not Verified
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth>
              <InputLabel id="status-filter-label">Status</InputLabel>
              <Select
                labelId="status-filter-label"
                value={statusFilter}
                label="Status"
                onChange={handleStatusFilterChange}
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value="verified">Verified</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="failed">Failed</MenuItem>
                <MenuItem value="notVerified">Not Verified</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              label="Search"
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              placeholder="Search by name or Aadhaar number"
            />
          </Grid>
          <Grid item xs={12} sm={12} md={5} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleRefresh}
              disabled={loading}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<FilterIcon />}
              sx={{ ml: 1 }}
              onClick={() => navigate('/vendor/verification-reports')}
            >
              View Reports
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Verifications Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Aadhaar Number</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Submitted On</TableCell>
                <TableCell>Verified On</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : filteredVerifications.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                    No verifications found matching the filters
                  </TableCell>
                </TableRow>
              ) : (
                filteredVerifications
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((verification) => {
                    const statusInfo = getStatusInfo(verification.status);
                    return (
                      <TableRow key={verification.id}>
                        <TableCell>{verification.fullName}</TableCell>
                        <TableCell>{verification.maskedAadhaarNumber}</TableCell>
                        <TableCell>
                          <Chip
                            label={statusInfo.text}
                            color={statusInfo.color as any}
                            size="small"
                            icon={statusInfo.icon}
                          />
                        </TableCell>
                        <TableCell>
                          {verification.createdAt ? formatDate(verification.createdAt) : 'N/A'}
                        </TableCell>
                        <TableCell>
                          {verification.verifiedAt ? formatDate(verification.verifiedAt) : 'N/A'}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<ViewIcon />}
                            onClick={() => handleViewDetails(verification.id)}
                          >
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={filteredVerifications.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
    </Box>
  );
};

export default VendorVerifications;
