import 'package:flutter/material.dart';
import 'package:hotel_booking_app/core/theme/app_theme.dart';
import 'package:hotel_booking_app/data/services/booking_service.dart';
import 'package:hotel_booking_app/data/services/checkin_service.dart';
import 'package:hotel_booking_app/presentation/screens/home/<USER>';
import 'package:hotel_booking_app/presentation/widgets/custom_button.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

class BookingConfirmationScreen extends StatefulWidget {
  final String bookingId;

  const BookingConfirmationScreen({
    super.key,
    required this.bookingId,
  });

  @override
  State<BookingConfirmationScreen> createState() =>
      _BookingConfirmationScreenState();
}

class _BookingConfirmationScreenState extends State<BookingConfirmationScreen> {
  @override
  void initState() {
    super.initState();

    // Fetch booking details
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchBookingDetails();
    });
  }

  Future<void> _fetchBookingDetails() async {
    final bookingService = Provider.of<BookingService>(context, listen: false);
    await bookingService.fetchBookingById(widget.bookingId);
  }

  Future<void> _generateBookingConfirmation() async {
    final checkinService = Provider.of<CheckinService>(context, listen: false);
    final filePath =
        await checkinService.generateBookingConfirmation(widget.bookingId);

    if (filePath != null) {
      // Open the PDF file
      final uri = Uri.file(filePath);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Could not open the confirmation PDF'),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to generate confirmation PDF'),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final bookingService = Provider.of<BookingService>(context);
    final booking = bookingService.selectedBooking;

    if (bookingService.isLoading || booking == null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor:
                    AlwaysStoppedAnimation<Color>(AppTheme.secondaryColor),
              ),
              const SizedBox(height: 16),
              Text(
                'Loading your booking details...',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Success Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(20, 60, 20, 40),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.green.shade400,
                    Colors.green.shade600,
                  ],
                ),
              ),
              child: Column(
                children: [
                  // Close Button
                  Align(
                    alignment: Alignment.topRight,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.3),
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.close, color: Colors.white),
                        onPressed: () {
                          Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const HomeScreen(),
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Success Icon
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          spreadRadius: 0,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.green,
                      size: 60,
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Success Text
                  const Text(
                    'Booking Confirmed!',
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'Booking ID: ${booking.id}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Your booking has been confirmed successfully!',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 30),

            // Booking Details
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16),
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor.withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.confirmation_number,
                              color: AppTheme.primaryColor,
                              size: 16,
                            ),
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            'Booking Details',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Details
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Hotel and Room
                          _buildDetailItem(
                            icon: Icons.hotel,
                            title: 'Hotel',
                            value: booking.hotelName,
                          ),
                          const Divider(height: 24),
                          _buildDetailItem(
                            icon: Icons.king_bed,
                            title: 'Room',
                            value: booking.roomName,
                          ),
                          const Divider(height: 24),

                          // Check-in and Check-out
                          Row(
                            children: [
                              Expanded(
                                child: _buildDetailItem(
                                  icon: Icons.login,
                                  title: 'Check-in',
                                  value: DateFormat('MMM dd, yyyy')
                                      .format(booking.checkIn),
                                  subtitle: 'From 2:00 PM',
                                ),
                              ),
                              Expanded(
                                child: _buildDetailItem(
                                  icon: Icons.logout,
                                  title: 'Check-out',
                                  value: DateFormat('MMM dd, yyyy')
                                      .format(booking.checkOut),
                                  subtitle: 'Until 12:00 PM',
                                ),
                              ),
                            ],
                          ),
                          const Divider(height: 24),

                          // Guests
                          _buildDetailItem(
                            icon: Icons.person,
                            title: 'Guests',
                            value:
                                '${booking.guests} ${booking.guests > 1 ? 'Persons' : 'Person'}',
                          ),
                          const Divider(height: 24),

                          // Status
                          _buildDetailItem(
                            icon: Icons.info_outline,
                            title: 'Status',
                            value: _getFormattedStatus(booking.status),
                            valueColor: _getStatusColor(booking.status),
                            showBadge: true,
                          ),
                          const Divider(height: 24),

                          // Payment Status
                          _buildDetailItem(
                            icon: Icons.payment,
                            title: 'Payment Status',
                            value: _getFormattedPaymentStatus(
                                booking.paymentStatus),
                            valueColor:
                                _getPaymentStatusColor(booking.paymentStatus),
                            showBadge: true,
                          ),
                          const Divider(height: 24),

                          // Total Amount
                          _buildDetailItem(
                            icon: Icons.attach_money,
                            title: 'Total Amount',
                            value: '\$${booking.totalPrice.toStringAsFixed(0)}',
                            valueColor: AppTheme.secondaryColor,
                            valueFontSize: 20,
                            valueWeight: FontWeight.bold,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Download Confirmation Button
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: CustomButton(
                text: 'Download Booking Confirmation',
                prefixIcon: const Icon(Icons.download, color: Colors.white),
                onPressed: _generateBookingConfirmation,
                height: 56,
                borderRadius: 12,
              ),
            ),

            const SizedBox(height: 24),

            // Guest Information
            if (booking.guestDetails != null)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        spreadRadius: 0,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Header
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(16),
                            topRight: Radius.circular(16),
                          ),
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: AppTheme.primaryColor.withOpacity(0.1),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.person,
                                color: AppTheme.primaryColor,
                                size: 16,
                              ),
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'Guest Information',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Details
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildDetailItem(
                              icon: Icons.person_outline,
                              title: 'Name',
                              value: booking.guestDetails!['name'] ?? '',
                            ),
                            const Divider(height: 24),
                            _buildDetailItem(
                              icon: Icons.email_outlined,
                              title: 'Email',
                              value: booking.guestDetails!['email'] ?? '',
                            ),
                            const Divider(height: 24),
                            _buildDetailItem(
                              icon: Icons.phone_outlined,
                              title: 'Phone',
                              value: booking.guestDetails!['phone'] ?? '',
                            ),
                            if (booking.guestDetails!['specialRequests'] !=
                                    null &&
                                booking.guestDetails!['specialRequests']
                                    .isNotEmpty) ...[
                              const Divider(height: 24),
                              _buildDetailItem(
                                icon: Icons.note_outlined,
                                title: 'Special Requests',
                                value: booking.guestDetails!['specialRequests'],
                                isMultiLine: true,
                              ),
                            ],
                            if (booking.guestDetails!['paymentMethod'] !=
                                null) ...[
                              const Divider(height: 24),
                              _buildDetailItem(
                                icon: Icons.payment_outlined,
                                title: 'Payment Method',
                                value: _getFormattedPaymentMethod(
                                    booking.guestDetails!['paymentMethod']),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // Important Information
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16),
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor.withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.info_outline,
                              color: AppTheme.primaryColor,
                              size: 16,
                            ),
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            'Important Information',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Details
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildDetailItem(
                            icon: Icons.access_time,
                            title: 'Check-in Time',
                            value: '2:00 PM',
                          ),
                          const Divider(height: 24),
                          _buildDetailItem(
                            icon: Icons.access_time_filled,
                            title: 'Check-out Time',
                            value: '12:00 PM',
                          ),
                          const Divider(height: 24),
                          _buildDetailItem(
                            icon: Icons.credit_card,
                            title: 'Required at Check-in',
                            value: 'Valid ID and credit card',
                          ),
                          const Divider(height: 24),
                          _buildDetailItem(
                            icon: Icons.smoke_free,
                            title: 'Smoking Policy',
                            value: 'Non-smoking rooms',
                          ),
                          const Divider(height: 24),
                          _buildDetailItem(
                            icon: Icons.pets,
                            title: 'Pet Policy',
                            value: 'Pets not allowed',
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Action Buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  CustomButton(
                    text: 'Download Booking Confirmation',
                    prefixIcon: const Icon(Icons.download, color: Colors.white),
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                              'Booking confirmation download feature coming soon!'),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    },
                    height: 56,
                    borderRadius: 12,
                  ),
                  const SizedBox(height: 16),
                  CustomButton(
                    text: 'View My Bookings',
                    prefixIcon: const Icon(Icons.list_alt, color: Colors.black),
                    onPressed: () {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              const HomeScreen(initialTabIndex: 2),
                        ),
                      );
                    },
                    height: 56,
                    borderRadius: 12,
                    backgroundColor: Colors.grey.shade200,
                    textColor: Colors.black,
                  ),
                  const SizedBox(height: 16),
                  CustomButton(
                    text: 'Return to Home',
                    prefixIcon: const Icon(Icons.home, color: Colors.black),
                    onPressed: () {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const HomeScreen(),
                        ),
                      );
                    },
                    height: 56,
                    borderRadius: 12,
                    backgroundColor: Colors.white,
                    textColor: Colors.black,
                    borderColor: Colors.grey.shade300,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String title,
    required String value,
    String? subtitle,
    Color? valueColor,
    double valueFontSize = 16,
    FontWeight valueWeight = FontWeight.w500,
    bool showBadge = false,
    bool isMultiLine = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            size: 16,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              if (showBadge)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: valueColor?.withOpacity(0.1) ?? Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    value,
                    style: TextStyle(
                      color: valueColor,
                      fontWeight: valueWeight,
                      fontSize: valueFontSize,
                    ),
                  ),
                )
              else
                Text(
                  value,
                  style: TextStyle(
                    color: valueColor,
                    fontWeight: valueWeight,
                    fontSize: valueFontSize,
                  ),
                  textAlign: isMultiLine ? TextAlign.left : TextAlign.start,
                ),
              if (subtitle != null) ...[
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade500,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  String _getFormattedPaymentMethod(String method) {
    switch (method.toLowerCase()) {
      case 'card':
        return 'Credit/Debit Card';
      case 'cash':
        return 'Pay at Hotel';
      case 'mobile':
        return 'Mobile Money';
      default:
        return method.capitalize();
    }
  }

  String _getFormattedStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Pending';
      case 'confirmed':
        return 'Confirmed';
      case 'checked_in':
        return 'Checked In';
      case 'checked_out':
        return 'Checked Out';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.capitalize();
    }
  }

  String _getFormattedPaymentStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Pending';
      case 'completed':
        return 'Paid';
      case 'failed':
        return 'Failed';
      case 'refunded':
        return 'Refunded';
      default:
        return status.capitalize();
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'checked_in':
        return Colors.green;
      case 'checked_out':
        return Colors.purple;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getPaymentStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'completed':
        return Colors.green;
      case 'failed':
        return Colors.red;
      case 'refunded':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}

extension StringExtension on String {
  String capitalize() {
    if (isEmpty) return this;
    return "${this[0].toUpperCase()}${substring(1).toLowerCase()}";
  }
}
