import { Timestamp } from 'firebase/firestore';
import {
  StaffShift,
  createStaffShift,
  getShiftsForDate,
  SHIFT_TYPE_MORNING,
  SHIFT_TYPE_AFTERNOON,
  SHIFT_TYPE_NIGHT
} from './staffSchedulingService';
import {
  StaffMember,
  getStaffForHotel
} from './staffManagementService';
import {
  StaffAvailability,
  getHotelStaffAvailability,
  isStaffAvailableForShift,
  AVAILABILITY_TYPE_PREFERRED,
  AVAILABILITY_TYPE_AVAILABLE
} from './staffAvailabilityService';
import {
  checkAllConflicts,
  ScheduleConflict,
  CONFLICT_SEVERITY_ERROR
} from './scheduleConflictService';
import {
  addDays,
  format,
  startOfWeek,
  isSameDay
} from 'date-fns';

// Shift time definitions
const SHIFT_TIMES = {
  [SHIFT_TYPE_MORNING]: { start: '06:00', end: '14:00' },
  [SHIFT_TYPE_AFTERNOON]: { start: '14:00', end: '22:00' },
  [SHIFT_TYPE_NIGHT]: { start: '22:00', end: '06:00' }
};

// Role requirements - how many staff of each role are needed for each shift
const ROLE_REQUIREMENTS: { [role: string]: { [shift: string]: number } } = {
  'housekeeping': {
    [SHIFT_TYPE_MORNING]: 2,
    [SHIFT_TYPE_AFTERNOON]: 1,
    [SHIFT_TYPE_NIGHT]: 0
  },
  'front_desk': {
    [SHIFT_TYPE_MORNING]: 1,
    [SHIFT_TYPE_AFTERNOON]: 1,
    [SHIFT_TYPE_NIGHT]: 1
  },
  'maintenance': {
    [SHIFT_TYPE_MORNING]: 1,
    [SHIFT_TYPE_AFTERNOON]: 1,
    [SHIFT_TYPE_NIGHT]: 0
  },
  'food_service': {
    [SHIFT_TYPE_MORNING]: 1,
    [SHIFT_TYPE_AFTERNOON]: 1,
    [SHIFT_TYPE_NIGHT]: 0
  },
  'manager': {
    [SHIFT_TYPE_MORNING]: 1,
    [SHIFT_TYPE_AFTERNOON]: 0,
    [SHIFT_TYPE_NIGHT]: 0
  }
};

// Interface for scheduling results
export interface SchedulingResult {
  success: boolean;
  shifts: StaffShift[];
  conflicts: ScheduleConflict[];
  unfilledPositions: {
    day: Date;
    role: string;
    shiftType: string;
    reason: string;
  }[];
}

/**
 * Generate an automated schedule for a week
 */
export const generateWeeklySchedule = async (
  hotelId: string,
  vendorId: string,
  startDate: Date,
  options: {
    respectAvailability?: boolean;
    maxShiftsPerWeek?: number;
    balanceShifts?: boolean;
  } = {}
): Promise<SchedulingResult> => {
  try {
    // Set default options
    const {
      respectAvailability = true,
      maxShiftsPerWeek = 5,
      balanceShifts = true
    } = options;

    // Initialize result
    const result: SchedulingResult = {
      success: false,
      shifts: [],
      conflicts: [],
      unfilledPositions: []
    };

    // Get staff members
    const staffMembers = await getStaffForHotel(hotelId, { status: 'active' });

    // Get staff availability if needed
    let staffAvailability: StaffAvailability[] = [];
    if (respectAvailability) {
      staffAvailability = await getHotelStaffAvailability(hotelId);
    }

    // Generate week days
    const weekDays = [];
    const weekStart = startOfWeek(startDate, { weekStartsOn: 1 }); // Week starts on Monday
    for (let i = 0; i < 7; i++) {
      weekDays.push(addDays(weekStart, i));
    }

    // Track shifts assigned to each staff member
    const staffShiftCounts: { [staffId: string]: number } = {};
    staffMembers.forEach(staff => {
      if (staff.id) {
        staffShiftCounts[staff.id] = 0;
      }
    });

    // Process each day
    for (const day of weekDays) {
      // Process each role
      for (const [role, requirements] of Object.entries(ROLE_REQUIREMENTS)) {
        // Get staff for this role
        const roleStaff = staffMembers.filter(staff => staff.role === role);

        if (roleStaff.length === 0) {
          // No staff for this role
          for (const [shiftType, count] of Object.entries(requirements)) {
            if (count > 0) {
              result.unfilledPositions.push({
                day,
                role,
                shiftType,
                reason: `No staff members with role ${role}`
              });
            }
          }
          continue;
        }

        // Process each shift type
        for (const [shiftType, requiredCount] of Object.entries(requirements)) {
          if (requiredCount === 0) continue;

          // Get shift times
          const shiftTimes = SHIFT_TIMES[shiftType as keyof typeof SHIFT_TIMES];

          // Sort staff by preference for this shift and number of shifts already assigned
          const sortedStaff = [...roleStaff].sort((a, b) => {
            const aId = a.id || '';
            const bId = b.id || '';

            // If balancing shifts, prioritize staff with fewer shifts
            if (balanceShifts) {
              const aShifts = staffShiftCounts[aId] || 0;
              const bShifts = staffShiftCounts[bId] || 0;
              if (aShifts !== bShifts) {
                return aShifts - bShifts;
              }
            }

            // If respecting availability, prioritize staff with preferred availability
            if (respectAvailability) {
              const aPreferred = isPreferredForShift(aId, day, shiftType, staffAvailability);
              const bPreferred = isPreferredForShift(bId, day, shiftType, staffAvailability);

              if (aPreferred && !bPreferred) return -1;
              if (!aPreferred && bPreferred) return 1;

              const aAvailable = isAvailableForShift(aId, day, shiftType, staffAvailability);
              const bAvailable = isAvailableForShift(bId, day, shiftType, staffAvailability);

              if (aAvailable && !bAvailable) return -1;
              if (!aAvailable && bAvailable) return 1;
            }

            return 0;
          });

          // Assign shifts
          let assignedCount = 0;
          for (const staff of sortedStaff) {
            if (assignedCount >= requiredCount) break;

            const staffId = staff.id;
            if (!staffId) continue;

            // Check if staff has reached max shifts
            if (staffShiftCounts[staffId] >= maxShiftsPerWeek) {
              continue;
            }

            // Check availability if needed
            if (respectAvailability) {
              const isAvailable = await isStaffAvailableForShift(
                staffId,
                day,
                shiftTimes.start,
                shiftTimes.end
              );

              if (!isAvailable.available) {
                continue;
              }
            }

            // Create shift
            const startTime = new Date(day);
            const [startHour, startMinute] = shiftTimes.start.split(':').map(Number);
            startTime.setHours(startHour, startMinute, 0, 0);

            const endTime = new Date(day);
            const [endHour, endMinute] = shiftTimes.end.split(':').map(Number);
            endTime.setHours(endHour, endMinute, 0, 0);

            // Handle overnight shifts
            if (endTime <= startTime) {
              endTime.setDate(endTime.getDate() + 1);
            }

            const shift: Omit<StaffShift, 'id'> = {
              staffId,
              staffName: staff.name,
              hotelId,
              vendorId,
              date: Timestamp.fromDate(day),
              role,
              shiftType,
              startTime: Timestamp.fromDate(startTime),
              endTime: Timestamp.fromDate(endTime),
              notes: `Auto-generated shift`,
              createdAt: Timestamp.now(),
              updatedAt: Timestamp.now()
            };

            // Check for conflicts
            const conflicts = await checkAllConflicts(shift as StaffShift, hotelId);
            const errorConflicts = conflicts.filter(c => c.severity === CONFLICT_SEVERITY_ERROR);

            if (errorConflicts.length > 0) {
              // Add conflicts to result
              result.conflicts.push(...errorConflicts);
              continue;
            }

            // Add shift to result
            result.shifts.push(shift as StaffShift);

            // Increment staff shift count
            staffShiftCounts[staffId] = (staffShiftCounts[staffId] || 0) + 1;

            assignedCount++;
          }

          // Check if all required positions were filled
          if (assignedCount < requiredCount) {
            for (let i = assignedCount; i < requiredCount; i++) {
              result.unfilledPositions.push({
                day,
                role,
                shiftType,
                reason: `Not enough available staff with role ${role}`
              });
            }
          }
        }
      }
    }

    // Set success based on unfilled positions
    result.success = result.unfilledPositions.length === 0;

    return result;
  } catch (error) {
    console.error('Error generating weekly schedule:', error);
    throw error;
  }
};

/**
 * Apply an automated schedule by creating shifts in the database
 */
export const applyAutoSchedule = async (
  schedulingResult: SchedulingResult
): Promise<boolean> => {
  try {
    // Create shifts in database
    for (const shift of schedulingResult.shifts) {
      await createStaffShift(shift);
    }

    return true;
  } catch (error) {
    console.error('Error applying auto schedule:', error);
    throw error;
  }
};

/**
 * Check if a staff member prefers a specific shift
 */
const isPreferredForShift = (
  staffId: string,
  date: Date,
  shiftType: string,
  availabilityList: StaffAvailability[]
): boolean => {
  // Get day of week (0-6, Monday-Sunday)
  const dayOfWeek = (date.getDay() + 6) % 7; // Convert from Sunday-Saturday (0-6) to Monday-Sunday (0-6)

  // Get shift times
  const shiftTimes = SHIFT_TIMES[shiftType as keyof typeof SHIFT_TIMES];

  // Check for preferred availability
  return availabilityList.some(a => {
    if (a.staffId !== staffId) return false;
    if (a.type !== AVAILABILITY_TYPE_PREFERRED) return false;

    // Check if it's a weekly recurring availability for this day
    if (a.recurring === 'weekly' && a.dayOfWeek === dayOfWeek) {
      // Check if shift is within availability
      return isTimeWithinRange(shiftTimes.start, shiftTimes.end, a.startTime, a.endTime);
    }

    // Check if it's a one-time availability for this specific date
    if (a.recurring === 'none' && a.specificDate && isSameDay(a.specificDate.toDate(), date)) {
      // Check if shift is within availability
      return isTimeWithinRange(shiftTimes.start, shiftTimes.end, a.startTime, a.endTime);
    }

    return false;
  });
};

/**
 * Check if a staff member is available for a specific shift
 */
const isAvailableForShift = (
  staffId: string,
  date: Date,
  shiftType: string,
  availabilityList: StaffAvailability[]
): boolean => {
  // Get day of week (0-6, Monday-Sunday)
  const dayOfWeek = (date.getDay() + 6) % 7; // Convert from Sunday-Saturday (0-6) to Monday-Sunday (0-6)

  // Get shift times
  const shiftTimes = SHIFT_TIMES[shiftType as keyof typeof SHIFT_TIMES];

  // Check for available or preferred availability
  return availabilityList.some(a => {
    if (a.staffId !== staffId) return false;
    if (a.type !== AVAILABILITY_TYPE_AVAILABLE && a.type !== AVAILABILITY_TYPE_PREFERRED) return false;

    // Check if it's a weekly recurring availability for this day
    if (a.recurring === 'weekly' && a.dayOfWeek === dayOfWeek) {
      // Check if shift is within availability
      return isTimeWithinRange(shiftTimes.start, shiftTimes.end, a.startTime, a.endTime);
    }

    // Check if it's a one-time availability for this specific date
    if (a.recurring === 'none' && a.specificDate && isSameDay(a.specificDate.toDate(), date)) {
      // Check if shift is within availability
      return isTimeWithinRange(shiftTimes.start, shiftTimes.end, a.startTime, a.endTime);
    }

    return false;
  });
};

/**
 * Check if a time range is within another time range
 */
const isTimeWithinRange = (
  innerStart: string,
  innerEnd: string,
  outerStart: string,
  outerEnd: string
): boolean => {
  // Convert times to minutes since midnight for easier comparison
  const innerStartMinutes = timeToMinutes(innerStart);
  const innerEndMinutes = timeToMinutes(innerEnd);
  const outerStartMinutes = timeToMinutes(outerStart);
  const outerEndMinutes = timeToMinutes(outerEnd);

  // Handle overnight shifts
  const isInnerOvernight = innerEndMinutes <= innerStartMinutes;
  const isOuterOvernight = outerEndMinutes <= outerStartMinutes;

  if (isOuterOvernight) {
    // Outer period is overnight
    if (isInnerOvernight) {
      // Both periods are overnight
      return innerStartMinutes >= outerStartMinutes && innerEndMinutes <= outerEndMinutes;
    } else {
      // Inner period is not overnight
      return (innerStartMinutes >= outerStartMinutes || innerStartMinutes < outerEndMinutes) &&
             (innerEndMinutes <= outerEndMinutes || innerEndMinutes > outerStartMinutes);
    }
  } else {
    // Outer period is not overnight
    if (isInnerOvernight) {
      // Inner period is overnight, cannot be within a non-overnight period
      return false;
    } else {
      // Neither period is overnight
      return innerStartMinutes >= outerStartMinutes && innerEndMinutes <= outerEndMinutes;
    }
  }
};

/**
 * Convert time string to minutes since midnight
 */
const timeToMinutes = (time: string): number => {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
};

export default {
  generateWeeklySchedule,
  applyAutoSchedule
};
