import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Grid,
  Card,
  CardContent
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

const Notifications: React.FC = () => {
  const [loading, setLoading] = useState(false);

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Notifications
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => setLoading(!loading)}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      <Paper sx={{ p: 3 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Typography variant="h6" gutterBottom>
              Coming Soon
            </Typography>
            <Typography variant="body1" paragraph>
              The Notifications feature is currently under development. This page will provide a comprehensive notification system for your hotel management system.
            </Typography>

            <Box sx={{ p: 5, textAlign: 'center' }}>
              <NotificationsIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No notifications found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                You have no notifications yet
              </Typography>
            </Box>

            <Grid container spacing={3} sx={{ mt: 2 }}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Notification Types
                    </Typography>
                    <Typography variant="body2">
                      • Booking notifications<br />
                      • System notifications<br />
                      • Alert notifications<br />
                      • Info notifications
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Features
                    </Typography>
                    <Typography variant="body2">
                      • Real-time notifications<br />
                      • Mark as read functionality<br />
                      • Filter and search capabilities<br />
                      • Notification management
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </>
        )}
      </Paper>
    </Box>
  );
};

export default Notifications;
