import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Grid,
  Card,
  CardContent,
  CardActions,
  Divider,
  Badge,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  RoomService as RoomServiceIcon,
  Restaurant as FoodIcon,
  CleaningServices as CleaningIcon,
  Build as MaintenanceIcon,
  CheckCircle as CompletedIcon,
  Cancel as CancelledIcon,
  AccessTime as PendingIcon,
  HourglassTop as InProgressIcon,
  Info as InfoIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import { collection, query, where, orderBy, onSnapshot, doc, updateDoc, Timestamp, getDoc } from 'firebase/firestore';
import { db, auth } from '../../firebase/config';
import { formatDistanceToNow, format } from 'date-fns';

// Service request types
const SERVICE_TYPE_CLEANING = 'cleaning';
const SERVICE_TYPE_FOOD = 'food';
const SERVICE_TYPE_MAINTENANCE = 'maintenance';

// Service request statuses
const STATUS_PENDING = 'pending';
const STATUS_IN_PROGRESS = 'in_progress';
const STATUS_COMPLETED = 'completed';
const STATUS_CANCELLED = 'cancelled';

// Interface for service request
interface ServiceRequest {
  id: string;
  type: string;
  status: string;
  roomNumber: string;
  hotelId: string;
  guestId: string;
  guestName: string;
  requestTime: Timestamp;
  notes?: string;
  priority: string;
  // Cleaning specific
  cleaningType?: string;
  cleaningItems?: string[];
  // Food specific
  items?: { name: string; quantity: number; price: number }[];
  totalAmount?: number;
  // Maintenance specific
  issueType?: string;
  description?: string;
}

const ServiceRequestsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [serviceRequests, setServiceRequests] = useState<ServiceRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRequest, setSelectedRequest] = useState<ServiceRequest | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [statusUpdateOpen, setStatusUpdateOpen] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [statusNote, setStatusNote] = useState('');
  const [updateLoading, setUpdateLoading] = useState(false);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Count by status
  const pendingCount = serviceRequests.filter(req => req.status === STATUS_PENDING).length;
  const inProgressCount = serviceRequests.filter(req => req.status === STATUS_IN_PROGRESS).length;
  const completedCount = serviceRequests.filter(req => req.status === STATUS_COMPLETED).length;
  const cancelledCount = serviceRequests.filter(req => req.status === STATUS_CANCELLED).length;

  // Count by type
  const cleaningCount = serviceRequests.filter(req => req.type === SERVICE_TYPE_CLEANING).length;
  const foodCount = serviceRequests.filter(req => req.type === SERVICE_TYPE_FOOD).length;
  const maintenanceCount = serviceRequests.filter(req => req.type === SERVICE_TYPE_MAINTENANCE).length;

  // Filter service requests based on active tab
  const filteredRequests = serviceRequests.filter(request => {
    switch (activeTab) {
      case 0: // All
        return true;
      case 1: // Pending
        return request.status === STATUS_PENDING;
      case 2: // In Progress
        return request.status === STATUS_IN_PROGRESS;
      case 3: // Completed
        return request.status === STATUS_COMPLETED;
      case 4: // Cancelled
        return request.status === STATUS_CANCELLED;
      case 5: // Cleaning
        return request.type === SERVICE_TYPE_CLEANING;
      case 6: // Food
        return request.type === SERVICE_TYPE_FOOD;
      case 7: // Maintenance
        return request.type === SERVICE_TYPE_MAINTENANCE;
      default:
        return true;
    }
  });

  useEffect(() => {
    const user = auth.currentUser;
    if (!user) return;

    setLoading(true);
    setError(null);

    // Get the vendor's hotel ID
    const fetchHotelId = async () => {
      try {
        const userDoc = await getDoc(doc(db, 'users', user.uid));
        if (!userDoc.exists()) {
          setError('User data not found');
          setLoading(false);
          return;
        }

        const userData = userDoc.data();
        const hotelId = userData.hotelId;

        if (!hotelId) {
          setError('No hotel associated with this vendor');
          setLoading(false);
          return;
        }

        // Query service requests for this hotel
        const serviceRequestsQuery = query(
          collection(db, 'serviceRequests'),
          where('hotelId', '==', hotelId),
          orderBy('requestTime', 'desc')
        );

        const unsubscribe = onSnapshot(serviceRequestsQuery, (snapshot) => {
          const requests: ServiceRequest[] = [];
          snapshot.forEach((doc) => {
            requests.push({ id: doc.id, ...doc.data() } as ServiceRequest);
          });

          setServiceRequests(requests);
          setLoading(false);
        }, (err) => {
          console.error('Error fetching service requests:', err);
          setError('Error fetching service requests');
          setLoading(false);
        });

        return unsubscribe;
      } catch (err) {
        console.error('Error fetching hotel ID:', err);
        setError('Error fetching hotel ID');
        setLoading(false);
      }
    };

    fetchHotelId();
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleViewDetails = (request: ServiceRequest) => {
    setSelectedRequest(request);
    setDetailsOpen(true);
  };

  const handleUpdateStatus = (request: ServiceRequest) => {
    setSelectedRequest(request);
    setNewStatus(request.status);
    setStatusNote('');
    setStatusUpdateOpen(true);
  };

  const handleStatusUpdate = async () => {
    if (!selectedRequest || !newStatus) return;

    setUpdateLoading(true);

    try {
      const requestRef = doc(db, 'serviceRequests', selectedRequest.id);

      await updateDoc(requestRef, {
        status: newStatus,
        statusNote: statusNote,
        updatedAt: Timestamp.now()
      });

      setStatusUpdateOpen(false);
      setUpdateLoading(false);
    } catch (err) {
      console.error('Error updating service request status:', err);
      setError('Error updating service request status');
      setUpdateLoading(false);
    }
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case STATUS_PENDING:
        return <Chip icon={<PendingIcon />} label="Pending" color="warning" size="small" />;
      case STATUS_IN_PROGRESS:
        return <Chip icon={<InProgressIcon />} label="In Progress" color="info" size="small" />;
      case STATUS_COMPLETED:
        return <Chip icon={<CompletedIcon />} label="Completed" color="success" size="small" />;
      case STATUS_CANCELLED:
        return <Chip icon={<CancelledIcon />} label="Cancelled" color="error" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case SERVICE_TYPE_CLEANING:
        return <CleaningIcon color="primary" />;
      case SERVICE_TYPE_FOOD:
        return <FoodIcon color="secondary" />;
      case SERVICE_TYPE_MAINTENANCE:
        return <MaintenanceIcon color="warning" />;
      default:
        return <RoomServiceIcon />;
    }
  };

  const getPriorityChip = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Chip label="High" color="error" size="small" />;
      case 'medium':
        return <Chip label="Medium" color="warning" size="small" />;
      case 'low':
        return <Chip label="Low" color="info" size="small" />;
      default:
        return <Chip label={priority} size="small" />;
    }
  };

  const formatTime = (timestamp: Timestamp) => {
    try {
      const date = timestamp.toDate();
      return format(date, 'PPpp');
    } catch (error) {
      return 'Invalid date';
    }
  };

  const getTimeAgo = (timestamp: Timestamp) => {
    try {
      const date = timestamp.toDate();
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
      return 'Unknown time';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Service Requests
      </Typography>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Pending
              </Typography>
              <Typography variant="h5">
                {pendingCount}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                In Progress
              </Typography>
              <Typography variant="h5">
                {inProgressCount}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Completed
              </Typography>
              <Typography variant="h5">
                {completedCount}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Cancelled
              </Typography>
              <Typography variant="h5">
                {cancelledCount}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant={isMobile ? "scrollable" : "standard"}
          scrollButtons="auto"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="All" />
          <Tab label={<Badge badgeContent={pendingCount} color="warning">Pending</Badge>} />
          <Tab label={<Badge badgeContent={inProgressCount} color="info">In Progress</Badge>} />
          <Tab label="Completed" />
          <Tab label="Cancelled" />
          <Tab label={<Badge badgeContent={cleaningCount} color="primary">Cleaning</Badge>} />
          <Tab label={<Badge badgeContent={foodCount} color="secondary">Food</Badge>} />
          <Tab label={<Badge badgeContent={maintenanceCount} color="warning">Maintenance</Badge>} />
        </Tabs>
      </Paper>

      {/* Service Requests Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Type</TableCell>
              <TableCell>Room</TableCell>
              <TableCell>Guest</TableCell>
              <TableCell>Time</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Priority</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredRequests.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  No service requests found
                </TableCell>
              </TableRow>
            ) : (
              filteredRequests.map((request) => (
                <TableRow key={request.id}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {getTypeIcon(request.type)}
                      <Typography sx={{ ml: 1 }}>
                        {request.type.charAt(0).toUpperCase() + request.type.slice(1)}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{request.roomNumber}</TableCell>
                  <TableCell>{request.guestName}</TableCell>
                  <TableCell>{getTimeAgo(request.requestTime)}</TableCell>
                  <TableCell>{getStatusChip(request.status)}</TableCell>
                  <TableCell>{getPriorityChip(request.priority)}</TableCell>
                  <TableCell>
                    <Button
                      size="small"
                      onClick={() => handleViewDetails(request)}
                    >
                      Details
                    </Button>
                    <Button
                      size="small"
                      onClick={() => handleUpdateStatus(request)}
                      disabled={request.status === STATUS_COMPLETED || request.status === STATUS_CANCELLED}
                    >
                      Update
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedRequest && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {getTypeIcon(selectedRequest.type)}
                <Typography variant="h6" sx={{ ml: 1 }}>
                  {selectedRequest.type.charAt(0).toUpperCase() + selectedRequest.type.slice(1)} Request Details
                </Typography>
              </Box>
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1">Room:</Typography>
                  <Typography variant="body1">{selectedRequest.roomNumber}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1">Guest:</Typography>
                  <Typography variant="body1">{selectedRequest.guestName}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1">Status:</Typography>
                  <Box sx={{ mt: 0.5 }}>{getStatusChip(selectedRequest.status)}</Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1">Priority:</Typography>
                  <Box sx={{ mt: 0.5 }}>{getPriorityChip(selectedRequest.priority)}</Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1">Request Time:</Typography>
                  <Typography variant="body1">{formatTime(selectedRequest.requestTime)}</Typography>
                </Grid>

                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }} />
                </Grid>

                {selectedRequest.notes && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle1">Notes:</Typography>
                    <Typography variant="body1">{selectedRequest.notes}</Typography>
                  </Grid>
                )}

                {/* Type-specific details */}
                {selectedRequest.type === SERVICE_TYPE_CLEANING && (
                  <>
                    <Grid item xs={12}>
                      <Typography variant="subtitle1">Cleaning Type:</Typography>
                      <Typography variant="body1">{selectedRequest.cleaningType}</Typography>
                    </Grid>
                    {selectedRequest.cleaningItems && selectedRequest.cleaningItems.length > 0 && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle1">Items to Clean:</Typography>
                        <ul>
                          {selectedRequest.cleaningItems.map((item, index) => (
                            <li key={index}>{item}</li>
                          ))}
                        </ul>
                      </Grid>
                    )}
                  </>
                )}

                {selectedRequest.type === SERVICE_TYPE_FOOD && (
                  <>
                    <Grid item xs={12}>
                      <Typography variant="subtitle1">Food Items:</Typography>
                      <TableContainer>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Item</TableCell>
                              <TableCell align="right">Quantity</TableCell>
                              <TableCell align="right">Price</TableCell>
                              <TableCell align="right">Total</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {selectedRequest.items && selectedRequest.items.map((item, index) => (
                              <TableRow key={index}>
                                <TableCell>{item.name}</TableCell>
                                <TableCell align="right">{item.quantity}</TableCell>
                                <TableCell align="right">₹{item.price.toFixed(2)}</TableCell>
                                <TableCell align="right">₹{(item.quantity * item.price).toFixed(2)}</TableCell>
                              </TableRow>
                            ))}
                            <TableRow>
                              <TableCell colSpan={3} align="right"><strong>Total:</strong></TableCell>
                              <TableCell align="right"><strong>₹{selectedRequest.totalAmount?.toFixed(2)}</strong></TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Grid>
                  </>
                )}

                {selectedRequest.type === SERVICE_TYPE_MAINTENANCE && (
                  <>
                    <Grid item xs={12}>
                      <Typography variant="subtitle1">Issue Type:</Typography>
                      <Typography variant="body1">{selectedRequest.issueType}</Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle1">Description:</Typography>
                      <Typography variant="body1">{selectedRequest.description}</Typography>
                    </Grid>
                  </>
                )}
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDetailsOpen(false)}>Close</Button>
              {selectedRequest.status !== STATUS_COMPLETED && selectedRequest.status !== STATUS_CANCELLED && (
                <Button
                  onClick={() => {
                    setDetailsOpen(false);
                    handleUpdateStatus(selectedRequest);
                  }}
                  color="primary"
                >
                  Update Status
                </Button>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Status Update Dialog */}
      <Dialog
        open={statusUpdateOpen}
        onClose={() => setStatusUpdateOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Update Service Request Status</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              select
              label="Status"
              value={newStatus}
              onChange={(e) => setNewStatus(e.target.value)}
              fullWidth
              margin="normal"
            >
              <MenuItem value={STATUS_PENDING}>Pending</MenuItem>
              <MenuItem value={STATUS_IN_PROGRESS}>In Progress</MenuItem>
              <MenuItem value={STATUS_COMPLETED}>Completed</MenuItem>
              <MenuItem value={STATUS_CANCELLED}>Cancelled</MenuItem>
            </TextField>

            <TextField
              label="Status Note (Optional)"
              value={statusNote}
              onChange={(e) => setStatusNote(e.target.value)}
              fullWidth
              margin="normal"
              multiline
              rows={3}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStatusUpdateOpen(false)}>Cancel</Button>
          <Button
            onClick={handleStatusUpdate}
            color="primary"
            disabled={updateLoading || !newStatus}
          >
            {updateLoading ? <CircularProgress size={24} /> : 'Update'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ServiceRequestsPage;
