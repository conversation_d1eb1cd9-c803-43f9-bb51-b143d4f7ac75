import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';

class DatabaseService extends ChangeNotifier {
  late final FirebaseFirestore _firestore;
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;

  DatabaseService() {
    try {
      // Check if Firebase is initialized
      if (Firebase.apps.isNotEmpty) {
        try {
          _firestore = FirebaseFirestore.instance;
          _isInitialized = true;
        } catch (firestoreError) {
          // Handle Firestore initialization errors
          _error = "Firestore initialization error: $firestoreError";
          debugPrint(_error);

          // For web platform, handle JavaScriptObject error
          if (kIsWeb &&
              firestoreError.toString().contains('JavaScriptObject')) {
            debugPrint("Detected JavaScriptObject error in web platform");
            // Set initialized to false but don't throw to allow app to continue
            _isInitialized = false;
          } else {
            // Rethrow for other platforms
            rethrow;
          }
        }
      } else {
        _error = "Firebase not initialized";
        debugPrint("Firebase not initialized when creating DatabaseService");
      }
    } catch (e) {
      _error = e.toString();
      debugPrint("Error initializing DatabaseService: $e");
    }
  }

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isInitialized => _isInitialized;

  // Initialize database with sample data
  Future<bool> initializeDatabase() async {
    // If Firebase is not initialized, return false
    if (!_isInitialized) {
      _error = "Firebase not initialized";
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Check if database is already initialized
      final isDbInitialized = await isDatabaseInitialized();
      if (isDbInitialized) {
        _isLoading = false;
        notifyListeners();
        return true;
      }

      // Only add sample data if we're not on web platform
      if (!kIsWeb) {
        // Add sample users
        await _addSampleUsers();

        // Add sample hotels
        await _addSampleHotels();

        // Add sample rooms
        await _addSampleRooms();
      } else {
        debugPrint("Skipping sample data creation on web platform");
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      debugPrint("Error initializing database: $e");
      notifyListeners();
      return false;
    }
  }

  // Check if database is already initialized
  Future<bool> isDatabaseInitialized() async {
    // If Firebase is not initialized, return false
    if (!_isInitialized) {
      return false;
    }

    try {
      // For web platform, handle differently to avoid JavaScriptObject errors
      if (kIsWeb) {
        debugPrint("Using web-safe database initialization check");
        try {
          final hotelsSnapshot = await _firestore
              .collection(AppConstants.hotelsCollection)
              .limit(1)
              .get();
          return hotelsSnapshot.docs.isNotEmpty;
        } catch (webError) {
          debugPrint("Web-specific error checking database: $webError");
          // For web, assume database is not initialized but don't fail
          return false;
        }
      } else {
        // For non-web platforms
        final hotelsSnapshot = await _firestore
            .collection(AppConstants.hotelsCollection)
            .limit(1)
            .get();
        return hotelsSnapshot.docs.isNotEmpty;
      }
    } catch (e) {
      _error = e.toString();
      debugPrint("Error checking if database is initialized: $e");
      return false;
    }
  }

  // Add sample users
  Future<void> _addSampleUsers() async {
    final users = [
      {
        'uid': 'admin1',
        'email': '<EMAIL>',
        'displayName': 'Admin User',
        'role': AppConstants.roleAdmin,
        'createdAt': FieldValue.serverTimestamp(),
      },
      {
        'uid': 'vendor1',
        'email': '<EMAIL>',
        'displayName': 'Vendor User',
        'role': AppConstants.roleVendor,
        'createdAt': FieldValue.serverTimestamp(),
      },
      {
        'uid': 'user1',
        'email': '<EMAIL>',
        'displayName': 'Regular User',
        'role': AppConstants.roleUser,
        'createdAt': FieldValue.serverTimestamp(),
      },
    ];

    final batch = _firestore.batch();
    for (final user in users) {
      final docRef = _firestore
          .collection(AppConstants.usersCollection)
          .doc(user['uid'] as String);
      batch.set(docRef, user);
    }

    await batch.commit();
  }

  // Add sample hotels
  Future<void> _addSampleHotels() async {
    final hotels = [
      {
        'id': 'hotel1',
        'name': 'Grand Hotel',
        'description': 'A luxurious hotel in the heart of the city',
        'address': '123 Main Street',
        'city': 'New York',
        'country': 'USA',
        'zipCode': '10001',
        'phone': '******-456-7890',
        'email': '<EMAIL>',
        'website': 'https://www.grandhotel.com',
        'rating': 4.5,
        'price': 200,
        'amenities': ['WiFi', 'Pool', 'Spa', 'Gym', 'Restaurant'],
        'images': [
          'https://source.unsplash.com/random/800x600?hotel',
          'https://source.unsplash.com/random/800x600?hotel-room',
          'https://source.unsplash.com/random/800x600?hotel-lobby',
        ],
        'latitude': 40.7128,
        'longitude': -74.0060,
        'createdAt': FieldValue.serverTimestamp(),
        'vendorId': 'vendor1',
      },
      {
        'id': 'hotel2',
        'name': 'Seaside Resort',
        'description': 'A beautiful resort by the beach',
        'address': '456 Ocean Drive',
        'city': 'Miami',
        'country': 'USA',
        'zipCode': '33139',
        'phone': '******-654-3210',
        'email': '<EMAIL>',
        'website': 'https://www.seasideresort.com',
        'rating': 4.8,
        'price': 350,
        'amenities': [
          'WiFi',
          'Pool',
          'Beach Access',
          'Spa',
          'Gym',
          'Restaurant',
          'Bar',
        ],
        'images': [
          'https://source.unsplash.com/random/800x600?resort',
          'https://source.unsplash.com/random/800x600?beach-hotel',
          'https://source.unsplash.com/random/800x600?resort-pool',
        ],
        'latitude': 25.7617,
        'longitude': -80.1918,
        'createdAt': FieldValue.serverTimestamp(),
        'vendorId': 'vendor1',
      },
    ];

    final batch = _firestore.batch();
    for (final hotel in hotels) {
      final docRef = _firestore
          .collection(AppConstants.hotelsCollection)
          .doc(hotel['id'] as String);
      batch.set(docRef, hotel);
    }

    await batch.commit();
  }

  // Add sample rooms
  Future<void> _addSampleRooms() async {
    final rooms = [
      {
        'id': 'room1',
        'hotelId': 'hotel1',
        'name': 'Deluxe Room',
        'description': 'A spacious room with a king-size bed',
        'price': 200,
        'capacity': 2,
        'amenities': ['WiFi', 'TV', 'Mini Bar', 'Air Conditioning'],
        'images': [
          'https://source.unsplash.com/random/800x600?hotel-room',
          'https://source.unsplash.com/random/800x600?hotel-bathroom',
        ],
        'status': AppConstants.roomAvailable,
        'createdAt': FieldValue.serverTimestamp(),
      },
      {
        'id': 'room2',
        'hotelId': 'hotel1',
        'name': 'Suite',
        'description': 'A luxurious suite with a separate living area',
        'price': 350,
        'capacity': 4,
        'amenities': ['WiFi', 'TV', 'Mini Bar', 'Air Conditioning', 'Jacuzzi'],
        'images': [
          'https://source.unsplash.com/random/800x600?hotel-suite',
          'https://source.unsplash.com/random/800x600?hotel-living-room',
        ],
        'status': AppConstants.roomAvailable,
        'createdAt': FieldValue.serverTimestamp(),
      },
      {
        'id': 'room3',
        'hotelId': 'hotel2',
        'name': 'Ocean View Room',
        'description': 'A beautiful room with an ocean view',
        'price': 300,
        'capacity': 2,
        'amenities': ['WiFi', 'TV', 'Mini Bar', 'Air Conditioning', 'Balcony'],
        'images': [
          'https://source.unsplash.com/random/800x600?ocean-view-room',
          'https://source.unsplash.com/random/800x600?hotel-balcony',
        ],
        'status': AppConstants.roomAvailable,
        'createdAt': FieldValue.serverTimestamp(),
      },
      {
        'id': 'room4',
        'hotelId': 'hotel2',
        'name': 'Beach Front Villa',
        'description': 'A spacious villa right on the beach',
        'price': 500,
        'capacity': 6,
        'amenities': [
          'WiFi',
          'TV',
          'Kitchen',
          'Air Conditioning',
          'Private Pool',
          'Beach Access',
        ],
        'images': [
          'https://source.unsplash.com/random/800x600?beach-villa',
          'https://source.unsplash.com/random/800x600?villa-pool',
        ],
        'status': AppConstants.roomAvailable,
        'createdAt': FieldValue.serverTimestamp(),
      },
    ];

    final batch = _firestore.batch();
    for (final room in rooms) {
      final docRef = _firestore
          .collection(AppConstants.roomsCollection)
          .doc(room['id'] as String);
      batch.set(docRef, room);
    }

    await batch.commit();
  }
}
