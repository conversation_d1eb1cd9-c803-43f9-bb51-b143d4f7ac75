import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

/// Utility class to check authentication status and handle common auth issues
class AuthChecker {
  /// Check if the user is authenticated
  /// Returns true if authenticated, false otherwise
  static bool isAuthenticated() {
    final user = FirebaseAuth.instance.currentUser;
    return user != null;
  }

  /// Get the current user ID
  /// Returns the user ID if authenticated, null otherwise
  static String? getCurrentUserId() {
    final user = FirebaseAuth.instance.currentUser;
    return user?.uid;
  }

  /// Show authentication error dialog
  static Future<void> showAuthErrorDialog(BuildContext context, String message) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Authentication Error'),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(message),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('OK'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  /// Check authentication and show error dialog if not authenticated
  /// Returns true if authenticated, false otherwise
  static Future<bool> checkAuthAndShowError(BuildContext context) async {
    if (!isAuthenticated()) {
      await showAuthErrorDialog(
        context,
        'You need to be logged in to perform this action. Please log in and try again.',
      );
      return false;
    }
    return true;
  }

  /// Log authentication details for debugging
  static void logAuthDetails() {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      debugPrint('User is authenticated:');
      debugPrint('  UID: ${user.uid}');
      debugPrint('  Email: ${user.email}');
      debugPrint('  Display Name: ${user.displayName}');
      debugPrint('  Phone Number: ${user.phoneNumber}');
      debugPrint('  Email Verified: ${user.emailVerified}');
    } else {
      debugPrint('User is NOT authenticated');
    }
  }
}
