const admin = require('firebase-admin');
const serviceAccount = require('../serviceAccountKey.json');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

// Sample Aadhaar verification data
const verifications = [
  {
    userId: 'user1',
    maskedAadhaarNumber: 'XXXX XXXX 1234',
    fullName: '<PERSON>',
    dateOfBirth: admin.firestore.Timestamp.fromDate(new Date('1990-05-15')),
    gender: 'Male',
    address: '123 Main St, Bangalore, Karnataka, 560001',
    status: 'verified',
    verifiedAt: admin.firestore.Timestamp.fromDate(new Date()),
    verificationMethod: 'OTP',
    referenceId: 'REF123456',
    createdAt: admin.firestore.Timestamp.fromDate(new Date()),
    updatedAt: admin.firestore.Timestamp.fromDate(new Date())
  },
  {
    userId: 'user2',
    maskedAadhaarNumber: 'XXXX XXXX 5678',
    fullName: '<PERSON>',
    dateOfBirth: admin.firestore.Timestamp.fromDate(new Date('1985-10-20')),
    gender: 'Female',
    address: '456 Park Ave, Mumbai, Maharashtra, 400001',
    status: 'verified',
    verifiedAt: admin.firestore.Timestamp.fromDate(new Date()),
    verificationMethod: 'OTP',
    referenceId: 'REF789012',
    createdAt: admin.firestore.Timestamp.fromDate(new Date()),
    updatedAt: admin.firestore.Timestamp.fromDate(new Date())
  },
  {
    userId: 'user3',
    maskedAadhaarNumber: 'XXXX XXXX 9012',
    fullName: 'Raj Kumar',
    status: 'pending',
    createdAt: admin.firestore.Timestamp.fromDate(new Date()),
    updatedAt: admin.firestore.Timestamp.fromDate(new Date())
  },
  {
    userId: 'user4',
    maskedAadhaarNumber: 'XXXX XXXX 3456',
    fullName: 'Priya Sharma',
    status: 'failed',
    createdAt: admin.firestore.Timestamp.fromDate(new Date()),
    updatedAt: admin.firestore.Timestamp.fromDate(new Date())
  },
  {
    userId: 'user5',
    maskedAadhaarNumber: 'XXXX XXXX 7890',
    fullName: 'Amit Patel',
    status: 'notVerified',
    createdAt: admin.firestore.Timestamp.fromDate(new Date()),
    updatedAt: admin.firestore.Timestamp.fromDate(new Date())
  }
];

// Create Aadhaar verifications collection
async function createAadhaarVerifications() {
  try {
    console.log('Creating Aadhaar verifications collection...');
    
    // Create a batch
    const batch = db.batch();
    
    // Add each verification to the batch
    for (const verification of verifications) {
      const verificationRef = db.collection('aadhaarVerifications').doc();
      batch.set(verificationRef, verification);
    }
    
    // Commit the batch
    await batch.commit();
    
    console.log('Aadhaar verifications collection created successfully!');
  } catch (error) {
    console.error('Error creating Aadhaar verifications collection:', error);
  }
}

// Run the script
createAadhaarVerifications()
  .then(() => {
    console.log('Script completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
