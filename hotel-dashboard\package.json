{"name": "hotel-dashboard", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fullcalendar/core": "^6.1.8", "@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/interaction": "^6.1.8", "@fullcalendar/react": "^6.1.8", "@fullcalendar/timegrid": "^6.1.8", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.17.1", "@mui/x-date-pickers": "^5.0.20", "@reduxjs/toolkit": "^1.9.5", "@testing-library/dom": "^9.3.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.23", "@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "axios": "^1.3.6", "date-fns": "^2.30.0", "firebase": "^9.20.0", "formik": "^2.2.9", "i18next": "^22.4.15", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^12.2.0", "react-redux": "^8.0.5", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "recharts": "^2.15.3", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "init-firestore": "node scripts/init-firestore.js", "init-firestore-admin": "node scripts/init-firestore-admin.js", "seed-users": "node scripts/seed-users.js", "seed-users-admin": "node scripts/seed-users-admin.js", "seed-users-direct": "node scripts/seed-users-direct.js", "fix-user-role": "node scripts/fix-user-role.js", "init-database": "node scripts/init-database.js", "create-notifications": "node scripts/create-sample-notification.js", "init-aadhaar-verification": "node scripts/init-aadhaar-verification.js", "seed-vendor-notifications": "node scripts/seed-vendor-notifications.js", "seed-service-requests": "node scripts/seed-service-requests.js", "seed-staff-members": "node scripts/seed-staff-members.js", "seed-staff-assignments": "node scripts/seed-staff-assignments.js", "seed-staff-schedules": "node scripts/seed-staff-schedules.js", "seed-time-off-requests": "node scripts/seed-time-off-requests.js", "seed-schedule-templates": "node scripts/seed-schedule-templates.js", "seed-staff-availability": "node scripts/seed-staff-availability.js", "fix-calendar": "node fix-calendar.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"firebase-admin": "^13.4.0", "ts-node": "^10.9.2"}}