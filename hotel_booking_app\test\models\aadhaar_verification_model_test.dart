import 'package:flutter_test/flutter_test.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hotel_booking_app/data/models/aadhaar_verification_model.dart';

void main() {
  group('AadhaarVerification Model', () {
    test('fromMap should create a valid model from Map', () {
      // Arrange
      final now = DateTime.now();
      final timestamp = Timestamp.fromDate(now);

      // Create map data
      final Map<String, dynamic> mapData = {
        'id': 'test-map-id',
        'userId': 'test-user-id',
        'maskedAadhaarNumber': 'XXXX-XXXX-5678',
        'fullName': 'Map User',
        'dateOfBirth': timestamp,
        'gender': 'F',
        'address': 'Map Address',
        'status': 'pending',
        'verifiedAt': timestamp,
        'verificationMethod': 'OTP',
        'referenceId': 'map-reference-id',
        'verificationDetails': {
          'otpVerified': false,
        },
        'createdAt': timestamp,
        'updatedAt': timestamp,
      };

      // Act
      final verification = AadhaarVerification.fromMap(mapData);

      // Assert
      expect(verification.id, equals('test-map-id'));
      expect(verification.userId, equals('test-user-id'));
      expect(verification.maskedAadhaarNumber, equals('XXXX-XXXX-5678'));
      expect(verification.fullName, equals('Map User'));
      expect(verification.dateOfBirth, equals(now));
      expect(verification.gender, equals('F'));
      expect(verification.address, equals('Map Address'));
      expect(verification.status, equals(AadhaarVerificationStatus.pending));
      expect(verification.verifiedAt, equals(now));
      expect(verification.verificationMethod, equals('OTP'));
      expect(verification.referenceId, equals('map-reference-id'));
      expect(verification.verificationDetails?['otpVerified'], isFalse);
      expect(verification.createdAt, equals(now));
      expect(verification.updatedAt, equals(now));
    });

    test('toMap should convert model to Map correctly', () {
      // Arrange
      final now = DateTime.now();
      final verification = AadhaarVerification(
        id: 'test-id',
        userId: 'test-user-id',
        maskedAadhaarNumber: 'XXXX-XXXX-9012',
        fullName: 'Test User',
        dateOfBirth: now,
        gender: 'M',
        address: 'Test Address',
        status: AadhaarVerificationStatus.verified,
        verifiedAt: now,
        verificationMethod: 'OTP',
        referenceId: 'test-reference-id',
        verificationDetails: {
          'otpVerified': true,
        },
        createdAt: now,
        updatedAt: now,
      );

      // Act
      final map = verification.toMap();

      // Assert
      expect(map['userId'], equals('test-user-id'));
      expect(map['maskedAadhaarNumber'], equals('XXXX-XXXX-9012'));
      expect(map['fullName'], equals('Test User'));
      expect(map['dateOfBirth'], equals(now));
      expect(map['gender'], equals('M'));
      expect(map['address'], equals('Test Address'));
      expect(map['status'], equals('verified'));
      expect(map['verifiedAt'], equals(now));
      expect(map['verificationMethod'], equals('OTP'));
      expect(map['referenceId'], equals('test-reference-id'));
      expect(map['verificationDetails']?['otpVerified'], isTrue);
      expect(map['createdAt'], equals(now));
      expect(map['updatedAt'], equals(now));
    });

    // Note: We can't test private methods directly, but we can test them indirectly
    // through the public API. For a real project, consider making these methods public
    // or providing public wrappers for testing.

    test('status conversion works correctly through the public API', () {
      // Arrange
      final now = DateTime.now();

      // Create verifications with different statuses
      final notVerified = AadhaarVerification(
        id: 'test-id-1',
        userId: 'user-1',
        maskedAadhaarNumber: 'XXXX-XXXX-1111',
        fullName: 'Not Verified User',
        status: AadhaarVerificationStatus.notVerified,
        createdAt: now,
        updatedAt: now,
      );

      final pending = AadhaarVerification(
        id: 'test-id-2',
        userId: 'user-2',
        maskedAadhaarNumber: 'XXXX-XXXX-2222',
        fullName: 'Pending User',
        status: AadhaarVerificationStatus.pending,
        createdAt: now,
        updatedAt: now,
      );

      final verified = AadhaarVerification(
        id: 'test-id-3',
        userId: 'user-3',
        maskedAadhaarNumber: 'XXXX-XXXX-3333',
        fullName: 'Verified User',
        status: AadhaarVerificationStatus.verified,
        createdAt: now,
        updatedAt: now,
      );

      final failed = AadhaarVerification(
        id: 'test-id-4',
        userId: 'user-4',
        maskedAadhaarNumber: 'XXXX-XXXX-4444',
        fullName: 'Failed User',
        status: AadhaarVerificationStatus.failed,
        createdAt: now,
        updatedAt: now,
      );

      // Act & Assert - Convert to map and check status string
      expect(notVerified.toMap()['status'], equals('notVerified'));
      expect(pending.toMap()['status'], equals('pending'));
      expect(verified.toMap()['status'], equals('verified'));
      expect(failed.toMap()['status'], equals('failed'));

      // Act & Assert - Convert back from map and check status enum
      expect(
          AadhaarVerification.fromMap({
            'id': 'id-1',
            'status': 'notVerified',
            'userId': 'u1',
            'maskedAadhaarNumber': 'XXXX-XXXX-1111',
            'fullName': 'Test User 1',
            'createdAt': Timestamp.now(),
            'updatedAt': Timestamp.now(),
          }).status,
          equals(AadhaarVerificationStatus.notVerified));
      expect(
          AadhaarVerification.fromMap({
            'id': 'id-2',
            'status': 'pending',
            'userId': 'u2',
            'maskedAadhaarNumber': 'XXXX-XXXX-2222',
            'fullName': 'Test User 2',
            'createdAt': Timestamp.now(),
            'updatedAt': Timestamp.now(),
          }).status,
          equals(AadhaarVerificationStatus.pending));
      expect(
          AadhaarVerification.fromMap({
            'id': 'id-3',
            'status': 'verified',
            'userId': 'u3',
            'maskedAadhaarNumber': 'XXXX-XXXX-3333',
            'fullName': 'Test User 3',
            'createdAt': Timestamp.now(),
            'updatedAt': Timestamp.now(),
          }).status,
          equals(AadhaarVerificationStatus.verified));
      expect(
          AadhaarVerification.fromMap({
            'id': 'id-4',
            'status': 'failed',
            'userId': 'u4',
            'maskedAadhaarNumber': 'XXXX-XXXX-4444',
            'fullName': 'Test User 4',
            'createdAt': Timestamp.now(),
            'updatedAt': Timestamp.now(),
          }).status,
          equals(AadhaarVerificationStatus.failed));
      expect(
          AadhaarVerification.fromMap({
            'id': 'id-5',
            'status': 'unknown',
            'userId': 'u5',
            'maskedAadhaarNumber': 'XXXX-XXXX-5555',
            'fullName': 'Test User 5',
            'createdAt': Timestamp.now(),
            'updatedAt': Timestamp.now(),
          }).status,
          equals(AadhaarVerificationStatus.notVerified)); // Default for unknown
    });
  });
}
