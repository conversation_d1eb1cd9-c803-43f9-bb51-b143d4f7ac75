import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hotel_booking_app/core/theme/app_theme.dart';
import 'package:hotel_booking_app/data/models/hotel_model.dart';
import 'package:hotel_booking_app/data/models/review_model.dart';
import 'package:hotel_booking_app/data/services/auth_service.dart';
import 'package:hotel_booking_app/data/services/hotel_service.dart';
import 'package:hotel_booking_app/data/services/review_service.dart';
import 'package:hotel_booking_app/presentation/screens/hotel/write_review_screen.dart';

class HotelReviewsScreen extends StatefulWidget {
  final String hotelId;
  final String hotelName;

  const HotelReviewsScreen({
    super.key,
    required this.hotelId,
    required this.hotelName,
  });

  @override
  _HotelReviewsScreenState createState() => _HotelReviewsScreenState();
}

class _HotelReviewsScreenState extends State<HotelReviewsScreen> {
  late ReviewService _reviewService;
  late AuthService _authService;
  late HotelService _hotelService;

  List<Review> _reviews = [];
  Hotel? _hotel;
  bool _isLoading = true;
  String? _error;

  // Filter options
  String _sortBy = 'newest'; // 'newest', 'oldest', 'highest', 'lowest'
  double? _filterRating; // null means all ratings

  @override
  void initState() {
    super.initState();
    _reviewService = Provider.of<ReviewService>(context, listen: false);
    _authService = Provider.of<AuthService>(context, listen: false);
    _hotelService = Provider.of<HotelService>(context, listen: false);

    _loadHotelAndReviews();
  }

  Future<void> _loadHotelAndReviews() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load hotel details
      await _hotelService.fetchHotelById(widget.hotelId);
      final hotel = _hotelService.selectedHotel;

      // Load reviews
      final reviews = await _reviewService.getHotelReviews(widget.hotelId);

      setState(() {
        _hotel = hotel;
        _reviews = reviews;
        _isLoading = false;
      });

      _applyFilters();
    } catch (e) {
      setState(() {
        _error = 'Failed to load reviews: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  void _applyFilters() {
    // Create a copy of the original reviews
    List<Review> filteredReviews = List.from(_reviews);

    // Apply rating filter if set
    if (_filterRating != null) {
      filteredReviews = filteredReviews.where((review) {
        return review.rating >= _filterRating! &&
            review.rating < _filterRating! + 1;
      }).toList();
    }

    // Apply sorting
    switch (_sortBy) {
      case 'newest':
        filteredReviews.sort((a, b) => (b.createdAt ?? DateTime.now())
            .compareTo(a.createdAt ?? DateTime.now()));
        break;
      case 'oldest':
        filteredReviews.sort((a, b) => (a.createdAt ?? DateTime.now())
            .compareTo(b.createdAt ?? DateTime.now()));
        break;
      case 'highest':
        filteredReviews.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'lowest':
        filteredReviews.sort((a, b) => a.rating.compareTo(b.rating));
        break;
    }

    setState(() {
      _reviews = filteredReviews;
    });
  }

  Future<void> _navigateToWriteReview() async {
    if (!_authService.isAuthenticated) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please sign in to write a review'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    // Check if user has already reviewed this hotel
    final existingReview = await _reviewService.getUserHotelReview(
      _authService.user!.uid,
      widget.hotelId,
    );

    if (!mounted) return;

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WriteReviewScreen(
          hotelId: widget.hotelId,
          hotelName: widget.hotelName,
          existingReview: existingReview,
        ),
      ),
    );

    if (result == true) {
      // Refresh reviews if a review was added or updated
      _loadHotelAndReviews();
    }
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Filter & Sort Reviews',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),

                // Sort options
                const Text(
                  'Sort by',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 10),
                Wrap(
                  spacing: 8,
                  children: [
                    _buildSortChip('Newest', 'newest', setState),
                    _buildSortChip('Oldest', 'oldest', setState),
                    _buildSortChip('Highest Rating', 'highest', setState),
                    _buildSortChip('Lowest Rating', 'lowest', setState),
                  ],
                ),

                const SizedBox(height: 20),

                // Rating filter
                const Text(
                  'Filter by Rating',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 10),
                Wrap(
                  spacing: 8,
                  children: [
                    _buildRatingFilterChip('All', null, setState),
                    _buildRatingFilterChip('5 ★', 5, setState),
                    _buildRatingFilterChip('4 ★', 4, setState),
                    _buildRatingFilterChip('3 ★', 3, setState),
                    _buildRatingFilterChip('2 ★', 2, setState),
                    _buildRatingFilterChip('1 ★', 1, setState),
                  ],
                ),

                const SizedBox(height: 20),

                // Apply button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _applyFilters();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Apply Filters'),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSortChip(String label, String value, StateSetter setState) {
    return FilterChip(
      label: Text(label),
      selected: _sortBy == value,
      onSelected: (selected) {
        setState(() {
          _sortBy = value;
        });
      },
      backgroundColor: Colors.grey.shade200,
      selectedColor: AppTheme.primaryColor.withAlpha(50),
      checkmarkColor: AppTheme.primaryColor,
    );
  }

  Widget _buildRatingFilterChip(
      String label, double? value, StateSetter setState) {
    return FilterChip(
      label: Text(label),
      selected: _filterRating == value,
      onSelected: (selected) {
        setState(() {
          _filterRating = selected ? value : null;
        });
      },
      backgroundColor: Colors.grey.shade200,
      selectedColor: AppTheme.primaryColor.withAlpha(50),
      checkmarkColor: AppTheme.primaryColor,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Reviews for ${widget.hotelName}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _error!,
                        textAlign: TextAlign.center,
                        style: const TextStyle(color: Colors.red),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadHotelAndReviews,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    _buildReviewSummary(),
                    Expanded(
                      child: _reviews.isEmpty
                          ? _buildNoReviewsMessage()
                          : _buildReviewsList(),
                    ),
                  ],
                ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _navigateToWriteReview,
        icon: const Icon(Icons.rate_review),
        label: const Text('Write a Review'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildReviewSummary() {
    if (_hotel == null) return const SizedBox.shrink();

    // Calculate rating distribution
    final Map<int, int> ratingCounts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0};
    for (final review in _reviews) {
      final rating = review.rating.round();
      if (rating >= 1 && rating <= 5) {
        ratingCounts[rating] = (ratingCounts[rating] ?? 0) + 1;
      }
    }

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                // Average rating display
                Column(
                  children: [
                    Text(
                      _hotel!.rating.toStringAsFixed(1),
                      style: const TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Row(
                      children: List.generate(5, (index) {
                        return Icon(
                          index < _hotel!.rating.floor()
                              ? Icons.star
                              : (index < _hotel!.rating
                                  ? Icons.star_half
                                  : Icons.star_border),
                          color: Colors.amber,
                          size: 20,
                        );
                      }),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${_reviews.length} reviews',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 24),

                // Rating bars
                Expanded(
                  child: Column(
                    children: [5, 4, 3, 2, 1].map((rating) {
                      final count = ratingCounts[rating] ?? 0;
                      final percentage =
                          _reviews.isEmpty ? 0.0 : count / _reviews.length;

                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Row(
                          children: [
                            Text(
                              '$rating',
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Icon(Icons.star,
                                size: 12, color: Colors.amber),
                            const SizedBox(width: 8),
                            Expanded(
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(4),
                                child: LinearProgressIndicator(
                                  value: percentage,
                                  backgroundColor: Colors.grey.shade200,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppTheme.primaryColor,
                                  ),
                                  minHeight: 8,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              count.toString(),
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoReviewsMessage() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.rate_review_outlined,
            size: 80,
            color: Colors.grey.shade300,
          ),
          const SizedBox(height: 16),
          Text(
            'No Reviews Yet',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Be the first to review this hotel!',
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _navigateToWriteReview,
            icon: const Icon(Icons.create),
            label: const Text('Write a Review'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _reviews.length,
      itemBuilder: (context, index) {
        final review = _reviews[index];
        return _buildReviewCard(review);
      },
    );
  }

  Widget _buildReviewCard(Review review) {
    final isCurrentUserReview =
        _authService.isAuthenticated && _authService.user!.uid == review.userId;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // User avatar
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.grey.shade200,
                  backgroundImage: review.userImage != null
                      ? NetworkImage(review.userImage!)
                      : null,
                  child: review.userImage == null
                      ? Text(
                          review.userName.isNotEmpty
                              ? review.userName[0].toUpperCase()
                              : '?',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: 12),

                // User name and date
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        review.userName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      if (review.createdAt != null)
                        Text(
                          _formatDate(review.createdAt!),
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                    ],
                  ),
                ),

                // Rating
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getRatingColor(review.rating),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.star,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        review.rating.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Review content
            Text(
              review.comment,
              style: const TextStyle(fontSize: 14),
            ),

            // Edit/Delete options for user's own reviews
            if (isCurrentUserReview) ...[
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('Edit'),
                    onPressed: () async {
                      final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => WriteReviewScreen(
                            hotelId: widget.hotelId,
                            hotelName: widget.hotelName,
                            existingReview: review,
                          ),
                        ),
                      );

                      if (result == true) {
                        _loadHotelAndReviews();
                      }
                    },
                  ),
                  TextButton.icon(
                    icon: const Icon(Icons.delete, size: 16),
                    label: const Text('Delete'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                    onPressed: () => _showDeleteConfirmation(review),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(Review review) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Review'),
        content: const Text(
          'Are you sure you want to delete your review? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);

              setState(() {
                _isLoading = true;
              });

              final success = await _reviewService.deleteReview(review.id);

              if (mounted) {
                setState(() {
                  _isLoading = false;
                });

                if (success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Review deleted successfully'),
                      behavior: SnackBarBehavior.floating,
                    ),
                  );

                  _loadHotelAndReviews();
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                          _reviewService.error ?? 'Failed to delete review'),
                      behavior: SnackBarBehavior.floating,
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Color _getRatingColor(double rating) {
    if (rating >= 4.5) return Colors.green;
    if (rating >= 3.5) return Colors.lightGreen;
    if (rating >= 2.5) return Colors.amber;
    if (rating >= 1.5) return Colors.orange;
    return Colors.red;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
