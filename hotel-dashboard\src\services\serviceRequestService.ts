import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit as firestoreLimit,
  Timestamp,
  serverTimestamp,
  DocumentSnapshot,
  writeBatch
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { createServiceRequestNotification } from './vendorNotificationService';

// Collection name
const SERVICE_REQUESTS_COLLECTION = 'serviceRequests';

// Service request types
export const SERVICE_TYPE_CLEANING = 'cleaning';
export const SERVICE_TYPE_FOOD = 'food';
export const SERVICE_TYPE_MAINTENANCE = 'maintenance';

// Service request statuses
export const SERVICE_STATUS_PENDING = 'pending';
export const SERVICE_STATUS_IN_PROGRESS = 'in_progress';
export const SERVICE_STATUS_COMPLETED = 'completed';
export const SERVICE_STATUS_CANCELLED = 'cancelled';

// Base service request interface
export interface BaseServiceRequest {
  id?: string;
  type: string;
  status: string;
  roomNumber: string;
  hotelId: string;
  guestId?: string;
  guestName?: string;
  requestTime: Timestamp;
  notes: string;
  priority: 'low' | 'medium' | 'high';
  assignedTo?: string;
  completedTime?: Timestamp;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// Cleaning request interface
export interface CleaningRequest extends BaseServiceRequest {
  type: typeof SERVICE_TYPE_CLEANING;
  cleaningType?: 'regular' | 'deep' | 'turndown' | 'special';
  items?: string[]; // e.g., ['bed', 'bathroom', 'floor']
}

// Food order interface
export interface FoodOrder extends BaseServiceRequest {
  type: typeof SERVICE_TYPE_FOOD;
  items: {
    name: string;
    quantity: number;
    price: number;
    specialInstructions?: string;
  }[];
  totalAmount: number;
  deliveryTime?: Timestamp;
  paymentMethod?: string;
}

// Maintenance request interface
export interface MaintenanceRequest extends BaseServiceRequest {
  type: typeof SERVICE_TYPE_MAINTENANCE;
  issueType: string; // e.g., 'plumbing', 'electrical', 'hvac', 'furniture'
  description: string;
  estimatedCompletionTime?: Timestamp;
  partsRequired?: string[];
}

// Union type for all service requests
export type ServiceRequest = CleaningRequest | FoodOrder | MaintenanceRequest;

/**
 * Create a new service request
 */
export const createServiceRequest = async <T extends ServiceRequest>(
  serviceRequest: Omit<T, 'id' | 'createdAt' | 'updatedAt' | 'status' | 'requestTime'>,
  vendorId: string
): Promise<T> => {
  try {
    // Set default values
    const now = Timestamp.now();
    const request: Omit<T, 'id'> = {
      ...serviceRequest,
      status: SERVICE_STATUS_PENDING,
      requestTime: now,
      createdAt: now,
      updatedAt: now
    } as Omit<T, 'id'>;

    // Add to Firestore
    const docRef = await addDoc(collection(db, SERVICE_REQUESTS_COLLECTION), request);

    // Get the created document
    const createdRequest = { id: docRef.id, ...request } as T;

    // Create notification for vendor
    await createServiceRequestNotification(
      vendorId,
      serviceRequest.hotelId,
      {
        id: docRef.id,
        type: serviceRequest.type,
        roomNumber: serviceRequest.roomNumber,
        guestName: serviceRequest.guestName,
        priority: serviceRequest.priority
      }
    );

    return createdRequest;
  } catch (error) {
    console.error('Error creating service request:', error);
    throw error;
  }
};

/**
 * Get service requests for a hotel
 */
export const getServiceRequestsForHotel = async <T extends ServiceRequest>(
  hotelId: string,
  options?: {
    type?: string;
    status?: string;
    limit?: number;
  }
): Promise<T[]> => {
  try {
    let requestsQuery = query(
      collection(db, SERVICE_REQUESTS_COLLECTION),
      where('hotelId', '==', hotelId),
      orderBy('requestTime', 'desc')
    );

    // Apply type filter if provided
    if (options?.type) {
      requestsQuery = query(
        requestsQuery,
        where('type', '==', options.type)
      );
    }

    // Apply status filter if provided
    if (options?.status) {
      requestsQuery = query(
        requestsQuery,
        where('status', '==', options.status)
      );
    }

    // Apply limit if provided
    if (options?.limit) {
      requestsQuery = query(requestsQuery, firestoreLimit(options.limit));
    }

    const querySnapshot = await getDocs(requestsQuery);
    const requests: T[] = [];

    querySnapshot.forEach((doc) => {
      // Create object with correct type
      const data = doc.data() as Omit<T, 'id'>;
      requests.push({
        id: doc.id,
        ...data
      } as T);
    });

    return requests;
  } catch (error) {
    console.error('Error getting service requests:', error);
    throw error;
  }
};

/**
 * Get a service request by ID
 */
export const getServiceRequestById = async <T extends ServiceRequest>(
  requestId: string
): Promise<T | null> => {
  try {
    const docRef = doc(db, SERVICE_REQUESTS_COLLECTION, requestId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      // Create object with correct type
      const data = docSnap.data() as Omit<T, 'id'>;
      return {
        id: docSnap.id,
        ...data
      } as T;
    }

    return null;
  } catch (error) {
    console.error('Error getting service request:', error);
    throw error;
  }
};

/**
 * Update a service request
 */
export const updateServiceRequest = async <T extends ServiceRequest>(
  requestId: string,
  updates: Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<boolean> => {
  try {
    const docRef = doc(db, SERVICE_REQUESTS_COLLECTION, requestId);

    // Add updatedAt timestamp
    const updatedData = {
      ...updates,
      updatedAt: serverTimestamp()
    };

    await updateDoc(docRef, updatedData);
    return true;
  } catch (error) {
    console.error('Error updating service request:', error);
    throw error;
  }
};

/**
 * Delete a service request
 */
export const deleteServiceRequest = async (requestId: string): Promise<boolean> => {
  try {
    await deleteDoc(doc(db, SERVICE_REQUESTS_COLLECTION, requestId));
    return true;
  } catch (error) {
    console.error('Error deleting service request:', error);
    throw error;
  }
};

/**
 * Update service request status
 */
export const updateServiceRequestStatus = async (
  requestId: string,
  status: string,
  assignedTo?: string
): Promise<boolean> => {
  try {
    const docRef = doc(db, SERVICE_REQUESTS_COLLECTION, requestId);

    const updates: any = {
      status,
      updatedAt: serverTimestamp()
    };

    // Add completedTime if status is completed
    if (status === SERVICE_STATUS_COMPLETED) {
      updates.completedTime = Timestamp.now();
    }

    // Add assignedTo if provided
    if (assignedTo) {
      updates.assignedTo = assignedTo;
    }

    await updateDoc(docRef, updates);
    return true;
  } catch (error) {
    console.error('Error updating service request status:', error);
    throw error;
  }
};

export default {
  createServiceRequest,
  getServiceRequestsForHotel,
  getServiceRequestById,
  updateServiceRequest,
  deleteServiceRequest,
  updateServiceRequestStatus
};
