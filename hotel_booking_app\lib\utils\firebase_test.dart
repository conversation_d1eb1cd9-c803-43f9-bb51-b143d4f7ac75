import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/utils/hotel_seeder.dart';

/// Utility class for testing Firebase connection and operations
class FirebaseTest {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  /// Test Firebase connection and print detailed information
  static Future<Map<String, dynamic>> testFirebaseConnection() async {
    try {
      debugPrint('Testing Firebase connection...');
      
      // Check if Firebase is initialized
      final apps = FirebaseFirestore.instance.app.options;
      debugPrint('Firebase app name: ${FirebaseFirestore.instance.app.name}');
      debugPrint('Firebase project ID: ${apps.projectId}');
      debugPrint('Firebase API key: ${apps.apiKey}');
      
      // Test Firestore connection by reading hotels collection
      final hotelsSnapshot = await _firestore.collection(AppConstants.hotelsCollection).get();
      
      debugPrint('Successfully connected to Firestore');
      debugPrint('Hotels collection exists: ${hotelsSnapshot.docs.isNotEmpty}');
      debugPrint('Number of hotels: ${hotelsSnapshot.docs.length}');
      
      if (hotelsSnapshot.docs.isNotEmpty) {
        final firstHotel = hotelsSnapshot.docs.first;
        debugPrint('First hotel ID: ${firstHotel.id}');
        debugPrint('First hotel name: ${firstHotel.data()['name']}');
      }
      
      return {
        'success': true,
        'message': 'Firebase connection successful',
        'hotelCount': hotelsSnapshot.docs.length,
        'hotelsExist': hotelsSnapshot.docs.isNotEmpty,
      };
    } catch (e) {
      debugPrint('Error testing Firebase connection: $e');
      return {
        'success': false,
        'message': 'Firebase connection failed: $e',
        'error': e.toString(),
      };
    }
  }
  
  /// Force create hotels in Firestore for testing
  static Future<Map<String, dynamic>> forceCreateHotels() async {
    try {
      debugPrint('Force creating hotels in Firestore...');
      
      // First check if hotels already exist
      final hotelsSnapshot = await _firestore.collection(AppConstants.hotelsCollection).get();
      final existingCount = hotelsSnapshot.docs.length;
      
      debugPrint('Found $existingCount existing hotels');
      
      // Create hotels using the HotelSeeder
      final success = await HotelSeeder.seedHotels();
      
      // Check how many hotels were created
      final newSnapshot = await _firestore.collection(AppConstants.hotelsCollection).get();
      final newCount = newSnapshot.docs.length;
      
      debugPrint('After seeding: $newCount hotels in collection');
      debugPrint('Added ${newCount - existingCount} new hotels');
      
      return {
        'success': success,
        'message': success ? 'Successfully created hotels' : 'Failed to create hotels',
        'previousCount': existingCount,
        'newCount': newCount,
        'addedCount': newCount - existingCount,
      };
    } catch (e) {
      debugPrint('Error force creating hotels: $e');
      return {
        'success': false,
        'message': 'Failed to create hotels: $e',
        'error': e.toString(),
      };
    }
  }
  
  /// Clear all hotels from Firestore for testing
  static Future<Map<String, dynamic>> clearAllHotels() async {
    try {
      debugPrint('Clearing all hotels from Firestore...');
      
      // Get all hotels
      final hotelsSnapshot = await _firestore.collection(AppConstants.hotelsCollection).get();
      final count = hotelsSnapshot.docs.length;
      
      debugPrint('Found $count hotels to delete');
      
      // Delete each hotel
      int deletedCount = 0;
      for (final doc in hotelsSnapshot.docs) {
        await _firestore.collection(AppConstants.hotelsCollection).doc(doc.id).delete();
        deletedCount++;
      }
      
      debugPrint('Successfully deleted $deletedCount hotels');
      
      return {
        'success': true,
        'message': 'Successfully cleared all hotels',
        'deletedCount': deletedCount,
      };
    } catch (e) {
      debugPrint('Error clearing hotels: $e');
      return {
        'success': false,
        'message': 'Failed to clear hotels: $e',
        'error': e.toString(),
      };
    }
  }
}
