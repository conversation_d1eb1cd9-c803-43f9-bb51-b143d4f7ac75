import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  Divider,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Tooltip
} from '@mui/material';
import {
  Search as SearchIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  Hotel as HotelIcon,
  VerifiedUser as VerifiedUserIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { getBookingByNumber } from '../../services/bookingService';
import { getVerificationByUserId } from '../../services/verificationService';
import AadhaarVerificationCard from '../../components/AadhaarVerificationCard';

const CheckIn: React.FC = () => {
  const navigate = useNavigate();
  const [bookingNumber, setBookingNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [booking, setBooking] = useState<any | null>(null);
  const [verification, setVerification] = useState<any | null>(null);
  const [verificationLoading, setVerificationLoading] = useState(false);
  const [verificationError, setVerificationError] = useState<string | null>(null);
  const [checkInSuccess, setCheckInSuccess] = useState(false);

  // Handle search booking
  const handleSearchBooking = async () => {
    if (!bookingNumber.trim()) {
      setError('Please enter a booking number');
      return;
    }

    setLoading(true);
    setError(null);
    setBooking(null);
    setVerification(null);
    setCheckInSuccess(false);

    try {
      const bookingData = await getBookingByNumber(bookingNumber.trim());
      setBooking(bookingData);

      if (bookingData && bookingData.userId) {
        // Fetch verification data if booking is found
        await fetchVerificationData(bookingData.userId);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to find booking');
    } finally {
      setLoading(false);
    }
  };

  // Fetch verification data for a user
  const fetchVerificationData = async (userId: string) => {
    setVerificationLoading(true);
    setVerificationError(null);

    try {
      const verificationData = await getVerificationByUserId(userId);
      setVerification(verificationData);
    } catch (err: any) {
      setVerificationError(err.message || 'Failed to fetch verification data');
    } finally {
      setVerificationLoading(false);
    }
  };

  // Handle check-in
  const handleCheckIn = async () => {
    if (!booking || !booking.id) {
      setError('Invalid booking data');
      return;
    }

    if (!verification || verification.status !== 'verified') {
      setError('Aadhaar verification is required for check-in');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Import the checkInGuest function
      const { checkInGuest } = await import('../../services/bookingService');

      // Check in the guest
      await checkInGuest(booking.id, 'Checked in with verified Aadhaar');

      // Update local booking state
      setBooking({
        ...booking,
        status: 'checked_in'
      });

      setCheckInSuccess(true);
    } catch (err: any) {
      setError(err.message || 'Failed to check in guest');
    } finally {
      setLoading(false);
    }
  };

  // Format date
  const formatDate = (date: any) => {
    if (!date) return 'N/A';
    const dateObj = date.toDate ? date.toDate() : new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Guest Check-In
      </Typography>

      {/* Search Form */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Booking Number"
              variant="outlined"
              value={bookingNumber}
              onChange={(e) => setBookingNumber(e.target.value)}
              placeholder="Enter booking number"
              InputProps={{
                endAdornment: (
                  <IconButton
                    onClick={handleSearchBooking}
                    disabled={loading}
                    size="small"
                  >
                    <SearchIcon />
                  </IconButton>
                ),
              }}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  handleSearchBooking();
                }
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Button
              variant="contained"
              onClick={handleSearchBooking}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <SearchIcon />}
            >
              Search
            </Button>
          </Grid>
        </Grid>

        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
      </Paper>

      {/* Booking Details */}
      {booking && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Booking Details
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Box display="flex" alignItems="center">
                    <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="subtitle1">
                      {booking.guestName || 'Guest'}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Booking Number
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {booking.bookingNumber}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Status
                  </Typography>
                  <Chip
                    label={booking.status || 'Confirmed'}
                    color={booking.status === 'checked_in' ? 'success' : 'primary'}
                    size="small"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Check-In Date
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formatDate(booking.checkInDate)}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Check-Out Date
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formatDate(booking.checkOutDate)}
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary">
                    Room
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {booking.roomName || 'Standard Room'}
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary">
                    Special Requests
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {booking.specialRequests || 'None'}
                  </Typography>
                </Grid>
              </Grid>

              <Box mt={3}>
                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  onClick={handleCheckIn}
                  disabled={booking.status === 'checked_in' || !verification || verification.status !== 'verified'}
                  startIcon={<CheckCircleIcon />}
                >
                  Complete Check-In
                </Button>
                {booking.status === 'checked_in' && (
                  <Alert severity="info" sx={{ mt: 2 }}>
                    This guest has already checked in.
                  </Alert>
                )}
                {(!verification || verification.status !== 'verified') && (
                  <Alert severity="warning" sx={{ mt: 2 }}>
                    Aadhaar verification is required for check-in.
                  </Alert>
                )}
                {checkInSuccess && (
                  <Alert severity="success" sx={{ mt: 2 }}>
                    Check-in completed successfully!
                  </Alert>
                )}
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            {/* Aadhaar Verification Card */}
            <Box sx={{ position: 'relative' }}>
              <AadhaarVerificationCard
                verification={verification}
                loading={verificationLoading}
                error={verificationError || undefined}
                onRefresh={() => booking?.userId && fetchVerificationData(booking.userId)}
              />
              {verification && (
                <Button
                  variant="outlined"
                  size="small"
                  sx={{ position: 'absolute', top: 16, right: 16 }}
                  onClick={() => navigate(`/vendor/verifications/${verification.id}`)}
                >
                  View Full Details
                </Button>
              )}
            </Box>

            {/* Verification Status */}
            {verification && verification.status !== 'verified' && (
              <Paper sx={{ p: 3, mb: 3, bgcolor: 'warning.light' }}>
                <Box display="flex" alignItems="center" mb={2}>
                  <WarningIcon color="warning" sx={{ mr: 1 }} />
                  <Typography variant="h6">Verification Required</Typography>
                </Box>
                <Typography variant="body1" paragraph>
                  The guest needs to complete Aadhaar verification through the mobile app before check-in.
                </Typography>
                <Typography variant="body2">
                  Please ask the guest to open the hotel booking app and complete verification from their profile section.
                </Typography>
              </Paper>
            )}
          </Grid>
        </Grid>
      )}

      {/* Instructions when no booking is loaded */}
      {!booking && !loading && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Check-In Instructions
          </Typography>
          <Typography variant="body1" paragraph>
            1. Enter the booking number provided by the guest
          </Typography>
          <Typography variant="body1" paragraph>
            2. Verify the guest's identity using their Aadhaar verification
          </Typography>
          <Typography variant="body1" paragraph>
            3. Complete the check-in process
          </Typography>
          <Alert severity="info">
            Guests must complete Aadhaar verification through the mobile app before check-in.
          </Alert>
        </Paper>
      )}
    </Box>
  );
};

export default CheckIn;
