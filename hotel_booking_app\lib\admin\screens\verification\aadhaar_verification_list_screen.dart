import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/data/models/aadhaar_verification_model.dart';

class AadhaarVerificationListScreen extends StatefulWidget {
  final String? hotelId;

  const AadhaarVerificationListScreen({
    super.key,
    this.hotelId,
  });

  @override
  State<AadhaarVerificationListScreen> createState() =>
      _AadhaarVerificationListScreenState();
}

class _AadhaarVerificationListScreenState
    extends State<AadhaarVerificationListScreen> {
  bool _isLoading = true;
  List<AadhaarVerification> _verifications = [];
  String _filterStatus = 'all';

  @override
  void initState() {
    super.initState();
    _fetchVerifications();
  }

  Future<void> _fetchVerifications() async {
    setState(() {
      _isLoading = true;
    });

    try {
      Query query = FirebaseFirestore.instance
          .collection(AppConstants.aadhaarVerificationsCollection);

      // Apply status filter if not 'all'
      if (_filterStatus != 'all') {
        query = query.where('status', isEqualTo: _filterStatus);
      }

      // If hotelId is provided, filter by hotel
      // In a real implementation, you would join with bookings to filter by hotel
      // For this demo, we'll just fetch all verifications

      final querySnapshot = await query.get();

      final verifications = querySnapshot.docs
          .map((doc) => AadhaarVerification.fromFirestore(doc))
          .toList();

      setState(() {
        _verifications = verifications;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error fetching verifications: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Aadhaar Verifications'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchVerifications,
          ),
        ],
      ),
      body: Column(
        children: [
          // Status filter
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Text(
                  'Filter by status:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _filterStatus,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    items: [
                      const DropdownMenuItem(
                        value: 'all',
                        child: Text('All'),
                      ),
                      DropdownMenuItem(
                        value: AadhaarVerificationStatus.notVerified.toString(),
                        child: const Text('Not Verified'),
                      ),
                      DropdownMenuItem(
                        value: AadhaarVerificationStatus.pending.toString(),
                        child: const Text('Pending'),
                      ),
                      DropdownMenuItem(
                        value: AadhaarVerificationStatus.verified.toString(),
                        child: const Text('Verified'),
                      ),
                      DropdownMenuItem(
                        value: AadhaarVerificationStatus.failed.toString(),
                        child: const Text('Failed'),
                      ),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _filterStatus = value;
                        });
                        _fetchVerifications();
                      }
                    },
                  ),
                ),
              ],
            ),
          ),

          // Stats cards
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                _buildStatCard(
                  'Total',
                  _verifications.length.toString(),
                  Colors.blue,
                  Icons.people,
                ),
                const SizedBox(width: 16),
                _buildStatCard(
                  'Verified',
                  _verifications
                      .where(
                          (v) => v.status == AadhaarVerificationStatus.verified)
                      .length
                      .toString(),
                  Colors.green,
                  Icons.check_circle,
                ),
                const SizedBox(width: 16),
                _buildStatCard(
                  'Pending',
                  _verifications
                      .where(
                          (v) => v.status == AadhaarVerificationStatus.pending)
                      .length
                      .toString(),
                  Colors.orange,
                  Icons.pending,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Verifications list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _verifications.isEmpty
                    ? const Center(
                        child: Text(
                          'No verifications found',
                          style: TextStyle(fontSize: 16),
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _verifications.length,
                        itemBuilder: (context, index) {
                          final verification = _verifications[index];
                          return _buildVerificationCard(verification);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, Color color, IconData icon) {
    return Expanded(
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVerificationCard(AadhaarVerification verification) {
    Color statusColor;
    IconData statusIcon;

    switch (verification.status) {
      case AadhaarVerificationStatus.verified:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case AadhaarVerificationStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.pending;
        break;
      case AadhaarVerificationStatus.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          _showVerificationDetails(verification);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: Colors.grey.shade200,
                    child: const Icon(
                      Icons.person,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          verification.fullName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Aadhaar: ${verification.maskedAadhaarNumber}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          statusIcon,
                          size: 16,
                          color: statusColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getStatusText(verification.status),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: statusColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Verification ID: ${verification.id.substring(0, 8)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  if (verification.verifiedAt != null)
                    Text(
                      'Verified on: ${DateFormat('dd MMM yyyy').format(verification.verifiedAt!)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showVerificationDetails(AadhaarVerification verification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Verification Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Name', verification.fullName),
              const SizedBox(height: 8),
              _buildDetailRow('Aadhaar', verification.maskedAadhaarNumber),
              const SizedBox(height: 8),
              _buildDetailRow(
                'Status',
                _getStatusText(verification.status),
                valueColor: _getStatusColor(verification.status),
              ),
              if (verification.dateOfBirth != null) ...[
                const SizedBox(height: 8),
                _buildDetailRow(
                  'Date of Birth',
                  DateFormat('dd MMM yyyy').format(verification.dateOfBirth!),
                ),
              ],
              if (verification.gender != null) ...[
                const SizedBox(height: 8),
                _buildDetailRow('Gender', verification.gender!),
              ],
              if (verification.address != null) ...[
                const SizedBox(height: 8),
                _buildDetailRow('Address', verification.address!),
              ],
              if (verification.verifiedAt != null) ...[
                const SizedBox(height: 8),
                _buildDetailRow(
                  'Verified On',
                  DateFormat('dd MMM yyyy, hh:mm a')
                      .format(verification.verifiedAt!),
                ),
              ],
              if (verification.verificationMethod != null) ...[
                const SizedBox(height: 8),
                _buildDetailRow('Method', verification.verificationMethod!),
              ],
              const SizedBox(height: 16),
              const Text(
                'User Information',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              _buildDetailRow('User ID', verification.userId),
              // In a real implementation, you would fetch and display more user information
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {Color? valueColor}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            '$label:',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(color: valueColor),
          ),
        ),
      ],
    );
  }

  String _getStatusText(AadhaarVerificationStatus status) {
    switch (status) {
      case AadhaarVerificationStatus.verified:
        return 'Verified';
      case AadhaarVerificationStatus.pending:
        return 'Pending';
      case AadhaarVerificationStatus.failed:
        return 'Failed';
      case AadhaarVerificationStatus.notVerified:
        return 'Not Verified';
      default:
        return 'Unknown';
    }
  }

  Color _getStatusColor(AadhaarVerificationStatus status) {
    switch (status) {
      case AadhaarVerificationStatus.verified:
        return Colors.green;
      case AadhaarVerificationStatus.pending:
        return Colors.orange;
      case AadhaarVerificationStatus.failed:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
