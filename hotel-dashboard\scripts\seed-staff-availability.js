const admin = require('firebase-admin');
const serviceAccount = require('../serviceAccountKey.json');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

// Collection name
const STAFF_AVAILABILITY_COLLECTION = 'staffAvailability';

// Availability types
const AVAILABILITY_TYPE_PREFERRED = 'preferred';
const AVAILABILITY_TYPE_AVAILABLE = 'available';
const AVAILABILITY_TYPE_UNAVAILABLE = 'unavailable';

// Recurring types
const RECURRING_WEEKLY = 'weekly';
const RECURRING_MONTHLY = 'monthly';
const RECURRING_NONE = 'none';

// Sample hotel and vendor IDs (replace with actual IDs)
const HOTEL_ID = 'hotel123';

// Get staff members
async function getStaffMembers() {
  try {
    const staffSnapshot = await db.collection('staff')
      .where('hotelId', '==', HOTEL_ID)
      .get();
    
    const staffMembers = [];
    staffSnapshot.forEach(doc => {
      staffMembers.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    return staffMembers;
  } catch (error) {
    console.error('Error getting staff members:', error);
    throw error;
  }
}

// Generate sample availability for a staff member
function generateAvailabilityForStaff(staffMember) {
  const availability = [];
  
  // Weekly recurring availability
  
  // Monday to Friday, 9-5 preferred for all staff
  for (let day = 0; day < 5; day++) {
    availability.push({
      staffId: staffMember.id,
      staffName: staffMember.name,
      hotelId: HOTEL_ID,
      dayOfWeek: day,
      startTime: '09:00',
      endTime: '17:00',
      type: AVAILABILITY_TYPE_PREFERRED,
      recurring: RECURRING_WEEKLY,
      notes: 'Regular working hours',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    });
  }
  
  // Role-specific availability
  switch (staffMember.role) {
    case 'housekeeping':
      // Early morning availability for housekeeping
      for (let day = 0; day < 5; day++) {
        availability.push({
          staffId: staffMember.id,
          staffName: staffMember.name,
          hotelId: HOTEL_ID,
          dayOfWeek: day,
          startTime: '06:00',
          endTime: '14:00',
          type: AVAILABILITY_TYPE_AVAILABLE,
          recurring: RECURRING_WEEKLY,
          notes: 'Early shift availability',
          createdAt: admin.firestore.Timestamp.now(),
          updatedAt: admin.firestore.Timestamp.now()
        });
      }
      
      // Weekend availability (Saturday)
      availability.push({
        staffId: staffMember.id,
        staffName: staffMember.name,
        hotelId: HOTEL_ID,
        dayOfWeek: 5,
        startTime: '08:00',
        endTime: '16:00',
        type: AVAILABILITY_TYPE_AVAILABLE,
        recurring: RECURRING_WEEKLY,
        notes: 'Weekend availability',
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });
      break;
      
    case 'front_desk':
      // Evening availability for front desk
      for (let day = 0; day < 5; day++) {
        availability.push({
          staffId: staffMember.id,
          staffName: staffMember.name,
          hotelId: HOTEL_ID,
          dayOfWeek: day,
          startTime: '14:00',
          endTime: '22:00',
          type: AVAILABILITY_TYPE_AVAILABLE,
          recurring: RECURRING_WEEKLY,
          notes: 'Evening shift availability',
          createdAt: admin.firestore.Timestamp.now(),
          updatedAt: admin.firestore.Timestamp.now()
        });
      }
      
      // Weekend availability (Sunday)
      availability.push({
        staffId: staffMember.id,
        staffName: staffMember.name,
        hotelId: HOTEL_ID,
        dayOfWeek: 6,
        startTime: '10:00',
        endTime: '18:00',
        type: AVAILABILITY_TYPE_AVAILABLE,
        recurring: RECURRING_WEEKLY,
        notes: 'Weekend availability',
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });
      break;
      
    case 'maintenance':
      // Daytime availability for maintenance
      for (let day = 0; day < 5; day++) {
        availability.push({
          staffId: staffMember.id,
          staffName: staffMember.name,
          hotelId: HOTEL_ID,
          dayOfWeek: day,
          startTime: '08:00',
          endTime: '16:00',
          type: AVAILABILITY_TYPE_AVAILABLE,
          recurring: RECURRING_WEEKLY,
          notes: 'Daytime shift availability',
          createdAt: admin.firestore.Timestamp.now(),
          updatedAt: admin.firestore.Timestamp.now()
        });
      }
      break;
      
    case 'manager':
      // Extended hours for managers
      for (let day = 0; day < 5; day++) {
        availability.push({
          staffId: staffMember.id,
          staffName: staffMember.name,
          hotelId: HOTEL_ID,
          dayOfWeek: day,
          startTime: '08:00',
          endTime: '18:00',
          type: AVAILABILITY_TYPE_AVAILABLE,
          recurring: RECURRING_WEEKLY,
          notes: 'Extended hours',
          createdAt: admin.firestore.Timestamp.now(),
          updatedAt: admin.firestore.Timestamp.now()
        });
      }
      
      // Weekend availability (both days)
      for (let day = 5; day < 7; day++) {
        availability.push({
          staffId: staffMember.id,
          staffName: staffMember.name,
          hotelId: HOTEL_ID,
          dayOfWeek: day,
          startTime: '10:00',
          endTime: '16:00',
          type: AVAILABILITY_TYPE_AVAILABLE,
          recurring: RECURRING_WEEKLY,
          notes: 'Weekend availability',
          createdAt: admin.firestore.Timestamp.now(),
          updatedAt: admin.firestore.Timestamp.now()
        });
      }
      break;
  }
  
  // Unavailable periods
  
  // All staff unavailable late night
  for (let day = 0; day < 7; day++) {
    availability.push({
      staffId: staffMember.id,
      staffName: staffMember.name,
      hotelId: HOTEL_ID,
      dayOfWeek: day,
      startTime: '22:00',
      endTime: '06:00',
      type: AVAILABILITY_TYPE_UNAVAILABLE,
      recurring: RECURRING_WEEKLY,
      notes: 'Not available late night',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    });
  }
  
  // One-time availability/unavailability
  
  // Random day off in the next month
  const today = new Date();
  const randomDaysAhead = Math.floor(Math.random() * 30) + 1;
  const dayOff = new Date(today);
  dayOff.setDate(today.getDate() + randomDaysAhead);
  
  availability.push({
    staffId: staffMember.id,
    staffName: staffMember.name,
    hotelId: HOTEL_ID,
    specificDate: admin.firestore.Timestamp.fromDate(dayOff),
    startTime: '00:00',
    endTime: '23:59',
    type: AVAILABILITY_TYPE_UNAVAILABLE,
    recurring: RECURRING_NONE,
    notes: 'Personal day off',
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  });
  
  return availability;
}

// Seed staff availability
async function seedStaffAvailability() {
  try {
    console.log('Seeding staff availability...');
    
    // Get staff members
    const staffMembers = await getStaffMembers();
    
    if (staffMembers.length === 0) {
      console.log('No staff members found. Please seed staff members first.');
      return;
    }
    
    console.log(`Found ${staffMembers.length} staff members.`);
    
    // Clear existing availability
    const existingAvailability = await db.collection(STAFF_AVAILABILITY_COLLECTION)
      .where('hotelId', '==', HOTEL_ID)
      .get();
    
    const batch = db.batch();
    
    // Delete existing availability
    existingAvailability.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
    
    console.log(`Deleted ${existingAvailability.size} existing availability records.`);
    
    // Generate and save new availability
    let totalAvailability = 0;
    
    for (const staffMember of staffMembers) {
      const availability = generateAvailabilityForStaff(staffMember);
      
      for (const record of availability) {
        await db.collection(STAFF_AVAILABILITY_COLLECTION).add(record);
        totalAvailability++;
      }
      
      console.log(`Added ${availability.length} availability records for ${staffMember.name}.`);
    }
    
    console.log(`Staff availability seeded successfully! Total records: ${totalAvailability}`);
  } catch (error) {
    console.error('Error seeding staff availability:', error);
  }
}

// Run the seed function
seedStaffAvailability()
  .then(() => {
    console.log('Script completed successfully!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
