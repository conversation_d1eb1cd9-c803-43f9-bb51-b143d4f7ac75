import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Divider,
  CircularProgress,
  Alert,
  Chip,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  VerifiedUser as VerifiedUserIcon,
  Person as PersonIcon,
  Home as HomeIcon,
  Event as EventIcon,
  Print as PrintIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { getVerificationById } from '../../services/verificationService';
// import { getBookingsForUser } from '../../services/bookingService';
import { AadhaarVerificationData } from '../../components/AadhaarVerificationCard';
import AadhaarVerificationCard from '../../components/AadhaarVerificationCard';

const VerificationDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [verification, setVerification] = useState<AadhaarVerificationData | null>(null);
  const [bookings, setBookings] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      fetchVerificationData(id);
    }
  }, [id]);

  const fetchVerificationData = async (verificationId: string) => {
    setLoading(true);
    setError(null);

    try {
      const verificationData = await getVerificationById(verificationId);
      setVerification(verificationData);

      if (verificationData && verificationData.userId) {
        // Fetch user's bookings - temporarily disabled
        // const userBookings = await getBookingsForUser(verificationData.userId);
        // setBookings(userBookings);

        // Mock data for now
        setBookings([
          {
            id: '1',
            bookingNumber: 'BK12345',
            checkInDate: { toDate: () => new Date() },
            bookingStatus: 'confirmed'
          },
          {
            id: '2',
            bookingNumber: 'BK12346',
            checkInDate: { toDate: () => new Date(Date.now() + 86400000) },
            bookingStatus: 'pending'
          }
        ]);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch verification details');
      console.error('Error fetching verification details:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    if (id) {
      fetchVerificationData(id);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleBack = () => {
    navigate('/vendor/verifications');
  };

  // Format date
  const formatDate = (date?: Date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status color and icon
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'verified':
        return {
          color: 'success',
          icon: <VerifiedUserIcon />,
          text: 'Verified'
        };
      case 'pending':
        return {
          color: 'warning',
          icon: null,
          text: 'Pending'
        };
      case 'failed':
        return {
          color: 'error',
          icon: null,
          text: 'Failed'
        };
      case 'notVerified':
        return {
          color: 'default',
          icon: null,
          text: 'Not Verified'
        };
      default:
        return {
          color: 'default',
          icon: null,
          text: 'Unknown'
        };
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button variant="outlined" onClick={handleBack} startIcon={<ArrowBackIcon />}>
          Back to Verifications
        </Button>
      </Box>
    );
  }

  if (!verification) {
    return (
      <Box>
        <Alert severity="warning" sx={{ mb: 3 }}>
          Verification not found
        </Alert>
        <Button variant="outlined" onClick={handleBack} startIcon={<ArrowBackIcon />}>
          Back to Verifications
        </Button>
      </Box>
    );
  }

  const statusInfo = getStatusInfo(verification.status);

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Verification Details
          </Typography>
          <Breadcrumbs aria-label="breadcrumb">
            <Link color="inherit" href="/vendor">
              Dashboard
            </Link>
            <Link color="inherit" href="/vendor/verifications">
              Verifications
            </Link>
            <Typography color="text.primary">Details</Typography>
          </Breadcrumbs>
        </Box>
        <Box>
          <Tooltip title="Back to Verifications">
            <IconButton onClick={handleBack} sx={{ mr: 1 }}>
              <ArrowBackIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Refresh">
            <IconButton onClick={handleRefresh} sx={{ mr: 1 }}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Print">
            <IconButton onClick={handlePrint}>
              <PrintIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Verification Card */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <AadhaarVerificationCard verification={verification} />
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Verification Summary
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Box display="flex" alignItems="center" mb={2}>
                <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="body1">
                  {verification.fullName}
                </Typography>
              </Box>

              <Box display="flex" alignItems="center" mb={2}>
                <Chip
                  label={statusInfo.text}
                  color={statusInfo.color as any}
                  icon={statusInfo.icon ? statusInfo.icon : undefined}
                  sx={{ fontWeight: 'bold' }}
                />
              </Box>

              {verification.verifiedAt && (
                <Box display="flex" alignItems="center" mb={2}>
                  <EventIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="body2">
                    Verified on: {formatDate(verification.verifiedAt)}
                  </Typography>
                </Box>
              )}

              <Box display="flex" alignItems="center">
                <HomeIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="body2">
                  {verification.address || 'Address not available'}
                </Typography>
              </Box>
            </CardContent>
          </Card>

          {/* User Bookings */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                User Bookings
              </Typography>
              <Divider sx={{ mb: 2 }} />

              {bookings.length === 0 ? (
                <Alert severity="info">
                  No bookings found for this user
                </Alert>
              ) : (
                <Box>
                  {bookings.slice(0, 5).map((booking, index) => (
                    <Box key={booking.id} mb={2}>
                      <Typography variant="subtitle2">
                        Booking #{booking.bookingNumber}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Check-in: {formatDate(booking.checkInDate?.toDate())}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Status: {booking.bookingStatus}
                      </Typography>
                      {index < bookings.length - 1 && <Divider sx={{ my: 1 }} />}
                    </Box>
                  ))}

                  {bookings.length > 5 && (
                    <Typography variant="body2" color="primary" sx={{ mt: 1, cursor: 'pointer' }}>
                      + {bookings.length - 5} more bookings
                    </Typography>
                  )}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default VerificationDetails;
