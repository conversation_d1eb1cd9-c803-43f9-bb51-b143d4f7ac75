import React, { useState } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  TextField,
  Typography,
  Link,
  InputAdornment,
  IconButton,
  Alert,
  CircularProgress,
  Snackbar,
  Paper,
  Grid,
  Tabs,
  Tab,
  Divider,
  Card,
  CardContent,
  CardMedia,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  AdminPanelSettings,
  Hotel as HotelIcon,
  Person as PersonIcon,
  SupervisorAccount as VendorIcon
} from '@mui/icons-material';
import { signIn, ROLE_SUPER_ADMIN, ROLE_VENDOR, ROLE_STAFF } from '../../firebase/auth';
import { createUserWithEmailAndPassword, getAuth } from 'firebase/auth';
import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../../firebase/config';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`login-tabpanel-${index}`}
      aria-labelledby={`login-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const a11yProps = (index: number) => {
  return {
    id: `login-tab-${index}`,
    'aria-controls': `login-tabpanel-${index}`,
  };
};

const Login: React.FC = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [userRole, setUserRole] = useState(ROLE_VENDOR);



  // Handle tab change
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setError(null);

    // Set appropriate role based on tab
    if (newValue === 0) {
      setUserRole(ROLE_VENDOR);
    } else if (newValue === 1) {
      setUserRole(ROLE_STAFF);
    } else if (newValue === 2) {
      setUserRole(ROLE_SUPER_ADMIN);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Check if this is the default admin credentials
      const isDefaultAdmin = email === '<EMAIL>' && password === 'Admin123!';

      if (isDefaultAdmin) {
        try {
          // Try to sign in first
          await signIn(email, password);
          // If successful, navigation will be handled by the App component
        } catch (signInError: any) {
          // If sign in fails, try to create the admin user
          if (signInError.message.includes('user-not-found') ||
              signInError.message.includes('no user record')) {
            try {
              // Create the admin user
              const auth = getAuth();
              const userCredential = await createUserWithEmailAndPassword(
                auth,
                email,
                password
              );

              const uid = userCredential.user.uid;

              // Add user data to Firestore
              await setDoc(doc(db, 'users', uid), {
                email: email,
                displayName: 'Super Admin',
                role: ROLE_SUPER_ADMIN,
                createdAt: serverTimestamp(),
                updatedAt: serverTimestamp(),
              });

              setSnackbarMessage('Admin user created successfully! Logging in...');
              setShowSnackbar(true);

              // No need to sign in again, the user is already signed in after creation
              // Navigation will be handled by the App component
            } catch (createError: any) {
              setError(`Failed to create admin user: ${createError.message}`);
              setLoading(false);
            }
          } else {
            setError(signInError.message);
            setLoading(false);
          }
        }
      } else {
        // Regular sign in for non-default users
        await signIn(email, password);

        // Show appropriate message based on role
        let roleText = "user";
        if (userRole === ROLE_VENDOR) roleText = "vendor";
        if (userRole === ROLE_STAFF) roleText = "staff";
        if (userRole === ROLE_SUPER_ADMIN) roleText = "admin";

        setSnackbarMessage(`Signed in successfully as ${roleText}!`);
        setShowSnackbar(true);

        // Navigation will be handled by the App component based on user role
      }
    } catch (error: any) {
      setError(error.message);
      setLoading(false);
    }
  };

  return (
    <>
    <Box sx={{ width: '100%' }}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >


          <Card sx={{ width: '100%', mb: 4, boxShadow: 'none', border: '1px solid rgba(0,0,0,0.08)', borderRadius: 2 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="login tabs"
                variant="fullWidth"
                sx={{
                  '& .MuiTab-root': {
                    fontWeight: 'bold',
                    py: 2,
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      backgroundColor: 'rgba(25, 118, 210, 0.04)'
                    }
                  },
                  '& .Mui-selected': {
                    color: 'primary.main',
                    fontWeight: 'bold'
                  },
                  '& .MuiTabs-indicator': {
                    height: 3,
                    borderRadius: '3px 3px 0 0'
                  }
                }}
              >
                <Tab
                  label="Hotel / Vendor"
                  icon={<VendorIcon />}
                  iconPosition="start"
                  {...a11yProps(0)}
                />
                <Tab
                  label="Staff"
                  icon={<PersonIcon />}
                  iconPosition="start"
                  {...a11yProps(1)}
                />
                <Tab
                  label="Admin"
                  icon={<AdminPanelSettings />}
                  iconPosition="start"
                  {...a11yProps(2)}
                />
              </Tabs>
            </Box>

            <TabPanel value={tabValue} index={0}>
              <Box component="form" onSubmit={handleSubmit} noValidate>
                <Typography variant="h6" gutterBottom>
                  Hotel / Vendor Login
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Access your hotel management dashboard
                </Typography>

                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <TextField
                  margin="normal"
                  required
                  fullWidth
                  id="email"
                  label="Email Address"
                  name="email"
                  autoComplete="email"
                  autoFocus
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={loading}
                />

                <TextField
                  margin="normal"
                  required
                  fullWidth
                  name="password"
                  label="Password"
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  autoComplete="current-password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={loading}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                <Box sx={{ mt: 2, mb: 2, textAlign: 'right' }}>
                  <Link component={RouterLink} to="/forgot-password" variant="body2">
                    Forgot password?
                  </Link>
                </Box>

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading}
                  sx={{
                    mt: 2,
                    mb: 2,
                    py: 1.5,
                    fontWeight: 'bold',
                    boxShadow: '0 4px 10px rgba(26, 115, 232, 0.25)',
                    '&:hover': {
                      boxShadow: '0 6px 15px rgba(26, 115, 232, 0.35)',
                    }
                  }}
                >
                  {loading ? <CircularProgress size={24} /> : 'Sign In'}
                </Button>
              </Box>
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <Box component="form" onSubmit={handleSubmit} noValidate>
                <Typography variant="h6" gutterBottom>
                  Staff Login
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Access your staff dashboard
                </Typography>

                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <TextField
                  margin="normal"
                  required
                  fullWidth
                  id="email"
                  label="Email Address"
                  name="email"
                  autoComplete="email"
                  autoFocus
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={loading}
                />

                <TextField
                  margin="normal"
                  required
                  fullWidth
                  name="password"
                  label="Password"
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  autoComplete="current-password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={loading}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                <Box sx={{ mt: 2, mb: 2, textAlign: 'right' }}>
                  <Link component={RouterLink} to="/forgot-password" variant="body2">
                    Forgot password?
                  </Link>
                </Box>

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading}
                  sx={{
                    mt: 2,
                    mb: 2,
                    py: 1.5,
                    fontWeight: 'bold',
                    boxShadow: '0 4px 10px rgba(26, 115, 232, 0.25)',
                    '&:hover': {
                      boxShadow: '0 6px 15px rgba(26, 115, 232, 0.35)',
                    }
                  }}
                >
                  {loading ? <CircularProgress size={24} /> : 'Sign In'}
                </Button>
              </Box>
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <Box component="form" onSubmit={handleSubmit} noValidate>
                <Typography variant="h6" gutterBottom>
                  Admin Login
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Access the system administration dashboard
                </Typography>

                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <TextField
                  margin="normal"
                  required
                  fullWidth
                  id="email"
                  label="Email Address"
                  name="email"
                  autoComplete="email"
                  autoFocus
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={loading}
                />

                <TextField
                  margin="normal"
                  required
                  fullWidth
                  name="password"
                  label="Password"
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  autoComplete="current-password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={loading}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                <Box sx={{ mt: 2, mb: 2, textAlign: 'right' }}>
                  <Link component={RouterLink} to="/forgot-password" variant="body2">
                    Forgot password?
                  </Link>
                </Box>

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading}
                  sx={{
                    mt: 2,
                    mb: 2,
                    py: 1.5,
                    fontWeight: 'bold',
                    boxShadow: '0 4px 10px rgba(26, 115, 232, 0.25)',
                    '&:hover': {
                      boxShadow: '0 6px 15px rgba(26, 115, 232, 0.35)',
                    }
                  }}
                >
                  {loading ? <CircularProgress size={24} /> : 'Sign In'}
                </Button>


              </Box>
            </TabPanel>
          </Card>

          <Typography variant="body2" color="text.secondary" sx={{ mt: 5, textAlign: 'center' }}>
            © {new Date().getFullYear()} Link In Blink Hotel Booking System. All rights reserved.
          </Typography>
        </Box>
      </Box>

      <Snackbar
        open={showSnackbar}
        autoHideDuration={6000}
        onClose={() => setShowSnackbar(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setShowSnackbar(false)}
          severity="success"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

export default Login;
