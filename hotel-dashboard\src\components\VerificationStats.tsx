import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  CircularProgress,
  Alert,
  Button,
  Divider,
  LinearProgress,
  Tooltip
} from '@mui/material';
import {
  VerifiedUser as VerifiedUserIcon,
  Refresh as RefreshIcon,
  PendingActions as PendingIcon,
  ErrorOutline as ErrorIcon,
  HelpOutline as HelpIcon
} from '@mui/icons-material';
import { getVerificationStats } from '../services/verificationService';
import { useNavigate } from 'react-router-dom';

interface VerificationStatsProps {
  title?: string;
  showViewAll?: boolean;
}

const VerificationStats: React.FC<VerificationStatsProps> = ({
  title = 'Aadhaar Verification Stats',
  showViewAll = true
}) => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    total: 0,
    verified: 0,
    pending: 0,
    failed: 0,
    notVerified: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    setLoading(true);
    setError(null);

    try {
      const statsData = await getVerificationStats();
      setStats(statsData);
    } catch (err: any) {
      setError(err.message || 'Failed to load verification statistics');
      console.error('Error fetching verification stats:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleViewAll = () => {
    navigate('/vendor/verifications');
  };

  // Calculate percentages
  const getPercentage = (value: number) => {
    if (stats.total === 0) return 0;
    return Math.round((value / stats.total) * 100);
  };

  const verifiedPercentage = getPercentage(stats.verified);
  const pendingPercentage = getPercentage(stats.pending);
  const failedPercentage = getPercentage(stats.failed);
  const notVerifiedPercentage = getPercentage(stats.notVerified);

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
          <Box display="flex" justifyContent="center" alignItems="center" py={3}>
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
          <Button
            variant="outlined"
            size="small"
            startIcon={<RefreshIcon />}
            onClick={fetchStats}
          >
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">
            {title}
          </Typography>
          {showViewAll && (
            <Button
              variant="text"
              size="small"
              onClick={handleViewAll}
            >
              View All
            </Button>
          )}
        </Box>

        <Divider sx={{ mb: 2 }} />

        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Box display="flex" alignItems="center">
                <VerifiedUserIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="body2">Verified</Typography>
              </Box>
              <Typography variant="body2" fontWeight="bold">
                {stats.verified} ({verifiedPercentage}%)
              </Typography>
            </Box>
            <Tooltip title={`${verifiedPercentage}% of users verified`}>
              <LinearProgress
                variant="determinate"
                value={verifiedPercentage}
                color="success"
                sx={{ height: 8, borderRadius: 4, mb: 2 }}
              />
            </Tooltip>
          </Grid>

          <Grid item xs={12}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Box display="flex" alignItems="center">
                <PendingIcon color="warning" sx={{ mr: 1 }} />
                <Typography variant="body2">Pending</Typography>
              </Box>
              <Typography variant="body2" fontWeight="bold">
                {stats.pending} ({pendingPercentage}%)
              </Typography>
            </Box>
            <Tooltip title={`${pendingPercentage}% of users with pending verification`}>
              <LinearProgress
                variant="determinate"
                value={pendingPercentage}
                color="warning"
                sx={{ height: 8, borderRadius: 4, mb: 2 }}
              />
            </Tooltip>
          </Grid>

          <Grid item xs={12}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Box display="flex" alignItems="center">
                <ErrorIcon color="error" sx={{ mr: 1 }} />
                <Typography variant="body2">Failed</Typography>
              </Box>
              <Typography variant="body2" fontWeight="bold">
                {stats.failed} ({failedPercentage}%)
              </Typography>
            </Box>
            <Tooltip title={`${failedPercentage}% of users with failed verification`}>
              <LinearProgress
                variant="determinate"
                value={failedPercentage}
                color="error"
                sx={{ height: 8, borderRadius: 4, mb: 2 }}
              />
            </Tooltip>
          </Grid>

          <Grid item xs={12}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Box display="flex" alignItems="center">
                <HelpIcon color="disabled" sx={{ mr: 1 }} />
                <Typography variant="body2">Not Verified</Typography>
              </Box>
              <Typography variant="body2" fontWeight="bold">
                {stats.notVerified} ({notVerifiedPercentage}%)
              </Typography>
            </Box>
            <Tooltip title={`${notVerifiedPercentage}% of users not verified`}>
              <LinearProgress
                variant="determinate"
                value={notVerifiedPercentage}
                sx={{ height: 8, borderRadius: 4, mb: 2, bgcolor: '#e0e0e0' }}
              />
            </Tooltip>
          </Grid>
        </Grid>

        <Divider sx={{ my: 2 }} />

        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="subtitle2" color="text.secondary">
            Total Users
          </Typography>
          <Typography variant="h6">
            {stats.total}
          </Typography>
        </Box>

        <Box display="flex" justifyContent="flex-end" mt={2}>
          <Button
            size="small"
            startIcon={<RefreshIcon />}
            onClick={fetchStats}
          >
            Refresh
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
};

export default VerificationStats;
