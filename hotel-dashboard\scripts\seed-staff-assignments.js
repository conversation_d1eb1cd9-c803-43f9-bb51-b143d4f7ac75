const admin = require('firebase-admin');
const serviceAccount = require('../serviceAccountKey.json');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

// Constants
const STAFF_COLLECTION = 'staff';
const STAFF_ASSIGNMENTS_COLLECTION = 'staffAssignments';
const SERVICE_REQUESTS_COLLECTION = 'serviceRequests';

// Assignment status
const ASSIGNMENT_STATUS_ASSIGNED = 'assigned';
const ASSIGNMENT_STATUS_IN_PROGRESS = 'in_progress';
const ASSIGNMENT_STATUS_COMPLETED = 'completed';
const ASSIGNMENT_STATUS_CANCELLED = 'cancelled';

// Sample hotel and vendor IDs (replace with actual IDs)
const HOTEL_ID = 'hotel123';
const VENDOR_ID = 'vendor123';

// Seed staff assignments
async function seedStaffAssignments() {
  try {
    console.log('Seeding staff assignments...');
    
    // Clear existing staff assignments
    const existingAssignments = await db.collection(STAFF_ASSIGNMENTS_COLLECTION)
      .where('hotelId', '==', HOTEL_ID)
      .get();
    
    const batch = db.batch();
    
    existingAssignments.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
    console.log(`Deleted ${existingAssignments.size} existing staff assignments.`);
    
    // Get staff members
    const staffSnapshot = await db.collection(STAFF_COLLECTION)
      .where('hotelId', '==', HOTEL_ID)
      .get();
    
    if (staffSnapshot.empty) {
      console.log('No staff members found. Please run seed-staff-members.js first.');
      return;
    }
    
    const staffMembers = [];
    staffSnapshot.forEach(doc => {
      staffMembers.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    // Get service requests
    const serviceRequestsSnapshot = await db.collection(SERVICE_REQUESTS_COLLECTION)
      .where('hotelId', '==', HOTEL_ID)
      .get();
    
    if (serviceRequestsSnapshot.empty) {
      console.log('No service requests found. Please run seed-service-requests.js first.');
      return;
    }
    
    const serviceRequests = [];
    serviceRequestsSnapshot.forEach(doc => {
      serviceRequests.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    // Create staff assignments
    const assignments = [];
    
    // Helper function to get staff by role
    const getStaffByRole = (role) => {
      return staffMembers.filter(staff => staff.role === role && staff.status === 'active');
    };
    
    // Assign cleaning requests to housekeeping staff
    const housekeepingStaff = getStaffByRole('housekeeping');
    const cleaningRequests = serviceRequests.filter(request => request.type === 'cleaning');
    
    if (housekeepingStaff.length > 0 && cleaningRequests.length > 0) {
      for (let i = 0; i < Math.min(cleaningRequests.length, 10); i++) {
        const request = cleaningRequests[i];
        const staff = housekeepingStaff[i % housekeepingStaff.length];
        
        // Skip requests that are cancelled
        if (request.status === 'cancelled') continue;
        
        const assignmentStatus = request.status === 'pending' ? ASSIGNMENT_STATUS_ASSIGNED :
                                request.status === 'in_progress' ? ASSIGNMENT_STATUS_IN_PROGRESS :
                                request.status === 'completed' ? ASSIGNMENT_STATUS_COMPLETED :
                                ASSIGNMENT_STATUS_CANCELLED;
        
        const assignedAt = admin.firestore.Timestamp.fromDate(
          new Date(request.requestTime.toDate().getTime() + 15 * 60 * 1000) // 15 minutes after request
        );
        
        const startedAt = assignmentStatus === ASSIGNMENT_STATUS_IN_PROGRESS || 
                          assignmentStatus === ASSIGNMENT_STATUS_COMPLETED ?
          admin.firestore.Timestamp.fromDate(
            new Date(assignedAt.toDate().getTime() + 30 * 60 * 1000) // 30 minutes after assignment
          ) : null;
        
        const completedAt = assignmentStatus === ASSIGNMENT_STATUS_COMPLETED ?
          admin.firestore.Timestamp.fromDate(
            new Date(startedAt.toDate().getTime() + 60 * 60 * 1000) // 1 hour after starting
          ) : null;
        
        assignments.push({
          staffId: staff.id,
          staffName: staff.name,
          serviceRequestId: request.id,
          serviceRequestType: request.type,
          roomNumber: request.roomNumber,
          status: assignmentStatus,
          priority: request.priority,
          assignedAt,
          startedAt,
          completedAt,
          notes: request.notes,
          hotelId: HOTEL_ID,
          vendorId: VENDOR_ID,
          createdAt: assignedAt,
          updatedAt: completedAt || startedAt || assignedAt
        });
      }
    }
    
    // Assign food orders to food service staff
    const foodServiceStaff = getStaffByRole('food_service');
    const foodRequests = serviceRequests.filter(request => request.type === 'food');
    
    if (foodServiceStaff.length > 0 && foodRequests.length > 0) {
      for (let i = 0; i < Math.min(foodRequests.length, 10); i++) {
        const request = foodRequests[i];
        const staff = foodServiceStaff[i % foodServiceStaff.length];
        
        // Skip requests that are cancelled
        if (request.status === 'cancelled') continue;
        
        const assignmentStatus = request.status === 'pending' ? ASSIGNMENT_STATUS_ASSIGNED :
                                request.status === 'in_progress' ? ASSIGNMENT_STATUS_IN_PROGRESS :
                                request.status === 'completed' ? ASSIGNMENT_STATUS_COMPLETED :
                                ASSIGNMENT_STATUS_CANCELLED;
        
        const assignedAt = admin.firestore.Timestamp.fromDate(
          new Date(request.requestTime.toDate().getTime() + 5 * 60 * 1000) // 5 minutes after request
        );
        
        const startedAt = assignmentStatus === ASSIGNMENT_STATUS_IN_PROGRESS || 
                          assignmentStatus === ASSIGNMENT_STATUS_COMPLETED ?
          admin.firestore.Timestamp.fromDate(
            new Date(assignedAt.toDate().getTime() + 10 * 60 * 1000) // 10 minutes after assignment
          ) : null;
        
        const completedAt = assignmentStatus === ASSIGNMENT_STATUS_COMPLETED ?
          admin.firestore.Timestamp.fromDate(
            new Date(startedAt.toDate().getTime() + 30 * 60 * 1000) // 30 minutes after starting
          ) : null;
        
        assignments.push({
          staffId: staff.id,
          staffName: staff.name,
          serviceRequestId: request.id,
          serviceRequestType: request.type,
          roomNumber: request.roomNumber,
          status: assignmentStatus,
          priority: request.priority,
          assignedAt,
          startedAt,
          completedAt,
          notes: request.notes,
          hotelId: HOTEL_ID,
          vendorId: VENDOR_ID,
          createdAt: assignedAt,
          updatedAt: completedAt || startedAt || assignedAt
        });
      }
    }
    
    // Assign maintenance requests to maintenance staff
    const maintenanceStaff = getStaffByRole('maintenance');
    const maintenanceRequests = serviceRequests.filter(request => request.type === 'maintenance');
    
    if (maintenanceStaff.length > 0 && maintenanceRequests.length > 0) {
      for (let i = 0; i < Math.min(maintenanceRequests.length, 10); i++) {
        const request = maintenanceRequests[i];
        const staff = maintenanceStaff[i % maintenanceStaff.length];
        
        // Skip requests that are cancelled
        if (request.status === 'cancelled') continue;
        
        const assignmentStatus = request.status === 'pending' ? ASSIGNMENT_STATUS_ASSIGNED :
                                request.status === 'in_progress' ? ASSIGNMENT_STATUS_IN_PROGRESS :
                                request.status === 'completed' ? ASSIGNMENT_STATUS_COMPLETED :
                                ASSIGNMENT_STATUS_CANCELLED;
        
        const assignedAt = admin.firestore.Timestamp.fromDate(
          new Date(request.requestTime.toDate().getTime() + 30 * 60 * 1000) // 30 minutes after request
        );
        
        const startedAt = assignmentStatus === ASSIGNMENT_STATUS_IN_PROGRESS || 
                          assignmentStatus === ASSIGNMENT_STATUS_COMPLETED ?
          admin.firestore.Timestamp.fromDate(
            new Date(assignedAt.toDate().getTime() + 60 * 60 * 1000) // 1 hour after assignment
          ) : null;
        
        const completedAt = assignmentStatus === ASSIGNMENT_STATUS_COMPLETED ?
          admin.firestore.Timestamp.fromDate(
            new Date(startedAt.toDate().getTime() + 2 * 60 * 60 * 1000) // 2 hours after starting
          ) : null;
        
        assignments.push({
          staffId: staff.id,
          staffName: staff.name,
          serviceRequestId: request.id,
          serviceRequestType: request.type,
          roomNumber: request.roomNumber,
          status: assignmentStatus,
          priority: request.priority,
          assignedAt,
          startedAt,
          completedAt,
          notes: request.notes,
          hotelId: HOTEL_ID,
          vendorId: VENDOR_ID,
          createdAt: assignedAt,
          updatedAt: completedAt || startedAt || assignedAt
        });
      }
    }
    
    // Add assignments to Firestore
    const assignmentPromises = assignments.map(assignment => 
      db.collection(STAFF_ASSIGNMENTS_COLLECTION).add(assignment)
    );
    
    await Promise.all(assignmentPromises);
    
    console.log(`Added ${assignments.length} new staff assignments.`);
    console.log('Staff assignments seeded successfully!');
  } catch (error) {
    console.error('Error seeding staff assignments:', error);
  }
}

// Run the seed function
seedStaffAssignments()
  .then(() => {
    console.log('Script completed successfully!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
