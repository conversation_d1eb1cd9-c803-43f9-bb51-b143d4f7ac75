import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Person as PersonIcon,
  NavigateBefore as PrevIcon,
  NavigateNext as NextIcon,
  Today as TodayIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import {
  startOfWeek,
  endOfWeek,
  addDays,
  format,
  addWeeks,
  subWeeks,
  isSameDay,
  isWithinInterval
} from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import ConflictAlert from './ConflictAlert';
import { checkAllConflicts, ScheduleConflict, CONFLICT_SEVERITY_ERROR } from '../../services/scheduleConflictService';

// Define shift types
const SHIFT_TYPE_MORNING = 'morning';
const SHIFT_TYPE_AFTERNOON = 'afternoon';
const SHIFT_TYPE_NIGHT = 'night';
const SHIFT_TYPE_CUSTOM = 'custom';

interface BasicCalendarProps {
  hotelId: string;
  vendorId: string;
  staffList: any[];
  shifts: any[];
  timeOffRequests: any[];
  loading: boolean;
  error: string | null;
  onRefresh: () => void;
  onShiftCreate: (shift: any) => Promise<void>;
  onShiftUpdate: (shiftId: string, updates: any) => Promise<void>;
  onShiftDelete: (shiftId: string) => Promise<void>;
}

const BasicCalendar: React.FC<BasicCalendarProps> = ({
  hotelId,
  vendorId,
  staffList,
  shifts,
  timeOffRequests,
  loading,
  error,
  onRefresh,
  onShiftCreate,
  onShiftUpdate,
  onShiftDelete
}) => {
  const [selectedStaff, setSelectedStaff] = useState<string>('all');
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(startOfWeek(new Date(), { weekStartsOn: 1 }));
  const [weekDays, setWeekDays] = useState<Date[]>([]);
  const [conflicts, setConflicts] = useState<ScheduleConflict[]>([]);
  const [showConflictDialog, setShowConflictDialog] = useState<boolean>(false);
  const [pendingShift, setPendingShift] = useState<any>(null);
  const [isUpdate, setIsUpdate] = useState<boolean>(false);

  // Generate week days when current week start changes
  useEffect(() => {
    const days = [];
    for (let i = 0; i < 7; i++) {
      days.push(addDays(currentWeekStart, i));
    }
    setWeekDays(days);
  }, [currentWeekStart]);

  const handlePrevWeek = () => {
    setCurrentWeekStart(subWeeks(currentWeekStart, 1));
  };

  const handleNextWeek = () => {
    setCurrentWeekStart(addWeeks(currentWeekStart, 1));
  };

  const handleCurrentWeek = () => {
    setCurrentWeekStart(startOfWeek(new Date(), { weekStartsOn: 1 }));
  };

  const handleStaffFilterChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedStaff(event.target.value as string);
  };

  const getShiftColor = (shiftType: string): string => {
    switch (shiftType) {
      case SHIFT_TYPE_MORNING:
        return '#e3f2fd'; // Light blue
      case SHIFT_TYPE_AFTERNOON:
        return '#fff8e1'; // Light amber
      case SHIFT_TYPE_NIGHT:
        return '#e8eaf6'; // Light indigo
      case SHIFT_TYPE_CUSTOM:
        return '#f1f8e9'; // Light green
      default:
        return '#f5f5f5'; // Light grey
    }
  };

  const formatShiftTime = (shift: any): string => {
    try {
      const startTime = format(shift.startTime.toDate(), 'HH:mm');
      const endTime = format(shift.endTime.toDate(), 'HH:mm');
      return `${startTime} - ${endTime}`;
    } catch (error) {
      return 'Invalid time';
    }
  };

  // Filter shifts for a specific day and staff member
  const getShiftsForDay = (day: Date, staffId: string = 'all'): any[] => {
    return shifts.filter(shift => {
      try {
        const shiftDate = shift.date.toDate();
        const isSameDate = isSameDay(shiftDate, day);
        const isForStaff = staffId === 'all' || shift.staffId === staffId;
        return isSameDate && isForStaff;
      } catch (error) {
        return false;
      }
    });
  };

  // Check if a staff member has time off on a specific day
  const hasTimeOff = (day: Date, staffId: string): boolean => {
    return timeOffRequests.some(timeOff => {
      try {
        if (timeOff.status !== 'approved' || timeOff.staffId !== staffId) {
          return false;
        }

        const startDate = timeOff.startDate.toDate();
        const endDate = timeOff.endDate.toDate();

        return isWithinInterval(day, { start: startDate, end: endDate });
      } catch (error) {
        return false;
      }
    });
  };

  // Function to check for conflicts
  const checkConflicts = async (shift: any) => {
    try {
      const detectedConflicts = await checkAllConflicts(shift, hotelId);
      setConflicts(detectedConflicts);

      if (detectedConflicts.length > 0) {
        // If there are conflicts, show the conflict dialog
        setPendingShift(shift);
        setShowConflictDialog(true);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error checking conflicts:', error);
      return false;
    }
  };

  // Function to handle shift creation with conflict checking
  const handleCreateShift = async (shift: any) => {
    const hasConflicts = await checkConflicts(shift);

    if (!hasConflicts) {
      // If no conflicts, create the shift directly
      await onShiftCreate(shift);
    } else {
      // If there are conflicts, the dialog will be shown
      // and the user can decide whether to proceed
      setIsUpdate(false);
    }
  };

  // Function to handle shift update with conflict checking
  const handleUpdateShift = async (shiftId: string, updates: any) => {
    const shift = {
      ...updates,
      id: shiftId
    };

    const hasConflicts = await checkConflicts(shift);

    if (!hasConflicts) {
      // If no conflicts, update the shift directly
      await onShiftUpdate(shiftId, updates);
    } else {
      // If there are conflicts, the dialog will be shown
      // and the user can decide whether to proceed
      setIsUpdate(true);
    }
  };

  // Function to proceed with shift creation/update despite conflicts
  const handleProceedDespiteConflicts = async () => {
    try {
      if (isUpdate && pendingShift) {
        await onShiftUpdate(pendingShift.id, pendingShift);
      } else if (pendingShift) {
        await onShiftCreate(pendingShift);
      }

      // Clear conflicts and close dialog
      setConflicts([]);
      setShowConflictDialog(false);
      setPendingShift(null);
    } catch (error) {
      console.error('Error proceeding with shift:', error);
    }
  };

  // Function to cancel shift creation/update due to conflicts
  const handleCancelDueToConflicts = () => {
    setConflicts([]);
    setShowConflictDialog(false);
    setPendingShift(null);
  };

  // Check if there are any error-level conflicts
  const hasErrorConflicts = conflicts.some(conflict => conflict.severity === CONFLICT_SEVERITY_ERROR);

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handlePrevWeek}>
            <PrevIcon />
          </IconButton>
          <Typography variant="h6" sx={{ mx: 2 }}>
            {format(currentWeekStart, 'MMM d')} - {format(addDays(currentWeekStart, 6), 'MMM d, yyyy')}
          </Typography>
          <IconButton onClick={handleNextWeek}>
            <NextIcon />
          </IconButton>
          <Button
            variant="outlined"
            startIcon={<TodayIcon />}
            onClick={handleCurrentWeek}
            sx={{ ml: 2 }}
          >
            Current Week
          </Button>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <FormControl sx={{ minWidth: 200, mr: 2 }}>
            <InputLabel id="staff-filter-label">Staff Member</InputLabel>
            <Select
              labelId="staff-filter-label"
              value={selectedStaff}
              label="Staff Member"
              onChange={handleStaffFilterChange as any}
              startAdornment={
                <PersonIcon sx={{ mr: 1, color: 'action.active' }} />
              }
            >
              <MenuItem value="all">All Staff</MenuItem>
              {staffList.map((staff) => (
                <MenuItem key={staff.id} value={staff.id || ''}>
                  {staff.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={onRefresh}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Display conflicts if any */}
      {conflicts.length > 0 && !showConflictDialog && (
        <ConflictAlert conflicts={conflicts} />
      )}

      <Paper sx={{ p: 2 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Box>
            {/* Week header */}
            <Grid container spacing={1}>
              {weekDays.map((day, index) => (
                <Grid item xs={12/7} key={index}>
                  <Box sx={{
                    textAlign: 'center',
                    p: 1,
                    bgcolor: 'primary.light',
                    color: 'primary.contrastText',
                    borderRadius: 1
                  }}>
                    <Typography variant="subtitle1">
                      {format(day, 'EEE')}
                    </Typography>
                    <Typography variant="body2">
                      {format(day, 'MMM d')}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>

            {/* Staff rows */}
            {selectedStaff === 'all' ? (
              // Show all staff members
              staffList.map((staff) => (
                <Grid container spacing={1} key={staff.id} sx={{ mt: 1 }}>
                  <Grid item xs={12}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      p: 1,
                      bgcolor: 'grey.100',
                      borderRadius: 1
                    }}>
                      <PersonIcon sx={{ mr: 1 }} />
                      <Typography variant="subtitle2">
                        {staff.name} ({staff.role?.replace(/_/g, ' ') || 'Staff'})
                      </Typography>
                    </Box>
                  </Grid>

                  {weekDays.map((day, dayIndex) => (
                    <Grid item xs={12/7} key={dayIndex}>
                      <Paper
                        variant="outlined"
                        sx={{
                          p: 1,
                          minHeight: 80,
                          bgcolor: hasTimeOff(day, staff.id) ? '#ffebee' : 'white' // Light red background for time off
                        }}
                      >
                        {hasTimeOff(day, staff.id) && (
                          <Chip
                            label="Time Off"
                            size="small"
                            color="error"
                            sx={{ mb: 1 }}
                          />
                        )}

                        {getShiftsForDay(day, staff.id).map((shift, shiftIndex) => (
                          <Box
                            key={shiftIndex}
                            sx={{
                              p: 1,
                              mb: 1,
                              borderRadius: 1,
                              bgcolor: getShiftColor(shift.shiftType)
                            }}
                          >
                            <Typography variant="caption" fontWeight="bold">
                              {shift.shiftType.replace(/_/g, ' ')}
                            </Typography>
                            <Typography variant="caption" display="block">
                              {formatShiftTime(shift)}
                            </Typography>
                          </Box>
                        ))}
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              ))
            ) : (
              // Show only selected staff member
              <Grid container spacing={1} sx={{ mt: 1 }}>
                {weekDays.map((day, dayIndex) => (
                  <Grid item xs={12/7} key={dayIndex}>
                    <Paper
                      variant="outlined"
                      sx={{
                        p: 1,
                        minHeight: 120,
                        bgcolor: hasTimeOff(day, selectedStaff) ? '#ffebee' : 'white' // Light red background for time off
                      }}
                    >
                      {hasTimeOff(day, selectedStaff) && (
                        <Chip
                          label="Time Off"
                          size="small"
                          color="error"
                          sx={{ mb: 1 }}
                        />
                      )}

                      {getShiftsForDay(day, selectedStaff).map((shift, shiftIndex) => (
                        <Box
                          key={shiftIndex}
                          sx={{
                            p: 1,
                            mb: 1,
                            borderRadius: 1,
                            bgcolor: getShiftColor(shift.shiftType)
                          }}
                        >
                          <Typography variant="caption" fontWeight="bold">
                            {shift.shiftType.replace(/_/g, ' ')}
                          </Typography>
                          <Typography variant="caption" display="block">
                            {formatShiftTime(shift)}
                          </Typography>
                          {shift.notes && (
                            <Typography variant="caption" display="block">
                              {shift.notes}
                            </Typography>
                          )}
                        </Box>
                      ))}
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        )}
      </Paper>

      {/* Conflict Dialog */}
      <Dialog
        open={showConflictDialog}
        onClose={handleCancelDueToConflicts}
        aria-labelledby="conflict-dialog-title"
        aria-describedby="conflict-dialog-description"
      >
        <DialogTitle id="conflict-dialog-title" sx={{
          display: 'flex',
          alignItems: 'center',
          color: hasErrorConflicts ? 'error.main' : 'warning.main'
        }}>
          <WarningIcon sx={{ mr: 1 }} />
          {hasErrorConflicts ? 'Schedule Conflicts Detected' : 'Schedule Warnings'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="conflict-dialog-description" sx={{ mb: 2 }}>
            {hasErrorConflicts ?
              'The following conflicts were detected in the schedule. These conflicts may cause problems with staff scheduling.' :
              'The following warnings were detected in the schedule. These are not critical issues but may affect staff well-being.'
            }
          </DialogContentText>

          <ConflictAlert conflicts={conflicts} />

          <DialogContentText sx={{ mt: 2 }}>
            {hasErrorConflicts ?
              'Do you want to proceed despite these conflicts?' :
              'Do you want to proceed with the schedule?'
            }
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDueToConflicts}>
            Cancel
          </Button>
          <Button
            onClick={handleProceedDespiteConflicts}
            color={hasErrorConflicts ? 'error' : 'warning'}
            variant="contained"
          >
            Proceed Anyway
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BasicCalendar;
