import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Button,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  FormControlLabel,
  Switch,
  Grid,
  Slider,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { auth } from '../../firebase/config';
import { Timestamp } from 'firebase/firestore';
import {
  getPricingRules,
  createPricingRule,
  updatePricingRule,
  deletePricingRule,
  PricingRule
} from '../../services/pricingService';
import { getHotelsForVendor, HotelData } from '../../firebase/hotelService';

// Define TabPanel component for tab content
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`pricing-tabpanel-${index}`}
      aria-labelledby={`pricing-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

// Interface for rule form data
interface RuleFormData {
  name: string;
  description: string;
  hotelId: string;
  roomTypeId: string;
  startDate: string;
  endDate: string;
  dayOfWeek: number[];
  adjustmentType: 'percentage' | 'fixed';
  adjustmentValue: number;
  minStayLength: number;
  maxStayLength: number;
  priority: number;
  status: 'active' | 'inactive';
}

const Pricing: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hotels, setHotels] = useState<HotelData[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [roomTypes, setRoomTypes] = useState<{id: string, name: string}[]>([]);

  // Pricing Rules state
  const [pricingRules, setPricingRules] = useState<PricingRule[]>([]);
  const [openRuleDialog, setOpenRuleDialog] = useState(false);
  const [editingRule, setEditingRule] = useState<PricingRule | null>(null);
  const [ruleFormData, setRuleFormData] = useState<RuleFormData>({
    name: '',
    description: '',
    hotelId: '',
    roomTypeId: '',
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString().split('T')[0],
    dayOfWeek: [],
    adjustmentType: 'percentage',
    adjustmentValue: 10,
    minStayLength: 1,
    maxStayLength: 30,
    priority: 1,
    status: 'active'
  });

  // Load data on component mount
  useEffect(() => {
    fetchHotels();
  }, []);

  // Load pricing rules when selected hotel changes
  useEffect(() => {
    if (selectedHotel) {
      fetchPricingRules();
      fetchRoomTypes();
    }
  }, [selectedHotel]);

  // Fetch hotels
  const fetchHotels = async () => {
    try {
      setLoading(true);
      if (!auth.currentUser) return;

      const hotelsList = await getHotelsForVendor(auth.currentUser.uid);
      setHotels(hotelsList);

      if (hotelsList.length > 0) {
        setSelectedHotel(hotelsList[0].id || '');
      }

      setLoading(false);
    } catch (err) {
      console.error('Error fetching hotels:', err);
      setError('Failed to load hotels');
      setLoading(false);
    }
  };

  // Fetch room types (placeholder for now)
  const fetchRoomTypes = async () => {
    // In a real app, this would fetch room types from the database
    setRoomTypes([
      { id: 'standard', name: 'Standard' },
      { id: 'deluxe', name: 'Deluxe' },
      { id: 'suite', name: 'Suite' },
      { id: 'family', name: 'Family Room' }
    ]);
  };

  // Fetch pricing rules
  const fetchPricingRules = async () => {
    try {
      setLoading(true);
      if (!selectedHotel) return;

      const rules = await getPricingRules(selectedHotel);
      setPricingRules(rules);

      setLoading(false);
    } catch (err) {
      console.error('Error fetching pricing rules:', err);
      setError('Failed to load pricing rules');
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle hotel change
  const handleHotelChange = (event: any) => {
    setSelectedHotel(event.target.value);
  };

  // Open rule dialog
  const handleOpenRuleDialog = (rule?: PricingRule) => {
    if (rule) {
      // Edit mode
      setEditingRule(rule);
      setRuleFormData({
        name: rule.name,
        description: rule.description || '',
        hotelId: rule.hotelId,
        roomTypeId: rule.roomTypeId || '',
        startDate: rule.startDate.toDate().toISOString().split('T')[0],
        endDate: rule.endDate.toDate().toISOString().split('T')[0],
        dayOfWeek: rule.dayOfWeek || [],
        adjustmentType: rule.adjustmentType,
        adjustmentValue: rule.adjustmentValue,
        minStayLength: rule.minStayLength || 1,
        maxStayLength: rule.maxStayLength || 30,
        priority: rule.priority,
        status: rule.status
      });
    } else {
      // Create mode
      setEditingRule(null);
      setRuleFormData({
        name: '',
        description: '',
        hotelId: selectedHotel,
        roomTypeId: '',
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString().split('T')[0],
        dayOfWeek: [],
        adjustmentType: 'percentage',
        adjustmentValue: 10,
        minStayLength: 1,
        maxStayLength: 30,
        priority: 1,
        status: 'active'
      });
    }
    setOpenRuleDialog(true);
  };

  // Close rule dialog
  const handleCloseRuleDialog = () => {
    setOpenRuleDialog(false);
  };

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRuleFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle select change
  const handleSelectChange = (e: any) => {
    const name = e.target.name;
    const value = e.target.value;
    setRuleFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle day of week selection
  const handleDayOfWeekChange = (day: number) => {
    setRuleFormData(prev => {
      const newDays = [...prev.dayOfWeek];
      if (newDays.includes(day)) {
        return {
          ...prev,
          dayOfWeek: newDays.filter(d => d !== day)
        };
      } else {
        return {
          ...prev,
          dayOfWeek: [...newDays, day]
        };
      }
    });
  };

  // Handle status change
  const handleStatusChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRuleFormData(prev => ({
      ...prev,
      status: e.target.checked ? 'active' : 'inactive'
    }));
  };

  // Handle slider change
  const handleSliderChange = (name: string) => (event: Event, newValue: number | number[]) => {
    setRuleFormData(prev => ({
      ...prev,
      [name]: newValue as number
    }));
  };

  // Save pricing rule
  const handleSaveRule = async () => {
    try {
      if (!auth.currentUser) return;

      // Validate form
      if (!ruleFormData.name || !ruleFormData.hotelId || !ruleFormData.startDate || !ruleFormData.endDate) {
        setError('Please fill in all required fields');
        return;
      }

      // Create rule object
      const ruleData: Partial<PricingRule> = {
        name: ruleFormData.name,
        description: ruleFormData.description,
        hotelId: ruleFormData.hotelId,
        roomTypeId: ruleFormData.roomTypeId || undefined,
        startDate: Timestamp.fromDate(new Date(ruleFormData.startDate)),
        endDate: Timestamp.fromDate(new Date(ruleFormData.endDate)),
        dayOfWeek: ruleFormData.dayOfWeek.length > 0 ? ruleFormData.dayOfWeek : undefined,
        adjustmentType: ruleFormData.adjustmentType,
        adjustmentValue: ruleFormData.adjustmentValue,
        minStayLength: ruleFormData.minStayLength || undefined,
        maxStayLength: ruleFormData.maxStayLength || undefined,
        priority: ruleFormData.priority,
        status: ruleFormData.status
      };

      if (editingRule) {
        // Update existing rule
        await updatePricingRule(editingRule.id!, ruleData);
      } else {
        // Create new rule
        await createPricingRule(ruleData as PricingRule);
      }

      // Close dialog and refresh rules
      handleCloseRuleDialog();
      fetchPricingRules();
    } catch (err) {
      console.error('Error saving pricing rule:', err);
      setError('Failed to save pricing rule');
    }
  };

  // Delete pricing rule
  const handleDeleteRule = async (ruleId: string) => {
    if (window.confirm('Are you sure you want to delete this pricing rule?')) {
      try {
        await deletePricingRule(ruleId);
        fetchPricingRules();
      } catch (err) {
        console.error('Error deleting pricing rule:', err);
        setError('Failed to delete pricing rule');
      }
    }
  };

  // Get day name
  const getDayName = (day: number) => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[day];
  };

  // Get hotel name
  const getHotelName = (hotelId: string) => {
    const hotel = hotels.find(h => h.id === hotelId);
    return hotel ? hotel.name : 'Unknown Hotel';
  };

  // Get room type name
  const getRoomTypeName = (roomTypeId: string) => {
    const roomType = roomTypes.find(rt => rt.id === roomTypeId);
    return roomType ? roomType.name : 'All Room Types';
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Dynamic Pricing Management
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Hotel selector */}
      {hotels.length > 0 && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <FormControl fullWidth>
            <InputLabel id="hotel-select-label">Select Hotel</InputLabel>
            <Select
              labelId="hotel-select-label"
              value={selectedHotel}
              label="Select Hotel"
              onChange={handleHotelChange}
            >
              {hotels.map((hotel) => (
                <MenuItem key={hotel.id} value={hotel.id}>
                  {hotel.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Paper>
      )}

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
        >
          <Tab label="Pricing Rules" />
          <Tab label="Seasonal Pricing" />
          <Tab label="Demand-Based Pricing" />
        </Tabs>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TabPanel value={tabValue} index={0}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Pricing Rules
                  </Typography>
                  <Typography variant="body1">
                    Create rules to adjust prices based on specific conditions like day of week, length of stay, etc.
                  </Typography>
                </Box>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => handleOpenRuleDialog()}
                  disabled={!selectedHotel}
                >
                  Add Rule
                </Button>
              </Box>

              {hotels.length === 0 ? (
                <Alert severity="info">
                  You need to create at least one hotel before you can manage pricing rules.
                </Alert>
              ) : !selectedHotel ? (
                <Alert severity="info">
                  Please select a hotel to manage pricing rules.
                </Alert>
              ) : pricingRules.length === 0 ? (
                <Alert severity="info">
                  No pricing rules found. Click the "Add Rule" button to create your first rule.
                </Alert>
              ) : (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>Room Type</TableCell>
                        <TableCell>Date Range</TableCell>
                        <TableCell>Adjustment</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Priority</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {pricingRules.map((rule) => (
                        <TableRow key={rule.id}>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {rule.name}
                            </Typography>
                            {rule.description && (
                              <Typography variant="caption" color="text.secondary">
                                {rule.description}
                              </Typography>
                            )}
                          </TableCell>
                          <TableCell>{rule.roomTypeId ? getRoomTypeName(rule.roomTypeId) : 'All Types'}</TableCell>
                          <TableCell>
                            {rule.startDate.toDate().toLocaleDateString()} - {rule.endDate.toDate().toLocaleDateString()}
                            {rule.dayOfWeek && rule.dayOfWeek.length > 0 && (
                              <Box sx={{ mt: 1 }}>
                                {rule.dayOfWeek.map((day) => (
                                  <Chip
                                    key={day}
                                    label={getDayName(day).substring(0, 3)}
                                    size="small"
                                    sx={{ mr: 0.5, mb: 0.5 }}
                                  />
                                ))}
                              </Box>
                            )}
                          </TableCell>
                          <TableCell>
                            {rule.adjustmentType === 'percentage' ? (
                              <Typography variant="body2">
                                {rule.adjustmentValue > 0 ? '+' : ''}{rule.adjustmentValue}%
                              </Typography>
                            ) : (
                              <Typography variant="body2">
                                {rule.adjustmentValue > 0 ? '+₹' : '-₹'}{Math.abs(rule.adjustmentValue)}
                              </Typography>
                            )}
                            {(rule.minStayLength || rule.maxStayLength) && (
                              <Typography variant="caption" color="text.secondary">
                                Stay: {rule.minStayLength || 1} - {rule.maxStayLength || '∞'} nights
                              </Typography>
                            )}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={rule.status === 'active' ? 'Active' : 'Inactive'}
                              color={rule.status === 'active' ? 'success' : 'default'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>{rule.priority}</TableCell>
                          <TableCell align="right">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleOpenRuleDialog(rule)}
                            >
                              <EditIcon />
                            </IconButton>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => rule.id && handleDeleteRule(rule.id)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <Typography variant="h6" gutterBottom>
                Seasonal Pricing
              </Typography>
              <Typography variant="body1">
                Set different prices for different seasons or date ranges.
              </Typography>
              {/* Seasonal Pricing content will be added here */}
              <Alert severity="info" sx={{ mt: 3 }}>
                This feature will be implemented in the next phase.
              </Alert>
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <Typography variant="h6" gutterBottom>
                Demand-Based Pricing
              </Typography>
              <Typography variant="body1">
                Configure automatic price adjustments based on occupancy and demand.
              </Typography>
              {/* Demand-Based Pricing content will be added here */}
              <Alert severity="info" sx={{ mt: 3 }}>
                This feature will be implemented in the next phase.
              </Alert>
            </TabPanel>
          </>
        )}
      </Paper>

      {/* Pricing Rule Dialog */}
      <Dialog open={openRuleDialog} onClose={handleCloseRuleDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingRule ? 'Edit Pricing Rule' : 'Create New Pricing Rule'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                label="Rule Name"
                name="name"
                value={ruleFormData.name}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Room Type</InputLabel>
                <Select
                  name="roomTypeId"
                  value={ruleFormData.roomTypeId}
                  label="Room Type"
                  onChange={handleSelectChange}
                >
                  <MenuItem value="">All Room Types</MenuItem>
                  {roomTypes.map((type) => (
                    <MenuItem key={type.id} value={type.id}>
                      {type.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                name="description"
                value={ruleFormData.description}
                onChange={handleInputChange}
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                label="Start Date"
                name="startDate"
                type="date"
                value={ruleFormData.startDate}
                onChange={handleInputChange}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                label="End Date"
                name="endDate"
                type="date"
                value={ruleFormData.endDate}
                onChange={handleInputChange}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Apply on Days
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {[0, 1, 2, 3, 4, 5, 6].map((day) => (
                  <Chip
                    key={day}
                    label={getDayName(day)}
                    onClick={() => handleDayOfWeekChange(day)}
                    color={ruleFormData.dayOfWeek.includes(day) ? 'primary' : 'default'}
                    variant={ruleFormData.dayOfWeek.includes(day) ? 'filled' : 'outlined'}
                  />
                ))}
              </Box>
              <Typography variant="caption" color="text.secondary">
                Leave empty to apply to all days
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Adjustment Type</InputLabel>
                <Select
                  name="adjustmentType"
                  value={ruleFormData.adjustmentType}
                  label="Adjustment Type"
                  onChange={handleSelectChange}
                >
                  <MenuItem value="percentage">Percentage</MenuItem>
                  <MenuItem value="fixed">Fixed Amount</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                label={`Adjustment Value (${ruleFormData.adjustmentType === 'percentage' ? '%' : '₹'})`}
                name="adjustmentValue"
                type="number"
                value={ruleFormData.adjustmentValue}
                onChange={handleInputChange}
                InputProps={{
                  startAdornment: ruleFormData.adjustmentType === 'fixed' ? '₹' : null,
                  endAdornment: ruleFormData.adjustmentType === 'percentage' ? '%' : null,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Stay Length (nights)
              </Typography>
              <Box sx={{ px: 2 }}>
                <Slider
                  value={[ruleFormData.minStayLength, ruleFormData.maxStayLength]}
                  onChange={(e, newValue) => {
                    const [min, max] = newValue as number[];
                    setRuleFormData(prev => ({
                      ...prev,
                      minStayLength: min,
                      maxStayLength: max
                    }));
                  }}
                  valueLabelDisplay="auto"
                  min={1}
                  max={30}
                  marks={[
                    { value: 1, label: '1' },
                    { value: 7, label: '7' },
                    { value: 14, label: '14' },
                    { value: 30, label: '30' }
                  ]}
                />
              </Box>
              <Typography variant="caption" color="text.secondary">
                Rule will apply only to bookings with stay length within this range
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                label="Priority"
                name="priority"
                type="number"
                value={ruleFormData.priority}
                onChange={handleInputChange}
                helperText="Higher priority rules are applied first"
                InputProps={{ inputProps: { min: 1, max: 100 } }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={ruleFormData.status === 'active'}
                    onChange={handleStatusChange}
                    color="success"
                  />
                }
                label="Active"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseRuleDialog}>Cancel</Button>
          <Button onClick={handleSaveRule} variant="contained">
            {editingRule ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Pricing;
