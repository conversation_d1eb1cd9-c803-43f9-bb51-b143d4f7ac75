import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hotel_booking_app/data/models/booking_model.dart';
import 'package:hotel_booking_app/data/models/service_request_model.dart';
import 'package:hotel_booking_app/data/services/auth_service.dart';
import 'package:hotel_booking_app/data/services/service_request_handler.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_button.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_text_field.dart';

class ServiceRequestForm extends StatefulWidget {
  final Booking booking;
  final String serviceType; // 'cleaning', 'food', 'maintenance'

  const ServiceRequestForm({
    super.key,
    required this.booking,
    required this.serviceType,
  });

  @override
  State<ServiceRequestForm> createState() => _ServiceRequestFormState();
}

class _ServiceRequestFormState extends State<ServiceRequestForm> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();

  String _priority = 'medium';
  bool _isLoading = false;
  String? _errorMessage;

  // Cleaning specific
  String _cleaningType = 'regular';
  final List<String> _selectedItems = [];

  // Food specific
  final List<FoodItem> _foodItems = [];

  // Maintenance specific
  String _issueType = 'other';
  final _descriptionController = TextEditingController();

  @override
  void dispose() {
    _notesController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Request ${_getServiceTypeTitle()}'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBookingInfo(),
                    const SizedBox(height: 24),

                    _buildPrioritySelector(),
                    const SizedBox(height: 24),

                    // Service type specific fields
                    if (widget.serviceType == 'cleaning')
                      _buildCleaningFields()
                    else if (widget.serviceType == 'food')
                      _buildFoodFields()
                    else if (widget.serviceType == 'maintenance')
                      _buildMaintenanceFields(),

                    const SizedBox(height: 24),

                    // Notes field for all service types
                    CustomTextField(
                      controller: _notesController,
                      labelText: 'Additional Notes',
                      hintText: 'Any special instructions or requests',
                      maxLines: 3,
                    ),

                    if (_errorMessage != null) ...[
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                      ),
                    ],

                    const SizedBox(height: 24),

                    CustomButton(
                      text: 'Submit Request',
                      onPressed: _submitRequest,
                      isLoading: _isLoading,
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBookingInfo() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Booking Details',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.hotel, size: 16),
                const SizedBox(width: 8),
                Text('Room: ${widget.booking.roomName}'),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16),
                const SizedBox(width: 8),
                Text(
                    'Check-in: ${_formatDate(Timestamp.fromDate(widget.booking.checkIn))}'),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16),
                const SizedBox(width: 8),
                Text(
                    'Check-out: ${_formatDate(Timestamp.fromDate(widget.booking.checkOut))}'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrioritySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Priority',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        SegmentedButton<String>(
          segments: const [
            ButtonSegment<String>(
              value: 'low',
              label: Text('Low'),
              icon: Icon(Icons.arrow_downward),
            ),
            ButtonSegment<String>(
              value: 'medium',
              label: Text('Medium'),
              icon: Icon(Icons.remove),
            ),
            ButtonSegment<String>(
              value: 'high',
              label: Text('High'),
              icon: Icon(Icons.arrow_upward),
            ),
          ],
          selected: {_priority},
          onSelectionChanged: (Set<String> newSelection) {
            setState(() {
              _priority = newSelection.first;
            });
          },
        ),
      ],
    );
  }

  Widget _buildCleaningFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Cleaning Type',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _cleaningType,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          items: const [
            DropdownMenuItem(value: 'regular', child: Text('Regular Cleaning')),
            DropdownMenuItem(value: 'deep', child: Text('Deep Cleaning')),
            DropdownMenuItem(
                value: 'turndown', child: Text('Turndown Service')),
            DropdownMenuItem(value: 'special', child: Text('Special Request')),
          ],
          onChanged: (value) {
            setState(() {
              _cleaningType = value!;
            });
          },
        ),
        const SizedBox(height: 16),
        Text(
          'Items to Clean',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            _buildSelectionChip('Bed', 'bed'),
            _buildSelectionChip('Bathroom', 'bathroom'),
            _buildSelectionChip('Floor', 'floor'),
            _buildSelectionChip('Windows', 'windows'),
            _buildSelectionChip('Furniture', 'furniture'),
          ],
        ),
      ],
    );
  }

  Widget _buildSelectionChip(String label, String value) {
    final isSelected = _selectedItems.contains(value);

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          if (selected) {
            _selectedItems.add(value);
          } else {
            _selectedItems.remove(value);
          }
        });
      },
    );
  }

  Widget _buildFoodFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Food Items',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            TextButton.icon(
              icon: const Icon(Icons.add),
              label: const Text('Add Item'),
              onPressed: _showAddFoodItemDialog,
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (_foodItems.isEmpty)
          const Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(
                child: Text(
                  'No items added yet. Tap "Add Item" to add food items to your order.',
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _foodItems.length,
            itemBuilder: (context, index) {
              final item = _foodItems[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  title: Text(item.name),
                  subtitle: Text(
                      '${item.quantity} x \$${item.price.toStringAsFixed(2)}'),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '\$${(item.quantity * item.price).toStringAsFixed(2)}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete_outline),
                        onPressed: () {
                          setState(() {
                            _foodItems.removeAt(index);
                          });
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        if (_foodItems.isNotEmpty) ...[
          const SizedBox(height: 16),
          Card(
            color: Theme.of(context).colorScheme.primaryContainer,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Total:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                  Text(
                    '\$${_calculateTotal().toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildMaintenanceFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Issue Type',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _issueType,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          items: const [
            DropdownMenuItem(value: 'plumbing', child: Text('Plumbing')),
            DropdownMenuItem(value: 'electrical', child: Text('Electrical')),
            DropdownMenuItem(
                value: 'hvac', child: Text('HVAC/Climate Control')),
            DropdownMenuItem(value: 'furniture', child: Text('Furniture')),
            DropdownMenuItem(value: 'appliance', child: Text('Appliance')),
            DropdownMenuItem(value: 'structural', child: Text('Structural')),
            DropdownMenuItem(value: 'other', child: Text('Other')),
          ],
          onChanged: (value) {
            setState(() {
              _issueType = value!;
            });
          },
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _descriptionController,
          labelText: 'Issue Description',
          hintText: 'Please describe the issue in detail',
          maxLines: 3,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please describe the issue';
            }
            return null;
          },
        ),
      ],
    );
  }

  void _showAddFoodItemDialog() {
    final nameController = TextEditingController();
    final priceController = TextEditingController();
    int quantity = 1;
    String? specialInstructions;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Add Food Item'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: 'Item Name',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: priceController,
                      decoration: const InputDecoration(
                        labelText: 'Price',
                        border: OutlineInputBorder(),
                        prefixText: '\$',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        const Text('Quantity:'),
                        const Spacer(),
                        IconButton(
                          icon: const Icon(Icons.remove),
                          onPressed: quantity > 1
                              ? () {
                                  setState(() {
                                    quantity--;
                                  });
                                }
                              : null,
                        ),
                        Text(
                          quantity.toString(),
                          style: const TextStyle(fontSize: 16),
                        ),
                        IconButton(
                          icon: const Icon(Icons.add),
                          onPressed: () {
                            setState(() {
                              quantity++;
                            });
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      decoration: const InputDecoration(
                        labelText: 'Special Instructions (Optional)',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                      onChanged: (value) {
                        specialInstructions = value.isEmpty ? null : value;
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    final name = nameController.text.trim();
                    final priceText = priceController.text.trim();

                    if (name.isEmpty || priceText.isEmpty) {
                      return;
                    }

                    final price = double.tryParse(priceText);
                    if (price == null || price <= 0) {
                      return;
                    }

                    this.setState(() {
                      _foodItems.add(FoodItem(
                        name: name,
                        quantity: quantity,
                        price: price,
                        specialInstructions: specialInstructions,
                      ));
                    });

                    Navigator.pop(context);
                  },
                  child: const Text('Add'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  double _calculateTotal() {
    return _foodItems.fold(
      0,
      (total, item) => total + (item.price * item.quantity),
    );
  }

  Future<void> _submitRequest() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate based on service type
    if (widget.serviceType == 'food' && _foodItems.isEmpty) {
      setState(() {
        _errorMessage = 'Please add at least one food item';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);

      final user = authService.user;
      if (user == null) {
        throw Exception('You must be logged in to submit a service request');
      }

      final BaseServiceRequest request;

      switch (widget.serviceType) {
        case 'cleaning':
          request = CleaningRequest(
            type: widget.serviceType,
            status: 'pending',
            roomNumber: widget.booking.roomName,
            hotelId: widget.booking.hotelId,
            guestId: user.uid,
            guestName: user.displayName ?? 'Guest',
            requestTime: Timestamp.now(),
            notes: _notesController.text,
            priority: _priority,
            cleaningType: _cleaningType,
            items: _selectedItems,
          );
          break;

        case 'food':
          request = FoodOrder(
            type: widget.serviceType,
            status: 'pending',
            roomNumber: widget.booking.roomName,
            hotelId: widget.booking.hotelId,
            guestId: user.uid,
            guestName: user.displayName ?? 'Guest',
            requestTime: Timestamp.now(),
            notes: _notesController.text,
            priority: _priority,
            items: _foodItems,
            totalAmount: _calculateTotal(),
          );
          break;

        case 'maintenance':
          request = MaintenanceRequest(
            type: widget.serviceType,
            status: 'pending',
            roomNumber: widget.booking.roomName,
            hotelId: widget.booking.hotelId,
            guestId: user.uid,
            guestName: user.displayName ?? 'Guest',
            requestTime: Timestamp.now(),
            notes: _notesController.text,
            priority: _priority,
            issueType: _issueType,
            description: _descriptionController.text,
          );
          break;

        default:
          throw Exception('Invalid service type');
      }

      // Create a service request handler
      final serviceRequestHandler = ServiceRequestHandler();

      // Create the service request and send notification to vendor
      await serviceRequestHandler.createServiceRequestWithNotification(request);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '${_getServiceTypeTitle()} request submitted successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error submitting request: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _getServiceTypeTitle() {
    switch (widget.serviceType) {
      case 'cleaning':
        return 'Cleaning';
      case 'food':
        return 'Food Service';
      case 'maintenance':
        return 'Maintenance';
      default:
        return 'Service';
    }
  }

  String _formatDate(Timestamp timestamp) {
    final date = timestamp.toDate();
    return '${date.day}/${date.month}/${date.year}';
  }
}
