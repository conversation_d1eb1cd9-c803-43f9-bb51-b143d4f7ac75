import 'package:cloud_firestore/cloud_firestore.dart';

/// Status of Aadhaar verification
enum AadhaarVerificationStatus {
  /// Not verified yet
  notVerified,
  
  /// Verification in progress
  pending,
  
  /// Verification successful
  verified,
  
  /// Verification failed
  failed,
}

/// Model for Aadhaar verification data
class AadhaarVerification {
  /// Unique ID for the verification record
  final String id;
  
  /// User ID associated with this verification
  final String userId;
  
  /// Aadhaar number (masked for security)
  final String maskedAadhaarNumber;
  
  /// Full name as per Aadhaar
  final String fullName;
  
  /// Date of birth as per Aadhaar
  final DateTime? dateOfBirth;
  
  /// Gender as per Aadhaar
  final String? gender;
  
  /// Address as per Aadhaar
  final String? address;
  
  /// Verification status
  final AadhaarVerificationStatus status;
  
  /// Verification timestamp
  final DateTime? verifiedAt;
  
  /// Verification method used (OTP, biometric, etc.)
  final String? verificationMethod;
  
  /// Reference ID from Aadhaar API
  final String? referenceId;
  
  /// Additional verification details
  final Map<String, dynamic>? verificationDetails;
  
  /// Creation timestamp
  final DateTime? createdAt;
  
  /// Last update timestamp
  final DateTime? updatedAt;

  /// Constructor
  AadhaarVerification({
    required this.id,
    required this.userId,
    required this.maskedAadhaarNumber,
    required this.fullName,
    this.dateOfBirth,
    this.gender,
    this.address,
    required this.status,
    this.verifiedAt,
    this.verificationMethod,
    this.referenceId,
    this.verificationDetails,
    this.createdAt,
    this.updatedAt,
  });

  /// Create from Firestore document
  factory AadhaarVerification.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return AadhaarVerification(
      id: doc.id,
      userId: data['userId'] ?? '',
      maskedAadhaarNumber: data['maskedAadhaarNumber'] ?? '',
      fullName: data['fullName'] ?? '',
      dateOfBirth: (data['dateOfBirth'] as Timestamp?)?.toDate(),
      gender: data['gender'],
      address: data['address'],
      status: _statusFromString(data['status'] ?? 'notVerified'),
      verifiedAt: (data['verifiedAt'] as Timestamp?)?.toDate(),
      verificationMethod: data['verificationMethod'],
      referenceId: data['referenceId'],
      verificationDetails: data['verificationDetails'],
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  /// Create from Map
  factory AadhaarVerification.fromMap(Map<String, dynamic> map) {
    return AadhaarVerification(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      maskedAadhaarNumber: map['maskedAadhaarNumber'] ?? '',
      fullName: map['fullName'] ?? '',
      dateOfBirth: map['dateOfBirth'] != null 
          ? (map['dateOfBirth'] is Timestamp 
              ? (map['dateOfBirth'] as Timestamp).toDate() 
              : DateTime.parse(map['dateOfBirth']))
          : null,
      gender: map['gender'],
      address: map['address'],
      status: _statusFromString(map['status'] ?? 'notVerified'),
      verifiedAt: map['verifiedAt'] != null 
          ? (map['verifiedAt'] is Timestamp 
              ? (map['verifiedAt'] as Timestamp).toDate() 
              : DateTime.parse(map['verifiedAt']))
          : null,
      verificationMethod: map['verificationMethod'],
      referenceId: map['referenceId'],
      verificationDetails: map['verificationDetails'],
      createdAt: map['createdAt'] != null 
          ? (map['createdAt'] is Timestamp 
              ? (map['createdAt'] as Timestamp).toDate() 
              : DateTime.parse(map['createdAt']))
          : null,
      updatedAt: map['updatedAt'] != null 
          ? (map['updatedAt'] is Timestamp 
              ? (map['updatedAt'] as Timestamp).toDate() 
              : DateTime.parse(map['updatedAt']))
          : null,
    );
  }

  /// Convert to Map
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'maskedAadhaarNumber': maskedAadhaarNumber,
      'fullName': fullName,
      'dateOfBirth': dateOfBirth,
      'gender': gender,
      'address': address,
      'status': _statusToString(status),
      'verifiedAt': verifiedAt,
      'verificationMethod': verificationMethod,
      'referenceId': referenceId,
      'verificationDetails': verificationDetails,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  /// Convert status enum to string
  static String _statusToString(AadhaarVerificationStatus status) {
    switch (status) {
      case AadhaarVerificationStatus.notVerified:
        return 'notVerified';
      case AadhaarVerificationStatus.pending:
        return 'pending';
      case AadhaarVerificationStatus.verified:
        return 'verified';
      case AadhaarVerificationStatus.failed:
        return 'failed';
      default:
        return 'notVerified';
    }
  }

  /// Convert string to status enum
  static AadhaarVerificationStatus _statusFromString(String status) {
    switch (status) {
      case 'notVerified':
        return AadhaarVerificationStatus.notVerified;
      case 'pending':
        return AadhaarVerificationStatus.pending;
      case 'verified':
        return AadhaarVerificationStatus.verified;
      case 'failed':
        return AadhaarVerificationStatus.failed;
      default:
        return AadhaarVerificationStatus.notVerified;
    }
  }
}
