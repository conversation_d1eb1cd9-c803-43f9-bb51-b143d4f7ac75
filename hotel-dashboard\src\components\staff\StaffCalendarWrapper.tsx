import React, { useState, useEffect } from 'react';
import { Box, Typography, CircularProgress, Alert, Button } from '@mui/material';
import StaffCalendar from './StaffCalendar';
import BasicCalendar from './BasicCalendar';

// Check if FullCalendar dependencies are available
const checkDependencies = () => {
  try {
    // Check if FullCalendar is defined in window
    return typeof window !== 'undefined' &&
           typeof window.document !== 'undefined' &&
           typeof window.document.createElement !== 'undefined';
  } catch (error) {
    console.error('Error checking dependencies:', error);
    return false;
  }
};

interface StaffCalendarWrapperProps {
  hotelId: string;
  vendorId: string;
  staffList: any[];
  shifts: any[];
  timeOffRequests: any[];
  loading: boolean;
  error: string | null;
  onRefresh: () => void;
  onShiftCreate: (shift: any) => Promise<void>;
  onShiftUpdate: (shiftId: string, updates: any) => Promise<void>;
  onShiftDelete: (shiftId: string) => Promise<void>;
}

const StaffCalendarWrapper: React.FC<StaffCalendarWrapperProps> = (props) => {
  const [dependenciesAvailable, setDependenciesAvailable] = useState<boolean | null>(null);

  useEffect(() => {
    // Check if dependencies are available
    setDependenciesAvailable(checkDependencies());
  }, []);

  if (dependenciesAvailable === null) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (dependenciesAvailable === false) {
    return (
      <Box>
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body1" gutterBottom>
            Using basic calendar view. For enhanced calendar features, install FullCalendar dependencies:
          </Typography>
          <Typography variant="body2" component="div" sx={{ mt: 1 }}>
            <Box component="pre" sx={{ bgcolor: '#f5f5f5', p: 1, borderRadius: 1, fontSize: '0.8rem' }}>
              npm install @fullcalendar/core @fullcalendar/daygrid @fullcalendar/interaction @fullcalendar/react @fullcalendar/timegrid
            </Box>
          </Typography>
        </Alert>

        {/* Use BasicCalendar as fallback */}
        <BasicCalendar {...props} />
      </Box>
    );
  }

  // If dependencies are available, render the StaffCalendar component
  return <StaffCalendar {...props} />;
};

export default StaffCalendarWrapper;
