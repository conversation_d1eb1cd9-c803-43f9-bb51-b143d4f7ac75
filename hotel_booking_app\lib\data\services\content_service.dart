import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

class ContentService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  Map<String, dynamic>? _aboutUs;
  Map<String, dynamic>? _privacyPolicy;
  Map<String, dynamic>? _termsOfService;
  List<Map<String, dynamic>> _socialLinks = [];
  
  bool _isLoading = false;
  String? _error;

  ContentService() {
    fetchAboutUs();
    fetchPrivacyPolicy();
    fetchTermsOfService();
    fetchSocialLinks();
  }

  // Getters
  Map<String, dynamic>? get aboutUs => _aboutUs;
  Map<String, dynamic>? get privacyPolicy => _privacyPolicy;
  Map<String, dynamic>? get termsOfService => _termsOfService;
  List<Map<String, dynamic>> get socialLinks => _socialLinks;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Fetch About Us content from Firestore
  Future<void> fetchAboutUs() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final doc = await _firestore.collection('content').doc('about_us').get();
      
      if (!doc.exists) {
        // If about us content doesn't exist, create default
        await _createDefaultAboutUs();
        final newDoc = await _firestore.collection('content').doc('about_us').get();
        _aboutUs = newDoc.data();
      } else {
        _aboutUs = doc.data();
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Create default About Us content in Firestore
  Future<void> _createDefaultAboutUs() async {
    final aboutUsData = {
      'title': 'About Link In Blink Hotel',
      'lastUpdated': FieldValue.serverTimestamp(),
      'sections': [
        {
          'title': 'Our Story',
          'content': 'Link In Blink Hotel was founded in 2023 with a vision to revolutionize the hotel booking experience. We believe that booking a hotel should be as simple as a blink of an eye, hence our name "Link In Blink".\n\nOur journey began with a small team of passionate individuals who wanted to create a seamless and enjoyable hotel booking platform. Today, we have grown into a trusted name in the hospitality industry, connecting travelers with the best hotels across India.',
          'icon': 'history',
          'order': 1,
        },
        {
          'title': 'Our Mission',
          'content': 'At Link In Blink Hotel, our mission is to provide a seamless and personalized hotel booking experience for every traveler. We strive to connect our users with the perfect accommodations that meet their unique needs and preferences.\n\nWe are committed to transparency, reliability, and exceptional customer service. Our goal is to make travel planning stress-free and enjoyable, allowing our users to focus on creating memorable experiences.',
          'icon': 'flag',
          'order': 2,
        },
        {
          'title': 'Our Team',
          'content': 'Behind Link In Blink Hotel is a diverse team of passionate individuals dedicated to transforming the hotel booking experience. Our team combines expertise in hospitality, technology, and customer service to create a platform that truly understands and meets the needs of modern travelers.\n\nWe are united by our commitment to innovation, quality, and customer satisfaction. Each team member brings unique skills and perspectives, contributing to our collective success.',
          'icon': 'people',
          'order': 3,
        },
      ],
      'contactInfo': {
        'email': '<EMAIL>',
        'phone': '+91 **********',
        'address': 'Link In Blink Hotel Headquarters\n123 Hospitality Road\nBangalore, Karnataka 560001\nIndia',
      },
    };

    await _firestore.collection('content').doc('about_us').set(aboutUsData);
  }

  // Fetch Privacy Policy content from Firestore
  Future<void> fetchPrivacyPolicy() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final doc = await _firestore.collection('content').doc('privacy_policy').get();
      
      if (!doc.exists) {
        // If privacy policy content doesn't exist, create default
        await _createDefaultPrivacyPolicy();
        final newDoc = await _firestore.collection('content').doc('privacy_policy').get();
        _privacyPolicy = newDoc.data();
      } else {
        _privacyPolicy = doc.data();
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Create default Privacy Policy content in Firestore
  Future<void> _createDefaultPrivacyPolicy() async {
    final privacyPolicyData = {
      'title': 'Privacy Policy',
      'lastUpdated': FieldValue.serverTimestamp(),
      'sections': [
        {
          'title': 'Introduction',
          'content': 'Welcome to Link In Blink Hotel\'s Privacy Policy. This Privacy Policy describes how we collect, use, process, and disclose your information, including personal information, in conjunction with your access to and use of the Link In Blink Hotel platform.\n\nWhen you use our services, you trust us with your information. We are committed to keeping that trust. That starts with helping you understand our privacy practices.\n\nThis policy describes the information we collect, how it is used and shared, and your choices regarding this information. We recommend that you read this Privacy Policy carefully when using our services or transacting business with us.',
          'order': 1,
        },
        {
          'title': 'Information We Collect',
          'content': 'We collect various types of information, including information that identifies you as an individual (personal information). Here\'s what we collect:',
          'subsections': [
            {
              'title': 'Information You Provide to Us',
              'bulletPoints': [
                'Account Information: When you create an account, we collect your name, email address, phone number, username, and password.',
                'Profile Information: When you complete your profile, we collect additional information such as your address, profile picture, and payment information.',
                'Verification Information: To verify your identity, we may collect government identification documents, such as your Aadhaar card details.',
                'Communications: When you communicate with us or other users through our platform, we collect the content of those communications.',
              ],
            },
            {
              'title': 'Information We Collect Automatically',
              'bulletPoints': [
                'Usage Information: We collect information about your interactions with our platform, such as the pages or content you view, your searches, bookings, and other actions.',
                'Device Information: We collect information about the devices you use to access our services, including IP address, browser type, operating system, and device identifiers.',
                'Location Information: With your consent, we may collect precise location information from your device.',
                'Cookies and Similar Technologies: We use cookies and similar technologies to collect information about your browsing behavior and preferences.',
              ],
            },
          ],
          'order': 2,
        },
        {
          'title': 'How We Use Your Information',
          'content': 'We use the information we collect for various purposes, including:',
          'bulletPoints': [
            'Providing, maintaining, and improving our services.',
            'Processing your bookings and transactions.',
            'Communicating with you about your bookings, account, and our services.',
            'Personalizing your experience and providing content and features that match your profile and interests.',
            'Ensuring the security and integrity of our platform.',
            'Enforcing our terms, conditions, and policies.',
            'Complying with legal obligations.',
          ],
          'order': 3,
        },
      ],
      'contactInfo': {
        'email': '<EMAIL>',
        'address': 'Link In Blink Hotel, 123 Hospitality Road, Bangalore, Karnataka 560001, India',
        'phone': '+91 **********',
      },
    };

    await _firestore.collection('content').doc('privacy_policy').set(privacyPolicyData);
  }

  // Fetch Terms of Service content from Firestore
  Future<void> fetchTermsOfService() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final doc = await _firestore.collection('content').doc('terms_of_service').get();
      
      if (!doc.exists) {
        // If terms of service content doesn't exist, create default
        await _createDefaultTermsOfService();
        final newDoc = await _firestore.collection('content').doc('terms_of_service').get();
        _termsOfService = newDoc.data();
      } else {
        _termsOfService = doc.data();
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Create default Terms of Service content in Firestore
  Future<void> _createDefaultTermsOfService() async {
    final termsOfServiceData = {
      'title': 'Terms of Service',
      'lastUpdated': FieldValue.serverTimestamp(),
      'sections': [
        {
          'title': 'Introduction',
          'content': 'Welcome to Link In Blink Hotel. These Terms of Service ("Terms") govern your access to and use of the Link In Blink Hotel platform, including our website, mobile applications, and services (collectively, the "Platform").\n\nBy accessing or using our Platform, you agree to be bound by these Terms. If you do not agree to these Terms, you may not access or use the Platform.\n\nPlease read these Terms carefully, as they contain important information about your legal rights, remedies, and obligations. By accessing or using our Platform, you agree to comply with and be bound by these Terms.',
          'order': 1,
        },
        {
          'title': 'Eligibility',
          'content': 'You must be at least 18 years old to access or use our Platform. By accessing or using our Platform, you represent and warrant that you are 18 years of age or older.\n\nIf you are accessing or using our Platform on behalf of a company, organization, or other legal entity, you represent and warrant that you have the authority to bind that entity to these Terms, in which case the terms "you" or "your" will refer to that entity.',
          'order': 2,
        },
        {
          'title': 'Account Registration',
          'content': 'To access certain features of our Platform, you may need to register for an account. When you register for an account, you agree to provide accurate, current, and complete information about yourself.\n\nYou are responsible for safeguarding your account credentials and for any activity that occurs under your account. You agree to notify us immediately of any unauthorized use of your account.\n\nWe reserve the right to suspend or terminate your account if any information provided during the registration process or thereafter proves to be inaccurate, false, or misleading, or if you violate any provision of these Terms.',
          'order': 3,
        },
      ],
      'contactInfo': {
        'email': '<EMAIL>',
        'address': 'Link In Blink Hotel, 123 Hospitality Road, Bangalore, Karnataka 560001, India',
        'phone': '+91 **********',
      },
    };

    await _firestore.collection('content').doc('terms_of_service').set(termsOfServiceData);
  }

  // Fetch Social Links from Firestore
  Future<void> fetchSocialLinks() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final snapshot = await _firestore.collection('social_links').orderBy('order').get();
      
      if (snapshot.docs.isEmpty) {
        // If no social links exist, create default ones
        await _createDefaultSocialLinks();
        final newSnapshot = await _firestore.collection('social_links').orderBy('order').get();
        _socialLinks = newSnapshot.docs.map((doc) => {
          'id': doc.id,
          ...doc.data(),
        }).toList();
      } else {
        _socialLinks = snapshot.docs.map((doc) => {
          'id': doc.id,
          ...doc.data(),
        }).toList();
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Create default Social Links in Firestore
  Future<void> _createDefaultSocialLinks() async {
    final batch = _firestore.batch();
    
    final defaultSocialLinks = [
      {
        'name': 'Facebook',
        'url': 'https://facebook.com/linkinblink',
        'icon': 'facebook',
        'color': '#1877F2',
        'order': 1,
      },
      {
        'name': 'Instagram',
        'url': 'https://instagram.com/linkinblink',
        'icon': 'camera_alt',
        'color': '#E1306C',
        'order': 2,
      },
      {
        'name': 'Twitter',
        'url': 'https://twitter.com/linkinblink',
        'icon': 'telegram',
        'color': '#0088CC',
        'order': 3,
      },
      {
        'name': 'YouTube',
        'url': 'https://youtube.com/linkinblink',
        'icon': 'video_library',
        'color': '#FF0000',
        'order': 4,
      },
      {
        'name': 'LinkedIn',
        'url': 'https://linkedin.com/company/linkinblink',
        'icon': 'link',
        'color': '#0A66C2',
        'order': 5,
      },
    ];

    for (final link in defaultSocialLinks) {
      final docRef = _firestore.collection('social_links').doc();
      batch.set(docRef, link);
    }

    await batch.commit();
  }
}
