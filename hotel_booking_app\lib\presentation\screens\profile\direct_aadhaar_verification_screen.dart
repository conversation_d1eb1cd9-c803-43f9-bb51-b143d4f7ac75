import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hotel_booking_app/data/services/direct_sandbox_api.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_button.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_text_field.dart';

class DirectAadhaarVerificationScreen extends StatefulWidget {
  const DirectAadhaarVerificationScreen({super.key});

  @override
  State<DirectAadhaarVerificationScreen> createState() =>
      _DirectAadhaarVerificationScreenState();
}

class _DirectAadhaarVerificationScreenState
    extends State<DirectAadhaarVerificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _aadhaarController = TextEditingController();
  final _otpController = TextEditingController();

  final DirectSandboxApi _api = DirectSandboxApi();

  bool _isLoading = false;
  bool _otpSent = false;
  String? _accessToken;
  String? _referenceId;
  String? _errorMessage;
  Map<String, dynamic>? _verificationData;

  @override
  void initState() {
    super.initState();
    _authenticate();
  }

  @override
  void dispose() {
    _aadhaarController.dispose();
    _otpController.dispose();
    _api.dispose();
    super.dispose();
  }

  Future<void> _authenticate() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final token = await _api.authenticate();

      if (token != null) {
        setState(() {
          _accessToken = token;
          _errorMessage = null;
        });
      } else {
        setState(() {
          _errorMessage =
              'Failed to authenticate with the API. Please try again.';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _generateOtp() async {
    if (_formKey.currentState!.validate() && _accessToken != null) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      try {
        final aadhaarNumber = _aadhaarController.text.replaceAll(' ', '');
        final result = await _api.generateOtp(aadhaarNumber, _accessToken!);

        if (result['success']) {
          setState(() {
            _referenceId = result['reference_id'];
            _otpSent = true;
            _errorMessage = null;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(result['message']),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          setState(() {
            _errorMessage = result['error'];
          });
        }
      } catch (e) {
        setState(() {
          _errorMessage = 'Error: ${e.toString()}';
        });
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _verifyOtp() async {
    if (_otpController.text.length == 6 &&
        _referenceId != null &&
        _accessToken != null) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      try {
        final result = await _api.verifyOtp(
            _referenceId!, _otpController.text, _accessToken!);

        if (result['success']) {
          setState(() {
            _verificationData = result['data'];
            _errorMessage = null;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Aadhaar verification successful!'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          setState(() {
            _errorMessage = result['error'];
          });
        }
      } catch (e) {
        setState(() {
          _errorMessage = 'Error: ${e.toString()}';
        });
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    } else {
      setState(() {
        _errorMessage = 'Please enter a valid 6-digit OTP';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Direct Aadhaar Verification'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_accessToken == null && !_isLoading)
              ElevatedButton(
                onPressed: _authenticate,
                child: const Text('Authenticate with API'),
              ),
            if (_errorMessage != null)
              Container(
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Text(
                  _errorMessage!,
                  style: TextStyle(color: Colors.red.shade800),
                ),
              ),
            if (_verificationData != null)
              _buildVerificationDetails()
            else if (_otpSent)
              _buildOtpVerificationForm()
            else
              _buildAadhaarForm(),
          ],
        ),
      ),
    );
  }

  Widget _buildAadhaarForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Enter your Aadhaar number',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _aadhaarController,
            labelText: 'Aadhaar Number',
            hintText: 'Enter your 12-digit Aadhaar number',
            prefixIcon: const Icon(Icons.credit_card),
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(12),
            ],
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your Aadhaar number';
              }
              final cleanValue = value.replaceAll(' ', '');
              if (cleanValue.length != 12) {
                return 'Aadhaar number must be 12 digits';
              }
              return null;
            },
          ),
          const SizedBox(height: 24),
          CustomButton(
            text: 'Send OTP',
            isLoading: _isLoading,
            onPressed: _generateOtp,
          ),
        ],
      ),
    );
  }

  Widget _buildOtpVerificationForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Enter OTP',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'An OTP has been sent to your registered mobile number',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _otpController,
          labelText: 'OTP',
          hintText: 'Enter 6-digit OTP',
          prefixIcon: const Icon(Icons.lock_outline),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(6),
          ],
        ),
        const SizedBox(height: 24),
        CustomButton(
          text: 'Verify OTP',
          isLoading: _isLoading,
          onPressed: _verifyOtp,
        ),
        const SizedBox(height: 16),
        TextButton(
          onPressed: () {
            setState(() {
              _otpSent = false;
              _referenceId = null;
            });
          },
          child: const Text('Back to Aadhaar Entry'),
        ),
      ],
    );
  }

  Widget _buildVerificationDetails() {
    final data = _verificationData!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Verification Successful',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.green,
          ),
        ),
        const SizedBox(height: 16),
        _buildInfoRow('Name', data['name'] ?? 'Not available'),
        _buildInfoRow('Gender', data['gender'] ?? 'Not available'),
        _buildInfoRow(
            'Year of Birth', data['year_of_birth'] ?? 'Not available'),
        _buildInfoRow('Address', data['full_address'] ?? 'Not available'),
        const SizedBox(height: 24),
        CustomButton(
          text: 'Verify Another Aadhaar',
          onPressed: () {
            setState(() {
              _otpSent = false;
              _referenceId = null;
              _verificationData = null;
              _aadhaarController.clear();
              _otpController.clear();
            });
          },
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
