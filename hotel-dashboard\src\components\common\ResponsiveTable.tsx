import React from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Card,
  CardContent,
  Divider,
  useTheme,
  useMediaQuery,
  Chip
} from '@mui/material';

interface Column {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'right' | 'left' | 'center';
  format?: (value: any) => React.ReactNode;
  hideOnMobile?: boolean;
}

interface ResponsiveTableProps {
  columns: Column[];
  rows: any[];
  emptyMessage?: string;
  rowsPerPage?: number;
  page?: number;
}

/**
 * A responsive table component that displays as a normal table on desktop
 * and as cards on mobile devices
 */
const ResponsiveTable: React.FC<ResponsiveTableProps> = ({
  columns,
  rows,
  emptyMessage = 'No data available',
  rowsPerPage = 10,
  page = 0
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // Filter out columns that should be hidden on mobile when in mobile view
  const visibleColumns = isMobile 
    ? columns.filter(column => !column.hideOnMobile)
    : columns;

  // Calculate which rows to display based on pagination
  const displayedRows = rows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  // Desktop view - standard table
  const renderDesktopTable = () => (
    <TableContainer component={Paper} sx={{ overflowX: 'auto' }}>
      <Table aria-label="responsive table">
        <TableHead>
          <TableRow>
            {columns.map((column) => (
              <TableCell
                key={column.id}
                align={column.align || 'left'}
                style={{ minWidth: column.minWidth }}
              >
                <Typography variant="subtitle2" fontWeight="bold">
                  {column.label}
                </Typography>
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {displayedRows.length > 0 ? (
            displayedRows.map((row, index) => (
              <TableRow hover tabIndex={-1} key={index}>
                {columns.map((column) => {
                  const value = row[column.id];
                  return (
                    <TableCell key={column.id} align={column.align || 'left'}>
                      {column.format ? column.format(value) : value}
                    </TableCell>
                  );
                })}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} align="center">
                {emptyMessage}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );

  // Mobile view - cards
  const renderMobileCards = () => (
    <Box>
      {displayedRows.length > 0 ? (
        displayedRows.map((row, index) => (
          <Card key={index} sx={{ mb: 2, borderRadius: 2 }}>
            <CardContent sx={{ p: 2 }}>
              {visibleColumns.map((column, colIndex) => (
                <React.Fragment key={column.id}>
                  <Box 
                    sx={{ 
                      display: 'flex', 
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      py: 1
                    }}
                  >
                    <Typography variant="body2" color="text.secondary">
                      {column.label}
                    </Typography>
                    <Box sx={{ textAlign: column.align || 'left' }}>
                      {column.format 
                        ? column.format(row[column.id]) 
                        : row[column.id]}
                    </Box>
                  </Box>
                  {colIndex < visibleColumns.length - 1 && (
                    <Divider sx={{ my: 0.5 }} />
                  )}
                </React.Fragment>
              ))}
            </CardContent>
          </Card>
        ))
      ) : (
        <Box sx={{ textAlign: 'center', py: 3 }}>
          <Typography variant="body1" color="text.secondary">
            {emptyMessage}
          </Typography>
        </Box>
      )}
    </Box>
  );

  return isMobile ? renderMobileCards() : renderDesktopTable();
};

export default ResponsiveTable;
