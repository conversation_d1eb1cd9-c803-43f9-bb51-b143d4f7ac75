import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/data/models/booking_model.dart';

class BookingRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Create booking
  Future<String?> createBooking(Map<String, dynamic> bookingData) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.bookingsCollection)
          .add(bookingData);

      // Update room status
      await _firestore
          .collection(AppConstants.roomsCollection)
          .doc(bookingData['roomId'])
          .update({'status': AppConstants.roomBooked});

      return docRef.id;
    } catch (e) {
      print('Error creating booking: $e');
      return null;
    }
  }

  // Fetch bookings by user ID
  Future<List<Booking>> fetchBookingsByUserId(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.bookingsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => Booking.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      print('Error fetching bookings by user ID: $e');
      return [];
    }
  }

  // Fetch booking by ID
  Future<Booking?> fetchBookingById(String bookingId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(bookingId)
          .get();

      if (doc.exists) {
        return Booking.fromMap({...doc.data()!, 'id': doc.id});
      }
      return null;
    } catch (e) {
      print('Error fetching booking by ID: $e');
      return null;
    }
  }

  // Update booking status
  Future<bool> updateBookingStatus(String bookingId, String status) async {
    try {
      await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(bookingId)
          .update({
        'status': status,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      return true;
    } catch (e) {
      print('Error updating booking status: $e');
      return false;
    }
  }

  // Update payment status
  Future<bool> updatePaymentStatus(String bookingId, String paymentStatus) async {
    try {
      await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(bookingId)
          .update({
        'paymentStatus': paymentStatus,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      return true;
    } catch (e) {
      print('Error updating payment status: $e');
      return false;
    }
  }

  // Cancel booking
  Future<bool> cancelBooking(String bookingId, String roomId) async {
    try {
      // Update booking status
      await _firestore
          .collection(AppConstants.bookingsCollection)
          .doc(bookingId)
          .update({
        'status': AppConstants.statusCancelled,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update room status
      await _firestore
          .collection(AppConstants.roomsCollection)
          .doc(roomId)
          .update({'status': AppConstants.roomAvailable});

      return true;
    } catch (e) {
      print('Error cancelling booking: $e');
      return false;
    }
  }

  // Fetch upcoming bookings
  Future<List<Booking>> fetchUpcomingBookings(String userId) async {
    try {
      final now = DateTime.now();
      final snapshot = await _firestore
          .collection(AppConstants.bookingsCollection)
          .where('userId', isEqualTo: userId)
          .where('checkIn', isGreaterThan: Timestamp.fromDate(now))
          .orderBy('checkIn')
          .get();

      return snapshot.docs
          .map((doc) => Booking.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      print('Error fetching upcoming bookings: $e');
      return [];
    }
  }

  // Fetch active bookings
  Future<List<Booking>> fetchActiveBookings(String userId) async {
    try {
      final now = DateTime.now();
      final snapshot = await _firestore
          .collection(AppConstants.bookingsCollection)
          .where('userId', isEqualTo: userId)
          .where('checkIn', isLessThanOrEqualTo: Timestamp.fromDate(now))
          .where('checkOut', isGreaterThan: Timestamp.fromDate(now))
          .orderBy('checkIn')
          .get();

      return snapshot.docs
          .map((doc) => Booking.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      print('Error fetching active bookings: $e');
      return [];
    }
  }

  // Fetch past bookings
  Future<List<Booking>> fetchPastBookings(String userId) async {
    try {
      final now = DateTime.now();
      final snapshot = await _firestore
          .collection(AppConstants.bookingsCollection)
          .where('userId', isEqualTo: userId)
          .where('checkOut', isLessThanOrEqualTo: Timestamp.fromDate(now))
          .orderBy('checkOut', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => Booking.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      print('Error fetching past bookings: $e');
      return [];
    }
  }
}
