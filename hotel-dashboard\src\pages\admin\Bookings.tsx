import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Snackbar,
  Alert,
  SelectChangeEvent,
  TablePagination,
  Chip,
  IconButton,
  Tooltip,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import ResponsiveTable from '../../components/common/ResponsiveTable';
import { useIsMobile } from '../../utils/responsiveUtils';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  CheckCircle as ConfirmIcon,
  Cancel as CancelIcon,
  Receipt as InvoiceIcon
} from '@mui/icons-material';
import { getAllBookings, BookingData } from '../../firebase/bookingService';
import { getAllHotels, HotelData } from '../../firebase/hotelService';
import { getRoomsForHotel, RoomData } from '../../firebase/roomService';

const Bookings: React.FC = () => {
  const isMobile = useIsMobile();

  // State for bookings data
  const [bookings, setBookings] = useState<BookingData[]>([]);
  const [hotels, setHotels] = useState<HotelData[]>([]);
  const [rooms, setRooms] = useState<RoomData[]>([]);
  const [loading, setLoading] = useState(true);

  // State for filtering and searching
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');

  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // State for notifications
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  // State for booking dialog
  const [openDialog, setOpenDialog] = useState(false);
  const [editingBooking, setEditingBooking] = useState<BookingData | null>(null);
  const [formData, setFormData] = useState<BookingData>({
    hotelId: '',
    roomId: '',
    userId: '',
    guestName: '',
    guestEmail: '',
    guestPhone: '',
    checkInDate: new Date(),
    checkOutDate: new Date(new Date().setDate(new Date().getDate() + 1)),
    adults: 1,
    children: 0,
    totalAmount: 0,
    paymentStatus: 'pending',
    bookingStatus: 'pending',
    specialRequests: ''
  });

  // Load bookings and hotels on component mount
  useEffect(() => {
    fetchBookings();
    fetchHotels();
  }, []);

  // Fetch rooms when selected hotel changes
  useEffect(() => {
    if (selectedHotel) {
      fetchRoomsForHotel(selectedHotel);
    }
  }, [selectedHotel]);

  const fetchBookings = async () => {
    try {
      setLoading(true);
      const bookingsList = await getAllBookings();
      // Cast the result to BookingData[] to ensure type safety
      setBookings(bookingsList as BookingData[]);
    } catch (error) {
      console.error('Error fetching bookings:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load bookings',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchHotels = async () => {
    try {
      const hotelsList = await getAllHotels();
      setHotels(hotelsList);
    } catch (error) {
      console.error('Error fetching hotels:', error);
    }
  };

  const fetchRoomsForHotel = async (hotelId: string) => {
    try {
      const roomsList = await getRoomsForHotel(hotelId);
      // Cast the result to RoomData[] to ensure type safety
      setRooms(roomsList as RoomData[]);
    } catch (error) {
      console.error('Error fetching rooms:', error);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleHotelFilter = (e: SelectChangeEvent) => {
    setSelectedHotel(e.target.value);
  };

  const handleStatusFilter = (e: SelectChangeEvent) => {
    setSelectedStatus(e.target.value);
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Dialog handlers
  const handleOpenDialog = (booking?: BookingData) => {
    if (booking) {
      // Edit mode
      setEditingBooking(booking);
      setFormData({
        hotelId: booking.hotelId,
        roomId: booking.roomId,
        userId: booking.userId,
        guestName: booking.guestName,
        guestEmail: booking.guestEmail,
        guestPhone: booking.guestPhone || '',
        checkInDate: booking.checkInDate,
        checkOutDate: booking.checkOutDate,
        adults: booking.adults,
        children: booking.children,
        totalAmount: booking.totalAmount,
        paymentStatus: booking.paymentStatus,
        bookingStatus: booking.bookingStatus,
        specialRequests: booking.specialRequests || ''
      });
      // Fetch rooms for the selected hotel
      if (booking.hotelId) {
        fetchRoomsForHotel(booking.hotelId);
      }
    } else {
      // Create mode
      setEditingBooking(null);
      setFormData({
        hotelId: hotels.length > 0 ? hotels[0].id! : '',
        roomId: '',
        userId: '',
        guestName: '',
        guestEmail: '',
        guestPhone: '',
        checkInDate: new Date(),
        checkOutDate: new Date(new Date().setDate(new Date().getDate() + 1)),
        adults: 1,
        children: 0,
        totalAmount: 0,
        paymentStatus: 'pending',
        bookingStatus: 'pending',
        specialRequests: ''
      });
      // Fetch rooms for the default hotel
      if (hotels.length > 0) {
        fetchRoomsForHotel(hotels[0].id!);
      }
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  // Filter bookings based on search term, selected hotel, and status
  // FIX: Added null checks and default values to prevent TypeError
  const filteredBookings = bookings.filter(booking => {
    if (!booking) return false;

    const matchesSearch =
      (booking.guestName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (booking.guestEmail?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (booking.guestPhone && booking.guestPhone.includes(searchTerm));

    const matchesHotel = selectedHotel ? booking.hotelId === selectedHotel : true;
    const matchesStatus = selectedStatus ? booking.bookingStatus === selectedStatus : true;

    return matchesSearch && matchesHotel && matchesStatus;
  });

  // Get hotel name by ID
  const getHotelName = (hotelId: string) => {
    const hotel = hotels.find(h => h.id === hotelId);
    return hotel ? hotel.name : 'Unknown Hotel';
  };

  // Get room name by ID
  const getRoomName = (roomId: string) => {
    const room = rooms.find(r => r.id === roomId);
    return room ? room.name : 'Unknown Room';
  };

  // Format date from Firestore Timestamp
  const formatDate = (date: any) => {
    if (!date) return 'N/A';
    // Handle Firestore Timestamp
    if (date && typeof date.toDate === 'function') {
      date = date.toDate();
    }
    // Handle JavaScript Date
    if (date instanceof Date) {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
    return 'N/A';
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Get booking status color
  const getBookingStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'error';
      case 'completed':
        return 'info';
      default:
        return 'default';
    }
  };

  // Get payment status color
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'success';
      case 'pending':
        return 'warning';
      case 'refunded':
        return 'info';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Box sx={{
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', sm: 'center' },
        gap: { xs: 2, sm: 0 },
        mb: 3
      }}>
        <Typography
          variant="h4"
          sx={{
            fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' }
          }}
        >
          Bookings Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          size={isMobile ? "small" : "medium"}
        >
          Add Booking
        </Button>
      </Box>

      <Paper sx={{ p: { xs: 1.5, sm: 2 }, mb: 3, borderRadius: 2 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <TextField
              placeholder="Search by guest name, email, or phone..."
              variant="outlined"
              fullWidth
              value={searchTerm}
              onChange={handleSearch}
              size={isMobile ? "small" : "medium"}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize={isMobile ? "small" : "medium"} />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size={isMobile ? "small" : "medium"}>
              <InputLabel>Filter by Hotel</InputLabel>
              <Select
                value={selectedHotel}
                onChange={handleHotelFilter}
                label="Filter by Hotel"
              >
                <MenuItem value="">All Hotels</MenuItem>
                {hotels.map((hotel) => (
                  <MenuItem key={hotel.id} value={hotel.id}>
                    {hotel.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size={isMobile ? "small" : "medium"}>
              <InputLabel>Filter by Status</InputLabel>
              <Select
                value={selectedStatus}
                onChange={handleStatusFilter}
                label="Filter by Status"
              >
                <MenuItem value="">All Statuses</MenuItem>
                <MenuItem value="confirmed">Confirmed</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="cancelled">Cancelled</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchBookings}
              fullWidth
              size={isMobile ? "small" : "medium"}
              sx={{ height: { xs: 'auto', md: '100%' } }}
            >
              Refresh
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Paper sx={{ borderRadius: 2, overflow: 'hidden' }}>
          {/* Use ResponsiveTable for better mobile experience */}
          <ResponsiveTable
            columns={[
              {
                id: 'guest',
                label: 'Guest',
                format: (value: any) => (
                  <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                    <Typography variant="body1" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                      {value.name || 'N/A'}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {value.email || 'N/A'}
                    </Typography>
                    {value.phone && (
                      <Typography variant="caption" color="text.secondary">
                        {value.phone}
                      </Typography>
                    )}
                  </Box>
                )
              },
              {
                id: 'hotel',
                label: 'Hotel / Room',
                format: (value: any) => (
                  <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                    <Typography variant="body2" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                      {value.hotel}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {value.room}
                    </Typography>
                  </Box>
                )
              },
              {
                id: 'checkIn',
                label: 'Check-in',
                hideOnMobile: true
              },
              {
                id: 'checkOut',
                label: 'Check-out',
                hideOnMobile: true
              },
              {
                id: 'guests',
                label: 'Guests',
                hideOnMobile: true
              },
              {
                id: 'amount',
                label: 'Amount',
                align: 'right'
              },
              {
                id: 'status',
                label: 'Status',
                format: (value: string) => (
                  <Chip
                    label={value?.charAt(0).toUpperCase() + value?.slice(1) || 'N/A'}
                    color={getBookingStatusColor(value) as any}
                    size="small"
                    sx={{
                      height: { xs: 20, sm: 24 },
                      '& .MuiChip-label': {
                        fontSize: { xs: '0.625rem', sm: '0.75rem' },
                        px: { xs: 1, sm: 1.5 }
                      }
                    }}
                  />
                )
              },
              {
                id: 'payment',
                label: 'Payment',
                hideOnMobile: true,
                format: (value: string) => (
                  <Chip
                    label={value?.charAt(0).toUpperCase() + value?.slice(1) || 'N/A'}
                    color={getPaymentStatusColor(value) as any}
                    size="small"
                    sx={{
                      height: { xs: 20, sm: 24 },
                      '& .MuiChip-label': {
                        fontSize: { xs: '0.625rem', sm: '0.75rem' },
                        px: { xs: 1, sm: 1.5 }
                      }
                    }}
                  />
                )
              },
              {
                id: 'actions',
                label: 'Actions',
                align: 'right',
                format: (booking: BookingData) => (
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 0.5 }}>
                    <Tooltip title="View Details">
                      <IconButton color="primary" size="small">
                        <ViewIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit">
                      <IconButton
                        color="primary"
                        size="small"
                        onClick={() => handleOpenDialog(booking)}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    {booking.bookingStatus === 'pending' && (
                      <Tooltip title="Confirm">
                        <IconButton color="success" size="small">
                          <ConfirmIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                    {(booking.bookingStatus === 'pending' || booking.bookingStatus === 'confirmed') && (
                      <Tooltip title="Cancel">
                        <IconButton color="error" size="small">
                          <CancelIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                    <Tooltip title="Invoice">
                      <IconButton color="default" size="small">
                        <InvoiceIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                )
              }
            ]}
            rows={filteredBookings
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map(booking => ({
                id: booking.id,
                guest: {
                  name: booking.guestName,
                  email: booking.guestEmail,
                  phone: booking.guestPhone
                },
                hotel: {
                  hotel: getHotelName(booking.hotelId),
                  room: getRoomName(booking.roomId)
                },
                checkIn: formatDate(booking.checkInDate),
                checkOut: formatDate(booking.checkOutDate),
                guests: `${booking.adults} ${booking.adults === 1 ? 'Adult' : 'Adults'}${booking.children > 0 ? `, ${booking.children} ${booking.children === 1 ? 'Child' : 'Children'}` : ''}`,
                amount: formatCurrency(booking.totalAmount),
                status: booking.bookingStatus,
                payment: booking.paymentStatus,
                actions: booking
              }))}
            emptyMessage="No bookings found"
            rowsPerPage={rowsPerPage}
            page={page}
          />
          <TablePagination
            rowsPerPageOptions={isMobile ? [5, 10] : [5, 10, 25]}
            component="div"
            count={filteredBookings.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            labelRowsPerPage={isMobile ? "Rows:" : "Rows per page:"}
            slotProps={{
              select: {
                inputProps: { 'aria-label': 'rows per page' },
                native: true,
              }
            }}
            sx={{
              '.MuiTablePagination-selectLabel': {
                fontSize: { xs: '0.75rem', sm: '0.875rem' }
              },
              '.MuiTablePagination-select': {
                fontSize: { xs: '0.75rem', sm: '0.875rem' }
              },
              '.MuiTablePagination-displayedRows': {
                fontSize: { xs: '0.75rem', sm: '0.875rem' }
              }
            }}
          />
        </Paper>
      )}

      {/* Add/Edit Booking Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        fullScreen={isMobile}
      >
        <DialogTitle sx={{ fontSize: { xs: '1.25rem', sm: '1.5rem' } }}>
          {editingBooking ? 'Edit Booking' : 'Add New Booking'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 1 }}>
            <Grid container spacing={2}>
              {/* Hotel Selection */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal" required>
                  <InputLabel>Hotel</InputLabel>
                  <Select
                    value={formData.hotelId}
                    onChange={(e) => {
                      const hotelId = e.target.value;
                      setFormData(prev => ({
                        ...prev,
                        hotelId,
                        roomId: '' // Reset room when hotel changes
                      }));
                      fetchRoomsForHotel(hotelId);
                    }}
                    label="Hotel"
                  >
                    {hotels.map((hotel) => (
                      <MenuItem key={hotel.id} value={hotel.id}>
                        {hotel.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              {/* Room Selection */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal" required>
                  <InputLabel>Room</InputLabel>
                  <Select
                    value={formData.roomId}
                    onChange={(e) => {
                      const roomId = e.target.value;
                      const selectedRoom = rooms.find(r => r.id === roomId);
                      setFormData(prev => ({
                        ...prev,
                        roomId,
                        totalAmount: selectedRoom ? selectedRoom.price : 0
                      }));
                    }}
                    label="Room"
                    disabled={!formData.hotelId || rooms.length === 0}
                  >
                    {rooms.map((room) => (
                      <MenuItem key={room.id} value={room.id}>
                        {room.name} - ₹{room.price}/night
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              {/* Guest Information */}
              <Grid item xs={12}>
                <Typography variant="subtitle1" sx={{ mt: 2, mb: 1 }}>Guest Information</Typography>
                <Divider />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Guest Name"
                  name="guestName"
                  value={formData.guestName}
                  onChange={(e) => setFormData(prev => ({ ...prev, guestName: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Guest Email"
                  name="guestEmail"
                  type="email"
                  value={formData.guestEmail}
                  onChange={(e) => setFormData(prev => ({ ...prev, guestEmail: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  fullWidth
                  label="Guest Phone"
                  name="guestPhone"
                  value={formData.guestPhone}
                  onChange={(e) => setFormData(prev => ({ ...prev, guestPhone: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="User ID"
                  name="userId"
                  value={formData.userId}
                  onChange={(e) => setFormData(prev => ({ ...prev, userId: e.target.value }))}
                  helperText="Firebase User ID (if available)"
                />
              </Grid>
              {/* Booking Details */}
              <Grid item xs={12}>
                <Typography variant="subtitle1" sx={{ mt: 2, mb: 1 }}>Booking Details</Typography>
                <Divider />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Check-in Date"
                  name="checkInDate"
                  type="date"
                  value={formData.checkInDate instanceof Date
                    ? formData.checkInDate.toISOString().split('T')[0]
                    : typeof formData.checkInDate?.toDate === 'function'
                      ? formData.checkInDate.toDate().toISOString().split('T')[0]
                      : ''}
                  onChange={(e) => {
                    const date = new Date(e.target.value);
                    setFormData(prev => ({ ...prev, checkInDate: date }));
                  }}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Check-out Date"
                  name="checkOutDate"
                  type="date"
                  value={formData.checkOutDate instanceof Date
                    ? formData.checkOutDate.toISOString().split('T')[0]
                    : typeof formData.checkOutDate?.toDate === 'function'
                      ? formData.checkOutDate.toDate().toISOString().split('T')[0]
                      : ''}
                  onChange={(e) => {
                    const date = new Date(e.target.value);
                    setFormData(prev => ({ ...prev, checkOutDate: date }));
                  }}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Adults"
                  name="adults"
                  type="number"
                  value={formData.adults}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    adults: parseInt(e.target.value) || 1
                  }))}
                  InputProps={{ inputProps: { min: 1 } }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  margin="normal"
                  fullWidth
                  label="Children"
                  name="children"
                  type="number"
                  value={formData.children}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    children: parseInt(e.target.value) || 0
                  }))}
                  InputProps={{ inputProps: { min: 0 } }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Booking Status</InputLabel>
                  <Select
                    value={formData.bookingStatus}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      bookingStatus: e.target.value as 'confirmed' | 'pending' | 'cancelled' | 'completed'
                    }))}
                    label="Booking Status"
                  >
                    <MenuItem value="confirmed">Confirmed</MenuItem>
                    <MenuItem value="pending">Pending</MenuItem>
                    <MenuItem value="cancelled">Cancelled</MenuItem>
                    <MenuItem value="completed">Completed</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Payment Status</InputLabel>
                  <Select
                    value={formData.paymentStatus}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      paymentStatus: e.target.value as 'paid' | 'pending' | 'refunded' | 'cancelled'
                    }))}
                    label="Payment Status"
                  >
                    <MenuItem value="paid">Paid</MenuItem>
                    <MenuItem value="pending">Pending</MenuItem>
                    <MenuItem value="refunded">Refunded</MenuItem>
                    <MenuItem value="cancelled">Cancelled</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Total Amount"
                  name="totalAmount"
                  type="number"
                  value={formData.totalAmount}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    totalAmount: parseFloat(e.target.value) || 0
                  }))}
                  InputProps={{
                    startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                    inputProps: { min: 0 }
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  margin="normal"
                  fullWidth
                  label="Special Requests"
                  name="specialRequests"
                  multiline
                  rows={3}
                  value={formData.specialRequests}
                  onChange={(e) => setFormData(prev => ({ ...prev, specialRequests: e.target.value }))}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: { xs: 2, sm: 3 } }}>
          <Button
            onClick={handleCloseDialog}
            size={isMobile ? "small" : "medium"}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              // In a real implementation, this would call createBooking or updateBooking
              handleCloseDialog();
              setSnackbar({
                open: true,
                message: editingBooking
                  ? 'Booking updated successfully (demo)'
                  : 'Booking created successfully (demo)',
                severity: 'success'
              });
            }}
            variant="contained"
            size={isMobile ? "small" : "medium"}
          >
            {editingBooking ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Bookings;