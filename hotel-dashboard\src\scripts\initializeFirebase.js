// This script initializes the Firebase database with sample data
// Run this script using Node.js after setting up Firebase

const { initializeApp } = require('firebase/app');
const { 
  getFirestore, 
  collection, 
  doc, 
  setDoc, 
  serverTimestamp 
} = require('firebase/firestore');
const { 
  getAuth, 
  createUserWithEmailAndPassword, 
  updateProfile 
} = require('firebase/auth');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDQPFmFzLxm6LFk5QwNxU9qiHQn4KTFizQ",
  authDomain: "linkinblink-hotel.firebaseapp.com",
  projectId: "linkinblink-hotel",
  storageBucket: "linkinblink-hotel.appspot.com",
  messagingSenderId: "132613661307",
  appId: "1:132613661307:web:a9e5c8f4c2b8e9a9e5c8f4",
  measurementId: "G-MEASUREMENT_ID"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);

// Sample data
const users = [
  {
    email: '<EMAIL>',
    password: 'admin123',
    displayName: 'Super Admin',
    role: 'super_admin',
  },
  {
    email: '<EMAIL>',
    password: 'vendor123',
    displayName: 'Hotel Vendor',
    role: 'vendor',
  },
  {
    email: '<EMAIL>',
    password: 'staff123',
    displayName: 'Hotel Staff',
    role: 'staff',
  },
  {
    email: '<EMAIL>',
    password: 'user123',
    displayName: 'Regular User',
    role: 'user',
  },
];

const hotels = [
  {
    name: 'Grand Hotel',
    description: 'A luxurious 5-star hotel in the heart of the city.',
    address: '123 Main Street, City Center',
    city: 'New York',
    country: 'USA',
    zipCode: '10001',
    phone: '******-456-7890',
    email: '<EMAIL>',
    website: 'https://www.grandhotel.com',
    rating: 4.8,
    amenities: [
      'Free WiFi',
      'Swimming Pool',
      'Spa',
      'Fitness Center',
      'Restaurant',
      'Bar',
      'Room Service',
      'Parking',
      'Airport Shuttle',
      'Concierge',
    ],
    images: [
      'https://source.unsplash.com/random/800x600/?hotel',
      'https://source.unsplash.com/random/800x600/?hotel-room',
      'https://source.unsplash.com/random/800x600/?hotel-lobby',
    ],
    vendorId: '', // Will be set after creating the vendor user
  },
  {
    name: 'Seaside Resort',
    description: 'A beautiful beachfront resort with stunning ocean views.',
    address: '456 Beach Road',
    city: 'Miami',
    country: 'USA',
    zipCode: '33139',
    phone: '******-654-3210',
    email: '<EMAIL>',
    website: 'https://www.seasideresort.com',
    rating: 4.6,
    amenities: [
      'Free WiFi',
      'Private Beach',
      'Swimming Pool',
      'Spa',
      'Fitness Center',
      'Restaurant',
      'Bar',
      'Room Service',
      'Parking',
      'Water Sports',
    ],
    images: [
      'https://source.unsplash.com/random/800x600/?beach-resort',
      'https://source.unsplash.com/random/800x600/?resort-room',
      'https://source.unsplash.com/random/800x600/?beach',
    ],
    vendorId: '', // Will be set after creating the vendor user
  },
];

const rooms = [
  {
    hotelId: '', // Will be set after creating the hotel
    name: 'Deluxe King Room',
    description: 'Spacious room with a king-size bed and city view.',
    price: 250,
    capacity: 2,
    beds: {
      king: 1,
      queen: 0,
      single: 0,
    },
    amenities: [
      'Air Conditioning',
      'Flat-screen TV',
      'Mini Bar',
      'Free WiFi',
      'Coffee Machine',
      'Safe',
      'Hairdryer',
    ],
    images: [
      'https://source.unsplash.com/random/800x600/?hotel-room',
      'https://source.unsplash.com/random/800x600/?hotel-bathroom',
    ],
    available: true,
  },
  {
    hotelId: '', // Will be set after creating the hotel
    name: 'Executive Suite',
    description: 'Luxury suite with separate living area and panoramic views.',
    price: 450,
    capacity: 3,
    beds: {
      king: 1,
      queen: 1,
      single: 0,
    },
    amenities: [
      'Air Conditioning',
      'Flat-screen TV',
      'Mini Bar',
      'Free WiFi',
      'Coffee Machine',
      'Safe',
      'Hairdryer',
      'Bathtub',
      'Separate Living Area',
      'Work Desk',
    ],
    images: [
      'https://source.unsplash.com/random/800x600/?hotel-suite',
      'https://source.unsplash.com/random/800x600/?luxury-bathroom',
    ],
    available: true,
  },
];

// Function to create users
async function createUsers() {
  try {
    for (const user of users) {
      // Create user with email and password
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        user.email,
        user.password
      );
      
      // Update user profile
      await updateProfile(userCredential.user, {
        displayName: user.displayName,
      });
      
      // Store additional user data in Firestore
      await setDoc(doc(db, 'users', userCredential.user.uid), {
        uid: userCredential.user.uid,
        email: user.email,
        displayName: user.displayName,
        role: user.role,
        createdAt: serverTimestamp(),
      });
      
      console.log(`User created: ${user.email} (${user.role})`);
      
      // Store vendor ID for hotel creation
      if (user.role === 'vendor') {
        hotels[0].vendorId = userCredential.user.uid;
        hotels[1].vendorId = userCredential.user.uid;
      }
    }
  } catch (error) {
    console.error('Error creating users:', error);
  }
}

// Function to create hotels
async function createHotels() {
  try {
    for (const hotel of hotels) {
      const hotelRef = doc(collection(db, 'hotels'));
      const hotelId = hotelRef.id;
      
      await setDoc(hotelRef, {
        ...hotel,
        id: hotelId,
        createdAt: serverTimestamp(),
      });
      
      console.log(`Hotel created: ${hotel.name}`);
      
      // Create rooms for this hotel
      for (const room of rooms) {
        const roomRef = doc(collection(db, 'rooms'));
        const roomId = roomRef.id;
        
        await setDoc(roomRef, {
          ...room,
          id: roomId,
          hotelId: hotelId,
          createdAt: serverTimestamp(),
        });
        
        console.log(`Room created: ${room.name} for ${hotel.name}`);
      }
    }
  } catch (error) {
    console.error('Error creating hotels and rooms:', error);
  }
}

// Initialize the database
async function initializeDatabase() {
  try {
    await createUsers();
    await createHotels();
    console.log('Database initialization completed successfully!');
  } catch (error) {
    console.error('Error initializing database:', error);
  }
}

// Run the initialization
initializeDatabase();
