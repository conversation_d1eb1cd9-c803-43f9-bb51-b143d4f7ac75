import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:hotel_booking_app/core/theme/app_theme.dart';
import 'package:hotel_booking_app/data/models/notification_model.dart';
import 'package:hotel_booking_app/data/services/notification_service.dart';
import 'package:hotel_booking_app/data/services/auth_service.dart';
import 'package:hotel_booking_app/presentation/widgets/custom_app_bar.dart';
import 'package:hotel_booking_app/presentation/widgets/empty_state.dart';
import 'package:hotel_booking_app/presentation/screens/notifications/notification_detail_screen.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadNotifications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadNotifications() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    if (authService.user != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        final notificationService =
            Provider.of<NotificationService>(context, listen: false);
        await notificationService
            .initializeNotifications(authService.user!.uid);
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text('Failed to load notifications: ${e.toString()}')),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  Future<void> _markAllAsRead() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    if (authService.user != null) {
      try {
        final notificationService =
            Provider.of<NotificationService>(context, listen: false);
        await notificationService.markAllAsRead(authService.user!.uid);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('All notifications marked as read')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    'Failed to mark notifications as read: ${e.toString()}')),
          );
        }
      }
    }
  }

  Future<void> _clearAllNotifications() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    if (authService.user != null) {
      try {
        final notificationService =
            Provider.of<NotificationService>(context, listen: false);
        await notificationService.clearAllNotifications(authService.user!.uid);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('All notifications cleared')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content:
                    Text('Failed to clear notifications: ${e.toString()}')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Notifications',
        actions: [
          Consumer<NotificationService>(
            builder: (context, notificationService, child) {
              if (notificationService.notifications.isNotEmpty) {
                return PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'mark_all_read') {
                      _markAllAsRead();
                    } else if (value == 'clear_all') {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('Clear All Notifications'),
                          content: const Text(
                              'Are you sure you want to clear all notifications? This action cannot be undone.'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context),
                              child: const Text('CANCEL'),
                            ),
                            TextButton(
                              onPressed: () {
                                Navigator.pop(context);
                                _clearAllNotifications();
                              },
                              child: const Text('CLEAR ALL'),
                            ),
                          ],
                        ),
                      );
                    }
                  },
                  itemBuilder: (context) => [
                    if (notificationService.unreadCount > 0)
                      const PopupMenuItem<String>(
                        value: 'mark_all_read',
                        child: Text('Mark all as read'),
                      ),
                    const PopupMenuItem<String>(
                      value: 'clear_all',
                      child: Text('Clear all'),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Unread'),
          ],
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: Colors.grey,
          indicatorColor: AppTheme.primaryColor,
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Consumer<NotificationService>(
              builder: (context, notificationService, child) {
                if (notificationService.error != null) {
                  return Center(
                    child: Text(
                      notificationService.error!,
                      style: const TextStyle(color: Colors.red),
                    ),
                  );
                }

                return TabBarView(
                  controller: _tabController,
                  children: [
                    // All notifications tab
                    _buildNotificationsList(
                      notificationService.notifications,
                      notificationService,
                      'You have no notifications',
                    ),

                    // Unread notifications tab
                    _buildNotificationsList(
                      notificationService.notifications
                          .where((notification) => !notification.read)
                          .toList(),
                      notificationService,
                      'You have no unread notifications',
                    ),
                  ],
                );
              },
            ),
    );
  }

  Widget _buildNotificationsList(
    List<NotificationModel> notifications,
    NotificationService notificationService,
    String emptyMessage,
  ) {
    if (notifications.isEmpty) {
      return EmptyState(
        icon: Icons.notifications_off_outlined,
        title: 'No Notifications',
        message: emptyMessage,
      );
    }

    return RefreshIndicator(
      onRefresh: _loadNotifications,
      child: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: notifications.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final notification = notifications[index];
          return _buildNotificationItem(notification, notificationService);
        },
      ),
    );
  }

  Widget _buildNotificationItem(
    NotificationModel notification,
    NotificationService notificationService,
  ) {
    return Dismissible(
      key: Key(notification.id),
      background: Container(
        color: Colors.red,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        child: const Icon(Icons.delete, color: Colors.white),
      ),
      direction: DismissDirection.endToStart,
      onDismissed: (direction) {
        notificationService.deleteNotification(notification.id);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Notification deleted')),
        );
      },
      child: InkWell(
        onTap: () {
          // Mark as read when tapped
          if (!notification.read) {
            notificationService.markAsRead(notification.id);
          }

          // Navigate to notification detail
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  NotificationDetailScreen(notification: notification),
            ),
          );
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            color:
                notification.read ? null : AppTheme.primaryColor.withAlpha(13),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildNotificationIcon(notification.type),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            notification.title,
                            style: TextStyle(
                              fontWeight: notification.read
                                  ? FontWeight.normal
                                  : FontWeight.bold,
                              fontSize: 16,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          _formatDate(notification.createdAt),
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      notification.message,
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationIcon(NotificationType type) {
    IconData iconData;
    Color iconColor;

    switch (type) {
      case NotificationType.booking:
        iconData = Icons.hotel;
        iconColor = Colors.blue;
        break;
      case NotificationType.promotion:
        iconData = Icons.local_offer;
        iconColor = Colors.purple;
        break;
      case NotificationType.system:
        iconData = Icons.info;
        iconColor = Colors.teal;
        break;
      case NotificationType.loyalty:
        iconData = Icons.card_membership;
        iconColor = Colors.amber;
        break;
      case NotificationType.payment:
        iconData = Icons.payment;
        iconColor = Colors.green;
        break;
      case NotificationType.reminder:
        iconData = Icons.alarm;
        iconColor = Colors.orange;
        break;
      case NotificationType.alert:
        iconData = Icons.warning;
        iconColor = Colors.red;
        break;
      case NotificationType.serviceRequest:
        iconData = Icons.room_service;
        iconColor = Colors.deepPurple;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: iconColor.withAlpha(25),
        shape: BoxShape.circle,
      ),
      child: Icon(iconData, color: iconColor, size: 20),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return DateFormat.jm().format(date); // Today, show time
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return DateFormat.E().format(date); // Weekday name
    } else {
      return DateFormat.MMMd().format(date); // Month and day
    }
  }
}
