import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Snackbar,
  Alert,
  CircularProgress,
  Rating,
  Chip,
  Grid,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  OutlinedInput,
  Checkbox,
  ListItemText,
  SelectChangeEvent
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Image as ImageIcon
} from '@mui/icons-material';
import { getAllHotels, createHotel, updateHotel, deleteHotel, HotelData } from '../../firebase/hotelService';
import { getVendors, UserData } from '../../firebase/userService';
import { auth } from '../../firebase/config';

// Common amenities for hotels
const AMENITIES = [
  'WiFi',
  'Pool',
  'Spa',
  'Gym',
  'Restaurant',
  'Bar',
  'Room Service',
  'Parking',
  'Air Conditioning',
  'Breakfast',
  'Pet Friendly',
  'Beach Access',
  'Business Center',
  'Laundry Service',
  'Concierge'
];

const Hotels: React.FC = () => {
  const [hotels, setHotels] = useState<HotelData[]>([]);
  const [vendors, setVendors] = useState<UserData[]>([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingHotel, setEditingHotel] = useState<HotelData | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState<HotelData>({
    name: '',
    description: '',
    address: '',
    city: '',
    country: '',
    zipCode: '',
    phone: '',
    email: '',
    website: '',
    rating: 0,
    price: 0,
    amenities: [],
    images: [],
    vendorId: ''
  });

  // State for image uploads
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [previewImages, setPreviewImages] = useState<string[]>([]);
  const [deleteImageUrls, setDeleteImageUrls] = useState<string[]>([]);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  // Load hotels and vendors on component mount
  useEffect(() => {
    fetchHotels();
    fetchVendors();
  }, []);

  const fetchHotels = async () => {
    try {
      setLoading(true);
      const hotelsList = await getAllHotels();
      setHotels(hotelsList);
    } catch (error) {
      console.error('Error fetching hotels:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load hotels',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchVendors = async () => {
    try {
      const vendorsList = await getVendors();
      setVendors(vendorsList);
    } catch (error) {
      console.error('Error fetching vendors:', error);
    }
  };

  const handleOpenDialog = (hotel?: HotelData) => {
    if (hotel) {
      // Edit mode
      setEditingHotel(hotel);
      setFormData({
        name: hotel.name,
        description: hotel.description,
        address: hotel.address,
        city: hotel.city,
        country: hotel.country,
        zipCode: hotel.zipCode || '',
        phone: hotel.phone || '',
        email: hotel.email || '',
        website: hotel.website || '',
        rating: hotel.rating || 0,
        price: hotel.price || 0,
        amenities: hotel.amenities || [],
        images: hotel.images || [],
        vendorId: hotel.vendorId
      });
    } else {
      // Create mode
      setEditingHotel(null);
      setFormData({
        name: '',
        description: '',
        address: '',
        city: '',
        country: '',
        zipCode: '',
        phone: '',
        email: '',
        website: '',
        rating: 0,
        price: 0,
        amenities: [],
        images: [],
        vendorId: vendors.length > 0 ? vendors[0].id! : ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    // Clear image upload states
    setImageFiles([]);
    setPreviewImages([]);
    setDeleteImageUrls([]);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNumberInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: parseFloat(value) || 0
    }));
  };

  const handleRatingChange = (_: React.SyntheticEvent, newValue: number | null) => {
    setFormData(prev => ({
      ...prev,
      rating: newValue || 0
    }));
  };

  const handleVendorChange = (e: SelectChangeEvent) => {
    setFormData(prev => ({
      ...prev,
      vendorId: e.target.value
    }));
  };

  const handleAmenitiesChange = (event: SelectChangeEvent<string[]>) => {
    const { value } = event.target;
    setFormData(prev => ({
      ...prev,
      amenities: typeof value === 'string' ? value.split(',') : value
    }));
  };

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setImageFiles(prev => [...prev, ...files]);

      // Create preview URLs
      const newPreviews = files.map(file => URL.createObjectURL(file));
      setPreviewImages(prev => [...prev, ...newPreviews]);
    }
  };

  // Handle removing a preview image
  const handleRemovePreviewImage = (index: number) => {
    setImageFiles(prev => {
      const newImages = [...prev];
      newImages.splice(index, 1);
      return newImages;
    });

    setPreviewImages(prev => {
      const newPreviews = [...prev];
      URL.revokeObjectURL(newPreviews[index]);
      newPreviews.splice(index, 1);
      return newPreviews;
    });
  };

  // Handle removing an existing image
  const handleRemoveImage = (index: number) => {
    const imageUrl = formData.images?.[index];
    if (imageUrl) {
      // Add to delete list
      setDeleteImageUrls(prev => [...prev, imageUrl]);

      // Remove from form data
      setFormData(prev => ({
        ...prev,
        images: prev.images?.filter((_, i) => i !== index) || []
      }));
    }
  };

  const handleSubmit = async () => {
    try {
      if (!auth.currentUser) {
        throw new Error('You must be logged in to perform this action');
      }

      if (!formData.name || !formData.description || !formData.address || !formData.city || !formData.country || !formData.vendorId) {
        throw new Error('Please fill in all required fields');
      }

      if (editingHotel) {
        // Update existing hotel with images
        await updateHotel(
          editingHotel.id!,
          formData,
          imageFiles.length > 0 ? imageFiles : undefined,
          deleteImageUrls.length > 0 ? deleteImageUrls : undefined
        );

        setSnackbar({
          open: true,
          message: 'Hotel updated successfully',
          severity: 'success'
        });
      } else {
        // Create new hotel with images
        await createHotel(formData, imageFiles.length > 0 ? imageFiles : undefined);

        setSnackbar({
          open: true,
          message: 'Hotel created successfully',
          severity: 'success'
        });
      }

      handleCloseDialog();
      fetchHotels(); // Refresh the list
    } catch (error: any) {
      console.error('Error saving hotel:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to save hotel',
        severity: 'error'
      });
    }
  };

  const handleDeleteHotel = async (hotelId: string) => {
    if (window.confirm('Are you sure you want to delete this hotel?')) {
      try {
        await deleteHotel(hotelId);
        setSnackbar({
          open: true,
          message: 'Hotel deleted successfully',
          severity: 'success'
        });
        fetchHotels(); // Refresh the list
      } catch (error) {
        console.error('Error deleting hotel:', error);
        setSnackbar({
          open: true,
          message: 'Failed to delete hotel',
          severity: 'error'
        });
      }
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Filter hotels based on search term
  const filteredHotels = hotels.filter(hotel =>
    hotel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    hotel.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
    hotel.country.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Find vendor name by ID
  const getVendorName = (vendorId: string) => {
    const vendor = vendors.find(v => v.id === vendorId);
    return vendor ? vendor.displayName : 'Unknown Vendor';
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Hotels Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Add Hotel
        </Button>
      </Box>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <TextField
            placeholder="Search hotels..."
            variant="outlined"
            fullWidth
            value={searchTerm}
            onChange={handleSearch}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchHotels}
          >
            Refresh
          </Button>
        </Box>
      </Paper>

      <Paper>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Location</TableCell>
                  <TableCell>Vendor</TableCell>
                  <TableCell>Rating</TableCell>
                  <TableCell>Price</TableCell>
                  <TableCell>Amenities</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredHotels.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      No hotels found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredHotels.map((hotel) => (
                    <TableRow key={hotel.id}>
                      <TableCell>{hotel.name}</TableCell>
                      <TableCell>{`${hotel.city}, ${hotel.country}`}</TableCell>
                      <TableCell>{getVendorName(hotel.vendorId)}</TableCell>
                      <TableCell>
                        <Rating value={hotel.rating || 0} readOnly precision={0.5} />
                      </TableCell>
                      <TableCell>
                        {hotel.price ? `₹${hotel.price}/night` : 'N/A'}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {hotel.amenities?.slice(0, 3).map((amenity, index) => (
                            <Chip key={index} label={amenity} size="small" />
                          ))}
                          {hotel.amenities && hotel.amenities.length > 3 && (
                            <Chip label={`+${hotel.amenities.length - 3} more`} size="small" variant="outlined" />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <IconButton
                          color="primary"
                          onClick={() => handleOpenDialog(hotel)}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          color="error"
                          onClick={() => hotel.id && handleDeleteHotel(hotel.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* Add/Edit Hotel Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingHotel ? 'Edit Hotel' : 'Add New Hotel'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Hotel Name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Vendor</InputLabel>
                  <Select
                    value={formData.vendorId}
                    onChange={handleVendorChange}
                    label="Vendor"
                    required
                  >
                    {vendors.map((vendor) => (
                      <MenuItem key={vendor.id} value={vendor.id}>
                        {vendor.displayName}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  multiline
                  rows={3}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Address"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="City"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  label="Country"
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  margin="normal"
                  fullWidth
                  label="Zip Code"
                  name="zipCode"
                  value={formData.zipCode}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  fullWidth
                  label="Phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  fullWidth
                  label="Email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  fullWidth
                  label="Website"
                  name="website"
                  value={formData.website}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  margin="normal"
                  fullWidth
                  label="Price per Night"
                  name="price"
                  type="number"
                  value={formData.price || ''}
                  onChange={handleNumberInputChange}
                  InputProps={{
                    startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <Typography component="legend" sx={{ mt: 2 }}>Rating</Typography>
                <Rating
                  name="rating"
                  value={formData.rating || 0}
                  onChange={handleRatingChange}
                  precision={0.5}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Amenities</InputLabel>
                  <Select
                    multiple
                    value={formData.amenities || []}
                    onChange={handleAmenitiesChange}
                    input={<OutlinedInput label="Amenities" />}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} />
                        ))}
                      </Box>
                    )}
                  >
                    {AMENITIES.map((amenity) => (
                      <MenuItem key={amenity} value={amenity}>
                        <Checkbox checked={(formData.amenities || []).indexOf(amenity) > -1} />
                        <ListItemText primary={amenity} />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" sx={{ mt: 2 }}>Images</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <Button
                    variant="outlined"
                    component="label"
                    startIcon={<AddIcon />}
                  >
                    Upload Images
                    <input
                      type="file"
                      hidden
                      accept="image/*"
                      multiple
                      onChange={handleImageUpload}
                    />
                  </Button>
                </Box>

                {/* Preview of new images to be uploaded */}
                {previewImages.length > 0 && (
                  <>
                    <Typography variant="subtitle2" sx={{ mt: 1, mb: 1 }}>New Images:</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                      {previewImages.map((preview, index) => (
                        <Box
                          key={`preview-${index}`}
                          sx={{
                            position: 'relative',
                            width: 100,
                            height: 100,
                            border: '1px solid #ddd',
                            borderRadius: 1,
                            overflow: 'hidden',
                          }}
                        >
                          <img
                            src={preview}
                            alt={`Upload Preview ${index + 1}`}
                            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                          />
                          <IconButton
                            size="small"
                            sx={{
                              position: 'absolute',
                              top: 0,
                              right: 0,
                              bgcolor: 'rgba(255,255,255,0.7)',
                            }}
                            onClick={() => handleRemovePreviewImage(index)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      ))}
                    </Box>
                  </>
                )}

                {/* Existing images */}
                {formData.images && formData.images.length > 0 && (
                  <>
                    <Typography variant="subtitle2" sx={{ mt: 1, mb: 1 }}>Existing Images:</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {formData.images.map((image, index) => (
                        <Box
                          key={`existing-${index}`}
                          sx={{
                            position: 'relative',
                            width: 100,
                            height: 100,
                            border: '1px solid #ddd',
                            borderRadius: 1,
                            overflow: 'hidden',
                          }}
                        >
                          <img
                            src={image}
                            alt={`Hotel ${index + 1}`}
                            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = 'https://via.placeholder.com/100?text=Error';
                            }}
                          />
                          <IconButton
                            size="small"
                            sx={{
                              position: 'absolute',
                              top: 0,
                              right: 0,
                              bgcolor: 'rgba(255,255,255,0.7)',
                            }}
                            onClick={() => handleRemoveImage(index)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      ))}
                    </Box>
                  </>
                )}

                {/* Empty state */}
                {(!formData.images || formData.images.length === 0) && previewImages.length === 0 && (
                  <Box
                    sx={{
                      width: 100,
                      height: 100,
                      border: '1px dashed #ddd',
                      borderRadius: 1,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <ImageIcon sx={{ color: '#ddd' }} />
                  </Box>
                )}
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingHotel ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Hotels;
