import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../firebase/config';

/**
 * Generate a unique booking number
 * Format: BK-YYMMDD-XXXX where XXXX is a random number
 */
export const generateBookingNumber = async (): Promise<string> => {
  // Generate date part (YYMMDD)
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const datePart = `${year}${month}${day}`;
  
  // Generate random part (XXXX)
  const randomPart = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  
  // Combine to create booking number
  const bookingNumber = `BK-${datePart}-${randomPart}`;
  
  // Check if this booking number already exists
  const bookingsQuery = query(
    collection(db, 'bookings'),
    where('bookingNumber', '==', bookingNumber)
  );
  
  const bookingsSnapshot = await getDocs(bookingsQuery);
  
  // If booking number already exists, generate a new one recursively
  if (!bookingsSnapshot.empty) {
    return generateBookingNumber();
  }
  
  return bookingNumber;
};

/**
 * Calculate the number of nights between two dates
 */
export const calculateNights = (checkInDate: Date, checkOutDate: Date): number => {
  const oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
  const diffDays = Math.round(Math.abs((checkOutDate.getTime() - checkInDate.getTime()) / oneDay));
  return diffDays;
};

/**
 * Format a date as YYYY-MM-DD
 */
export const formatDateYYYYMMDD = (date: Date): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * Format a date as DD MMM YYYY (e.g., 01 Jan 2023)
 */
export const formatDateDDMMMYYYY = (date: Date): string => {
  const options: Intl.DateTimeFormatOptions = { 
    day: '2-digit', 
    month: 'short', 
    year: 'numeric' 
  };
  return date.toLocaleDateString('en-US', options);
};

/**
 * Format a date range as "DD MMM - DD MMM YYYY" (e.g., 01 Jan - 05 Jan 2023)
 */
export const formatDateRange = (checkInDate: Date, checkOutDate: Date): string => {
  const options: Intl.DateTimeFormatOptions = { 
    day: '2-digit', 
    month: 'short'
  };
  const yearOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric'
  };
  
  const checkInStr = checkInDate.toLocaleDateString('en-US', options);
  
  // If same year, only show year once at the end
  if (checkInDate.getFullYear() === checkOutDate.getFullYear()) {
    const checkOutStr = checkOutDate.toLocaleDateString('en-US', options);
    const year = checkOutDate.toLocaleDateString('en-US', yearOptions);
    return `${checkInStr} - ${checkOutStr} ${year}`;
  } else {
    // Different years, show both years
    const checkOutStr = checkOutDate.toLocaleDateString('en-US', { ...options, year: 'numeric' });
    return `${checkInStr} ${checkInDate.getFullYear()} - ${checkOutStr}`;
  }
};

/**
 * Format currency as $X,XXX.XX
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(amount);
};

/**
 * Get booking status display text and color
 */
export const getBookingStatusInfo = (status: string): { text: string; color: string } => {
  switch (status) {
    case 'confirmed':
      return { text: 'Confirmed', color: 'success' };
    case 'pending':
      return { text: 'Pending', color: 'warning' };
    case 'checked_in':
      return { text: 'Checked In', color: 'primary' };
    case 'checked_out':
      return { text: 'Checked Out', color: 'default' };
    case 'cancelled':
      return { text: 'Cancelled', color: 'error' };
    case 'no_show':
      return { text: 'No Show', color: 'error' };
    default:
      return { text: status, color: 'default' };
  }
};

/**
 * Get payment status display text and color
 */
export const getPaymentStatusInfo = (status: string): { text: string; color: string } => {
  switch (status) {
    case 'paid':
      return { text: 'Paid', color: 'success' };
    case 'pending':
      return { text: 'Pending', color: 'warning' };
    case 'partially_paid':
      return { text: 'Partially Paid', color: 'info' };
    case 'refunded':
      return { text: 'Refunded', color: 'error' };
    case 'cancelled':
      return { text: 'Cancelled', color: 'error' };
    default:
      return { text: status, color: 'default' };
  }
};

/**
 * Generate a confirmation email template for a booking
 */
export const generateBookingConfirmationEmail = (
  bookingNumber: string,
  hotelName: string,
  roomName: string,
  guestName: string,
  checkInDate: Date,
  checkOutDate: Date,
  totalAmount: number,
  adults: number,
  children: number
): string => {
  const nights = calculateNights(checkInDate, checkOutDate);
  
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Booking Confirmation</h2>
      <p>Dear ${guestName},</p>
      <p>Thank you for booking with us. Your reservation has been confirmed.</p>
      
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0;">Booking Details</h3>
        <p><strong>Booking Number:</strong> ${bookingNumber}</p>
        <p><strong>Hotel:</strong> ${hotelName}</p>
        <p><strong>Room:</strong> ${roomName}</p>
        <p><strong>Check-in:</strong> ${formatDateDDMMMYYYY(checkInDate)}</p>
        <p><strong>Check-out:</strong> ${formatDateDDMMMYYYY(checkOutDate)}</p>
        <p><strong>Duration:</strong> ${nights} night${nights !== 1 ? 's' : ''}</p>
        <p><strong>Guests:</strong> ${adults} adult${adults !== 1 ? 's' : ''}${children > 0 ? `, ${children} child${children !== 1 ? 'ren' : ''}` : ''}</p>
        <p><strong>Total Amount:</strong> ${formatCurrency(totalAmount)}</p>
      </div>
      
      <p>We look forward to welcoming you to ${hotelName}.</p>
      <p>If you have any questions or need to make changes to your reservation, please contact us.</p>
      
      <p>Best regards,<br>${hotelName} Team</p>
    </div>
  `;
};
