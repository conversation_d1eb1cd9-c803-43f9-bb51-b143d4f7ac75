import React, { useState, useEffect } from 'react';
import { SelectChangeEvent } from '@mui/material';
import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  Grid,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Chip,
  CircularProgress,
  Alert,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ContentCopy as CopyIcon,
  Schedule as ScheduleIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  AccessTime as TimeIcon,
  Work as WorkIcon
} from '@mui/icons-material';
import {
  ScheduleTemplate,
  TemplateShift,
  getTemplatesForHotel,
  getShiftsForTemplate,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  addShiftToTemplate,
  updateTemplateShift,
  deleteTemplateShift,
  TEMPLATE_TYPE_WEEKLY,
  TEMPLATE_TYPE_DAILY,
  TEMPLATE_TYPE_CUSTOM
} from '../../services/scheduleTemplateService';

// Day of week options
const DAYS_OF_WEEK = [
  { value: 0, label: 'Monday' },
  { value: 1, label: 'Tuesday' },
  { value: 2, label: 'Wednesday' },
  { value: 3, label: 'Thursday' },
  { value: 4, label: 'Friday' },
  { value: 5, label: 'Saturday' },
  { value: 6, label: 'Sunday' }
];

// Shift type options
const SHIFT_TYPES = [
  { value: 'morning', label: 'Morning (6:00 - 14:00)' },
  { value: 'afternoon', label: 'Afternoon (14:00 - 22:00)' },
  { value: 'night', label: 'Night (22:00 - 6:00)' },
  { value: 'custom', label: 'Custom' }
];

// Role options
const ROLE_OPTIONS = [
  { value: 'housekeeping', label: 'Housekeeping' },
  { value: 'food_service', label: 'Food Service' },
  { value: 'maintenance', label: 'Maintenance' },
  { value: 'front_desk', label: 'Front Desk' },
  { value: 'manager', label: 'Manager' },
  { value: 'other', label: 'Other' }
];

// Shift times based on shift type
const SHIFT_TIMES = {
  morning: { start: '06:00', end: '14:00' },
  afternoon: { start: '14:00', end: '22:00' },
  night: { start: '22:00', end: '06:00' },
  custom: { start: '09:00', end: '17:00' }
};

interface ScheduleTemplatesProps {
  hotelId: string;
  vendorId: string;
}

const ScheduleTemplates: React.FC<ScheduleTemplatesProps> = ({ hotelId, vendorId }) => {
  // State for templates
  const [templates, setTemplates] = useState<ScheduleTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ScheduleTemplate | null>(null);
  const [templateShifts, setTemplateShifts] = useState<TemplateShift[]>([]);

  // State for dialogs
  const [templateDialogOpen, setTemplateDialogOpen] = useState<boolean>(false);
  const [shiftDialogOpen, setShiftDialogOpen] = useState<boolean>(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);

  // State for form data
  const [templateFormData, setTemplateFormData] = useState<{
    name: string;
    description: string;
    type: string;
    isDefault: boolean;
  }>({
    name: '',
    description: '',
    type: TEMPLATE_TYPE_WEEKLY,
    isDefault: false
  });

  const [shiftFormData, setShiftFormData] = useState<{
    dayOfWeek: number;
    role: string;
    shiftType: string;
    startTime: string;
    endTime: string;
    notes: string;
  }>({
    dayOfWeek: 0,
    role: 'housekeeping',
    shiftType: 'morning',
    startTime: SHIFT_TIMES.morning.start,
    endTime: SHIFT_TIMES.morning.end,
    notes: ''
  });

  // State for loading and errors
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedShift, setSelectedShift] = useState<TemplateShift | null>(null);

  // Fetch templates on component mount
  useEffect(() => {
    fetchTemplates();
  }, [hotelId]);

  // Fetch template shifts when a template is selected
  useEffect(() => {
    if (selectedTemplate) {
      fetchTemplateShifts(selectedTemplate.id!);
    } else {
      setTemplateShifts([]);
    }
  }, [selectedTemplate]);

  // Fetch templates
  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const fetchedTemplates = await getTemplatesForHotel(hotelId);
      setTemplates(fetchedTemplates);
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching templates:', err);
      setError(err.message || 'Failed to fetch templates');
      setLoading(false);
    }
  };

  // Fetch template shifts
  const fetchTemplateShifts = async (templateId: string) => {
    try {
      setLoading(true);
      const fetchedShifts = await getShiftsForTemplate(templateId);
      setTemplateShifts(fetchedShifts);
      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching template shifts:', err);
      setError(err.message || 'Failed to fetch template shifts');
      setLoading(false);
    }
  };

  // Handle template selection
  const handleSelectTemplate = (template: ScheduleTemplate) => {
    setSelectedTemplate(template);
  };

  // Open template dialog for creating a new template
  const handleOpenNewTemplateDialog = () => {
    setTemplateFormData({
      name: '',
      description: '',
      type: TEMPLATE_TYPE_WEEKLY,
      isDefault: false
    });
    setSelectedTemplate(null);
    setTemplateDialogOpen(true);
  };

  // Open template dialog for editing an existing template
  const handleOpenEditTemplateDialog = (template: ScheduleTemplate) => {
    setTemplateFormData({
      name: template.name,
      description: template.description || '',
      type: template.type,
      isDefault: template.isDefault || false
    });
    setSelectedTemplate(template);
    setTemplateDialogOpen(true);
  };

  // Close template dialog
  const handleCloseTemplateDialog = () => {
    setTemplateDialogOpen(false);
  };

  // Handle template form change
  const handleTemplateFormChange = (event: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = event.target;

    if (name) {
      if (name === 'isDefault') {
        setTemplateFormData(prev => ({
          ...prev,
          [name]: !prev.isDefault
        }));
      } else {
        setTemplateFormData(prev => ({
          ...prev,
          [name]: value
        }));
      }
    }
  };

  // Type-safe wrapper for Select onChange
  const handleTemplateSelectChange = (event: SelectChangeEvent<string>) => {
    handleTemplateFormChange(event as unknown as React.ChangeEvent<{ name?: string; value: unknown }>);
  };

  // Submit template form
  const handleSubmitTemplate = async () => {
    try {
      if (selectedTemplate) {
        // Update existing template
        await updateTemplate(selectedTemplate.id!, {
          name: templateFormData.name,
          description: templateFormData.description,
          type: templateFormData.type,
          isDefault: templateFormData.isDefault
        });
      } else {
        // Create new template
        const newTemplate = await createTemplate({
          name: templateFormData.name,
          description: templateFormData.description,
          type: templateFormData.type,
          isDefault: templateFormData.isDefault,
          hotelId,
          vendorId
        });

        setSelectedTemplate(newTemplate);
      }

      // Refresh templates
      await fetchTemplates();
      handleCloseTemplateDialog();
    } catch (err: any) {
      console.error('Error saving template:', err);
      setError(err.message || 'Failed to save template');
    }
  };

  // Open delete dialog
  const handleOpenDeleteDialog = (template: ScheduleTemplate) => {
    setSelectedTemplate(template);
    setDeleteDialogOpen(true);
  };

  // Close delete dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  // Delete template
  const handleDeleteTemplate = async () => {
    try {
      if (selectedTemplate) {
        await deleteTemplate(selectedTemplate.id!);
        setSelectedTemplate(null);
        await fetchTemplates();
      }

      handleCloseDeleteDialog();
    } catch (err: any) {
      console.error('Error deleting template:', err);
      setError(err.message || 'Failed to delete template');
    }
  };

  // Open shift dialog for creating a new shift
  const handleOpenNewShiftDialog = () => {
    setShiftFormData({
      dayOfWeek: 0,
      role: 'housekeeping',
      shiftType: 'morning',
      startTime: SHIFT_TIMES.morning.start,
      endTime: SHIFT_TIMES.morning.end,
      notes: ''
    });
    setSelectedShift(null);
    setShiftDialogOpen(true);
  };

  // Open shift dialog for editing an existing shift
  const handleOpenEditShiftDialog = (shift: TemplateShift) => {
    setShiftFormData({
      dayOfWeek: shift.dayOfWeek || 0,
      role: shift.role,
      shiftType: shift.shiftType,
      startTime: shift.startTime,
      endTime: shift.endTime,
      notes: shift.notes || ''
    });
    setSelectedShift(shift);
    setShiftDialogOpen(true);
  };

  // Close shift dialog
  const handleCloseShiftDialog = () => {
    setShiftDialogOpen(false);
  };

  // Handle shift form change
  const handleShiftFormChange = (event: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = event.target;

    if (name) {
      setShiftFormData(prev => ({
        ...prev,
        [name]: value
      }));

      // Update shift times when shift type changes
      if (name === 'shiftType' && value !== 'custom') {
        const shiftType = value as string;
        setShiftFormData(prev => ({
          ...prev,
          startTime: SHIFT_TIMES[shiftType as keyof typeof SHIFT_TIMES].start,
          endTime: SHIFT_TIMES[shiftType as keyof typeof SHIFT_TIMES].end
        }));
      }
    }
  };

  // Type-safe wrapper for Select onChange
  const handleShiftSelectChange = (event: SelectChangeEvent<string | number>) => {
    handleShiftFormChange(event as unknown as React.ChangeEvent<{ name?: string; value: unknown }>);
  };

  // Submit shift form
  const handleSubmitShift = async () => {
    try {
      if (!selectedTemplate) return;

      if (selectedShift) {
        // Update existing shift
        await updateTemplateShift(selectedShift.id!, {
          dayOfWeek: shiftFormData.dayOfWeek,
          role: shiftFormData.role,
          shiftType: shiftFormData.shiftType,
          startTime: shiftFormData.startTime,
          endTime: shiftFormData.endTime,
          notes: shiftFormData.notes
        });
      } else {
        // Create new shift
        await addShiftToTemplate({
          templateId: selectedTemplate.id!,
          dayOfWeek: shiftFormData.dayOfWeek,
          role: shiftFormData.role,
          shiftType: shiftFormData.shiftType,
          startTime: shiftFormData.startTime,
          endTime: shiftFormData.endTime,
          notes: shiftFormData.notes
        });
      }

      // Refresh template shifts
      await fetchTemplateShifts(selectedTemplate.id!);
      handleCloseShiftDialog();
    } catch (err: any) {
      console.error('Error saving shift:', err);
      setError(err.message || 'Failed to save shift');
    }
  };

  // Delete shift
  const handleDeleteShift = async (shift: TemplateShift) => {
    try {
      await deleteTemplateShift(shift.id!);

      // Refresh template shifts
      if (selectedTemplate) {
        await fetchTemplateShifts(selectedTemplate.id!);
      }
    } catch (err: any) {
      console.error('Error deleting shift:', err);
      setError(err.message || 'Failed to delete shift');
    }
  };

  // Get day name from day of week
  const getDayName = (dayOfWeek: number): string => {
    const day = DAYS_OF_WEEK.find(d => d.value === dayOfWeek);
    return day ? day.label : 'Unknown';
  };

  // Get role name from role value
  const getRoleName = (role: string): string => {
    const roleOption = ROLE_OPTIONS.find(r => r.value === role);
    return roleOption ? roleOption.label : role;
  };

  // Get shift type name from shift type value
  const getShiftTypeName = (shiftType: string): string => {
    const shiftTypeOption = SHIFT_TYPES.find(s => s.value === shiftType);
    return shiftTypeOption ? shiftTypeOption.label.split(' ')[0] : shiftType;
  };

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5">Schedule Templates</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleOpenNewTemplateDialog}
        >
          New Template
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Templates
            </Typography>

            {loading && templates.length === 0 ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : templates.length === 0 ? (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary">
                  No templates found. Create your first template to get started.
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={handleOpenNewTemplateDialog}
                  sx={{ mt: 2 }}
                >
                  Create Template
                </Button>
              </Box>
            ) : (
              <List>
                {templates.map((template) => (
                  <ListItem
                    key={template.id}
                    button
                    selected={selectedTemplate?.id === template.id}
                    onClick={() => handleSelectTemplate(template)}
                    sx={{
                      borderRadius: 1,
                      mb: 1,
                      '&.Mui-selected': {
                        bgcolor: 'primary.light',
                      }
                    }}
                  >
                    <ListItemIcon>
                      <ScheduleIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={template.name}
                      secondary={
                        <>
                          {template.type.charAt(0).toUpperCase() + template.type.slice(1)} Template
                          {template.isDefault && ' • Default'}
                        </>
                      }
                    />
                    <ListItemSecondaryAction>
                      <Tooltip title="Edit">
                        <IconButton
                          edge="end"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenEditTemplateDialog(template);
                          }}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton
                          edge="end"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenDeleteDialog(template);
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2, height: '100%' }}>
            {selectedTemplate ? (
              <>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">
                    {selectedTemplate.name}
                    {selectedTemplate.isDefault && (
                      <Chip
                        icon={<StarIcon fontSize="small" />}
                        label="Default"
                        size="small"
                        color="primary"
                        sx={{ ml: 1 }}
                      />
                    )}
                  </Typography>
                  <Button
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={handleOpenNewShiftDialog}
                  >
                    Add Shift
                  </Button>
                </Box>

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {selectedTemplate.description || 'No description provided.'}
                </Typography>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle1" gutterBottom>
                  Shifts
                </Typography>

                {loading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                    <CircularProgress />
                  </Box>
                ) : templateShifts.length === 0 ? (
                  <Box sx={{ p: 3, textAlign: 'center' }}>
                    <Typography variant="body1" color="text.secondary">
                      No shifts found in this template. Add shifts to define the schedule pattern.
                    </Typography>
                    <Button
                      variant="outlined"
                      startIcon={<AddIcon />}
                      onClick={handleOpenNewShiftDialog}
                      sx={{ mt: 2 }}
                    >
                      Add Shift
                    </Button>
                  </Box>
                ) : (
                  <List>
                    {templateShifts.map((shift) => (
                      <ListItem
                        key={shift.id}
                        sx={{
                          borderRadius: 1,
                          mb: 1,
                          bgcolor: 'background.paper',
                          border: '1px solid',
                          borderColor: 'divider'
                        }}
                      >
                        <ListItemIcon>
                          <TimeIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <>
                              {getDayName(shift.dayOfWeek || 0)} • {getShiftTypeName(shift.shiftType)}
                            </>
                          }
                          secondary={
                            <>
                              <Box component="span" sx={{ display: 'block' }}>
                                {getRoleName(shift.role)} • {shift.startTime} - {shift.endTime}
                              </Box>
                              {shift.notes && (
                                <Box component="span" sx={{ display: 'block', mt: 0.5 }}>
                                  {shift.notes}
                                </Box>
                              )}
                            </>
                          }
                        />
                        <ListItemSecondaryAction>
                          <Tooltip title="Edit">
                            <IconButton
                              edge="end"
                              onClick={() => handleOpenEditShiftDialog(shift)}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton
                              edge="end"
                              onClick={() => handleDeleteShift(shift)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                )}
              </>
            ) : (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary">
                  Select a template from the list to view and edit its shifts.
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Template Dialog */}
      <Dialog open={templateDialogOpen} onClose={handleCloseTemplateDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedTemplate ? 'Edit Template' : 'Create Template'}
        </DialogTitle>
        <DialogContent dividers>
          <TextField
            fullWidth
            label="Template Name"
            name="name"
            value={templateFormData.name}
            onChange={handleTemplateFormChange}
            margin="normal"
            required
          />

          <TextField
            fullWidth
            label="Description"
            name="description"
            value={templateFormData.description}
            onChange={handleTemplateFormChange}
            margin="normal"
            multiline
            rows={3}
          />

          <FormControl fullWidth margin="normal">
            <InputLabel id="template-type-label">Template Type</InputLabel>
            <Select
              labelId="template-type-label"
              name="type"
              value={templateFormData.type}
              label="Template Type"
              onChange={handleTemplateSelectChange}
            >
              <MenuItem value={TEMPLATE_TYPE_WEEKLY}>Weekly</MenuItem>
              <MenuItem value={TEMPLATE_TYPE_DAILY}>Daily</MenuItem>
              <MenuItem value={TEMPLATE_TYPE_CUSTOM}>Custom</MenuItem>
            </Select>
          </FormControl>

          <Box sx={{ mt: 2 }}>
            <Button
              variant={templateFormData.isDefault ? 'contained' : 'outlined'}
              startIcon={templateFormData.isDefault ? <StarIcon /> : <StarBorderIcon />}
              onClick={() => handleTemplateFormChange({ target: { name: 'isDefault', value: !templateFormData.isDefault } } as any)}
            >
              {templateFormData.isDefault ? 'Default Template' : 'Set as Default'}
            </Button>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseTemplateDialog}>Cancel</Button>
          <Button
            onClick={handleSubmitTemplate}
            variant="contained"
            disabled={!templateFormData.name}
          >
            {selectedTemplate ? 'Save Changes' : 'Create Template'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Shift Dialog */}
      <Dialog open={shiftDialogOpen} onClose={handleCloseShiftDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedShift ? 'Edit Shift' : 'Add Shift'}
        </DialogTitle>
        <DialogContent dividers>
          <FormControl fullWidth margin="normal">
            <InputLabel id="day-of-week-label">Day of Week</InputLabel>
            <Select
              labelId="day-of-week-label"
              name="dayOfWeek"
              value={shiftFormData.dayOfWeek}
              label="Day of Week"
              onChange={handleShiftSelectChange}
            >
              {DAYS_OF_WEEK.map((day) => (
                <MenuItem key={day.value} value={day.value}>
                  {day.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth margin="normal">
            <InputLabel id="role-label">Role</InputLabel>
            <Select
              labelId="role-label"
              name="role"
              value={shiftFormData.role}
              label="Role"
              onChange={handleShiftSelectChange}
            >
              {ROLE_OPTIONS.map((role) => (
                <MenuItem key={role.value} value={role.value}>
                  {role.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth margin="normal">
            <InputLabel id="shift-type-label">Shift Type</InputLabel>
            <Select
              labelId="shift-type-label"
              name="shiftType"
              value={shiftFormData.shiftType}
              label="Shift Type"
              onChange={handleShiftSelectChange}
            >
              {SHIFT_TYPES.map((type) => (
                <MenuItem key={type.value} value={type.value}>
                  {type.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Start Time"
                name="startTime"
                type="time"
                value={shiftFormData.startTime}
                onChange={handleShiftFormChange}
                InputLabelProps={{ shrink: true }}
                inputProps={{ step: 300 }}
                disabled={shiftFormData.shiftType !== 'custom'}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="End Time"
                name="endTime"
                type="time"
                value={shiftFormData.endTime}
                onChange={handleShiftFormChange}
                InputLabelProps={{ shrink: true }}
                inputProps={{ step: 300 }}
                disabled={shiftFormData.shiftType !== 'custom'}
              />
            </Grid>
          </Grid>

          <TextField
            fullWidth
            label="Notes"
            name="notes"
            value={shiftFormData.notes}
            onChange={handleShiftFormChange}
            margin="normal"
            multiline
            rows={3}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseShiftDialog}>Cancel</Button>
          <Button
            onClick={handleSubmitShift}
            variant="contained"
          >
            {selectedShift ? 'Save Changes' : 'Add Shift'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Delete Template</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the template "{selectedTemplate?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button onClick={handleDeleteTemplate} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ScheduleTemplates;
