const admin = require('firebase-admin');
const serviceAccount = require('../serviceAccountKey.json');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

// Constants
const SERVICE_REQUESTS_COLLECTION = 'serviceRequests';
const SERVICE_TYPE_CLEANING = 'cleaning';
const SERVICE_TYPE_FOOD = 'food';
const SERVICE_TYPE_MAINTENANCE = 'maintenance';
const SERVICE_STATUS_PENDING = 'pending';
const SERVICE_STATUS_IN_PROGRESS = 'in_progress';
const SERVICE_STATUS_COMPLETED = 'completed';
const SERVICE_STATUS_CANCELLED = 'cancelled';

// Sample hotel and vendor IDs (replace with actual IDs)
const HOTEL_ID = 'hotel123';
const VENDOR_ID = 'vendor123';

// Sample guest IDs and names
const guests = [
  { id: 'guest1', name: '<PERSON>' },
  { id: 'guest2', name: '<PERSON>' },
  { id: 'guest3', name: '<PERSON>' },
  { id: 'guest4', name: 'Sarah Williams' },
  { id: 'guest5', name: 'Michael Brown' }
];

// Sample room numbers
const roomNumbers = ['101', '102', '203', '204', '305', '306', '401', '402', '501', '502'];

// Sample staff members
const staffMembers = ['Staff Member 1', 'Staff Member 2', 'Staff Member 3'];

// Sample cleaning requests
const generateCleaningRequests = () => {
  const requests = [];
  const now = admin.firestore.Timestamp.now();
  
  // Pending requests
  for (let i = 0; i < 3; i++) {
    const guest = guests[Math.floor(Math.random() * guests.length)];
    const roomNumber = roomNumbers[Math.floor(Math.random() * roomNumbers.length)];
    const priority = ['low', 'medium', 'high'][Math.floor(Math.random() * 3)];
    const cleaningType = ['regular', 'deep', 'turndown', 'special'][Math.floor(Math.random() * 4)];
    const items = [];
    
    if (Math.random() > 0.5) items.push('bed');
    if (Math.random() > 0.5) items.push('bathroom');
    if (Math.random() > 0.5) items.push('floor');
    if (Math.random() > 0.7) items.push('windows');
    
    requests.push({
      type: SERVICE_TYPE_CLEANING,
      status: SERVICE_STATUS_PENDING,
      roomNumber,
      hotelId: HOTEL_ID,
      guestId: guest.id,
      guestName: guest.name,
      requestTime: admin.firestore.Timestamp.fromDate(new Date(now.toDate().getTime() - Math.floor(Math.random() * 3600000))),
      notes: `Please clean the room ${cleaningType === 'special' ? 'with special attention to ' + items.join(', ') : ''}`,
      priority,
      cleaningType,
      items,
      createdAt: admin.firestore.Timestamp.fromDate(new Date(now.toDate().getTime() - Math.floor(Math.random() * 3600000))),
      updatedAt: admin.firestore.Timestamp.fromDate(new Date(now.toDate().getTime() - Math.floor(Math.random() * 3600000)))
    });
  }
  
  // In-progress requests
  for (let i = 0; i < 2; i++) {
    const guest = guests[Math.floor(Math.random() * guests.length)];
    const roomNumber = roomNumbers[Math.floor(Math.random() * roomNumbers.length)];
    const priority = ['low', 'medium', 'high'][Math.floor(Math.random() * 3)];
    const cleaningType = ['regular', 'deep', 'turndown', 'special'][Math.floor(Math.random() * 4)];
    const assignedTo = staffMembers[Math.floor(Math.random() * staffMembers.length)];
    const items = [];
    
    if (Math.random() > 0.5) items.push('bed');
    if (Math.random() > 0.5) items.push('bathroom');
    if (Math.random() > 0.5) items.push('floor');
    if (Math.random() > 0.7) items.push('windows');
    
    requests.push({
      type: SERVICE_TYPE_CLEANING,
      status: SERVICE_STATUS_IN_PROGRESS,
      roomNumber,
      hotelId: HOTEL_ID,
      guestId: guest.id,
      guestName: guest.name,
      requestTime: admin.firestore.Timestamp.fromDate(new Date(now.toDate().getTime() - Math.floor(Math.random() * 7200000))),
      notes: `Please clean the room ${cleaningType === 'special' ? 'with special attention to ' + items.join(', ') : ''}`,
      priority,
      cleaningType,
      items,
      assignedTo,
      createdAt: admin.firestore.Timestamp.fromDate(new Date(now.toDate().getTime() - Math.floor(Math.random() * 7200000))),
      updatedAt: admin.firestore.Timestamp.fromDate(new Date(now.toDate().getTime() - Math.floor(Math.random() * 3600000)))
    });
  }
  
  // Completed requests
  for (let i = 0; i < 4; i++) {
    const guest = guests[Math.floor(Math.random() * guests.length)];
    const roomNumber = roomNumbers[Math.floor(Math.random() * roomNumbers.length)];
    const priority = ['low', 'medium', 'high'][Math.floor(Math.random() * 3)];
    const cleaningType = ['regular', 'deep', 'turndown', 'special'][Math.floor(Math.random() * 4)];
    const assignedTo = staffMembers[Math.floor(Math.random() * staffMembers.length)];
    const requestTime = admin.firestore.Timestamp.fromDate(new Date(now.toDate().getTime() - Math.floor(Math.random() * 86400000)));
    const completedTime = admin.firestore.Timestamp.fromDate(new Date(requestTime.toDate().getTime() + Math.floor(Math.random() * 7200000)));
    const items = [];
    
    if (Math.random() > 0.5) items.push('bed');
    if (Math.random() > 0.5) items.push('bathroom');
    if (Math.random() > 0.5) items.push('floor');
    if (Math.random() > 0.7) items.push('windows');
    
    requests.push({
      type: SERVICE_TYPE_CLEANING,
      status: SERVICE_STATUS_COMPLETED,
      roomNumber,
      hotelId: HOTEL_ID,
      guestId: guest.id,
      guestName: guest.name,
      requestTime,
      notes: `Please clean the room ${cleaningType === 'special' ? 'with special attention to ' + items.join(', ') : ''}`,
      priority,
      cleaningType,
      items,
      assignedTo,
      completedTime,
      createdAt: requestTime,
      updatedAt: completedTime
    });
  }
  
  // Cancelled request
  const guest = guests[Math.floor(Math.random() * guests.length)];
  const roomNumber = roomNumbers[Math.floor(Math.random() * roomNumbers.length)];
  const priority = ['low', 'medium', 'high'][Math.floor(Math.random() * 3)];
  const cleaningType = ['regular', 'deep', 'turndown', 'special'][Math.floor(Math.random() * 4)];
  const requestTime = admin.firestore.Timestamp.fromDate(new Date(now.toDate().getTime() - Math.floor(Math.random() * 86400000)));
  const cancelledTime = admin.firestore.Timestamp.fromDate(new Date(requestTime.toDate().getTime() + Math.floor(Math.random() * 3600000)));
  
  requests.push({
    type: SERVICE_TYPE_CLEANING,
    status: SERVICE_STATUS_CANCELLED,
    roomNumber,
    hotelId: HOTEL_ID,
    guestId: guest.id,
    guestName: guest.name,
    requestTime,
    notes: 'Please clean the room. Guest cancelled the request.',
    priority,
    cleaningType,
    createdAt: requestTime,
    updatedAt: cancelledTime
  });
  
  return requests;
};

// Sample food orders
const generateFoodOrders = () => {
  const requests = [];
  const now = admin.firestore.Timestamp.now();
  
  // Menu items
  const menuItems = [
    { name: 'Burger', price: 12.99 },
    { name: 'Pizza', price: 14.99 },
    { name: 'Pasta', price: 13.99 },
    { name: 'Salad', price: 8.99 },
    { name: 'Steak', price: 24.99 },
    { name: 'Chicken Curry', price: 16.99 },
    { name: 'Fish and Chips', price: 15.99 },
    { name: 'Sandwich', price: 9.99 },
    { name: 'Fries', price: 4.99 },
    { name: 'Soda', price: 2.99 },
    { name: 'Water', price: 1.99 },
    { name: 'Wine', price: 12.99 },
    { name: 'Beer', price: 6.99 },
    { name: 'Dessert', price: 7.99 }
  ];
  
  // Generate random food orders
  for (let i = 0; i < 10; i++) {
    const guest = guests[Math.floor(Math.random() * guests.length)];
    const roomNumber = roomNumbers[Math.floor(Math.random() * roomNumbers.length)];
    const priority = ['low', 'medium', 'high'][Math.floor(Math.random() * 3)];
    const status = [SERVICE_STATUS_PENDING, SERVICE_STATUS_IN_PROGRESS, SERVICE_STATUS_COMPLETED, SERVICE_STATUS_CANCELLED][Math.floor(Math.random() * 4)];
    const requestTime = admin.firestore.Timestamp.fromDate(new Date(now.toDate().getTime() - Math.floor(Math.random() * 86400000)));
    
    // Generate random items
    const items = [];
    const numItems = Math.floor(Math.random() * 4) + 1; // 1-4 items
    let totalAmount = 0;
    
    for (let j = 0; j < numItems; j++) {
      const menuItem = menuItems[Math.floor(Math.random() * menuItems.length)];
      const quantity = Math.floor(Math.random() * 3) + 1; // 1-3 quantity
      const price = menuItem.price;
      const itemTotal = price * quantity;
      totalAmount += itemTotal;
      
      items.push({
        name: menuItem.name,
        quantity,
        price,
        specialInstructions: Math.random() > 0.7 ? 'Special instructions for this item' : undefined
      });
    }
    
    // Round to 2 decimal places
    totalAmount = Math.round(totalAmount * 100) / 100;
    
    const order = {
      type: SERVICE_TYPE_FOOD,
      status,
      roomNumber,
      hotelId: HOTEL_ID,
      guestId: guest.id,
      guestName: guest.name,
      requestTime,
      notes: Math.random() > 0.5 ? 'Please deliver to room' : '',
      priority,
      items,
      totalAmount,
      createdAt: requestTime,
      updatedAt: admin.firestore.Timestamp.fromDate(new Date(requestTime.toDate().getTime() + Math.floor(Math.random() * 3600000)))
    };
    
    // Add additional fields based on status
    if (status === SERVICE_STATUS_IN_PROGRESS || status === SERVICE_STATUS_COMPLETED) {
      order.assignedTo = staffMembers[Math.floor(Math.random() * staffMembers.length)];
    }
    
    if (status === SERVICE_STATUS_COMPLETED) {
      order.completedTime = admin.firestore.Timestamp.fromDate(new Date(requestTime.toDate().getTime() + Math.floor(Math.random() * 7200000)));
      order.updatedAt = order.completedTime;
    }
    
    requests.push(order);
  }
  
  return requests;
};

// Sample maintenance requests
const generateMaintenanceRequests = () => {
  const requests = [];
  const now = admin.firestore.Timestamp.now();
  
  // Issue types
  const issueTypes = ['plumbing', 'electrical', 'hvac', 'furniture', 'appliance', 'structural', 'other'];
  
  // Issue descriptions by type
  const issueDescriptions = {
    plumbing: ['Leaking faucet', 'Clogged drain', 'Toilet not flushing', 'Low water pressure', 'Water temperature issues'],
    electrical: ['Light bulb replacement', 'Outlet not working', 'TV not functioning', 'Wi-Fi issues', 'Power outage'],
    hvac: ['AC not cooling', 'Heater not working', 'Thermostat issues', 'Strange noise from AC', 'Poor air circulation'],
    furniture: ['Broken chair', 'Bed frame issue', 'Drawer stuck', 'Sofa damage', 'Table wobbling'],
    appliance: ['Refrigerator not cooling', 'Microwave not working', 'Coffee maker broken', 'Iron not heating', 'Hair dryer issue'],
    structural: ['Ceiling leak', 'Wall damage', 'Floor damage', 'Window won\'t close', 'Door not locking properly'],
    other: ['Pest sighting', 'Odor in room', 'Noise complaint', 'Safety concern', 'General maintenance']
  };
  
  // Parts required by issue type
  const partsRequired = {
    plumbing: ['Faucet washer', 'Drain snake', 'Toilet flapper', 'Pipe fitting', 'Shower head'],
    electrical: ['Light bulb', 'Outlet', 'Power cord', 'Circuit breaker', 'Switch'],
    hvac: ['AC filter', 'Refrigerant', 'Thermostat', 'Fan belt', 'Heating element'],
    furniture: ['Chair leg', 'Bed slat', 'Drawer slide', 'Sofa cushion', 'Table leg'],
    appliance: ['Refrigerator part', 'Microwave fuse', 'Coffee maker filter', 'Iron element', 'Hair dryer switch'],
    structural: ['Ceiling tile', 'Wall patch', 'Floor tile', 'Window latch', 'Door hinge'],
    other: ['Pest trap', 'Air freshener', 'Noise dampening material', 'Safety equipment', 'General supplies']
  };
  
  // Generate random maintenance requests
  for (let i = 0; i < 10; i++) {
    const guest = guests[Math.floor(Math.random() * guests.length)];
    const roomNumber = roomNumbers[Math.floor(Math.random() * roomNumbers.length)];
    const priority = ['low', 'medium', 'high'][Math.floor(Math.random() * 3)];
    const status = [SERVICE_STATUS_PENDING, SERVICE_STATUS_IN_PROGRESS, SERVICE_STATUS_COMPLETED, SERVICE_STATUS_CANCELLED][Math.floor(Math.random() * 4)];
    const requestTime = admin.firestore.Timestamp.fromDate(new Date(now.toDate().getTime() - Math.floor(Math.random() * 86400000)));
    const issueType = issueTypes[Math.floor(Math.random() * issueTypes.length)];
    const description = issueDescriptions[issueType][Math.floor(Math.random() * issueDescriptions[issueType].length)];
    
    const request = {
      type: SERVICE_TYPE_MAINTENANCE,
      status,
      roomNumber,
      hotelId: HOTEL_ID,
      guestId: guest.id,
      guestName: guest.name,
      requestTime,
      notes: `Guest reported: ${description}`,
      priority,
      issueType,
      description,
      createdAt: requestTime,
      updatedAt: admin.firestore.Timestamp.fromDate(new Date(requestTime.toDate().getTime() + Math.floor(Math.random() * 3600000)))
    };
    
    // Add additional fields based on status
    if (status === SERVICE_STATUS_IN_PROGRESS || status === SERVICE_STATUS_COMPLETED) {
      request.assignedTo = staffMembers[Math.floor(Math.random() * staffMembers.length)];
      
      // Add parts required for some requests
      if (Math.random() > 0.5) {
        const numParts = Math.floor(Math.random() * 3) + 1; // 1-3 parts
        request.partsRequired = [];
        
        for (let j = 0; j < numParts; j++) {
          if (partsRequired[issueType] && partsRequired[issueType].length > 0) {
            const partIndex = Math.floor(Math.random() * partsRequired[issueType].length);
            request.partsRequired.push(partsRequired[issueType][partIndex]);
          }
        }
      }
      
      // Add estimated completion time for in-progress
      if (status === SERVICE_STATUS_IN_PROGRESS) {
        request.estimatedCompletionTime = admin.firestore.Timestamp.fromDate(new Date(now.toDate().getTime() + Math.floor(Math.random() * 86400000)));
      }
    }
    
    if (status === SERVICE_STATUS_COMPLETED) {
      request.completedTime = admin.firestore.Timestamp.fromDate(new Date(requestTime.toDate().getTime() + Math.floor(Math.random() * 7200000)));
      request.updatedAt = request.completedTime;
    }
    
    requests.push(request);
  }
  
  return requests;
};

// Seed service requests
async function seedServiceRequests() {
  try {
    console.log('Seeding service requests...');
    
    // Clear existing service requests
    const existingRequests = await db.collection(SERVICE_REQUESTS_COLLECTION)
      .where('hotelId', '==', HOTEL_ID)
      .get();
    
    const batch = db.batch();
    
    existingRequests.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
    console.log(`Deleted ${existingRequests.size} existing service requests.`);
    
    // Generate new service requests
    const cleaningRequests = generateCleaningRequests();
    const foodOrders = generateFoodOrders();
    const maintenanceRequests = generateMaintenanceRequests();
    
    const allRequests = [...cleaningRequests, ...foodOrders, ...maintenanceRequests];
    
    // Add new service requests
    const requestPromises = allRequests.map(request => 
      db.collection(SERVICE_REQUESTS_COLLECTION).add(request)
    );
    
    await Promise.all(requestPromises);
    
    console.log(`Added ${allRequests.length} new service requests:`);
    console.log(`- ${cleaningRequests.length} cleaning requests`);
    console.log(`- ${foodOrders.length} food orders`);
    console.log(`- ${maintenanceRequests.length} maintenance requests`);
    console.log('Service requests seeded successfully!');
  } catch (error) {
    console.error('Error seeding service requests:', error);
  }
}

// Run the seed function
seedServiceRequests()
  .then(() => {
    console.log('Script completed successfully!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
