import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db, auth } from './config';

// Define the verification data structure
export interface VerificationData {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  aadhaarNumber: string;
  aadhaarImage: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: any;
  reviewedAt?: any;
  reviewedBy?: string;
  notes?: string;
}

/**
 * Get all verifications (for admin)
 */
export const getAllVerifications = async () => {
  try {
    // First try to get real verifications from Firestore
    try {
      const verificationsQuery = query(
        collection(db, 'verifications'),
        orderBy('submittedAt', 'desc')
      );

      const verificationsSnapshot = await getDocs(verificationsQuery);
      const verifications: VerificationData[] = [];

      verificationsSnapshot.forEach((doc) => {
        try {
          const data = doc.data();
          verifications.push({
            id: doc.id,
            ...data,
            // Ensure these fields have default values if missing
            status: data.status || 'pending',
          } as VerificationData);
        } catch (err) {
          console.error('Error processing verification document:', err);
        }
      });

      // If we have real verifications, return them
      if (verifications.length > 0) {
        return verifications;
      }
    } catch (err) {
      console.error('Error fetching real verifications:', err);
    }

    // Fetch real verification data from Firebase
    const verificationsRef = collection(db, 'verifications');
    const q = query(verificationsRef, orderBy('submittedAt', 'desc'));
    const querySnapshot = await getDocs(q);

    const verifications: VerificationData[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      verifications.push({
        id: doc.id,
        userId: data.userId,
        userName: data.userName,
        userEmail: data.userEmail,
        aadhaarNumber: data.aadhaarNumber,
        aadhaarImage: data.aadhaarImage,
        status: data.status,
        submittedAt: data.submittedAt,
        reviewedAt: data.reviewedAt,
        reviewedBy: data.reviewedBy,
        notes: data.notes
      });
    });

    return verifications;
  } catch (error) {
    console.error('Error getting all verifications:', error);
    // Return empty array instead of throwing
    return [];
  }
};

/**
 * Get verification by ID
 */
export const getVerificationById = async (verificationId: string) => {
  try {
    const verificationDoc = await getDoc(doc(db, 'verifications', verificationId));

    if (verificationDoc.exists()) {
      return { id: verificationDoc.id, ...verificationDoc.data() } as VerificationData;
    }

    return null;
  } catch (error) {
    console.error('Error getting verification by ID:', error);
    throw error;
  }
};

/**
 * Get verifications for a specific user
 */
export const getVerificationsForUser = async (userId: string) => {
  try {
    const verificationsQuery = query(
      collection(db, 'verifications'),
      where('userId', '==', userId),
      orderBy('submittedAt', 'desc')
    );

    const verificationsSnapshot = await getDocs(verificationsQuery);
    const verifications: VerificationData[] = [];

    verificationsSnapshot.forEach((doc) => {
      verifications.push({ id: doc.id, ...doc.data() } as VerificationData);
    });

    return verifications;
  } catch (error) {
    console.error('Error getting verifications for user:', error);
    throw error;
  }
};

/**
 * Submit a new verification request
 */
export const submitVerification = async (verificationData: Omit<VerificationData, 'id' | 'status' | 'submittedAt'>) => {
  try {
    const newVerification = {
      ...verificationData,
      status: 'pending',
      submittedAt: serverTimestamp()
    };

    const docRef = await addDoc(collection(db, 'verifications'), newVerification);
    return docRef.id;
  } catch (error) {
    console.error('Error submitting verification:', error);
    throw error;
  }
};

/**
 * Approve a verification request
 */
export const approveVerification = async (verificationId: string) => {
  try {
    const currentUser = auth.currentUser;

    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    await updateDoc(doc(db, 'verifications', verificationId), {
      status: 'approved',
      reviewedAt: serverTimestamp(),
      reviewedBy: currentUser.displayName || currentUser.email
    });

    return true;
  } catch (error) {
    console.error('Error approving verification:', error);
    throw error;
  }
};

/**
 * Reject a verification request
 */
export const rejectVerification = async (verificationId: string, notes: string) => {
  try {
    const currentUser = auth.currentUser;

    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    await updateDoc(doc(db, 'verifications', verificationId), {
      status: 'rejected',
      reviewedAt: serverTimestamp(),
      reviewedBy: currentUser.displayName || currentUser.email,
      notes
    });

    return true;
  } catch (error) {
    console.error('Error rejecting verification:', error);
    throw error;
  }
};
