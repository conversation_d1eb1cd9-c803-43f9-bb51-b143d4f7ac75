import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/data/models/service_request_model.dart';
import 'package:hotel_booking_app/data/services/service_request_notification_service.dart';
import 'package:hotel_booking_app/data/repositories/hotel_repository.dart';

/// Service for handling service requests and notifications
class ServiceRequestHandler {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final ServiceRequestNotificationService _notificationService = ServiceRequestNotificationService();
  final HotelRepository _hotelRepository = HotelRepository();

  /// Create a service request and send notification to vendor
  Future<String> createServiceRequestWithNotification(BaseServiceRequest request) async {
    try {
      // Create the service request
      final docRef = await _firestore
          .collection(AppConstants.serviceRequestsCollection)
          .add(request.toMap());

      // Get the hotel to find the vendor ID
      final hotel = await _hotelRepository.fetchHotelById(request.hotelId);
      
      // If hotel has a vendor ID, send notification
      if (hotel != null && hotel.vendorId != null) {
        await _notificationService.sendServiceRequestNotificationToVendor(
          request: request,
          vendorId: hotel.vendorId!,
        );
      }

      return docRef.id;
    } catch (e) {
      debugPrint('Error creating service request with notification: $e');
      rethrow;
    }
  }

  /// Update a service request status and send notification to user
  Future<void> updateServiceRequestStatusWithNotification(
    String id, 
    String newStatus,
  ) async {
    try {
      // Get the current service request to check for status changes
      final docSnapshot = await _firestore
          .collection(AppConstants.serviceRequestsCollection)
          .doc(id)
          .get();

      if (!docSnapshot.exists) {
        throw Exception('Service request not found');
      }

      // Parse the service request
      final currentRequest = _parseServiceRequest(docSnapshot);
      final String previousStatus = currentRequest?.status ?? '';
      
      // If status is the same, no need to update
      if (previousStatus == newStatus) {
        return;
      }

      // Update the service request
      await _firestore
          .collection(AppConstants.serviceRequestsCollection)
          .doc(id)
          .update({
        'status': newStatus,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      // Get the updated service request
      final updatedDocSnapshot = await _firestore
          .collection(AppConstants.serviceRequestsCollection)
          .doc(id)
          .get();
      
      final updatedRequest = _parseServiceRequest(updatedDocSnapshot);
      
      if (updatedRequest != null) {
        // Send notification about status change
        await _notificationService.sendServiceRequestStatusUpdateToUser(
          request: updatedRequest,
          previousStatus: previousStatus,
        );
      }
    } catch (e) {
      debugPrint('Error updating service request status with notification: $e');
      rethrow;
    }
  }

  /// Parse a service request from a document snapshot
  BaseServiceRequest? _parseServiceRequest(DocumentSnapshot doc) {
    if (!doc.exists) {
      return null;
    }

    final data = doc.data() as Map<String, dynamic>;
    final type = data['type'] as String?;

    if (type == null) {
      return null;
    }

    switch (type) {
      case 'cleaning':
        return CleaningRequest.fromDocument(doc);
      case 'food':
        return FoodOrder.fromDocument(doc);
      case 'maintenance':
        return MaintenanceRequest.fromDocument(doc);
      default:
        debugPrint('Unknown service request type: $type');
        return null;
    }
  }
}
