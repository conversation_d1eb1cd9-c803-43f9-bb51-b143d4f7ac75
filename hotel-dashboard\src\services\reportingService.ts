import {
  collection,
  query,
  where,
  getDocs,
  orderBy,
  Timestamp,
  startAt,
  endAt,
  limit,
  getDoc,
  doc
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { BookingData } from './bookingService';
import { RoomData } from './roomService';

/**
 * Interface for date range
 */
export interface DateRange {
  startDate: Date;
  endDate: Date;
}

/**
 * Interface for revenue data
 */
export interface RevenueData {
  totalRevenue: number;
  roomRevenue: number;
  addonRevenue: number;
  averageDailyRate: number;
  revenuePerAvailableRoom: number;
  dailyRevenue: { date: string; revenue: number }[];
  monthlyRevenue: { month: string; revenue: number }[];
  revenueByRoomType: { [roomType: string]: number };
  revenueBySource: { [source: string]: number };
}

/**
 * Interface for occupancy data
 */
export interface OccupancyData {
  averageOccupancy: number;
  peakOccupancy: number;
  lowestOccupancy: number;
  dailyOccupancy: { date: string; occupancy: number }[];
  occupancyByRoomType: { [roomType: string]: number };
  occupancyByDayOfWeek: { [day: string]: number };
}

/**
 * Interface for booking data
 */
export interface BookingAnalyticsData {
  totalBookings: number;
  averageStayLength: number;
  bookingsBySource: { [source: string]: number };
  bookingsByStatus: { [status: string]: number };
  bookingsByMonth: { month: string; bookings: number }[];
  cancellationRate: number;
  leadTime: number; // Average days between booking and check-in
}

/**
 * Interface for guest data
 */
export interface GuestAnalyticsData {
  totalGuests: number;
  newGuests: number;
  returningGuests: number;
  averageGuestsPerBooking: number;
  topCountries: { country: string; count: number }[];
  guestDemographics: {
    families: number;
    couples: number;
    soloTravelers: number;
    businessTravelers: number;
  };
}

/**
 * Interface for combined report data
 */
export interface ReportData {
  revenue: RevenueData;
  occupancy: OccupancyData;
  bookings: BookingAnalyticsData;
  guests: GuestAnalyticsData;
  dateRange: DateRange;
}

/**
 * Get revenue report for a hotel
 */
export const getRevenueReport = async (hotelId: string, dateRange: DateRange): Promise<RevenueData> => {
  try {
    // Get bookings for the date range
    const bookings = await getBookingsInDateRange(hotelId, dateRange);

    // Calculate total revenue
    const totalRevenue = bookings.reduce((sum, booking) => sum + booking.totalAmount, 0);

    // Calculate room revenue (excluding addons)
    const roomRevenue = bookings.reduce((sum, booking) => {
      const addonRevenue = booking.addons?.reduce((addonSum, addon) =>
        addonSum + (addon.price * addon.quantity), 0) || 0;
      return sum + (booking.totalAmount - addonRevenue);
    }, 0);

    // Calculate addon revenue
    const addonRevenue = totalRevenue - roomRevenue;

    // Calculate average daily rate
    const totalRoomNights = bookings.reduce((sum, booking) => {
      const checkInDate = booking.checkInDate.toDate();
      const checkOutDate = booking.checkOutDate.toDate();
      const nights = Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24));
      return sum + nights;
    }, 0);

    const averageDailyRate = totalRoomNights > 0 ? roomRevenue / totalRoomNights : 0;

    // Get total available rooms
    const rooms = await getRoomsForHotel(hotelId);
    const totalRooms = rooms.length;

    // Calculate revenue per available room (RevPAR)
    const daysDiff = Math.ceil((dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24));
    const totalRoomDays = totalRooms * daysDiff;
    const revenuePerAvailableRoom = totalRoomDays > 0 ? roomRevenue / totalRoomDays : 0;

    // Calculate daily revenue
    const dailyRevenue = calculateDailyRevenue(bookings, dateRange);

    // Calculate monthly revenue
    const monthlyRevenue = calculateMonthlyRevenue(bookings, dateRange);

    // Calculate revenue by room type
    const revenueByRoomType = await calculateRevenueByRoomType(bookings);

    // Calculate revenue by source
    const revenueBySource = calculateRevenueBySource(bookings);

    return {
      totalRevenue,
      roomRevenue,
      addonRevenue,
      averageDailyRate,
      revenuePerAvailableRoom,
      dailyRevenue,
      monthlyRevenue,
      revenueByRoomType,
      revenueBySource
    };
  } catch (error) {
    console.error('Error getting revenue report:', error);
    throw error;
  }
};

/**
 * Get occupancy report for a hotel
 */
export const getOccupancyReport = async (hotelId: string, dateRange: DateRange): Promise<OccupancyData> => {
  try {
    // Get bookings for the date range
    const bookings = await getBookingsInDateRange(hotelId, dateRange);

    // Get rooms for the hotel
    const rooms = await getRoomsForHotel(hotelId);
    const totalRooms = rooms.length;

    if (totalRooms === 0) {
      throw new Error('No rooms found for this hotel');
    }

    // Calculate daily occupancy
    const dailyOccupancy = calculateDailyOccupancy(bookings, dateRange, totalRooms);

    // Calculate average occupancy
    const totalOccupancy = dailyOccupancy.reduce((sum, day) => sum + day.occupancy, 0);
    const averageOccupancy = dailyOccupancy.length > 0 ? totalOccupancy / dailyOccupancy.length : 0;

    // Find peak and lowest occupancy
    const peakOccupancy = Math.max(...dailyOccupancy.map(day => day.occupancy));
    const lowestOccupancy = Math.min(...dailyOccupancy.map(day => day.occupancy));

    // Calculate occupancy by room type
    const occupancyByRoomType = await calculateOccupancyByRoomType(bookings, rooms);

    // Calculate occupancy by day of week
    const occupancyByDayOfWeek = calculateOccupancyByDayOfWeek(dailyOccupancy);

    return {
      averageOccupancy,
      peakOccupancy,
      lowestOccupancy,
      dailyOccupancy,
      occupancyByRoomType,
      occupancyByDayOfWeek
    };
  } catch (error) {
    console.error('Error getting occupancy report:', error);
    throw error;
  }
};

/**
 * Get booking analytics for a hotel
 */
export const getBookingAnalytics = async (hotelId: string, dateRange: DateRange): Promise<BookingAnalyticsData> => {
  try {
    // Get bookings for the date range
    const bookings = await getBookingsInDateRange(hotelId, dateRange);

    // Calculate total bookings
    const totalBookings = bookings.length;

    // Calculate average stay length
    let totalNights = 0;
    bookings.forEach(booking => {
      const checkInDate = booking.checkInDate.toDate();
      const checkOutDate = booking.checkOutDate.toDate();
      const nights = Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24));
      totalNights += nights;
    });
    const averageStayLength = totalBookings > 0 ? totalNights / totalBookings : 0;

    // Calculate bookings by source
    const bookingsBySource = bookings.reduce((result, booking) => {
      const source = booking.source;
      result[source] = (result[source] || 0) + 1;
      return result;
    }, {} as { [source: string]: number });

    // Calculate bookings by status
    const bookingsByStatus = bookings.reduce((result, booking) => {
      const status = booking.bookingStatus;
      result[status] = (result[status] || 0) + 1;
      return result;
    }, {} as { [status: string]: number });

    // Calculate bookings by month
    const bookingsByMonth = calculateBookingsByMonth(bookings, dateRange);

    // Calculate cancellation rate
    const cancelledBookings = bookings.filter(booking =>
      booking.bookingStatus === 'cancelled' || booking.bookingStatus === 'no_show'
    ).length;
    const cancellationRate = totalBookings > 0 ? cancelledBookings / totalBookings : 0;

    // Calculate lead time (days between booking and check-in)
    let totalLeadTime = 0;
    let bookingsWithLeadTime = 0;

    bookings.forEach(booking => {
      if (booking.createdAt) {
        const bookingDate = booking.createdAt.toDate();
        const checkInDate = booking.checkInDate.toDate();
        const leadTimeDays = Math.ceil((checkInDate.getTime() - bookingDate.getTime()) / (1000 * 60 * 60 * 24));

        if (leadTimeDays >= 0) {
          totalLeadTime += leadTimeDays;
          bookingsWithLeadTime++;
        }
      }
    });

    const leadTime = bookingsWithLeadTime > 0 ? totalLeadTime / bookingsWithLeadTime : 0;

    return {
      totalBookings,
      averageStayLength,
      bookingsBySource,
      bookingsByStatus,
      bookingsByMonth,
      cancellationRate,
      leadTime
    };
  } catch (error) {
    console.error('Error getting booking analytics:', error);
    throw error;
  }
};

/**
 * Get guest analytics for a hotel
 */
export const getGuestAnalytics = async (hotelId: string, dateRange: DateRange): Promise<GuestAnalyticsData> => {
  try {
    // Get bookings for the date range
    const bookings = await getBookingsInDateRange(hotelId, dateRange);

    // Calculate total guests
    const totalGuests = bookings.reduce((sum, booking) => sum + booking.adults + (booking.children || 0), 0);

    // Get unique guest emails
    const guestEmails = new Set<string>();
    bookings.forEach(booking => {
      if (booking.guestInfo.email) {
        guestEmails.add(booking.guestInfo.email);
      }
    });

    // Get all previous bookings to identify returning guests
    const allBookings = await getAllBookingsForHotel(hotelId);

    // Identify new vs returning guests
    const returningGuestEmails = new Set<string>();
    const newGuestEmails = new Set<string>();

    // First, collect all emails from bookings before the date range
    const previousGuestEmails = new Set<string>();
    allBookings.forEach(booking => {
      if (booking.checkInDate.toDate() < dateRange.startDate && booking.guestInfo.email) {
        previousGuestEmails.add(booking.guestInfo.email);
      }
    });

    // Then categorize guests in the current date range
    bookings.forEach(booking => {
      if (booking.guestInfo.email) {
        if (previousGuestEmails.has(booking.guestInfo.email)) {
          returningGuestEmails.add(booking.guestInfo.email);
        } else {
          newGuestEmails.add(booking.guestInfo.email);
        }
      }
    });

    const newGuests = newGuestEmails.size;
    const returningGuests = returningGuestEmails.size;

    // Calculate average guests per booking
    const averageGuestsPerBooking = bookings.length > 0 ? totalGuests / bookings.length : 0;

    // Calculate top countries
    const countryCounts: { [country: string]: number } = {};
    bookings.forEach(booking => {
      if (booking.guestInfo.country) {
        countryCounts[booking.guestInfo.country] = (countryCounts[booking.guestInfo.country] || 0) + 1;
      }
    });

    const topCountries = Object.entries(countryCounts)
      .map(([country, count]) => ({ country, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Calculate guest demographics (simplified)
    const guestDemographics = {
      families: bookings.filter(booking => booking.children && booking.children > 0).length,
      couples: bookings.filter(booking => booking.adults === 2 && (!booking.children || booking.children === 0)).length,
      soloTravelers: bookings.filter(booking => booking.adults === 1 && (!booking.children || booking.children === 0)).length,
      businessTravelers: 0 // Would need additional data to determine this
    };

    return {
      totalGuests,
      newGuests,
      returningGuests,
      averageGuestsPerBooking,
      topCountries,
      guestDemographics
    };
  } catch (error) {
    console.error('Error getting guest analytics:', error);
    throw error;
  }
};

/**
 * Get comprehensive report for a hotel
 */
export const getComprehensiveReport = async (hotelId: string, dateRange: DateRange): Promise<ReportData> => {
  try {
    const [revenue, occupancy, bookings, guests] = await Promise.all([
      getRevenueReport(hotelId, dateRange),
      getOccupancyReport(hotelId, dateRange),
      getBookingAnalytics(hotelId, dateRange),
      getGuestAnalytics(hotelId, dateRange)
    ]);

    return {
      revenue,
      occupancy,
      bookings,
      guests,
      dateRange
    };
  } catch (error) {
    console.error('Error getting comprehensive report:', error);
    throw error;
  }
};

// Helper functions

/**
 * Get bookings for a hotel in a date range
 */
const getBookingsInDateRange = async (hotelId: string, dateRange: DateRange): Promise<BookingData[]> => {
  try {
    const startTimestamp = Timestamp.fromDate(dateRange.startDate);
    const endTimestamp = Timestamp.fromDate(dateRange.endDate);

    const q = query(
      collection(db, 'bookings'),
      where('hotelId', '==', hotelId),
      where('checkInDate', '<=', endTimestamp),
      where('checkOutDate', '>=', startTimestamp)
    );

    const querySnapshot = await getDocs(q);
    const bookings: BookingData[] = [];

    querySnapshot.forEach((doc) => {
      bookings.push({ id: doc.id, ...doc.data() } as BookingData);
    });

    return bookings;
  } catch (error) {
    console.error('Error getting bookings in date range:', error);
    throw error;
  }
};

/**
 * Get all bookings for a hotel (for historical analysis)
 */
const getAllBookingsForHotel = async (hotelId: string): Promise<BookingData[]> => {
  try {
    const q = query(
      collection(db, 'bookings'),
      where('hotelId', '==', hotelId)
    );

    const querySnapshot = await getDocs(q);
    const bookings: BookingData[] = [];

    querySnapshot.forEach((doc) => {
      bookings.push({ id: doc.id, ...doc.data() } as BookingData);
    });

    return bookings;
  } catch (error) {
    console.error('Error getting all bookings for hotel:', error);
    throw error;
  }
};

/**
 * Get rooms for a hotel
 */
const getRoomsForHotel = async (hotelId: string): Promise<RoomData[]> => {
  try {
    const q = query(
      collection(db, 'rooms'),
      where('hotelId', '==', hotelId)
    );

    const querySnapshot = await getDocs(q);
    const rooms: RoomData[] = [];

    querySnapshot.forEach((doc) => {
      rooms.push({ id: doc.id, ...doc.data() } as RoomData);
    });

    return rooms;
  } catch (error) {
    console.error('Error getting rooms for hotel:', error);
    throw error;
  }
};

/**
 * Calculate daily revenue
 */
const calculateDailyRevenue = (bookings: BookingData[], dateRange: DateRange): { date: string; revenue: number }[] => {
  const result: { date: string; revenue: number }[] = [];

  // Create a map of dates to revenue
  const revenueByDate: { [date: string]: number } = {};

  // Initialize all dates in the range with zero revenue
  const currentDate = new Date(dateRange.startDate);
  while (currentDate <= dateRange.endDate) {
    const dateString = currentDate.toISOString().split('T')[0];
    revenueByDate[dateString] = 0;
    currentDate.setDate(currentDate.getDate() + 1);
  }

  // Calculate revenue for each booking and distribute it across the stay dates
  bookings.forEach(booking => {
    const checkInDate = new Date(booking.checkInDate.toDate());
    const checkOutDate = new Date(booking.checkOutDate.toDate());

    // Calculate number of nights
    const nights = Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24));

    if (nights <= 0) return;

    // Distribute revenue evenly across the stay
    const revenuePerNight = booking.totalAmount / nights;

    // Add revenue to each date of the stay
    const currentDate = new Date(checkInDate);
    while (currentDate < checkOutDate) {
      const dateString = currentDate.toISOString().split('T')[0];

      // Only count dates within the requested range
      if (currentDate >= dateRange.startDate && currentDate <= dateRange.endDate) {
        revenueByDate[dateString] = (revenueByDate[dateString] || 0) + revenuePerNight;
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }
  });

  // Convert the map to an array of objects
  Object.entries(revenueByDate).forEach(([date, revenue]) => {
    result.push({ date, revenue });
  });

  // Sort by date
  result.sort((a, b) => a.date.localeCompare(b.date));

  return result;
};

/**
 * Calculate monthly revenue
 */
const calculateMonthlyRevenue = (bookings: BookingData[], dateRange: DateRange): { month: string; revenue: number }[] => {
  const result: { month: string; revenue: number }[] = [];

  // Create a map of months to revenue
  const revenueByMonth: { [month: string]: number } = {};

  // Initialize all months in the range with zero revenue
  const startYear = dateRange.startDate.getFullYear();
  const startMonth = dateRange.startDate.getMonth();
  const endYear = dateRange.endDate.getFullYear();
  const endMonth = dateRange.endDate.getMonth();

  for (let year = startYear; year <= endYear; year++) {
    const monthStart = (year === startYear) ? startMonth : 0;
    const monthEnd = (year === endYear) ? endMonth : 11;

    for (let month = monthStart; month <= monthEnd; month++) {
      const monthString = `${year}-${(month + 1).toString().padStart(2, '0')}`;
      revenueByMonth[monthString] = 0;
    }
  }

  // Calculate revenue for each booking and distribute it across the stay dates
  bookings.forEach(booking => {
    const checkInDate = new Date(booking.checkInDate.toDate());
    const checkOutDate = new Date(booking.checkOutDate.toDate());

    // Calculate number of nights
    const nights = Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24));

    if (nights <= 0) return;

    // Distribute revenue evenly across the stay
    const revenuePerNight = booking.totalAmount / nights;

    // Add revenue to each date of the stay
    const currentDate = new Date(checkInDate);
    while (currentDate < checkOutDate) {
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;
      const monthString = `${year}-${month.toString().padStart(2, '0')}`;

      // Only count dates within the requested range
      if (currentDate >= dateRange.startDate && currentDate <= dateRange.endDate) {
        revenueByMonth[monthString] = (revenueByMonth[monthString] || 0) + revenuePerNight;
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }
  });

  // Convert the map to an array of objects
  Object.entries(revenueByMonth).forEach(([month, revenue]) => {
    result.push({ month, revenue });
  });

  // Sort by month
  result.sort((a, b) => a.month.localeCompare(b.month));

  return result;
};

/**
 * Calculate revenue by room type
 */
const calculateRevenueByRoomType = async (bookings: BookingData[]): Promise<{ [roomType: string]: number }> => {
  const result: { [roomType: string]: number } = {};

  // Get room types for each booking
  for (const booking of bookings) {
    try {
      // Get room data to find the room type
      const roomDoc = await getDoc(doc(db, 'rooms', booking.roomId));

      if (roomDoc.exists()) {
        const roomData = roomDoc.data();
        const roomTypeId = roomData.type;

        // Get room type name
        const roomTypeDoc = await getDoc(doc(db, 'roomTypes', roomTypeId));

        if (roomTypeDoc.exists()) {
          const roomTypeData = roomTypeDoc.data();
          const roomTypeName = roomTypeData.name;

          // Add revenue to the room type
          result[roomTypeName] = (result[roomTypeName] || 0) + booking.totalAmount;
        }
      }
    } catch (error) {
      console.error('Error getting room type for booking:', error);
    }
  }

  return result;
};

/**
 * Calculate revenue by source
 */
const calculateRevenueBySource = (bookings: BookingData[]): { [source: string]: number } => {
  const result: { [source: string]: number } = {};

  bookings.forEach(booking => {
    const source = booking.source;
    result[source] = (result[source] || 0) + booking.totalAmount;
  });

  return result;
};

/**
 * Calculate daily occupancy
 */
const calculateDailyOccupancy = (
  bookings: BookingData[],
  dateRange: DateRange,
  totalRooms: number
): { date: string; occupancy: number }[] => {
  const result: { date: string; occupancy: number }[] = [];

  // Create a map of dates to occupied rooms count
  const occupiedRoomsByDate: { [date: string]: Set<string> } = {};

  // Initialize all dates in the range with empty sets
  const currentDate = new Date(dateRange.startDate);
  while (currentDate <= dateRange.endDate) {
    const dateString = currentDate.toISOString().split('T')[0];
    occupiedRoomsByDate[dateString] = new Set<string>();
    currentDate.setDate(currentDate.getDate() + 1);
  }

  // Count occupied rooms for each date
  bookings.forEach(booking => {
    const checkInDate = new Date(booking.checkInDate.toDate());
    const checkOutDate = new Date(booking.checkOutDate.toDate());

    const currentDate = new Date(checkInDate);
    while (currentDate < checkOutDate) {
      const dateString = currentDate.toISOString().split('T')[0];

      // Only count dates within the requested range
      if (currentDate >= dateRange.startDate && currentDate <= dateRange.endDate) {
        occupiedRoomsByDate[dateString].add(booking.roomId);
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }
  });

  // Calculate occupancy rate for each date
  Object.entries(occupiedRoomsByDate).forEach(([date, occupiedRooms]) => {
    const occupancy = totalRooms > 0 ? occupiedRooms.size / totalRooms : 0;
    result.push({ date, occupancy });
  });

  // Sort by date
  result.sort((a, b) => a.date.localeCompare(b.date));

  return result;
};

/**
 * Calculate occupancy by room type
 */
const calculateOccupancyByRoomType = async (
  bookings: BookingData[],
  rooms: RoomData[]
): Promise<{ [roomType: string]: number }> => {
  const result: { [roomType: string]: number } = {};

  // Count total rooms by type
  const totalRoomsByType: { [roomTypeId: string]: number } = {};
  const roomTypeNames: { [roomTypeId: string]: string } = {};

  // Get room type names and count rooms by type
  for (const room of rooms) {
    const roomTypeId = room.type;
    totalRoomsByType[roomTypeId] = (totalRoomsByType[roomTypeId] || 0) + 1;

    // Get room type name if not already fetched
    if (!roomTypeNames[roomTypeId]) {
      try {
        const roomTypeDoc = await getDoc(doc(db, 'roomTypes', roomTypeId));

        if (roomTypeDoc.exists()) {
          roomTypeNames[roomTypeId] = roomTypeDoc.data().name;
        }
      } catch (error) {
        console.error('Error getting room type name:', error);
      }
    }
  }

  // Count occupied room-nights by type
  const occupiedRoomNightsByType: { [roomTypeId: string]: number } = {};

  // Get date range from the first booking or use a default range
  const startDate = bookings.length > 0 ?
    new Date(Math.min(...bookings.map(b => b.checkInDate.toDate().getTime()))) :
    new Date();
  const endDate = bookings.length > 0 ?
    new Date(Math.max(...bookings.map(b => b.checkOutDate.toDate().getTime()))) :
    new Date();

  // Count total possible room-nights in the date range
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  // Initialize with zero occupied nights
  Object.keys(totalRoomsByType).forEach(roomTypeId => {
    occupiedRoomNightsByType[roomTypeId] = 0;
  });

  // Count occupied room-nights for each booking
  for (const booking of bookings) {
    try {
      // Get room data to find the room type
      const roomDoc = await getDoc(doc(db, 'rooms', booking.roomId));

      if (roomDoc.exists()) {
        const roomData = roomDoc.data();
        const roomTypeId = roomData.type;

        // Calculate number of nights
        const checkInDate = new Date(booking.checkInDate.toDate());
        const checkOutDate = new Date(booking.checkOutDate.toDate());
        const nights = Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24));

        // Add to occupied room-nights
        occupiedRoomNightsByType[roomTypeId] = (occupiedRoomNightsByType[roomTypeId] || 0) + nights;
      }
    } catch (error) {
      console.error('Error calculating occupancy by room type:', error);
    }
  }

  // Calculate occupancy rate by room type
  Object.entries(totalRoomsByType).forEach(([roomTypeId, totalRooms]) => {
    const roomTypeName = roomTypeNames[roomTypeId] || roomTypeId;
    const totalPossibleRoomNights = totalRooms * daysDiff;
    const occupiedRoomNights = occupiedRoomNightsByType[roomTypeId] || 0;

    const occupancy = totalPossibleRoomNights > 0 ? occupiedRoomNights / totalPossibleRoomNights : 0;
    result[roomTypeName] = occupancy;
  });

  return result;
};

/**
 * Calculate occupancy by day of week
 */
const calculateOccupancyByDayOfWeek = (
  dailyOccupancy: { date: string; occupancy: number }[]
): { [day: string]: number } => {
  const result: { [day: string]: number } = {
    'Sunday': 0,
    'Monday': 0,
    'Tuesday': 0,
    'Wednesday': 0,
    'Thursday': 0,
    'Friday': 0,
    'Saturday': 0
  };

  const dayCount: { [day: string]: number } = {
    'Sunday': 0,
    'Monday': 0,
    'Tuesday': 0,
    'Wednesday': 0,
    'Thursday': 0,
    'Friday': 0,
    'Saturday': 0
  };

  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

  // Sum occupancy by day of week
  dailyOccupancy.forEach(({ date, occupancy }) => {
    const dayOfWeek = new Date(date).getDay();
    const dayName = dayNames[dayOfWeek];

    result[dayName] += occupancy;
    dayCount[dayName]++;
  });

  // Calculate average occupancy for each day of week
  Object.keys(result).forEach(day => {
    if (dayCount[day] > 0) {
      result[day] /= dayCount[day];
    }
  });

  return result;
};

/**
 * Calculate bookings by month
 */
const calculateBookingsByMonth = (
  bookings: BookingData[],
  dateRange: DateRange
): { month: string; bookings: number }[] => {
  const result: { month: string; bookings: number }[] = [];

  // Create a map of months to booking counts
  const bookingsByMonth: { [month: string]: number } = {};

  // Initialize all months in the range with zero bookings
  const startYear = dateRange.startDate.getFullYear();
  const startMonth = dateRange.startDate.getMonth();
  const endYear = dateRange.endDate.getFullYear();
  const endMonth = dateRange.endDate.getMonth();

  for (let year = startYear; year <= endYear; year++) {
    const monthStart = (year === startYear) ? startMonth : 0;
    const monthEnd = (year === endYear) ? endMonth : 11;

    for (let month = monthStart; month <= monthEnd; month++) {
      const monthString = `${year}-${(month + 1).toString().padStart(2, '0')}`;
      bookingsByMonth[monthString] = 0;
    }
  }

  // Count bookings by check-in month
  bookings.forEach(booking => {
    const checkInDate = new Date(booking.checkInDate.toDate());
    const year = checkInDate.getFullYear();
    const month = checkInDate.getMonth() + 1;
    const monthString = `${year}-${month.toString().padStart(2, '0')}`;

    // Only count dates within the requested range
    if (checkInDate >= dateRange.startDate && checkInDate <= dateRange.endDate) {
      bookingsByMonth[monthString] = (bookingsByMonth[monthString] || 0) + 1;
    }
  });

  // Convert the map to an array of objects
  Object.entries(bookingsByMonth).forEach(([month, bookingCount]) => {
    result.push({ month, bookings: bookingCount });
  });

  // Sort by month
  result.sort((a, b) => a.month.localeCompare(b.month));

  return result;
};