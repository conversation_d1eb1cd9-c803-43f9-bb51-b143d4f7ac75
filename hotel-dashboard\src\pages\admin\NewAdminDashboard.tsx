import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Divider,
  IconButton,
  Button,
  CircularProgress,
  Card,
  CardContent,
  CardHeader,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Tooltip,
  useTheme
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Refresh as RefreshIcon,
  Hotel as HotelIcon,
  People as PeopleIcon,
  BookOnline as BookingIcon,
  AttachMoney as MoneyIcon,
  Visibility as VisibilityIcon,
  Add as AddIcon,
  Dashboard as DashboardIcon,
  SupervisorAccount as AdminIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { auth } from '../../firebase/config';
import { getVendors, UserData } from '../../firebase/userService';
import { getHotelsCount } from '../../firebase/hotelService';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>A<PERSON>s,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  <PERSON>lt<PERSON> as RechartsTooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
  LineChart,
  Line
} from 'recharts';

const NewAdminDashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalHotels: 0,
    totalVendors: 0,
    totalBookings: 0,
    totalRevenue: 0,
    totalUsers: 0,
    verifiedUsers: 0
  });
  const [vendors, setVendors] = useState<UserData[]>([]);
  const [recentBookings, setRecentBookings] = useState<any[]>([]);

  // Load data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Fetch vendors
      const vendorsList = await getVendors();
      setVendors(vendorsList);

      // Fetch hotel count
      const hotelCount = await getHotelsCount();

      // Set stats
      setStats({
        totalHotels: hotelCount,
        totalVendors: vendorsList.length,
        totalBookings: 325, // Sample data
        totalRevenue: 85000, // Sample data
        totalUsers: 450, // Sample data
        verifiedUsers: 320 // Sample data
      });

      // Sample recent bookings data
      setRecentBookings([
        { id: 1, guest: 'John Doe', hotel: 'Grand Hotel', checkIn: '2023-07-15', checkOut: '2023-07-20', status: 'Confirmed', amount: 1200 },
        { id: 2, guest: 'Jane Smith', hotel: 'Luxury Resort', checkIn: '2023-07-18', checkOut: '2023-07-25', status: 'Pending', amount: 2100 },
        { id: 3, guest: 'Robert Johnson', hotel: 'Seaside Inn', checkIn: '2023-07-20', checkOut: '2023-07-22', status: 'Confirmed', amount: 800 },
      ]);

      setLoading(false);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchData();
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Colors for charts
  const COLORS = [
    '#3f51b5', // Indigo
    '#f44336', // Red
    '#4caf50', // Green
    '#ff9800', // Orange
    '#9c27b0', // Purple
    '#00bcd4', // Cyan
  ];

  // Sample data for revenue chart
  const revenueData = [
    { month: 'Jan', revenue: 32000 },
    { month: 'Feb', revenue: 45000 },
    { month: 'Mar', revenue: 38000 },
    { month: 'Apr', revenue: 46000 },
    { month: 'May', revenue: 51000 },
    { month: 'Jun', revenue: 65000 },
    { month: 'Jul', revenue: 85000 },
  ];

  // Sample data for hotel distribution
  const hotelDistributionData = [
    { name: 'New York', value: 35 },
    { name: 'Miami', value: 25 },
    { name: 'Los Angeles', value: 20 },
    { name: 'Chicago', value: 15 },
    { name: 'Other', value: 5 },
  ];

  // Sample data for booking status
  const bookingStatusData = [
    { name: 'Confirmed', value: 65 },
    { name: 'Pending', value: 20 },
    { name: 'Cancelled', value: 10 },
    { name: 'No-show', value: 5 },
  ];

  // Sample data for user verification
  const verificationData = [
    { name: 'Verified', value: stats.verifiedUsers },
    { name: 'Unverified', value: stats.totalUsers - stats.verifiedUsers },
  ];

  return (
    <Box>
      {/* Header with gradient background */}
      <Paper
        elevation={3}
        sx={{
          p: 3,
          mb: 3,
          background: 'linear-gradient(45deg, #3f51b5 30%, #2196f3 90%)',
          color: 'white',
          borderRadius: '16px'
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <AdminIcon sx={{ fontSize: 40, mr: 2 }} />
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                Admin Dashboard
              </Typography>
              <Typography variant="subtitle1">
                Welcome back, Super Admin! Here's what's happening across all hotels.
              </Typography>
            </Box>
          </Box>
          <IconButton onClick={handleRefresh} sx={{ color: 'white', bgcolor: 'rgba(255,255,255,0.1)', '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' } }}>
            {loading ? <CircularProgress size={24} color="inherit" /> : <RefreshIcon />}
          </IconButton>
        </Box>
      </Paper>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          {/* Stats Cards */}
          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={3}
              sx={{
                p: 3,
                borderRadius: '16px',
                background: 'linear-gradient(135deg, #3f51b5 0%, #5c6bc0 100%)',
                color: 'white',
                height: '100%'
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Hotels</Typography>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)' }}>
                  <HotelIcon />
                </Avatar>
              </Box>
              <Typography variant="h3" sx={{ mb: 1 }}>
                {stats.totalHotels}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ mr: 0.5, fontSize: 16 }} />
                <Typography variant="body2">+5% this month</Typography>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={3}
              sx={{
                p: 3,
                borderRadius: '16px',
                background: 'linear-gradient(135deg, #f44336 0%, #e57373 100%)',
                color: 'white',
                height: '100%'
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Vendors</Typography>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)' }}>
                  <PeopleIcon />
                </Avatar>
              </Box>
              <Typography variant="h3" sx={{ mb: 1 }}>
                {stats.totalVendors}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ mr: 0.5, fontSize: 16 }} />
                <Typography variant="body2">+2% this month</Typography>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={3}
              sx={{
                p: 3,
                borderRadius: '16px',
                background: 'linear-gradient(135deg, #4caf50 0%, #81c784 100%)',
                color: 'white',
                height: '100%'
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Bookings</Typography>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)' }}>
                  <BookingIcon />
                </Avatar>
              </Box>
              <Typography variant="h3" sx={{ mb: 1 }}>
                {stats.totalBookings}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ mr: 0.5, fontSize: 16 }} />
                <Typography variant="body2">+12% this month</Typography>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={3}
              sx={{
                p: 3,
                borderRadius: '16px',
                background: 'linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)',
                color: 'white',
                height: '100%'
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Revenue</Typography>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)' }}>
                  <MoneyIcon />
                </Avatar>
              </Box>
              <Typography variant="h3" sx={{ mb: 1 }}>
                {formatCurrency(stats.totalRevenue)}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ mr: 0.5, fontSize: 16 }} />
                <Typography variant="body2">+8% this month</Typography>
              </Box>
            </Paper>
          </Grid>

          {/* Revenue Chart */}
          <Grid item xs={12} md={8}>
            <Paper elevation={3} sx={{ p: 3, borderRadius: '16px', height: '100%' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Revenue Trends</Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<VisibilityIcon />}
                  onClick={() => navigate('/admin/reports')}
                >
                  View Reports
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
              <ResponsiveContainer width="100%" height={300}>
                <LineChart
                  data={revenueData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <RechartsTooltip formatter={(value: any) => formatCurrency(value)} />
                  <Line
                    type="monotone"
                    dataKey="revenue"
                    stroke="#3f51b5"
                    strokeWidth={3}
                    dot={{ r: 6 }}
                    activeDot={{ r: 8 }}
                    name="Revenue"
                  />
                </LineChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>

          {/* Booking Status */}
          <Grid item xs={12} md={4}>
            <Paper elevation={3} sx={{ p: 3, borderRadius: '16px', height: '100%' }}>
              <Typography variant="h6" sx={{ mb: 2 }}>Booking Status</Typography>
              <Divider sx={{ mb: 2 }} />
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={bookingStatusData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {bookingStatusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <RechartsTooltip formatter={(value: any, name: any) => [`${value}%`, name]} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>

          {/* Hotel Distribution */}
          <Grid item xs={12} md={6}>
            <Paper elevation={3} sx={{ p: 3, borderRadius: '16px', height: '100%' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Hotel Distribution</Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={() => navigate('/admin/hotels')}
                >
                  Add Hotel
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={hotelDistributionData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <RechartsTooltip formatter={(value: any) => `${value}%`} />
                  <Bar dataKey="value" name="Hotels" radius={[4, 4, 0, 0]}>
                    {hotelDistributionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>

          {/* User Verification */}
          <Grid item xs={12} md={6}>
            <Paper elevation={3} sx={{ p: 3, borderRadius: '16px', height: '100%' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">User Verification Status</Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<VisibilityIcon />}
                  onClick={() => navigate('/admin/verifications')}
                >
                  View Details
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f5f5f5', borderRadius: '8px', width: '48%' }}>
                  <Typography variant="h4" color="primary" sx={{ fontWeight: 'bold' }}>
                    {stats.verifiedUsers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Verified Users
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f5f5f5', borderRadius: '8px', width: '48%' }}>
                  <Typography variant="h4" color="error" sx={{ fontWeight: 'bold' }}>
                    {stats.totalUsers - stats.verifiedUsers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Unverified Users
                  </Typography>
                </Box>
              </Box>
              <ResponsiveContainer width="100%" height={200}>
                <PieChart>
                  <Pie
                    data={verificationData}
                    cx="50%"
                    cy="50%"
                    startAngle={180}
                    endAngle={0}
                    innerRadius={60}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    <Cell fill="#4caf50" />
                    <Cell fill="#f44336" />
                  </Pie>
                  <RechartsTooltip formatter={(value: any, name: any) => [value, name]} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>

          {/* Recent Vendors */}
          <Grid item xs={12} md={6}>
            <Paper elevation={3} sx={{ p: 3, borderRadius: '16px' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Recent Vendors</Typography>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => navigate('/admin/vendors')}
                >
                  View All
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
              <List sx={{ p: 0 }}>
                {vendors.length === 0 ? (
                  <ListItem>
                    <ListItemText primary="No vendors found" />
                  </ListItem>
                ) : (
                  vendors.slice(0, 3).map((vendor) => (
                    <React.Fragment key={vendor.id}>
                      <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: '#3f51b5' }}>
                            {vendor.displayName.charAt(0)}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={vendor.displayName}
                          secondary={vendor.email}
                        />
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => navigate(`/admin/vendors?id=${vendor.id}`)}
                        >
                          View
                        </Button>
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))
                )}
              </List>
            </Paper>
          </Grid>

          {/* Recent Bookings */}
          <Grid item xs={12} md={6}>
            <Paper elevation={3} sx={{ p: 3, borderRadius: '16px' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Recent Bookings</Typography>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => navigate('/admin/bookings')}
                >
                  View All
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
              <List sx={{ p: 0 }}>
                {recentBookings.length === 0 ? (
                  <ListItem>
                    <ListItemText primary="No recent bookings" />
                  </ListItem>
                ) : (
                  recentBookings.map((booking) => (
                    <React.Fragment key={booking.id}>
                      <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: '#4caf50' }}>
                            {booking.guest.charAt(0)}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={booking.guest}
                          secondary={
                            <React.Fragment>
                              <Typography
                                sx={{ display: 'inline' }}
                                component="span"
                                variant="body2"
                                color="text.primary"
                              >
                                {booking.hotel}
                              </Typography>
                              {` — ${booking.checkIn} to ${booking.checkOut}`}
                            </React.Fragment>
                          }
                        />
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                          <Chip
                            label={booking.status}
                            color={booking.status === 'Confirmed' ? 'success' : 'warning'}
                            size="small"
                            sx={{ mb: 1 }}
                          />
                          <Typography variant="body2" color="text.secondary">
                            {formatCurrency(booking.amount)}
                          </Typography>
                        </Box>
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  ))
                )}
              </List>
            </Paper>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default NewAdminDashboard;