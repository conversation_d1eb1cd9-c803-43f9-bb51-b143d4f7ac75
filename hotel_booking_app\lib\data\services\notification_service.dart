import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/data/models/notification_model.dart';

class NotificationService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  List<NotificationModel> _notifications = [];
  bool _isLoading = false;
  String? _error;
  int _unreadCount = 0;

  // Getters
  List<NotificationModel> get notifications => _notifications;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get unreadCount => _unreadCount;

  // Initialize notifications for a user
  Future<void> initializeNotifications(String userId) async {
    if (userId.isEmpty) return;
    
    try {
      _isLoading = true;
      notifyListeners();

      // Set up listener for notifications
      _firestore
          .collection(AppConstants.mobileNotificationsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .snapshots()
          .listen((snapshot) {
        _notifications = snapshot.docs
            .map((doc) => NotificationModel.fromMap(doc.id, doc.data()))
            .toList();
        
        // Calculate unread count
        _unreadCount = _notifications.where((notification) => !notification.read).length;
        
        _isLoading = false;
        _error = null;
        notifyListeners();
      }, onError: (e) {
        _error = 'Failed to load notifications: ${e.toString()}';
        _isLoading = false;
        notifyListeners();
      });
    } catch (e) {
      _error = 'Failed to initialize notifications: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Mark a notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await _firestore
          .collection(AppConstants.mobileNotificationsCollection)
          .doc(notificationId)
          .update({'read': true});
      
      // Update local state
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(read: true);
        if (_unreadCount > 0) _unreadCount--;
        notifyListeners();
      }
    } catch (e) {
      _error = 'Failed to mark notification as read: ${e.toString()}';
      notifyListeners();
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead(String userId) async {
    try {
      _isLoading = true;
      notifyListeners();

      // Get all unread notifications
      final unreadNotifications = await _firestore
          .collection(AppConstants.mobileNotificationsCollection)
          .where('userId', isEqualTo: userId)
          .where('read', isEqualTo: false)
          .get();

      // Create a batch to update all notifications
      final batch = _firestore.batch();
      for (var doc in unreadNotifications.docs) {
        batch.update(doc.reference, {'read': true});
      }
      await batch.commit();

      // Update local state
      _notifications = _notifications.map((notification) => 
        notification.copyWith(read: true)
      ).toList();
      _unreadCount = 0;
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to mark all notifications as read: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Delete a notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _firestore
          .collection(AppConstants.mobileNotificationsCollection)
          .doc(notificationId)
          .delete();
      
      // Update local state
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        final wasUnread = !_notifications[index].read;
        _notifications.removeAt(index);
        if (wasUnread && _unreadCount > 0) _unreadCount--;
        notifyListeners();
      }
    } catch (e) {
      _error = 'Failed to delete notification: ${e.toString()}';
      notifyListeners();
    }
  }

  // Clear all notifications for a user
  Future<void> clearAllNotifications(String userId) async {
    try {
      _isLoading = true;
      notifyListeners();

      // Get all notifications for the user
      final userNotifications = await _firestore
          .collection(AppConstants.mobileNotificationsCollection)
          .where('userId', isEqualTo: userId)
          .get();

      // Create a batch to delete all notifications
      final batch = _firestore.batch();
      for (var doc in userNotifications.docs) {
        batch.delete(doc.reference);
      }
      await batch.commit();

      // Update local state
      _notifications = [];
      _unreadCount = 0;
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to clear notifications: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
    }
  }
}
