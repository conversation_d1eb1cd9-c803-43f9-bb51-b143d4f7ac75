import {
  collection,
  doc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  getDocs,
  getDoc,
  serverTimestamp
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from './config';

// Interface for hotel data
export interface HotelData {
  id?: string;
  name: string;
  description: string;
  address: string;
  city: string;
  country: string;
  zipCode?: string;
  phone?: string;
  email?: string;
  website?: string;
  rating?: number;
  price?: number;
  amenities?: string[];
  images?: string[];
  latitude?: number;
  longitude?: number;
  vendorId: string;
  createdAt?: any;
  updatedAt?: any;
}

/**
 * Create a new hotel with image uploads
 */
export const createHotel = async (hotelData: HotelData, imageFiles?: File[]): Promise<string> => {
  try {
    // Create a new document reference
    const hotelRef = doc(collection(db, 'hotels'));
    const hotelId = hotelRef.id;

    // Upload images if provided
    const imageUrls: string[] = [];

    if (imageFiles && imageFiles.length > 0) {
      for (const file of imageFiles) {
        // Create a storage reference
        const storageRef = ref(storage, `hotels/${hotelId}/${file.name}`);

        // Upload the file
        await uploadBytes(storageRef, file);

        // Get the download URL
        const url = await getDownloadURL(storageRef);
        imageUrls.push(url);
      }
    }

    // Add the hotel data with timestamps and image URLs
    await setDoc(hotelRef, {
      ...hotelData,
      images: [...(hotelData.images || []), ...imageUrls],
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return hotelId;
  } catch (error) {
    console.error('Error creating hotel:', error);
    throw error;
  }
};

/**
 * Get all hotels for a specific vendor
 */
export const getHotelsForVendor = async (vendorId: string) => {
  try {
    const hotelsQuery = query(
      collection(db, 'hotels'),
      where('vendorId', '==', vendorId)
    );

    const hotelsSnapshot = await getDocs(hotelsQuery);
    const hotels: HotelData[] = [];

    hotelsSnapshot.forEach((doc) => {
      hotels.push({ id: doc.id, ...doc.data() } as HotelData);
    });

    return hotels;
  } catch (error) {
    console.error('Error getting hotels:', error);
    throw error;
  }
};

/**
 * Get all hotels (for admin)
 */
export const getAllHotels = async () => {
  try {
    // First try to get real hotels from Firestore
    try {
      const hotelsSnapshot = await getDocs(collection(db, 'hotels'));
      const hotels: HotelData[] = [];

      hotelsSnapshot.forEach((doc) => {
        try {
          const data = doc.data();
          hotels.push({
            id: doc.id,
            ...data,
            // Ensure these fields have default values if missing
            name: data.name || 'Unnamed Hotel',
            description: data.description || '',
            address: data.address || '',
            city: data.city || '',
            country: data.country || '',
            vendorId: data.vendorId || '',
            rating: data.rating || 0,
            price: data.price || 0,
            amenities: data.amenities || [],
            images: data.images || []
          } as HotelData);
        } catch (err) {
          console.error('Error processing hotel document:', err);
        }
      });

      // If we have real hotels, return them
      if (hotels.length > 0) {
        return hotels;
      }
    } catch (err) {
      console.error('Error fetching real hotels:', err);
    }

    // If no real hotels or error occurred, return sample data
    console.log('Returning sample hotel data');

    return [
      {
        id: 'sample-hotel-1',
        name: 'Grand Hotel Luxury',
        description: 'A luxurious 5-star hotel in the heart of the city with stunning views and world-class amenities.',
        address: '123 Main Street',
        city: 'New York',
        country: 'USA',
        zipCode: '10001',
        phone: '******-555-1234',
        email: '<EMAIL>',
        website: 'www.grandhotelluxury.com',
        rating: 4.8,
        price: 350,
        amenities: ['Swimming Pool', 'Spa', 'Gym', 'Restaurant', 'Bar', 'Room Service', 'Free WiFi', 'Parking'],
        images: [
          'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
          'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80'
        ],
        latitude: 40.7128,
        longitude: -74.0060,
        vendorId: 'sample-vendor-1',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      },
      {
        id: 'sample-hotel-2',
        name: 'Seaside Resort & Spa',
        description: 'A beautiful beachfront resort offering relaxation and luxury with stunning ocean views.',
        address: '789 Ocean Drive',
        city: 'Miami',
        country: 'USA',
        zipCode: '33139',
        phone: '******-555-6789',
        email: '<EMAIL>',
        website: 'www.seasideresort.com',
        rating: 4.6,
        price: 280,
        amenities: ['Private Beach', 'Spa', 'Outdoor Pool', 'Restaurant', 'Bar', 'Room Service', 'Free WiFi', 'Parking'],
        images: [
          'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
          'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1049&q=80'
        ],
        latitude: 25.7617,
        longitude: -80.1918,
        vendorId: 'sample-vendor-2',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      },
      {
        id: 'sample-hotel-3',
        name: 'Mountain View Lodge',
        description: 'A cozy mountain retreat with breathtaking views and outdoor activities for all seasons.',
        address: '456 Mountain Road',
        city: 'Aspen',
        country: 'USA',
        zipCode: '81611',
        phone: '******-555-4321',
        email: '<EMAIL>',
        website: 'www.mountainviewlodge.com',
        rating: 4.5,
        price: 220,
        amenities: ['Fireplace', 'Hot Tub', 'Ski Storage', 'Restaurant', 'Bar', 'Free WiFi', 'Parking'],
        images: [
          'https://images.unsplash.com/photo-1548704606-191a5f68b4b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1025&q=80',
          'https://images.unsplash.com/photo-1521783988139-89397d761dce?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1025&q=80'
        ],
        latitude: 39.1911,
        longitude: -106.8175,
        vendorId: 'sample-vendor-3',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      }
    ];
  } catch (error) {
    console.error('Error getting all hotels:', error);
    // Return empty array instead of throwing
    return [];
  }
};

/**
 * Get a hotel by ID
 */
export const getHotelById = async (hotelId: string) => {
  try {
    const hotelDoc = await getDoc(doc(db, 'hotels', hotelId));

    if (hotelDoc.exists()) {
      return { id: hotelDoc.id, ...hotelDoc.data() } as HotelData;
    } else {
      throw new Error('Hotel not found');
    }
  } catch (error) {
    console.error('Error getting hotel:', error);
    throw error;
  }
};

/**
 * Update a hotel with image uploads and deletions
 */
export const updateHotel = async (
  hotelId: string,
  hotelData: Partial<HotelData>,
  imageFiles?: File[],
  deleteImageUrls?: string[]
) => {
  try {
    const hotelRef = doc(db, 'hotels', hotelId);

    // Get current hotel data to access existing images
    const hotelDoc = await getDoc(hotelRef);
    if (!hotelDoc.exists()) {
      throw new Error('Hotel not found');
    }

    const currentHotel = hotelDoc.data() as HotelData;
    let updatedImages = [...(currentHotel.images || [])];

    // Delete images if specified
    if (deleteImageUrls && deleteImageUrls.length > 0) {
      // Remove from storage
      for (const url of deleteImageUrls) {
        try {
          // Extract the path from the URL
          const urlPath = url.split('?')[0].split('/o/')[1];
          if (urlPath) {
            const decodedPath = decodeURIComponent(urlPath);
            const imageRef = ref(storage, decodedPath);
            await deleteObject(imageRef);
          }
        } catch (err) {
          console.error('Error deleting image from storage:', err);
          // Continue with other deletions even if one fails
        }
      }

      // Remove from images array
      updatedImages = updatedImages.filter(url => !deleteImageUrls.includes(url));
    }

    // Upload new images if provided
    if (imageFiles && imageFiles.length > 0) {
      for (const file of imageFiles) {
        const storageRef = ref(storage, `hotels/${hotelId}/${file.name}`);
        await uploadBytes(storageRef, file);
        const url = await getDownloadURL(storageRef);
        updatedImages.push(url);
      }
    }

    // Update the hotel data
    await updateDoc(hotelRef, {
      ...hotelData,
      images: updatedImages,
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('Error updating hotel:', error);
    throw error;
  }
};

/**
 * Delete a hotel
 */
export const deleteHotel = async (hotelId: string) => {
  try {
    await deleteDoc(doc(db, 'hotels', hotelId));
    return true;
  } catch (error) {
    console.error('Error deleting hotel:', error);
    throw error;
  }
};

/**
 * Get total number of hotels
 */
export const getHotelsCount = async (): Promise<number> => {
  try {
    const hotelsSnapshot = await getDocs(collection(db, 'hotels'));
    return hotelsSnapshot.size;
  } catch (error) {
    console.error('Error getting hotels count:', error);
    throw error;
  }
};

/**
 * Get hotels by vendor ID
 * This is an alias for getHotelsForVendor with a different return format
 */
export const getHotelsByVendor = async (vendorId: string) => {
  try {
    const hotels = await getHotelsForVendor(vendorId);
    return { hotels };
  } catch (error) {
    console.error('Error getting hotels by vendor:', error);
    throw error;
  }
};
