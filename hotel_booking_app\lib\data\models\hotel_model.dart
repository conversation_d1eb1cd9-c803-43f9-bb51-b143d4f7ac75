import 'package:cloud_firestore/cloud_firestore.dart';

class Hotel {
  final String id;
  final String name;
  final String description;
  final String address;
  final String city;
  final String country;
  final String? zipCode;
  final String? phone;
  final String? email;
  final String? website;
  final double rating;
  final List<String> amenities;
  final List<String> images;
  final String? vendorId;
  final DateTime? createdAt;
  final double price; // Starting price for rooms
  final int? reviewCount;
  final String? type;

  Hotel({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.city,
    required this.country,
    this.zipCode,
    this.phone,
    this.email,
    this.website,
    required this.rating,
    required this.amenities,
    required this.images,
    this.vendorId,
    this.createdAt,
    required this.price,
    this.reviewCount,
    this.type,
  });

  factory Hotel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return Hotel(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      address: data['address'] ?? '',
      city: data['city'] ?? '',
      country: data['country'] ?? '',
      zipCode: data['zipCode'] ?? '',
      phone: data['phone'] ?? '',
      email: data['email'] ?? '',
      website: data['website'] ?? '',
      rating: (data['rating'] ?? 0.0).toDouble(),
      amenities: List<String>.from(data['amenities'] ?? []),
      images: List<String>.from(data['images'] ?? []),
      vendorId: data['vendorId'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      price: (data['price'] ?? 0.0).toDouble(),
      reviewCount: data['reviewCount'] ?? 0,
      type: data['type'],
    );
  }

  factory Hotel.fromMap(Map<String, dynamic> map) {
    return Hotel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      address: map['address'] ?? '',
      city: map['city'] ?? '',
      country: map['country'] ?? '',
      zipCode: map['zipCode'],
      phone: map['phone'],
      email: map['email'],
      website: map['website'],
      rating: (map['rating'] ?? 0.0).toDouble(),
      amenities: List<String>.from(map['amenities'] ?? []),
      images: List<String>.from(map['images'] ?? []),
      vendorId: map['vendorId'],
      createdAt: map['createdAt'] != null
          ? (map['createdAt'] as Timestamp).toDate()
          : null,
      price: (map['price'] ?? 0.0).toDouble(),
      reviewCount: map['reviewCount'],
      type: map['type'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'address': address,
      'city': city,
      'country': country,
      'zipCode': zipCode,
      'phone': phone,
      'email': email,
      'website': website,
      'rating': rating,
      'amenities': amenities,
      'images': images,
      'vendorId': vendorId,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'price': price,
      'reviewCount': reviewCount,
      'type': type,
    };
  }

  Hotel copyWith({
    String? id,
    String? name,
    String? description,
    String? address,
    String? city,
    String? country,
    String? zipCode,
    String? phone,
    String? email,
    String? website,
    double? rating,
    List<String>? amenities,
    List<String>? images,
    String? vendorId,
    DateTime? createdAt,
    double? price,
    int? reviewCount,
    String? type,
  }) {
    return Hotel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      zipCode: zipCode ?? this.zipCode,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      website: website ?? this.website,
      rating: rating ?? this.rating,
      amenities: amenities ?? this.amenities,
      images: images ?? this.images,
      vendorId: vendorId ?? this.vendorId,
      createdAt: createdAt ?? this.createdAt,
      price: price ?? this.price,
      reviewCount: reviewCount ?? this.reviewCount,
      type: type ?? this.type,
    );
  }
}
