import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Snackbar,
  Alert,
  CircularProgress,
  Grid,
  Chip,
  Stack,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Hotel as HotelIcon
} from '@mui/icons-material';
import { getHotelsForVendor, createHotel, updateHotel, deleteHotel, HotelData } from '../../firebase/hotelService';
import { auth } from '../../firebase/config';

const Hotels: React.FC = () => {
  const [hotels, setHotels] = useState<HotelData[]>([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingHotel, setEditingHotel] = useState<HotelData | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    address: '',
    city: '',
    country: '',
    zipCode: '',
    phone: '',
    email: '',
    website: '',
    amenities: ''
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  // Load hotels on component mount
  useEffect(() => {
    fetchHotels();
  }, []);

  const fetchHotels = async () => {
    try {
      setLoading(true);
      if (!auth.currentUser) return;

      const hotelsList = await getHotelsForVendor(auth.currentUser.uid);
      setHotels(hotelsList);
    } catch (error) {
      console.error('Error fetching hotels:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load hotels',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (hotel?: HotelData) => {
    if (hotel) {
      // Edit mode
      setEditingHotel(hotel);
      setFormData({
        name: hotel.name,
        description: hotel.description || '',
        address: hotel.address || '',
        city: hotel.city || '',
        country: hotel.country || '',
        zipCode: hotel.zipCode || '',
        phone: hotel.phone || '',
        email: hotel.email || '',
        website: hotel.website || '',
        amenities: hotel.amenities ? hotel.amenities.join(', ') : ''
      });
    } else {
      // Create mode
      setEditingHotel(null);
      setFormData({
        name: '',
        description: '',
        address: '',
        city: '',
        country: '',
        zipCode: '',
        phone: '',
        email: '',
        website: '',
        amenities: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async () => {
    try {
      if (!auth.currentUser) {
        throw new Error('You must be logged in to perform this action');
      }

      // Parse amenities from comma-separated string to array
      const amenitiesArray = formData.amenities
        ? formData.amenities.split(',').map(item => item.trim()).filter(item => item !== '')
        : [];

      if (editingHotel) {
        // Update existing hotel
        await updateHotel(editingHotel.id!, {
          name: formData.name,
          description: formData.description,
          address: formData.address,
          city: formData.city,
          country: formData.country,
          zipCode: formData.zipCode,
          phone: formData.phone,
          email: formData.email,
          website: formData.website,
          amenities: amenitiesArray
        });

        setSnackbar({
          open: true,
          message: 'Hotel updated successfully',
          severity: 'success'
        });
      } else {
        // Create new hotel
        if (!formData.name || !formData.address || !formData.city || !formData.country) {
          throw new Error('Name, address, city, and country are required');
        }

        const hotelData: HotelData = {
          name: formData.name,
          description: formData.description,
          address: formData.address,
          city: formData.city,
          country: formData.country,
          zipCode: formData.zipCode,
          phone: formData.phone,
          email: formData.email,
          website: formData.website,
          amenities: amenitiesArray,
          vendorId: auth.currentUser.uid
        };

        await createHotel(hotelData);

        setSnackbar({
          open: true,
          message: 'Hotel created successfully',
          severity: 'success'
        });
      }

      handleCloseDialog();
      fetchHotels(); // Refresh the list
    } catch (error: any) {
      console.error('Error saving hotel:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to save hotel',
        severity: 'error'
      });
    }
  };

  const handleDeleteHotel = async (hotelId: string) => {
    if (window.confirm('Are you sure you want to delete this hotel?')) {
      try {
        await deleteHotel(hotelId);
        setSnackbar({
          open: true,
          message: 'Hotel deleted successfully',
          severity: 'success'
        });
        fetchHotels(); // Refresh the list
      } catch (error) {
        console.error('Error deleting hotel:', error);
        setSnackbar({
          open: true,
          message: 'Failed to delete hotel',
          severity: 'error'
        });
      }
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          My Hotel
        </Typography>
        {hotels.length === 0 && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Add Hotel
          </Button>
        )}
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          {hotels.length === 0 ? (
            <Grid item xs={12}>
              <Paper sx={{ p: 3, textAlign: 'center' }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                  <HotelIcon sx={{ fontSize: 60, color: 'primary.main', opacity: 0.7 }} />
                  <Typography variant="h6">
                    You haven't set up your hotel yet
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    As a hotel vendor, you need to set up your hotel profile before you can manage rooms, bookings, and other services.
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => handleOpenDialog()}
                    sx={{ mt: 2 }}
                  >
                    Set Up My Hotel
                  </Button>
                </Box>
              </Paper>
            </Grid>
          ) : (
            hotels.map((hotel) => (
              <Grid item xs={12} key={hotel.id}>
                <Paper sx={{ p: 3, display: 'flex', flexDirection: 'column' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <HotelIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                      <Box>
                        <Typography variant="h5" component="div" gutterBottom={false}>
                          {hotel.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {hotel.address}, {hotel.city}, {hotel.country}
                        </Typography>
                      </Box>
                    </Box>
                    <Box>
                      <Button
                        variant="outlined"
                        startIcon={<EditIcon />}
                        onClick={() => handleOpenDialog(hotel)}
                        sx={{ mr: 1 }}
                      >
                        Edit Details
                      </Button>
                    </Box>
                  </Box>

                  <Divider sx={{ mb: 3 }} />

                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="h6" gutterBottom>
                        Hotel Description
                      </Typography>
                      <Typography variant="body1" paragraph>
                        {hotel.description || 'No description provided.'}
                      </Typography>

                      <Typography variant="h6" gutterBottom>
                        Contact Information
                      </Typography>
                      <Typography variant="body2" paragraph>
                        <strong>Phone:</strong> {hotel.phone || 'Not provided'}<br />
                        <strong>Email:</strong> {hotel.email || 'Not provided'}<br />
                        <strong>Website:</strong> {hotel.website || 'Not provided'}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <Typography variant="h6" gutterBottom>
                        Amenities
                      </Typography>
                      {hotel.amenities && hotel.amenities.length > 0 ? (
                        <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>
                          {hotel.amenities.map((amenity, index) => (
                            <Chip key={index} label={amenity} size="small" />
                          ))}
                        </Stack>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          No amenities listed
                        </Typography>
                      )}

                      <Box sx={{ mt: 3 }}>
                        <Typography variant="h6" gutterBottom>
                          Quick Links
                        </Typography>
                        <Button variant="text" color="primary" sx={{ mr: 1 }}>
                          Manage Rooms
                        </Button>
                        <Button variant="text" color="primary" sx={{ mr: 1 }}>
                          View Bookings
                        </Button>
                        <Button variant="text" color="primary">
                          Service Requests
                        </Button>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
            ))
          )}
        </Grid>
      )}

      {/* Add/Edit Hotel Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingHotel ? 'Edit Hotel' : 'Add New Hotel'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  required
                  fullWidth
                  label="Hotel Name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  multiline
                  rows={3}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  required
                  fullWidth
                  label="Address"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  label="City"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  label="Country"
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Zip Code"
                  name="zipCode"
                  value={formData.zipCode}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Website"
                  name="website"
                  value={formData.website}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Amenities (comma-separated)"
                  name="amenities"
                  value={formData.amenities}
                  onChange={handleInputChange}
                  helperText="E.g. WiFi, Pool, Gym, Restaurant"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingHotel ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Hotels;
