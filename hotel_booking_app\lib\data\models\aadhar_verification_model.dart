import 'package:cloud_firestore/cloud_firestore.dart';

class AadharVerification {
  final String id;
  final String userId;
  final String aadharNumber;
  final String name;
  final String dob;
  final String gender;
  final String address;
  final bool isVerified;
  final String? verificationMethod; // 'profile' or 'checkin'
  final String? hotelId; // If verified during check-in
  final String? bookingId; // If verified during check-in
  final DateTime? verifiedAt;
  final String? photoUrl; // URL to the Aadhar card photo
  final Map<String, dynamic>? additionalDetails; // Any additional details from API

  AadharVerification({
    required this.id,
    required this.userId,
    required this.aadharNumber,
    required this.name,
    required this.dob,
    required this.gender,
    required this.address,
    required this.isVerified,
    this.verificationMethod,
    this.hotelId,
    this.bookingId,
    this.verifiedAt,
    this.photoUrl,
    this.additionalDetails,
  });

  factory AadharVerification.fromMap(Map<String, dynamic> map) {
    return AadharVerification(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      aadharNumber: map['aadharNumber'] ?? '',
      name: map['name'] ?? '',
      dob: map['dob'] ?? '',
      gender: map['gender'] ?? '',
      address: map['address'] ?? '',
      isVerified: map['isVerified'] ?? false,
      verificationMethod: map['verificationMethod'],
      hotelId: map['hotelId'],
      bookingId: map['bookingId'],
      verifiedAt: map['verifiedAt'] != null
          ? (map['verifiedAt'] as Timestamp).toDate()
          : null,
      photoUrl: map['photoUrl'],
      additionalDetails: map['additionalDetails'],
    );
  }

  factory AadharVerification.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    if (data == null) {
      throw Exception('Document data was null');
    }

    return AadharVerification(
      id: doc.id,
      userId: data['userId'] ?? '',
      aadharNumber: data['aadharNumber'] ?? '',
      name: data['name'] ?? '',
      dob: data['dob'] ?? '',
      gender: data['gender'] ?? '',
      address: data['address'] ?? '',
      isVerified: data['isVerified'] ?? false,
      verificationMethod: data['verificationMethod'],
      hotelId: data['hotelId'],
      bookingId: data['bookingId'],
      verifiedAt: data['verifiedAt'] != null
          ? (data['verifiedAt'] as Timestamp).toDate()
          : null,
      photoUrl: data['photoUrl'],
      additionalDetails: data['additionalDetails'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'aadharNumber': aadharNumber,
      'name': name,
      'dob': dob,
      'gender': gender,
      'address': address,
      'isVerified': isVerified,
      'verificationMethod': verificationMethod,
      'hotelId': hotelId,
      'bookingId': bookingId,
      'verifiedAt': verifiedAt != null ? Timestamp.fromDate(verifiedAt!) : null,
      'photoUrl': photoUrl,
      'additionalDetails': additionalDetails,
    };
  }

  AadharVerification copyWith({
    String? id,
    String? userId,
    String? aadharNumber,
    String? name,
    String? dob,
    String? gender,
    String? address,
    bool? isVerified,
    String? verificationMethod,
    String? hotelId,
    String? bookingId,
    DateTime? verifiedAt,
    String? photoUrl,
    Map<String, dynamic>? additionalDetails,
  }) {
    return AadharVerification(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      aadharNumber: aadharNumber ?? this.aadharNumber,
      name: name ?? this.name,
      dob: dob ?? this.dob,
      gender: gender ?? this.gender,
      address: address ?? this.address,
      isVerified: isVerified ?? this.isVerified,
      verificationMethod: verificationMethod ?? this.verificationMethod,
      hotelId: hotelId ?? this.hotelId,
      bookingId: bookingId ?? this.bookingId,
      verifiedAt: verifiedAt ?? this.verifiedAt,
      photoUrl: photoUrl ?? this.photoUrl,
      additionalDetails: additionalDetails ?? this.additionalDetails,
    );
  }
}
