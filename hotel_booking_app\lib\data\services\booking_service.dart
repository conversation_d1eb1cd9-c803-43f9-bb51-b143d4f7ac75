import 'package:flutter/foundation.dart';
import 'package:hotel_booking_app/core/constants/app_constants.dart';
import 'package:hotel_booking_app/data/models/booking_model.dart';
import 'package:hotel_booking_app/data/models/hotel_model.dart';
import 'package:hotel_booking_app/data/models/room_model.dart';
import 'package:hotel_booking_app/data/repositories/booking_repository.dart';
import 'package:hotel_booking_app/data/repositories/hotel_repository.dart';

class BookingService extends ChangeNotifier {
  final BookingRepository _bookingRepository = BookingRepository();
  final HotelRepository _hotelRepository = HotelRepository();

  List<Booking> _userBookings = [];
  List<Booking> _upcomingBookings = [];
  List<Booking> _activeBookings = [];
  List<Booking> _pastBookings = [];
  Booking? _selectedBooking;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Booking> get userBookings => _userBookings;
  List<Booking> get upcomingBookings => _upcomingBookings;
  List<Booking> get activeBookings => _activeBookings;
  List<Booking> get pastBookings => _pastBookings;
  Booking? get selectedBooking => _selectedBooking;
  Booking? get currentBooking => _selectedBooking;
  List<Booking> get bookings => _userBookings;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Create a new booking
  Future<String?> createBooking({
    required String userId,
    required Hotel hotel,
    required Room room,
    required DateTime checkIn,
    required DateTime checkOut,
    required int guests,
    Map<String, dynamic>? guestDetails,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // For now, assume room is available

      // Calculate total price
      final durationInDays = checkOut.difference(checkIn).inDays;
      final totalPrice = room.price * durationInDays;

      // Create booking
      final booking = Booking(
        id: '', // Will be set by Firestore
        userId: userId,
        hotelId: hotel.id,
        roomId: room.id,
        hotelName: hotel.name,
        roomName: room.name,
        checkIn: checkIn,
        checkOut: checkOut,
        guests: guests,
        totalPrice: totalPrice,
        status: AppConstants.statusPending,
        paymentStatus: AppConstants.paymentPending,
        createdAt: DateTime.now(),
        guestDetails: guestDetails,
      );

      final bookingId = await _bookingRepository.createBooking(booking.toMap());

      _isLoading = false;
      notifyListeners();
      return bookingId;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  // Fetch user bookings
  Future<void> fetchUserBookings(String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _userBookings = await _bookingRepository.fetchBookingsByUserId(userId);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Fetch upcoming bookings
  Future<void> fetchUpcomingBookings(String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _upcomingBookings =
          await _bookingRepository.fetchUpcomingBookings(userId);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Fetch active bookings
  Future<void> fetchActiveBookings(String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _activeBookings = await _bookingRepository.fetchActiveBookings(userId);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Fetch past bookings
  Future<void> fetchPastBookings(String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _pastBookings = await _bookingRepository.fetchPastBookings(userId);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Fetch booking by ID
  Future<void> fetchBookingById(String bookingId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _selectedBooking = await _bookingRepository.fetchBookingById(bookingId);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Get booking by ID (returns the booking without setting it as selected)
  Future<Booking?> getBookingById(String bookingId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final booking = await _bookingRepository.fetchBookingById(bookingId);
      _isLoading = false;
      notifyListeners();
      return booking;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  // Cancel booking
  Future<void> cancelBooking(String bookingId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Get the room ID from the selected booking
      final roomId = _selectedBooking?.roomId ?? '';

      // Cancel the booking
      await _bookingRepository.cancelBooking(bookingId, roomId);

      // Refresh selected booking
      if (_selectedBooking != null && _selectedBooking!.id == bookingId) {
        await fetchBookingById(bookingId);
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // Calculate booking price
  double calculateBookingPrice(Room room, DateTime checkIn, DateTime checkOut) {
    final durationInDays = checkOut.difference(checkIn).inDays;
    return room.price * durationInDays;
  }

  // Check if a room is available for the given dates
  Future<bool> isRoomAvailable(
      String roomId, DateTime checkIn, DateTime checkOut) async {
    try {
      // For now, always return true
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Update booking status
  Future<bool> updateBookingStatus(String bookingId, String status) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Update booking status
      final success =
          await _bookingRepository.updateBookingStatus(bookingId, status);

      // Refresh selected booking if it's the one being updated
      if (success &&
          _selectedBooking != null &&
          _selectedBooking!.id == bookingId) {
        await fetchBookingById(bookingId);
      }

      _isLoading = false;
      notifyListeners();
      return success;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Update payment status
  Future<bool> updatePaymentStatus(
      String bookingId, String paymentStatus) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Update payment status
      final success = await _bookingRepository.updatePaymentStatus(
          bookingId, paymentStatus);

      // Refresh selected booking if it's the one being updated
      if (success &&
          _selectedBooking != null &&
          _selectedBooking!.id == bookingId) {
        await fetchBookingById(bookingId);
      }

      _isLoading = false;
      notifyListeners();
      return success;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
}
